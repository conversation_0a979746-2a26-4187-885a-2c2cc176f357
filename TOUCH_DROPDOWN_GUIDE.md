# Touch Dropdown Handler - <PERSON><PERSON><PERSON>ng dẫn sử dụng

## Tổng quan

Touch Dropdown Handler là tính năng tự động phát hiện thiết bị có màn hình cảm ứng và điều chỉnh hành vi của dropdown Cart và User Account để tối ưu trải nghiệm người dùng.

## Tính năng

### 🖥️ **Desktop (Hover devices)**
- Giữ nguyên hành vi hover hiện có
- Khi hover vào Cart/User Account → hiển thị dropdown
- Không cần thay đổi gì

### 📱 **Touch devices (Mobile/Tablet)**
- Tự động phát hiện thiết bị cảm ứng
- **<PERSON><PERSON><PERSON> tap đầu tiên**: Hiển thị dropdown
- **<PERSON><PERSON><PERSON> tap thứ hai**: Đóng dropdown và chuyển trang
- **Tap ra ngoài**: Đóng dropdown
- **ESC key**: Đóng dropdown

## C<PERSON>ch hoạt động

### Ph<PERSON>t hiện thiết bị cảm ứng
```javascript
detectTouchDevice() {
    return (
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0 ||
        window.matchMedia('(hover: none)').matches
    );
}
```

### Xử lý Cart
1. **Tap lần 1**: Mở mini cart dropdown
2. **Tap lần 2**: Đóng dropdown và chuyển đến `/cart.php`
3. **Tap vào "Xem giỏ hàng"**: Chuyển đến `/cart.php`
4. **Tap vào "Thanh toán"**: Chuyển đến `/checkout.php`

### Xử lý User Account
1. **Tap lần 1**: Mở user menu dropdown
2. **Tap lần 2**: Đóng dropdown
3. **Tap vào menu item**: Chuyển đến trang tương ứng

## Files được thêm

### 1. JavaScript Handler
```
assets/js/touch-dropdown-handler.js
```
- Class `TouchDropdownHandler` chính
- Phát hiện thiết bị cảm ứng
- Xử lý events cho Cart và User Account
- Quản lý trạng thái dropdown

### 2. CSS Styling
```
assets/css/touch-dropdown.css
```
- Styling cho touch behavior
- Animation cho dropdown
- Responsive adjustments
- Media queries cho touch devices

### 3. Test File
```
test-touch-dropdown.html
```
- File test để kiểm tra tính năng
- Hiển thị loại thiết bị
- Demo dropdown behavior

## Cài đặt

### 1. Files đã được thêm vào header
```php
<!-- Touch Dropdown CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/touch-dropdown.css">

<!-- Touch Dropdown Handler JS -->
<script src="<?php echo BASE_URL; ?>/assets/js/touch-dropdown-handler.js" defer></script>
```

### 2. Tự động khởi chạy
```javascript
document.addEventListener('DOMContentLoaded', () => {
    new TouchDropdownHandler();
});
```

## CSS Classes

### Trạng thái Active
- `.dropdown-active` - Được thêm khi dropdown mở
- `.dropdown-opening` - Animation khi mở
- `.dropdown-closing` - Animation khi đóng

### Media Queries
```css
@media (hover: none) {
    /* Chỉ áp dụng cho touch devices */
}

@media (hover: hover) {
    /* Chỉ áp dụng cho desktop */
}
```

## Kiểm tra tính năng

### 1. Chạy file test
```
http://localhost/noithatbangvu/test-touch-dropdown.html
```

### 2. Test trên các thiết bị
- **Desktop**: Hover để xem dropdown
- **Mobile/Tablet**: Tap để mở/đóng dropdown
- **Chrome DevTools**: Toggle device toolbar để test

### 3. Kiểm tra console
- Mở Developer Tools
- Xem console để debug nếu cần

## Tương thích

### Browsers hỗ trợ
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers

### Touch Detection Methods
1. `'ontouchstart' in window` - Cơ bản
2. `navigator.maxTouchPoints > 0` - Modern
3. `navigator.msMaxTouchPoints > 0` - IE/Edge legacy
4. `window.matchMedia('(hover: none)')` - CSS Media Query

## Troubleshooting

### Dropdown không hiển thị
1. Kiểm tra CSS file đã load
2. Kiểm tra JavaScript không có lỗi
3. Verify HTML structure (class names)

### Touch detection không chính xác
1. Test trên thiết bị thật
2. Kiểm tra user agent
3. Sử dụng Chrome DevTools device mode

### Animation không mượt
1. Kiểm tra CSS transitions
2. Verify z-index values
3. Test performance trên thiết bị cũ

## Customization

### Thay đổi animation timing
```css
.dropdown-opening,
.dropdown-closing {
    animation-duration: 0.2s; /* Thay đổi từ 0.3s */
}
```

### Thay đổi delay chuyển trang
```javascript
setTimeout(() => {
    window.location.href = cartBtn.href;
}, 200); // Thay đổi từ 150ms
```

### Thêm thiết bị cảm ứng khác
```javascript
detectTouchDevice() {
    return (
        // ... existing checks ...
        || navigator.userAgent.includes('CustomDevice')
    );
}
```

## Performance

### Tối ưu hóa
- ✅ Chỉ chạy trên touch devices
- ✅ Event delegation
- ✅ Minimal DOM queries
- ✅ CSS hardware acceleration

### Memory usage
- ✅ Cleanup event listeners
- ✅ Không memory leaks
- ✅ Efficient DOM manipulation

---

**Tác giả**: Nội Thất Băng Vũ Development Team  
**Phiên bản**: 1.0.0  
**Cập nhật**: 2024 