<?php
require_once 'includes/init.php';
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Fix - Nội Thất Băng Vũ</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-variables.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-improved.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #F37321 0%, #ff6b35 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #F37321;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 1.2rem;
        }
        
        .search-form {
            position: relative;
            margin: 20px 0;
        }
        
        .search-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #F37321;
            box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
        }
        
        .test-results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e2e8f0;
            min-height: 100px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 5px 0;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-error {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .test-instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .test-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
        
        .test-instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-bug-slash"></i> Test Khắc Phục Lỗi Tìm Kiếm</h1>
            <p>Kiểm tra việc hiển thị thông báo "Không tìm thấy sản phẩm" trong quá trình loading</p>
        </div>
        
        <div class="test-content">
            <div class="test-instructions">
                <h4><i class="fas fa-list-check"></i> Hướng dẫn test:</h4>
                <ol>
                    <li>Nhập từ khóa vào ô tìm kiếm bên dưới (ví dụ: "sofa", "xyz123", "bàn")</li>
                    <li>Quan sát trạng thái loading - phải hiển thị spinner và text "Đang tìm kiếm..."</li>
                    <li>Kiểm tra thông báo "Không tìm thấy..." chỉ xuất hiện SAU khi loading hoàn tất</li>
                    <li>Thử với từ khóa không tồn tại để test trường hợp không có kết quả</li>
                </ol>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search"></i> Test Tìm Kiếm với search-improved.js</h3>
                <form class="search-form" id="search-form-improved">
                    <input type="text" class="search-input" placeholder="Nhập từ khóa để test (search-improved.js)..." autocomplete="off">
                </form>
                <div class="test-results" id="results-improved">
                    <p><i class="fas fa-info-circle"></i> Nhập từ khóa để bắt đầu test...</p>
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search"></i> Test Tìm Kiếm với search.js (cũ đã fix)</h3>
                <form class="search-form" id="search-form-old">
                    <input type="text" class="search-input" placeholder="Nhập từ khóa để test (search.js cũ)..." autocomplete="off">
                </form>
                <div class="test-results" id="results-old">
                    <p><i class="fas fa-info-circle"></i> Nhập từ khóa để bắt đầu test...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        
        // Test search-improved.js logic
        function testSearchImproved() {
            const searchInput = document.querySelector('#search-form-improved .search-input');
            const searchForm = document.getElementById('search-form-improved');
            const resultsDiv = document.getElementById('results-improved');
            
            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions hidden';
            searchForm.appendChild(suggestionsContainer);
            
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim();
                clearTimeout(searchTimeout);
                
                if (keyword.length < 1) {
                    suggestionsContainer.classList.add('hidden');
                    resultsDiv.innerHTML = '<p><i class="fas fa-info-circle"></i> Nhập từ khóa để bắt đầu test...</p>';
                    return;
                }
                
                let debounceTime = keyword.length === 1 ? 600 : keyword.length === 2 ? 400 : 200;
                
                searchTimeout = setTimeout(() => {
                    testSearchLogic(keyword, suggestionsContainer, resultsDiv, 'improved');
                }, debounceTime);
            });
        }
        
        // Test search.js logic
        function testSearchOld() {
            const searchInput = document.querySelector('#search-form-old .search-input');
            const searchForm = document.getElementById('search-form-old');
            const resultsDiv = document.getElementById('results-old');
            
            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions hidden';
            searchForm.appendChild(suggestionsContainer);
            
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim();
                clearTimeout(searchTimeout);
                
                if (keyword.length < 1) {
                    suggestionsContainer.classList.add('hidden');
                    resultsDiv.innerHTML = '<p><i class="fas fa-info-circle"></i> Nhập từ khóa để bắt đầu test...</p>';
                    return;
                }
                
                let debounceTime = keyword.length === 1 ? 600 : keyword.length === 2 ? 400 : 200;
                
                searchTimeout = setTimeout(() => {
                    testSearchLogic(keyword, suggestionsContainer, resultsDiv, 'old');
                }, debounceTime);
            });
        }
        
        function testSearchLogic(keyword, container, resultsDiv, type) {
            resultsDiv.innerHTML = `
                <div class="status-info">
                    <i class="fas fa-search"></i> Đang test với từ khóa: "${keyword}" (${type})
                </div>
            `;
            
            // Hiển thị loading state
            container.innerHTML = `
                <div class="search-loading">
                    <div class="search-loading-spinner"></div>
                    <p style="margin: 0; color: #666; font-size: 0.9rem;">Đang tìm kiếm...</p>
                </div>
            `;
            container.classList.remove('hidden');
            
            const startTime = Date.now();
            const minLoadingTime = 300;
            
            fetch(`${BASE_URL}/api/search_suggestions.php?keyword=${encodeURIComponent(keyword)}`)
                .then(response => response.json())
                .then(data => {
                    const elapsedTime = Date.now() - startTime;
                    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
                    
                    setTimeout(() => {
                        container.innerHTML = '';
                        
                        if (data.suggestions.length === 0) {
                            container.innerHTML = `
                                <div class="search-no-results" style="padding: 2rem; text-align: center; color: #666;">
                                    <i class="fas fa-search" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #999;"></i>
                                    <p style="margin: 0; font-size: 0.9rem;">Không tìm thấy sản phẩm nào phù hợp với từ khóa "<strong>${keyword}</strong>"</p>
                                    <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #999;">Hãy thử tìm kiếm với từ khóa khác</p>
                                </div>
                            `;
                            resultsDiv.innerHTML += `
                                <div class="status-success">
                                    <i class="fas fa-check-circle"></i> ✅ Thông báo "Không tìm thấy..." chỉ hiển thị SAU khi loading hoàn tất (${elapsedTime + remainingTime}ms)
                                </div>
                            `;
                        } else {
                            data.suggestions.slice(0, 3).forEach(product => {
                                const productElement = document.createElement('div');
                                productElement.style.padding = '10px';
                                productElement.style.borderBottom = '1px solid #eee';
                                productElement.innerHTML = `
                                    <strong>${product.name}</strong><br>
                                    <small style="color: #666;">${product.category} - ${product.price}</small>
                                `;
                                container.appendChild(productElement);
                            });
                            resultsDiv.innerHTML += `
                                <div class="status-success">
                                    <i class="fas fa-check-circle"></i> ✅ Tìm thấy ${data.suggestions.length} sản phẩm (${elapsedTime + remainingTime}ms)
                                </div>
                            `;
                        }
                        
                        container.classList.remove('hidden');
                    }, remainingTime);
                })
                .catch(error => {
                    const elapsedTime = Date.now() - startTime;
                    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
                    
                    setTimeout(() => {
                        container.innerHTML = `
                            <div style="padding: 20px; color: #dc2626; text-align: center;">
                                <i class="fas fa-exclamation-triangle"></i> Có lỗi xảy ra: ${error.message}
                            </div>
                        `;
                        resultsDiv.innerHTML += `
                            <div class="status-error">
                                <i class="fas fa-times-circle"></i> ❌ Lỗi: ${error.message}
                            </div>
                        `;
                    }, remainingTime);
                });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            testSearchImproved();
            testSearchOld();
        });
    </script>
</body>
</html>
