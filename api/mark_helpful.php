<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON>ơ<PERSON> thức không được hỗ trợ']);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ']);
    exit;
}

// Lấy dữ liệu từ request
$review_id = isset($_POST['review_id']) ? intval($_POST['review_id']) : 0;

// Kiểm tra dữ liệu
if (empty($review_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID đánh giá không hợp lệ']);
    exit;
}

// Lấy user_id nếu đã đăng nhập
$user_id = is_logged_in() ? $_SESSION['user_id'] : null;

// Đánh dấu đánh giá là hữu ích
$result = mark_review_helpful($review_id, $user_id);

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
