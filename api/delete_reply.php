<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON>ơng thức không được hỗ trợ']);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ']);
    exit;
}

// Kiểm tra người dùng đã đăng nhập chưa
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Bạn cần đăng nhập để thực hiện thao tác này']);
    exit;
}

// L<PERSON>y dữ liệu từ request
$reply_id = isset($_POST['reply_id']) ? intval($_POST['reply_id']) : 0;

// Kiểm tra dữ liệu
if (empty($reply_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID phản hồi không hợp lệ']);
    exit;
}

// Xóa phản hồi
$result = delete_reply($reply_id, $_SESSION['user_id']);

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
