<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra phương thức yêu cầu
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    header('HTTP/1.1 405 Method Not Allowed');
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Lấy từ khóa tìm kiếm
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

// Nếu từ khóa quá ngắn, trả về mảng rỗng
if (strlen($keyword) < 1) {
    header('Content-Type: application/json');
    echo json_encode(['suggestions' => []]);
    exit;
}

// Tìm kiếm sản phẩm
try {
    global $conn;

    // Tạo từ khóa tìm kiếm không dấu
    $keyword_no_accent = remove_accents($keyword);

    // Tách từ khóa thành các từ riêng biệt và loại bỏ từ ngắn
    $keywords = array_filter(
        preg_split('/\s+/', trim($keyword)),
        fn($word) => mb_strlen($word, 'UTF-8') >= 1
    );

    $keywords_no_accent = array_filter(
        preg_split('/\s+/', trim($keyword_no_accent)),
        fn($word) => mb_strlen($word, 'UTF-8') >= 1
    );

    // Nếu không còn từ khóa nào sau khi lọc, sử dụng từ khóa gốc
    if (empty($keywords)) {
        $keywords = [$keyword];
    }

    if (empty($keywords_no_accent)) {
        $keywords_no_accent = [$keyword_no_accent];
    }

    // Tạo SQL query thông minh với scoring system
    $sql = "SELECT p.id, p.name, p.slug, p.image, p.price, p.sale_price, p.price_type, c.name as category_name,
                  (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as rating,
                  (SELECT COUNT(*) FROM order_items oi
                   JOIN orders o ON oi.order_id = o.id
                   WHERE oi.product_id = p.id AND o.status = 'COMPLETED') as sales,
                  (";

    // Tạo scoring system thông minh
    $score_parts = [];
    $params = [];

    // 1. Điểm cho exact match toàn bộ cụm từ
    $score_parts[] = "CASE WHEN p.name LIKE :exact_keyword THEN 1000 ELSE 0 END";
    $params[':exact_keyword'] = "%$keyword%";

    // 2. Điểm cho exact match toàn bộ cụm từ không dấu
    $score_parts[] = "CASE WHEN p.name LIKE :exact_keyword_no_accent THEN 900 ELSE 0 END";
    $params[':exact_keyword_no_accent'] = "%$keyword_no_accent%";

    // 3. Điểm cho từng từ khóa có trong tên (word matching)
    $word_score = 0;
    foreach ($keywords as $i => $word) {
        if (mb_strlen($word, 'UTF-8') >= 1) {
            $param_name = ":word_$i";
            $score_parts[] = "CASE WHEN p.name LIKE $param_name THEN " . (100 - $word_score * 5) . " ELSE 0 END";
            $params[$param_name] = "%$word%";
            $word_score++;
        }
    }

    // 4. Điểm cho từng từ khóa không dấu
    foreach ($keywords_no_accent as $i => $word) {
        if (mb_strlen($word, 'UTF-8') >= 1) {
            $param_name = ":word_no_accent_$i";
            $score_parts[] = "CASE WHEN p.name LIKE $param_name THEN " . (80 - $i * 5) . " ELSE 0 END";
            $params[$param_name] = "%$word%";
        }
    }

    // 5. Điểm cho description match (thấp hơn)
    $score_parts[] = "CASE WHEN p.description LIKE :desc_keyword THEN 50 ELSE 0 END";
    $params[':desc_keyword'] = "%$keyword%";

    // 6. Điểm cho ID match nếu là số
    if (is_numeric($keyword)) {
        $score_parts[] = "CASE WHEN p.id = :keyword_id THEN 2000 ELSE 0 END";
        $params[':keyword_id'] = $keyword;
    }

    $sql .= implode(" + ", $score_parts);
    $sql .= ") as match_score
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 1 AND (";

    // Điều kiện WHERE - sản phẩm phải chứa ít nhất 1 từ khóa
    $where_conditions = [];

    // Exact match conditions
    $where_conditions[] = "p.name LIKE :exact_keyword";
    $where_conditions[] = "p.name LIKE :exact_keyword_no_accent";
    $where_conditions[] = "p.description LIKE :desc_keyword";

    // Individual word conditions
    foreach ($keywords as $i => $word) {
        if (mb_strlen($word, 'UTF-8') >= 1) {
            $where_conditions[] = "p.name LIKE :word_$i";
        }
    }

    foreach ($keywords_no_accent as $i => $word) {
        if (mb_strlen($word, 'UTF-8') >= 1) {
            $where_conditions[] = "p.name LIKE :word_no_accent_$i";
        }
    }

    // ID condition
    if (is_numeric($keyword)) {
        $where_conditions[] = "p.id = :keyword_id";
    }

    $sql .= implode(" OR ", $where_conditions);
    $sql .= ") HAVING match_score > 0 ORDER BY match_score DESC, p.name ASC LIMIT 15";

    $stmt = $conn->prepare($sql);

    // Bind tất cả các tham số
    foreach ($params as $param => $value) {
        $stmt->bindValue($param, $value);
    }

    $stmt->execute();
    $products = $stmt->fetchAll();

    // Định dạng kết quả
    $suggestions = [];
    foreach ($products as $product) {
        $image_url = !empty($product['image'])
            ? BASE_URL . '/uploads/products/' . $product['image']
            : BASE_URL . '/assets/img/no-image.jpg';

        $price = !empty($product['sale_price'])
            ? format_currency($product['sale_price'])
            : format_currency($product['price']);

        // Định dạng đánh giá
        $rating = !empty($product['rating']) ? number_format($product['rating'], 1) : '0.0';

        // Định dạng lượt bán
        $sales = !empty($product['sales']) ? (int)$product['sales'] : 0;

        $suggestions[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'url' => get_product_url($product['slug']),
            'slug' => $product['slug'],
            'image' => $image_url,
            'price' => $price,
            'price_type' => $product['price_type'] ?? 'fixed',
            'category' => $product['category_name'] ?? 'Không có danh mục',
            'rating' => $rating,
            'sales' => $sales
        ];
    }

    // Trả về kết quả dưới dạng JSON
    header('Content-Type: application/json');
    echo json_encode(['suggestions' => $suggestions]);

} catch (PDOException $e) {
    // Trả về lỗi
    header('HTTP/1.1 500 Internal Server Error');
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Đã xảy ra lỗi khi tìm kiếm']);
    exit;
}
?>
