<?php
/**
 * API endpoint để kiểm tra tên đăng nhập và email đã tồn tại hay chưa
 */

// Bắt đầu session và include các file cần thiết
require_once '../includes/init.php';

// <PERSON><PERSON><PERSON> bảo request là AJAX
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
if (!$is_ajax) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Thiết lập header JSON
header('Content-Type: application/json');

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// <PERSON><PERSON><PERSON> dữ liệu từ request
$data = json_decode(file_get_contents('php://input'), true);

// <PERSON><PERSON>m tra dữ liệu
if (!isset($data['field']) || !isset($data['value'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Bad Request']);
    exit;
}

$field = $data['field'];
$value = trim($data['value']);

// Kiểm tra field hợp lệ
if (!in_array($field, ['username', 'email'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid field']);
    exit;
}

// Kiểm tra giá trị rỗng
if (empty($value)) {
    echo json_encode(['exists' => false, 'message' => '']);
    exit;
}

// Kiểm tra tên đăng nhập hợp lệ
if ($field === 'username') {
    // Kiểm tra tên đăng nhập hợp lệ (không dấu cách, không dấu tiếng Việt, chỉ chứa chữ cái, số và dấu gạch dưới)
    if (strpos($value, ' ') !== false ||
        preg_match('/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i', $value) ||
        !preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
        echo json_encode([
            'exists' => true,
            'message' => 'Tên đăng nhập chỉ được phép chứa chữ cái không dấu, số và dấu gạch dưới'
        ]);
        exit;
    }
}

// Sử dụng kết nối database từ init.php
global $conn;

// Kiểm tra tên đăng nhập hoặc email đã tồn tại chưa
try {
    $sql = "SELECT COUNT(*) as count FROM users WHERE $field = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$value]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $exists = $result['count'] > 0;

    if ($exists) {
        $message = $field === 'username'
            ? 'Tên đăng nhập này đã được sử dụng. Vui lòng chọn tên khác.'
            : 'Email này đã được đăng ký. Vui lòng sử dụng email khác hoặc đăng nhập.';
    } else {
        $message = $field === 'username'
            ? 'Tên đăng nhập hợp lệ.'
            : 'Email hợp lệ.';
    }

    echo json_encode([
        'exists' => $exists,
        'message' => $message
    ]);
} catch (PDOException $e) {
    // Log lỗi nhưng không hiển thị chi tiết lỗi cho người dùng
    error_log('Database error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal Server Error']);
}
