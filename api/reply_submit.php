<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON>ơ<PERSON> thức không được hỗ trợ']);
    exit;
}

// Kiểm tra CSRF token
if (!check_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ']);
    exit;
}

// Lấy dữ liệu từ request
$review_id = isset($_POST['review_id']) ? intval($_POST['review_id']) : 0;
$reply_content = isset($_POST['reply_content']) ? sanitize($_POST['reply_content']) : '';

// <PERSON><PERSON><PERSON> tra dữ liệu
if (empty($review_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID đánh giá không hợp lệ']);
    exit;
}

if (empty($reply_content)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Vui lòng nhập nội dung phản hồi']);
    exit;
}

// Chuẩn bị dữ liệu phản hồi
$reply_data = [
    'reply_content' => $reply_content
];

// Nếu người dùng đã đăng nhập
if (is_logged_in()) {
    $reply_data['user_id'] = $_SESSION['user_id'];
} else {
    // Nếu là khách, yêu cầu thông tin
    $guest_name = isset($_POST['guest_name']) ? sanitize($_POST['guest_name']) : '';
    $guest_email = isset($_POST['guest_email']) ? sanitize($_POST['guest_email']) : '';
    
    if (empty($guest_name) || empty($guest_email)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Vui lòng nhập tên và email']);
        exit;
    }
    
    // Kiểm tra định dạng email
    if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Email không hợp lệ']);
        exit;
    }
    
    $reply_data['guest_name'] = $guest_name;
    $reply_data['guest_email'] = $guest_email;
}

// Xử lý upload media
$media_files = [];
if (isset($_FILES['media']) && is_array($_FILES['media']['name'])) {
    for ($i = 0; $i < count($_FILES['media']['name']); $i++) {
        if ($_FILES['media']['error'][$i] === UPLOAD_ERR_OK) {
            $media_files[] = [
                'name' => $_FILES['media']['name'][$i],
                'type' => $_FILES['media']['type'][$i],
                'tmp_name' => $_FILES['media']['tmp_name'][$i],
                'error' => $_FILES['media']['error'][$i],
                'size' => $_FILES['media']['size'][$i]
            ];
        }
    }
}

// Thêm phản hồi
$result = add_review_reply($review_id, $reply_data, $media_files);

// Trả về kết quả
header('Content-Type: application/json');
echo json_encode($result);
?>
