<?php
/**
 * Simple API test
 */

// Đặt header JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    echo json_encode([
        'success' => true,
        'message' => 'API is working',
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $_SERVER['REQUEST_METHOD'],
        'input' => file_get_contents('php://input')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
