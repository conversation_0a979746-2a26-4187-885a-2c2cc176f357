<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Button Events</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        button {
            background: #f97316;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #ea580c;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Test <PERSON><PERSON> Events</h1>
    
    <div class="test-section">
        <h2>Test Event Binding</h2>
        
        <!-- Simulate the apply filters button -->
        <button type="button" id="applyFilters"
            class="apply-filters-btn group w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5 hover:scale-[1.02] relative overflow-hidden">
            <div class="btn-content relative z-10">
                <div class="btn-spinner"></div>
                <i class="btn-success fas fa-check"></i>
                <i class="btn-filter-icon fas fa-filter mr-2 text-xs group-hover:scale-110 transition-transform duration-300"></i>
                <span class="btn-text text-sm">Áp dụng bộ lọc</span>
                <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <i class="fas fa-arrow-right text-xs"></i>
                </div>
            </div>
        </button>
        
        <button onclick="testEventBinding()">Test Event Binding</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <div id="log" class="log"></div>
    </div>

    <!-- Include AJAX Filter script -->
    <script>
        window.BASE_URL = '';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testEventBinding() {
            log('=== Testing Event Binding ===');
            
            const btn = document.getElementById('applyFilters');
            if (!btn) {
                log('ERROR: Button not found');
                return;
            }
            
            log('Button found: ' + btn.id);
            
            // Test 1: Add multiple event listeners
            log('Adding event listener 1 (old style)');
            btn.addEventListener('click', function(e) {
                log('Event listener 1 triggered');
                e.preventDefault();
                e.stopPropagation();
                log('Event listener 1: preventDefault and stopPropagation called');
                return false;
            });
            
            log('Adding event listener 2 (AJAX style)');
            btn.addEventListener('click', function(e) {
                log('Event listener 2 triggered');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                log('Event listener 2: All propagation stopped');
                return false;
            }, true); // Capture phase
            
            log('Adding event listener 3 (normal)');
            btn.addEventListener('click', function(e) {
                log('Event listener 3 triggered');
                log('Event listener 3: Normal handler');
            });
            
            log('Event listeners added. Click the button to test.');
        }
        
        // Simulate some existing event handlers
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded');
            
            const btn = document.getElementById('applyFilters');
            if (btn) {
                log('Adding initial event listener');
                btn.addEventListener('click', function(e) {
                    log('Initial event listener triggered');
                });
            }
        });
        
        // Simulate AJAX Filter initialization
        setTimeout(() => {
            log('Simulating AJAX Filter initialization...');
            
            const btn = document.getElementById('applyFilters');
            if (btn) {
                log('AJAX Filter: Replacing button to remove old events');
                
                // Clone button to remove all event listeners
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
                
                log('AJAX Filter: Adding new event listener');
                newBtn.addEventListener('click', function(e) {
                    log('AJAX Filter event listener triggered');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    log('AJAX Filter: All propagation stopped');
                    
                    // Simulate AJAX call
                    log('AJAX Filter: Making API call...');
                    setTimeout(() => {
                        log('AJAX Filter: API call completed');
                    }, 1000);
                    
                    return false;
                }, true);
                
                log('AJAX Filter: Initialization complete');
            }
        }, 200);
    </script>
</body>
</html>
