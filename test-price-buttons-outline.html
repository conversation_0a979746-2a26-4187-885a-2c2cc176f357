<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 20 Ki<PERSON><PERSON>ng Giá Outline</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .header h1 {
            color: #1f2937;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #6b7280;
            font-size: 1.1rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .style-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .style-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            text-align: center;
        }

        .button-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            min-height: 80px;
            align-items: center;
        }

        /* Base button styles - Giống như hiện tại */
        .price-btn {
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 80px;
            position: relative;
            overflow: visible;
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
        }

        /* Dấu tích ở góc phải trên - chỉ hiện khi hover/active - MÀU CAM */
        .price-btn::after {
            content: '✓';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #f97316;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            font-weight: bold;
            line-height: 1;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
        }

        .price-btn:hover::after,
        .price-btn.active::after {
            opacity: 1;
            transform: scale(1);
        }

        /* Style 1: Classic Orange - Cơ bản */
        .style-1 .price-btn:hover,
        .style-1 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            background: white;
        }

        /* Style 2: Thick Border + Shadow */
        .style-2 .price-btn:hover,
        .style-2 .price-btn.active {
            border: 3px solid #f97316;
            color: #374151;
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
        }

        /* Style 3: Rounded + Scale */
        .style-3 .price-btn {
            border-radius: 12px;
        }
        .style-3 .price-btn:hover,
        .style-3 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            transform: scale(1.05);
        }

        /* Style 4: Pills + Slide Up */
        .style-4 .price-btn {
            border-radius: 20px;
            padding: 6px 14px;
        }
        .style-4 .price-btn:hover,
        .style-4 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            transform: translateY(-2px);
        }

        /* Style 5: Square + Rotate */
        .style-5 .price-btn {
            border-radius: 4px;
        }
        .style-5 .price-btn:hover,
        .style-5 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            transform: rotate(2deg);
        }

        /* Style 6: Glow Effect */
        .style-6 .price-btn:hover,
        .style-6 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            box-shadow: 0 0 15px rgba(249, 115, 22, 0.4);
        }

        /* Style 7: Pulse Animation */
        .style-7 .price-btn:hover,
        .style-7 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.03); }
        }

        /* Style 8: Skew Effect */
        .style-8 .price-btn:hover,
        .style-8 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            transform: skew(-3deg);
        }

        /* Style 9: Bounce Effect */
        .style-9 .price-btn:hover,
        .style-9 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-4px); }
        }

        /* Style 10: Gradient Border */
        .style-10 .price-btn:hover,
        .style-10 .price-btn.active {
            border: 2px solid transparent;
            background: linear-gradient(white, white) padding-box,
                        linear-gradient(45deg, #f97316, #ea580c) border-box;
            color: #374151;
        }

        /* Style 11: Shake Effect */
        .style-11 .price-btn:hover,
        .style-11 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        /* Style 12: Flip Effect */
        .style-12 .price-btn:hover,
        .style-12 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            transform: rotateY(180deg);
        }

        /* Style 13: Elastic Scale */
        .style-13 .price-btn:hover,
        .style-13 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: elastic 0.6s ease-out;
        }

        @keyframes elastic {
            0% { transform: scale(1); }
            30% { transform: scale(1.1); }
            60% { transform: scale(0.95); }
            100% { transform: scale(1.05); }
        }

        /* Style 14: Double Border */
        .style-14 .price-btn:hover,
        .style-14 .price-btn.active {
            border: 3px double #f97316;
            color: #374151;
        }

        /* Style 15: Dashed Border */
        .style-15 .price-btn:hover,
        .style-15 .price-btn.active {
            border: 2px dashed #f97316;
            color: #374151;
        }

        /* Style 16: Dotted Border */
        .style-16 .price-btn:hover,
        .style-16 .price-btn.active {
            border: 2px dotted #f97316;
            color: #374151;
        }

        /* Style 17: Inset Shadow */
        .style-17 .price-btn:hover,
        .style-17 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            box-shadow: inset 0 2px 4px rgba(249, 115, 22, 0.2);
        }

        /* Style 18: Wobble Effect */
        .style-18 .price-btn:hover,
        .style-18 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: wobble 0.8s ease-in-out;
        }

        @keyframes wobble {
            0%, 100% { transform: rotate(0deg); }
            15% { transform: rotate(-5deg); }
            30% { transform: rotate(3deg); }
            45% { transform: rotate(-3deg); }
            60% { transform: rotate(2deg); }
            75% { transform: rotate(-1deg); }
        }

        /* Style 19: Slide Border */
        .style-19 .price-btn {
            position: relative;
            overflow: hidden;
        }
        .style-19 .price-btn:hover,
        .style-19 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
        }
        .style-19 .price-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            border: 2px solid #f97316;
            transition: left 0.3s ease;
            z-index: -1;
        }
        .style-19 .price-btn:hover::before,
        .style-19 .price-btn.active::before {
            left: 0;
        }

        /* Style 20: Rainbow Border */
        .style-20 .price-btn:hover,
        .style-20 .price-btn.active {
            border: 2px solid #f97316;
            color: #374151;
            animation: rainbow 2s linear infinite;
        }

        @keyframes rainbow {
            0% { border-color: #f97316; }
            16% { border-color: #ef4444; }
            32% { border-color: #ec4899; }
            48% { border-color: #8b5cf6; }
            64% { border-color: #3b82f6; }
            80% { border-color: #10b981; }
            100% { border-color: #f97316; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .price-btn {
                font-size: 12px;
                padding: 6px 12px;
                min-width: 70px;
            }

            .price-btn::after {
                width: 16px;
                height: 16px;
                font-size: 10px;
                top: -5px;
                right: -5px;
            }
        }

        @media (max-width: 480px) {
            .price-btn {
                font-size: 11px;
                padding: 5px 10px;
                min-width: 60px;
            }
            
            .button-container {
                gap: 6px;
            }

            .price-btn::after {
                width: 14px;
                height: 14px;
                font-size: 9px;
                top: -4px;
                right: -4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> Test 20 Hiệu Ứng Nút Khoảng Giá - Màu Cam</h1>
            <p>Ban đầu nền trắng viền xám, hover/active có outline cam và dấu tích ✓ với các hiệu ứng khác nhau</p>
        </div>

        <div class="grid">
            <!-- Style 1: Classic Orange -->
            <div class="style-card style-1">
                <div class="style-title">1. Classic Orange - Cơ bản</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 2: Thick Blue Border -->
            <div class="style-card style-2">
                <div class="style-title">2. Thick Border + Shadow</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 3: Rounded Blue -->
            <div class="style-card style-3">
                <div class="style-title">3. Rounded Blue</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 4: Pills Shape -->
            <div class="style-card style-4">
                <div class="style-title">4. Pills Shape</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 5: Square Corners -->
            <div class="style-card style-5">
                <div class="style-title">5. Square Corners</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 6: Indigo Variant -->
            <div class="style-card style-6">
                <div class="style-title">6. Indigo Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 7: Purple Variant -->
            <div class="style-card style-7">
                <div class="style-title">7. Purple Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 8: Teal Variant -->
            <div class="style-card style-8">
                <div class="style-title">8. Teal Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 9: Emerald Variant -->
            <div class="style-card style-9">
                <div class="style-title">9. Emerald Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 10: Sky Blue Variant -->
            <div class="style-card style-10">
                <div class="style-title">10. Sky Blue Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 11: Cyan Variant -->
            <div class="style-card style-11">
                <div class="style-title">11. Cyan Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 12: Rose Variant -->
            <div class="style-card style-12">
                <div class="style-title">12. Rose Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 13: Amber Variant -->
            <div class="style-card style-13">
                <div class="style-title">13. Amber Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 14: Orange Variant -->
            <div class="style-card style-14">
                <div class="style-title">14. Orange Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 15: Red Variant -->
            <div class="style-card style-15">
                <div class="style-title">15. Red Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 16: Pink Variant -->
            <div class="style-card style-16">
                <div class="style-title">16. Pink Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 17: Violet Variant -->
            <div class="style-card style-17">
                <div class="style-title">17. Violet Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 18: Fuchsia Variant -->
            <div class="style-card style-18">
                <div class="style-title">18. Fuchsia Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 19: Lime Variant -->
            <div class="style-card style-19">
                <div class="style-title">19. Lime Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>

            <!-- Style 20: Slate Variant -->
            <div class="style-card style-20">
                <div class="style-title">20. Slate Variant</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 100.000đ</button>
                    <button class="price-btn active">100.000đ đến 300.000đ</button>
                    <button class="price-btn">300.000đ đến 500.000đ</button>
                    <button class="price-btn">Trên 500.000đ</button>
                </div>
            </div>
        </div>

        <!-- Interactive Demo Section -->
        <div style="background: white; border-radius: 16px; padding: 30px; margin-top: 30px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);">
            <h2 style="text-align: center; color: #1f2937; margin-bottom: 20px;">
                <i class="fas fa-mouse-pointer"></i> Demo Tương Tác
            </h2>
            <p style="text-align: center; color: #6b7280; margin-bottom: 30px;">
                Click vào các nút để xem hiệu ứng active với dấu tích ✓. Thiết kế outline phân biệt rõ ràng với nút "Áp dụng bộ lọc".
            </p>
            <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Desktop</div>
                    <div style="font-size: 24px;">💻</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Tablet</div>
                    <div style="font-size: 24px;">📱</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Mobile</div>
                    <div style="font-size: 24px;">📱</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers to all buttons
            document.querySelectorAll('.price-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from siblings
                    const container = this.closest('.button-container');
                    container.querySelectorAll('.price-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to clicked button
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
