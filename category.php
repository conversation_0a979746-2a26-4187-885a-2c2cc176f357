<?php
// Include header
include_once 'partials/header.php';

// Lấy slug từ URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Nếu không có slug, chuyển hướng về trang sản phẩm
if (empty($slug)) {
    redirect(BASE_URL . '/products.php');
}

// Lấy thông tin danh mục
$category = get_category_by_slug($slug);

// Nếu không tìm thấy danh mục, chuyển hướng về trang sản phẩm
if (!$category) {
    set_flash_message('error', 'Danh mục không tồn tại.');
    redirect(BASE_URL . '/products.php');
}

// Thiết lập tiêu đề trang
$page_title = $category['name'];
$page_description = $category['description'] ? $category['description'] : 'Sản phẩm thuộc danh mục ' . $category['name'] . ' tại Nội Thất Băng Vũ';

// Phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Lấy sản phẩm theo danh mục (chỉ lấy sản phẩm có trạng thái hiển thị)
$products = get_products($limit, $offset, $category['id'], null, null, 1);
$total_products = count_products($category['id'], null, 1);
$total_pages = ceil($total_products / $limit);
?>

<!-- Breadcrumb -->
<div class="bg-gray-100 py-3">
    <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-gray-600">
            <a href="<?php echo BASE_URL; ?>" class="hover:text-blue-500">Trang chủ</a>
            <span class="mx-2">/</span>
            <span class="text-gray-800"><?php echo $category['name']; ?></span>
        </div>
    </div>
</div>

<!-- Category Banner -->
<div class="py-6 bg-white">
    <div class="container mx-auto px-4">
        <div class="bg-gray-100 rounded-lg overflow-hidden">
            <div class="md:flex items-center">
                <div class="md:w-1/3 p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4"><?php echo $category['name']; ?></h1>
                    <?php if ($category['description']): ?>
                    <p class="text-gray-600 mb-4"><?php echo $category['description']; ?></p>
                    <?php endif; ?>
                    <p class="text-gray-700">Hiển thị <?php echo count($products); ?> trên <?php echo $total_products; ?> sản phẩm</p>
                </div>
                <div class="md:w-2/3">
                    <?php if ($category['image']): ?>
                    <img src="<?php echo BASE_URL; ?>/uploads/categories/<?php echo $category['image']; ?>" alt="<?php echo $category['name']; ?>" class="w-full h-auto">
                    <?php else: ?>
                    <div class="w-full h-64 bg-gray-300 flex items-center justify-center">
                        <i class="fas fa-image text-gray-500 text-4xl"></i>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products -->
<div class="py-8">
    <div class="container mx-auto px-4">
        <?php if (count($products) > 0): ?>
        <div class="mb-6 flex flex-col md:flex-row md:justify-between md:items-center">
            <div class="mb-4 md:mb-0">
                <h2 class="text-2xl font-bold text-gray-800">Sản phẩm</h2>
            </div>
            <div class="flex items-center">
                <label for="sort" class="mr-2 text-gray-700">Sắp xếp theo:</label>
                <select id="sort" class="border border-gray-300 rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="newest">Mới nhất</option>
                    <option value="price_asc">Giá: Thấp đến cao</option>
                    <option value="price_desc">Giá: Cao đến thấp</option>
                    <option value="name_asc">Tên: A-Z</option>
                    <option value="name_desc">Tên: Z-A</option>
                </select>
            </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <?php foreach ($products as $product): ?>
            <div class="bg-white rounded-lg overflow-hidden shadow-md product-card">
                <a href="<?php echo get_product_url($product['slug']); ?>" class="block product-image">
                    <?php if ($product['image']): ?>
                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                    <?php else: ?>
                    <div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                        <i class="fas fa-image text-gray-500 text-4xl"></i>
                    </div>
                    <?php endif; ?>
                </a>
                <div class="p-4">
                    <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" class="text-blue-500 text-sm hover:underline">
                        <?php echo $category['name']; ?>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $product['slug']; ?>" class="block">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 hover:text-blue-500 transition duration-200"><?php echo $product['name']; ?></h3>
                    </a>
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <?php if ($product['sale_price'] > 0): ?>
                            <span class="text-gray-500 line-through mr-2"><?php echo format_currency($product['price']); ?></span>
                            <span class="text-red-500 font-bold"><?php echo format_currency($product['sale_price']); ?></span>
                            <?php else: ?>
                            <span class="text-gray-800 font-bold"><?php echo format_currency($product['price']); ?></span>
                            <?php endif; ?>
                        </div>
                        <?php if ($product['quantity'] > 0): ?>
                        <span class="text-green-500 text-sm">Còn hàng</span>
                        <?php else: ?>
                        <span class="text-red-500 text-sm">Hết hàng</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex-grow transition duration-200 add-to-cart-btn" data-product-id="<?php echo $product['id']; ?>">
                            <i class="fas fa-shopping-cart mr-2"></i> Thêm vào giỏ
                        </button>
                        <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $product['slug']; ?>" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-2 rounded transition duration-200">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="mt-8 flex justify-center">
            <div class="flex space-x-1">
                <?php if ($page > 1): ?>
                <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $slug; ?>&page=<?php echo $page - 1; ?>" class="px-3 py-1 border border-gray-300 rounded text-blue-500 hover:bg-blue-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>

                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php if ($i == $page): ?>
                <span class="px-3 py-1 border border-gray-300 rounded bg-blue-500 text-white">
                    <?php echo $i; ?>
                </span>
                <?php else: ?>
                <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $slug; ?>&page=<?php echo $i; ?>" class="px-3 py-1 border border-gray-300 rounded text-blue-500 hover:bg-blue-50">
                    <?php echo $i; ?>
                </a>
                <?php endif; ?>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $slug; ?>&page=<?php echo $page + 1; ?>" class="px-3 py-1 border border-gray-300 rounded text-blue-500 hover:bg-blue-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php else: ?>
        <div class="bg-white rounded-lg shadow-md p-8 text-center">
            <div class="text-gray-500 text-6xl mb-4">
                <i class="fas fa-box-open"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Không có sản phẩm nào</h2>
            <p class="text-gray-600 mb-6">Hiện tại danh mục này chưa có sản phẩm nào.</p>
            <a href="<?php echo BASE_URL; ?>" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded inline-block transition duration-200">
                Quay lại trang chủ
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
