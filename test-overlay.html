<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Overlay Modal - Nội <PERSON>h<PERSON>t <PERSON>ng <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            background: #f5f5f5;
        }

        /* Fake content để test */
        .fake-header {
            background: #333;
            color: white;
            padding: 1rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .fake-content {
            margin-top: 60px;
            padding: 2rem;
            min-height: 200vh;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
        }

        .fake-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            width: 250px;
            height: calc(100vh - 60px);
            background: white;
            border-right: 1px solid #ddd;
            z-index: 100;
            padding: 1rem;
        }

        .fake-main {
            margin-left: 250px;
            padding: 2rem;
        }

        .test-button {
            background: #f97316;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }

        .test-button:hover {
            background: #ea580c;
        }

        /* Test Overlay Styles */
        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            z-index: 999999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            pointer-events: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-overlay.active {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        .test-modal {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .test-overlay.active .test-modal {
            transform: scale(1);
        }

        .close-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            float: right;
        }

        /* Test với z-index cao hơn */
        .high-z-element {
            position: fixed;
            top: 100px;
            right: 20px;
            background: red;
            color: white;
            padding: 1rem;
            z-index: 999998;
            border-radius: 8px;
        }

        /* Test overlay với z-index cực cao */
        .super-high-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(255, 0, 0, 0.5);
            z-index: 2147483647;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .super-high-overlay.active {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        /* Test với !important */
        .important-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 255, 0, 0.5) !important;
            z-index: 2147483647 !important;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .important-overlay.active {
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
        }

        /* Debug info */
        .debug-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: black;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            z-index: 999999;
        }

        /* Responsive test */
        @media (min-width: 769px) and (max-width: 1024px) {
            .tablet-test {
                background: yellow !important;
                color: black !important;
            }
        }
    </style>
</head>
<body>
    <!-- Fake Header -->
    <div class="fake-header">
        <h1>Test Overlay - Nội Thất Băng Vũ</h1>
    </div>

    <!-- High Z-Index Element để test conflict -->
    <div class="high-z-element">
        High Z-Index Element (999998)
    </div>

    <!-- Fake Sidebar -->
    <div class="fake-sidebar">
        <h3>Sidebar</h3>
        <p>Đây là sidebar giả để test layout</p>
    </div>

    <!-- Main Content -->
    <div class="fake-main">
        <div class="fake-content">
            <h2>Test Overlay Modal</h2>
            <p>Trang này để test overlay modal trên tablet (769px - 1024px)</p>

            <button class="test-button" onclick="openTestOverlay()">
                Test Overlay Thường (z-index: 999999)
            </button>

            <button class="test-button" onclick="openSuperHighOverlay()">
                Test Overlay Cao (z-index: 2147483647)
            </button>

            <button class="test-button" onclick="openImportantOverlay()">
                Test Overlay !important
            </button>

            <button class="test-button" onclick="testCurrentModal()">
                Test Modal Hiện Tại
            </button>

            <h3>Hướng dẫn test:</h3>
            <ol>
                <li>Mở DevTools (F12)</li>
                <li>Chuyển sang tablet mode (769px - 1024px)</li>
                <li>Click các nút test overlay</li>
                <li>Kiểm tra xem overlay nào hiển thị</li>
                <li>Xem Console để debug info</li>
            </ol>

            <div style="height: 100vh; background: linear-gradient(45deg, #ff9a9e, #fecfef);"></div>
            <div style="height: 100vh; background: linear-gradient(45deg, #a8edea, #fed6e3);"></div>
        </div>
    </div>

    <!-- Test Overlays -->
    <div id="testOverlay" class="test-overlay">
        <div class="test-modal">
            <button class="close-btn" onclick="closeTestOverlay()">×</button>
            <h3>Test Overlay Thường</h3>
            <p>Z-index: 999999</p>
            <p>Nếu bạn thấy overlay này, nghĩa là overlay cơ bản hoạt động.</p>
        </div>
    </div>

    <div id="superHighOverlay" class="super-high-overlay">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 12px;">
            <button class="close-btn" onclick="closeSuperHighOverlay()">×</button>
            <h3>Super High Overlay</h3>
            <p>Z-index: 2147483647 (cao nhất)</p>
            <p>Background: Đỏ mờ</p>
        </div>
    </div>

    <div id="importantOverlay" class="important-overlay">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 12px;">
            <button class="close-btn" onclick="closeImportantOverlay()">×</button>
            <h3>Important Overlay</h3>
            <p>Z-index: 2147483647 !important</p>
            <p>Background: Xanh lá mờ</p>
        </div>
    </div>

    <!-- Debug Info -->
    <div class="debug-info" id="debugInfo">
        <div>Screen: <span id="screenSize"></span></div>
        <div>Viewport: <span id="viewportSize"></span></div>
        <div>Active Overlays: <span id="activeOverlays">0</span></div>
    </div>

<script>
    // Debug functions
    function updateDebugInfo() {
        document.getElementById('screenSize').textContent = `${screen.width}x${screen.height}`;
        document.getElementById('viewportSize').textContent = `${window.innerWidth}x${window.innerHeight}`;

        const activeOverlays = document.querySelectorAll('.test-overlay.active, .super-high-overlay.active, .important-overlay.active').length;
        document.getElementById('activeOverlays').textContent = activeOverlays;
    }

    // Test overlay functions
    function openTestOverlay() {
        console.log('Opening test overlay...');
        const overlay = document.getElementById('testOverlay');
        overlay.classList.add('active');
        console.log('Overlay classes:', overlay.className);
        console.log('Overlay computed styles:', {
            position: getComputedStyle(overlay).position,
            zIndex: getComputedStyle(overlay).zIndex,
            opacity: getComputedStyle(overlay).opacity,
            visibility: getComputedStyle(overlay).visibility,
            display: getComputedStyle(overlay).display
        });
        updateDebugInfo();
    }

    function closeTestOverlay() {
        document.getElementById('testOverlay').classList.remove('active');
        updateDebugInfo();
    }

    function openSuperHighOverlay() {
        console.log('Opening super high overlay...');
        const overlay = document.getElementById('superHighOverlay');
        overlay.classList.add('active');
        console.log('Super high overlay styles:', {
            zIndex: getComputedStyle(overlay).zIndex,
            opacity: getComputedStyle(overlay).opacity,
            visibility: getComputedStyle(overlay).visibility
        });
        updateDebugInfo();
    }

    function closeSuperHighOverlay() {
        document.getElementById('superHighOverlay').classList.remove('active');
        updateDebugInfo();
    }

    function openImportantOverlay() {
        console.log('Opening important overlay...');
        const overlay = document.getElementById('importantOverlay');
        overlay.classList.add('active');
        console.log('Important overlay styles:', {
            zIndex: getComputedStyle(overlay).zIndex,
            opacity: getComputedStyle(overlay).opacity,
            visibility: getComputedStyle(overlay).visibility
        });
        updateDebugInfo();
    }

    function closeImportantOverlay() {
        document.getElementById('importantOverlay').classList.remove('active');
        updateDebugInfo();
    }

    function testCurrentModal() {
        console.log('Testing current modal system...');

        // Kiểm tra xem có modal hiện tại không
        const currentOverlay = document.querySelector('.filter-modal-overlay');
        const currentModal = document.querySelector('.filter-modal');

        if (currentOverlay && currentModal) {
            console.log('Found existing modal system');
            console.log('Current overlay styles:', {
                position: getComputedStyle(currentOverlay).position,
                zIndex: getComputedStyle(currentOverlay).zIndex,
                opacity: getComputedStyle(currentOverlay).opacity,
                visibility: getComputedStyle(currentOverlay).visibility,
                display: getComputedStyle(currentOverlay).display,
                width: getComputedStyle(currentOverlay).width,
                height: getComputedStyle(currentOverlay).height
            });

            // Test mở modal hiện tại
            currentOverlay.classList.add('active');
            currentModal.classList.add('active');
            document.body.classList.add('filter-modal-open');

            console.log('Activated current modal, new styles:', {
                opacity: getComputedStyle(currentOverlay).opacity,
                visibility: getComputedStyle(currentOverlay).visibility
            });

            // Tự động đóng sau 3 giây
            setTimeout(() => {
                currentOverlay.classList.remove('active');
                currentModal.classList.remove('active');
                document.body.classList.remove('filter-modal-open');
                console.log('Auto-closed current modal');
            }, 3000);
        } else {
            console.log('No existing modal system found');
            alert('Không tìm thấy modal system hiện tại. Hãy mở trang products.php trước.');
        }
    }

    // Check for existing modal elements
    function checkExistingModal() {
        const overlay = document.querySelector('.filter-modal-overlay');
        const modal = document.querySelector('.filter-modal');

        console.log('Existing modal check:', {
            overlay: !!overlay,
            modal: !!modal,
            overlayClasses: overlay ? overlay.className : 'not found',
            modalClasses: modal ? modal.className : 'not found'
        });

        if (overlay) {
            console.log('Existing overlay computed styles:', {
                position: getComputedStyle(overlay).position,
                zIndex: getComputedStyle(overlay).zIndex,
                opacity: getComputedStyle(overlay).opacity,
                visibility: getComputedStyle(overlay).visibility,
                display: getComputedStyle(overlay).display
            });
        }
    }

    // Initialize
    window.addEventListener('load', () => {
        updateDebugInfo();
        checkExistingModal();

        // Update debug info on resize
        window.addEventListener('resize', updateDebugInfo);

        console.log('Test page loaded. Current viewport:', window.innerWidth + 'x' + window.innerHeight);

        if (window.innerWidth >= 769 && window.innerWidth <= 1024) {
            console.log('✅ Currently in tablet range (769px - 1024px)');
            document.body.style.border = '5px solid green';
        } else {
            console.log('❌ Not in tablet range. Current width:', window.innerWidth);
            document.body.style.border = '5px solid red';
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.key === '1') openTestOverlay();
        if (e.key === '2') openSuperHighOverlay();
        if (e.key === '3') openImportantOverlay();
        if (e.key === '4') testCurrentModal();
        if (e.key === 'Escape') {
            closeTestOverlay();
            closeSuperHighOverlay();
            closeImportantOverlay();
        }
    });
</script>
</body>
</html>