<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 20 Ki<PERSON><PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .header h1 {
            color: #1f2937;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #6b7280;
            font-size: 1.1rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .style-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .style-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            text-align: center;
        }

        .button-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            min-height: 80px;
            align-items: center;
        }

        /* Base button styles - Outline với dấu tích */
        .price-btn {
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 80px;
            position: relative;
            overflow: hidden;
            background: white;
            border: 2px solid #e5e7eb;
            color: #374151;
        }

        /* Dấu tích ở góc phải trên */
        .price-btn::after {
            content: '✓';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: #10b981;
            color: white;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
            font-weight: bold;
            line-height: 1;
        }

        .price-btn.active::after {
            opacity: 1;
            transform: scale(1);
        }

        /* Style 1: Classic Orange Outline */
        .style-1 .price-btn {
            border: 2px solid #f97316;
            color: #f97316;
        }

        .style-1 .price-btn:hover {
            border-color: #ea580c;
            color: #ea580c;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
        }

        .style-1 .price-btn.active {
            border-color: #ea580c;
            color: #ea580c;
            background: rgba(249, 115, 22, 0.05);
        }

        /* Style 2: Dashed Orange Outline */
        .style-2 .price-btn {
            border: 2px dashed #f97316;
            color: #f97316;
        }

        .style-2 .price-btn:hover {
            border-style: solid;
            transform: scale(1.02);
        }

        .style-2 .price-btn.active {
            border-style: solid;
            background: rgba(249, 115, 22, 0.08);
        }

        /* Style 3: Thick Border */
        .style-3 .price-btn {
            border: 3px solid #f97316;
            color: #f97316;
            font-weight: 600;
        }

        .style-3 .price-btn:hover {
            border-color: #ea580c;
            color: #ea580c;
        }

        .style-3 .price-btn.active {
            border-color: #ea580c;
            color: #ea580c;
            background: rgba(249, 115, 22, 0.1);
        }

        /* Style 4: Rounded Pills Outline */
        .style-4 .price-btn {
            border: 2px solid #f97316;
            color: #f97316;
            border-radius: 20px;
            padding: 6px 14px;
        }

        .style-4 .price-btn:hover {
            background: rgba(249, 115, 22, 0.05);
            transform: translateY(-1px);
        }

        .style-4 .price-btn.active {
            background: rgba(249, 115, 22, 0.1);
            border-color: #ea580c;
            color: #ea580c;
        }

        /* Style 5: Minimal Thin */
        .style-5 .price-btn {
            border: 1px solid #f97316;
            color: #f97316;
            border-radius: 6px;
        }

        .style-5 .price-btn:hover {
            border-width: 2px;
            margin: -1px;
        }

        .style-5 .price-btn.active {
            border-width: 2px;
            margin: -1px;
            background: rgba(249, 115, 22, 0.05);
        }

        /* Style 6: Double Border */
        .style-6 .price-btn {
            border: 3px double #f97316;
            color: #f97316;
        }

        .style-6 .price-btn:hover {
            border-color: #ea580c;
            color: #ea580c;
        }

        .style-6 .price-btn.active {
            border-color: #ea580c;
            color: #ea580c;
            background: rgba(249, 115, 22, 0.05);
        }

        /* Style 7: 3D Effect */
        .style-7 .price-btn {
            background: linear-gradient(145deg, #ffffff, #e2e8f0);
            color: #374151;
            border: none;
            border-radius: 12px;
            box-shadow: 5px 5px 10px #d1d5db, -5px -5px 10px #ffffff;
        }

        .style-7 .price-btn:hover {
            box-shadow: 2px 2px 5px #d1d5db, -2px -2px 5px #ffffff;
        }

        .style-7 .price-btn.active {
            background: linear-gradient(145deg, #f97316, #ea580c);
            color: white;
            box-shadow: inset 2px 2px 5px #ea580c, inset -2px -2px 5px #fb923c;
        }

        /* Style 8: Glass Effect */
        .style-8 .price-btn {
            background: rgba(255, 255, 255, 0.2);
            color: #374151;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .style-8 .price-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .style-8 .price-btn.active {
            background: rgba(249, 115, 22, 0.8);
            color: white;
            border-color: rgba(249, 115, 22, 0.9);
        }

        /* Style 9: Gradient Border */
        .style-9 .price-btn {
            background: white;
            color: #374151;
            border: 2px solid transparent;
            border-radius: 8px;
            background-clip: padding-box;
            position: relative;
        }

        .style-9 .price-btn::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 2px;
            background: linear-gradient(45deg, #f97316, #ea580c, #fb923c);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
        }

        .style-9 .price-btn:hover {
            transform: scale(1.02);
        }

        .style-9 .price-btn.active {
            background: linear-gradient(45deg, #f97316, #ea580c);
            color: white;
        }

        /* Style 10: Animated Underline */
        .style-10 .price-btn {
            background: transparent;
            color: #374151;
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            position: relative;
        }

        .style-10 .price-btn::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            width: 0;
            height: 2px;
            background: #f97316;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .style-10 .price-btn:hover::after {
            width: 100%;
        }

        .style-10 .price-btn.active {
            color: #f97316;
            font-weight: 600;
        }

        .style-10 .price-btn.active::after {
            width: 100%;
        }

        /* Style 11: Shadow Lift */
        .style-11 .price-btn {
            background: white;
            color: #374151;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .style-11 .price-btn:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .style-11 .price-btn.active {
            background: #f97316;
            color: white;
            border-color: #f97316;
            box-shadow: 0 8px 20px rgba(249, 115, 22, 0.4);
            transform: translateY(-3px);
        }

        /* Style 12: Pulse Effect */
        .style-12 .price-btn {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }

        .style-12 .price-btn:hover {
            animation: pulse 0.6s ease-in-out;
        }

        .style-12 .price-btn.active {
            background: #f97316;
            color: white;
            border-color: #f97316;
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Style 13: Slide Fill */
        .style-13 .price-btn {
            background: white;
            color: #f97316;
            border: 2px solid #f97316;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .style-13 .price-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: #f97316;
            transition: left 0.3s ease;
            z-index: -1;
        }

        .style-13 .price-btn:hover::before {
            left: 0;
        }

        .style-13 .price-btn:hover {
            color: white;
        }

        .style-13 .price-btn.active::before {
            left: 0;
        }

        .style-13 .price-btn.active {
            color: white;
        }

        /* Style 14: Dotted Border */
        .style-14 .price-btn {
            background: transparent;
            color: #374151;
            border: 2px dotted #d1d5db;
            border-radius: 8px;
        }

        .style-14 .price-btn:hover {
            border-style: solid;
            border-color: #f97316;
            color: #f97316;
        }

        .style-14 .price-btn.active {
            background: #f97316;
            color: white;
            border-style: solid;
            border-color: #f97316;
        }

        /* Style 15: Gradient Text */
        .style-15 .price-btn {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: linear-gradient(45deg, #f97316, #ea580c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .style-15 .price-btn:hover {
            transform: scale(1.05);
            border-color: #f97316;
        }

        .style-15 .price-btn.active {
            background: linear-gradient(45deg, #f97316, #ea580c);
            -webkit-background-clip: initial;
            -webkit-text-fill-color: white;
            background-clip: initial;
            color: white;
            border-color: #f97316;
        }

        /* Style 16: Skew Effect */
        .style-16 .price-btn {
            background: #f3f4f6;
            color: #374151;
            border: none;
            border-radius: 0;
            transform: skew(-10deg);
            margin: 0 5px;
        }

        .style-16 .price-btn:hover {
            background: #e5e7eb;
            transform: skew(-10deg) scale(1.05);
        }

        .style-16 .price-btn.active {
            background: #f97316;
            color: white;
            transform: skew(-10deg) scale(1.1);
        }

        /* Style 17: Double Border */
        .style-17 .price-btn {
            background: white;
            color: #374151;
            border: 3px double #d1d5db;
            border-radius: 8px;
        }

        .style-17 .price-btn:hover {
            border-color: #f97316;
            color: #f97316;
        }

        .style-17 .price-btn.active {
            background: #f97316;
            color: white;
            border-color: #ea580c;
        }

        /* Style 18: Flip Effect */
        .style-18 .price-btn {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }

        .style-18 .price-btn:hover {
            transform: rotateY(180deg);
        }

        .style-18 .price-btn.active {
            background: #f97316;
            color: white;
            border-color: #f97316;
            transform: rotateY(360deg);
        }

        /* Style 19: Neon Border */
        .style-19 .price-btn {
            background: #1f2937;
            color: #f97316;
            border: 2px solid #f97316;
            border-radius: 8px;
            text-shadow: 0 0 5px #f97316;
            box-shadow: 0 0 5px #f97316, inset 0 0 5px rgba(249, 115, 22, 0.1);
        }

        .style-19 .price-btn:hover {
            box-shadow: 0 0 10px #f97316, 0 0 20px #f97316, inset 0 0 10px rgba(249, 115, 22, 0.2);
        }

        .style-19 .price-btn.active {
            background: #f97316;
            color: #1f2937;
            text-shadow: none;
            box-shadow: 0 0 15px #f97316, 0 0 30px #f97316, inset 0 0 15px rgba(31, 41, 55, 0.2);
        }

        /* Style 20: Elastic Scale */
        .style-20 .price-btn {
            background: white;
            color: #374151;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .style-20 .price-btn:hover {
            transform: scale(1.1);
            border-color: #f97316;
            color: #f97316;
        }

        .style-20 .price-btn.active {
            background: #f97316;
            color: white;
            border-color: #f97316;
            transform: scale(1.15);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .price-btn {
                font-size: 12px;
                padding: 6px 12px;
                min-width: 70px;
            }
        }

        @media (max-width: 480px) {
            .price-btn {
                font-size: 11px;
                padding: 5px 10px;
                min-width: 60px;
            }
            
            .button-container {
                gap: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-palette"></i> Test 20 Kiểu Nút Khoảng Giá Outline</h1>
            <p>Thiết kế outline với dấu tích ở góc phải trên - Phân biệt với nút "Áp dụng bộ lọc"</p>
        </div>

        <div class="grid">
            <!-- Style 1: Classic Orange -->
            <div class="style-card style-1">
                <div class="style-title">1. Classic Orange Gradient</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 2: Outline Orange -->
            <div class="style-card style-2">
                <div class="style-title">2. Outline Orange</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 3: Soft Orange -->
            <div class="style-card style-3">
                <div class="style-title">3. Soft Orange</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 4: Rounded Pills -->
            <div class="style-card style-4">
                <div class="style-title">4. Rounded Pills</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 5: Minimal -->
            <div class="style-card style-5">
                <div class="style-title">5. Minimal Clean</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 6: Neon Glow -->
            <div class="style-card style-6">
                <div class="style-title">6. Neon Glow</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 7: 3D Effect -->
            <div class="style-card style-7">
                <div class="style-title">7. 3D Neumorphism</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 8: Glass Effect -->
            <div class="style-card style-8">
                <div class="style-title">8. Glass Morphism</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 9: Gradient Border -->
            <div class="style-card style-9">
                <div class="style-title">9. Gradient Border</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 10: Animated Underline -->
            <div class="style-card style-10">
                <div class="style-title">10. Animated Underline</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 11: Shadow Lift -->
            <div class="style-card style-11">
                <div class="style-title">11. Shadow Lift</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 12: Pulse Effect -->
            <div class="style-card style-12">
                <div class="style-title">12. Pulse Effect</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 13: Slide Fill -->
            <div class="style-card style-13">
                <div class="style-title">13. Slide Fill</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 14: Dotted Border -->
            <div class="style-card style-14">
                <div class="style-title">14. Dotted Border</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 15: Gradient Text -->
            <div class="style-card style-15">
                <div class="style-title">15. Gradient Text</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 16: Skew Effect -->
            <div class="style-card style-16">
                <div class="style-title">16. Skew Effect</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 17: Double Border -->
            <div class="style-card style-17">
                <div class="style-title">17. Double Border</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 18: Flip Effect -->
            <div class="style-card style-18">
                <div class="style-title">18. Flip Effect</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 19: Neon Border -->
            <div class="style-card style-19">
                <div class="style-title">19. Neon Border</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>

            <!-- Style 20: Elastic Scale -->
            <div class="style-card style-20">
                <div class="style-title">20. Elastic Scale</div>
                <div class="button-container">
                    <button class="price-btn">Dưới 1 triệu</button>
                    <button class="price-btn active">1-5 triệu</button>
                    <button class="price-btn">5-10 triệu</button>
                    <button class="price-btn">Trên 10 triệu</button>
                </div>
            </div>
        </div>

        <!-- Interactive Demo Section -->
        <div style="background: white; border-radius: 16px; padding: 30px; margin-top: 30px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);">
            <h2 style="text-align: center; color: #1f2937; margin-bottom: 20px;">
                <i class="fas fa-mouse-pointer"></i> Demo Tương Tác
            </h2>
            <p style="text-align: center; color: #6b7280; margin-bottom: 30px;">
                Click vào các nút để xem hiệu ứng active. Tất cả đều được tối ưu cho responsive design.
            </p>
            <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Desktop</div>
                    <div style="font-size: 24px;">💻</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Tablet</div>
                    <div style="font-size: 24px;">📱</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #6b7280; margin-bottom: 5px;">Mobile</div>
                    <div style="font-size: 24px;">📱</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers to all buttons
            document.querySelectorAll('.price-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from siblings
                    const container = this.closest('.button-container');
                    container.querySelectorAll('.price-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to clicked button
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
