/**
 * Interior Handbook JavaScript - Nội Thất Bàng Vũ
 */

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo các hiệu ứng cho phần cẩm nang nội thất
    initInteriorHandbook();
});

/**
 * Khởi tạo các hiệu ứng cho phần cẩm nang nội thất
 */
function initInteriorHandbook() {
    // Hiệu ứng xuất hiện khi scroll
    initScrollReveal();
    
    // Hiệu ứng hover cho các card
    initCardHoverEffects();
    
    // Hiệu ứng parallax cho background
    initParallaxEffect();
}

/**
 * Khởi tạo hiệu ứng xuất hiện khi scroll
 */
function initScrollReveal() {
    // Kiểm tra xem ScrollReveal có tồn tại không
    if (typeof ScrollReveal === 'undefined') {
        console.warn('ScrollReveal is not loaded. Skipping scroll reveal effects.');
        return;
    }
    
    // C<PERSON>u hình <PERSON>rollReveal
    const sr = ScrollReveal({
        origin: 'bottom',
        distance: '20px',
        duration: 800,
        delay: 200,
        easing: 'ease-out',
        reset: false
    });
    
    // Áp dụng hiệu ứng cho các phần tử
    sr.reveal('.handbook-header', { delay: 100 });
    sr.reveal('.handbook-featured', { delay: 200 });
    sr.reveal('.handbook-card', { 
        interval: 100,
        delay: 300
    });
    sr.reveal('.handbook-view-all', { delay: 400 });
}

/**
 * Khởi tạo hiệu ứng hover cho các card
 */
function initCardHoverEffects() {
    // Lấy tất cả các card
    const cards = document.querySelectorAll('.handbook-card');
    
    // Thêm hiệu ứng hover cho từng card
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Thêm hiệu ứng khi hover vào
            this.classList.add('is-hovered');
        });
        
        card.addEventListener('mouseleave', function() {
            // Xóa hiệu ứng khi hover ra
            this.classList.remove('is-hovered');
        });
    });
}

/**
 * Khởi tạo hiệu ứng parallax cho background
 */
function initParallaxEffect() {
    // Lấy phần tử background
    const bgElement = document.querySelector('.handbook-bg');
    
    if (!bgElement) return;
    
    // Thêm sự kiện scroll
    window.addEventListener('scroll', function() {
        // Tính toán vị trí scroll
        const scrollY = window.scrollY;
        const sectionTop = document.querySelector('.interior-handbook-section').offsetTop;
        const relativeScroll = scrollY - sectionTop;
        
        // Áp dụng hiệu ứng parallax nếu đang trong vùng nhìn thấy
        if (relativeScroll > -500 && relativeScroll < 1000) {
            // Di chuyển background theo tỷ lệ scroll
            bgElement.style.transform = `translateY(${relativeScroll * 0.05}px)`;
        }
    });
}

/**
 * Hiệu ứng đặc biệt cho featured article
 */
document.addEventListener('DOMContentLoaded', function() {
    const featuredArticle = document.querySelector('.handbook-featured');
    
    if (featuredArticle) {
        let animationId = null;

        // Thêm hiệu ứng hover 3D với tối ưu hóa hiệu suất
        featuredArticle.addEventListener('mousemove', function(e) {
            // Hủy animation frame trước đó nếu có
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            // Sử dụng requestAnimationFrame để tối ưu hiệu suất
            animationId = requestAnimationFrame(() => {
                // Tính toán vị trí chuột tương đối
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Tính toán góc xoay dựa trên vị trí chuột
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                // Giới hạn góc xoay nhỏ hơn để mượt mà hơn
                const maxRotation = 3;

                // Tính toán góc xoay với công thức tối ưu
                const rotateX = (maxRotation * (y - centerY) / centerY).toFixed(2);
                const rotateY = (-maxRotation * (x - centerX) / centerX).toFixed(2);

                // Áp dụng hiệu ứng với GPU acceleration
                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
        });

        // Reset khi hover ra
        featuredArticle.addEventListener('mouseleave', function() {
            // Hủy animation frame nếu có
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }

            // Reset transform với transition mượt mà
            this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
        });
    }
});
