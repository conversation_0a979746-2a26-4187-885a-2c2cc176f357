/**
 * Cart Unified System - Nội Thất Bàng Vũ
 * Thống nhất tất cả logic giỏ hàng để tránh xung đột
 */

// Global cart state management
window.CartSystem = {
    isProcessing: false,
    lastUpdateTime: 0,
    cartCount: 0,
    
    // Initialize cart system
    init: function() {
        console.log('CartSystem: Initializing unified cart system...');
        
        // Prevent multiple initialization
        if (window.CartSystem.initialized) {
            console.log('CartSystem: Already initialized, skipping...');
            return;
        }
        
        // Load initial cart count
        this.loadCartCount();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Setup periodic sync
        this.setupPeriodicSync();
        
        // Mark as initialized
        window.CartSystem.initialized = true;
        
        console.log('CartSystem: Initialization complete');
    },
    
    // Load cart count from server
    loadCartCount: function() {
        fetch(`${BASE_URL}/ajax/get_cart_count.php`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.cartCount = parseInt(data.count);
                    localStorage.setItem('cartCount', this.cartCount.toString());
                    this.updateAllBadges();
                    console.log('CartSystem: Cart count loaded:', this.cartCount);
                }
            })
            .catch(error => {
                console.error('CartSystem: Error loading cart count:', error);
                // Fallback to localStorage
                this.cartCount = parseInt(localStorage.getItem('cartCount') || '0');
                this.updateAllBadges();
            });
    },
    
    // Setup all event listeners
    setupEventListeners: function() {
        // Add to cart buttons
        this.setupAddToCartButtons();
        
        // Update cart buttons (for cart page)
        this.setupUpdateCartButtons();
        
        // Remove from cart buttons
        this.setupRemoveCartButtons();
        
        // Quantity controls
        this.setupQuantityControls();
        
        // Listen for storage changes (multi-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'cartCount') {
                this.cartCount = parseInt(e.newValue || '0');
                this.updateAllBadges();
            }
        });
    },
    
    // Setup add to cart buttons
    setupAddToCartButtons: function() {
        document.querySelectorAll('.add-to-cart-btn, .quick-add-to-cart-btn').forEach(button => {
            // Skip if already registered
            if (button.dataset.cartUnifiedRegistered) return;
            
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleAddToCart(button);
            });
            
            button.dataset.cartUnifiedRegistered = 'true';
        });
    },
    
    // Handle add to cart
    handleAddToCart: function(button) {
        // Prevent multiple clicks
        if (this.isProcessing) {
            console.log('CartSystem: Already processing, ignoring click');
            return;
        }
        
        this.isProcessing = true;
        
        // Get product info
        const productId = button.getAttribute('data-product-id') || button.dataset.productId;
        let quantity = 1;
        
        if (button.classList.contains('add-to-cart-btn')) {
            const quantityInput = document.getElementById('quantity');
            quantity = quantityInput ? parseInt(quantityInput.value) : 1;
        }
        
        // Show loading state
        this.setButtonLoading(button, true);
        
        // Add visual effects
        this.addVisualEffects(button);
        
        // Send request
        fetch(`${BASE_URL}/ajax/add_to_cart.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `product_id=${productId}&quantity=${quantity}`,
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update cart count
                this.cartCount = parseInt(data.count);
                localStorage.setItem('cartCount', this.cartCount.toString());
                localStorage.setItem('cartLastUpdated', Date.now().toString());
                
                // Update all badges immediately
                this.updateAllBadges();
                
                // Show success notification
                this.showNotification(data.message, 'success');
                
                // Success button state
                this.setButtonSuccess(button);
                
                console.log('CartSystem: Product added successfully, new count:', this.cartCount);
            } else {
                this.showNotification(data.message, 'error');
                this.setButtonLoading(button, false);
            }
        })
        .catch(error => {
            console.error('CartSystem: Add to cart error:', error);
            this.showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');
            this.setButtonLoading(button, false);
        })
        .finally(() => {
            // Reset processing state after delay
            setTimeout(() => {
                this.isProcessing = false;
            }, 1000);
        });
    },
    
    // Update all cart badges
    updateAllBadges: function() {
        const now = Date.now();
        
        // Prevent too frequent updates
        if (now - this.lastUpdateTime < 100) {
            return;
        }
        
        this.lastUpdateTime = now;
        
        console.log('CartSystem: Updating all badges with count:', this.cartCount);
        
        // Desktop badges
        const desktopBadges = document.querySelectorAll('.cart-badge');
        desktopBadges.forEach(badge => {
            this.updateBadge(badge, this.cartCount);
        });
        
        // Mobile badges
        const mobileBadges = document.querySelectorAll('.mobile-nav-badge');
        mobileBadges.forEach(badge => {
            this.updateBadge(badge, this.cartCount);
        });
        
        // Mini cart count
        const miniCartCounts = document.querySelectorAll('.mini-cart-count');
        miniCartCounts.forEach(count => {
            count.textContent = this.cartCount;
        });
    },
    
    // Update individual badge
    updateBadge: function(badge, count) {
        if (!badge) return;
        
        if (count > 0) {
            const displayCount = count > 99 ? '99+' : count.toString();
            badge.textContent = displayCount;
            badge.setAttribute('data-count', displayCount);
            badge.style.display = 'flex';
            
            // Add pulse effect
            badge.classList.add('badge-pulse');
            setTimeout(() => {
                badge.classList.remove('badge-pulse');
            }, 1000);
        } else {
            badge.style.display = 'none';
        }
    },
    
    // Set button loading state
    setButtonLoading: function(button, loading) {
        if (loading) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            
            if (button.classList.contains('quick-add-to-cart-btn')) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            } else {
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
            }
            
            button.classList.add('loading');
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || button.innerHTML;
            button.classList.remove('loading');
        }
    },
    
    // Set button success state
    setButtonSuccess: function(button) {
        if (button.classList.contains('quick-add-to-cart-btn')) {
            button.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                button.innerHTML = button.dataset.originalText;
                button.disabled = false;
                button.classList.remove('loading');
            }, 1000);
        } else {
            button.innerHTML = button.dataset.originalText;
            button.disabled = false;
            button.classList.remove('loading');
        }
    },
    
    // Add visual effects
    addVisualEffects: function(button) {
        button.classList.add('clicked', 'button-clicked');
        
        // Fly to cart effect
        if (typeof window.flyToCart === 'function') {
            window.flyToCart(button);
        }
        
        setTimeout(() => {
            button.classList.remove('clicked', 'button-clicked');
        }, 300);
    },
    
    // Show notification
    showNotification: function(message, type) {
        // Try multiple notification systems
        if (typeof window.showSimpleNotification === 'function') {
            window.showSimpleNotification(message, type);
        } else if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof window.showNotificationRealtime === 'function') {
            window.showNotificationRealtime(message, type);
        } else {
            // Fallback: create simple notification
            this.createSimpleNotification(message, type);
        }
    },
    
    // Create simple notification fallback
    createSimpleNotification: function(message, type) {
        const notification = document.createElement('div');
        notification.className = `cart-unified-notification cart-unified-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        if (type === 'success') {
            notification.style.backgroundColor = '#10b981';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#ef4444';
        } else {
            notification.style.backgroundColor = '#3b82f6';
        }
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
};
