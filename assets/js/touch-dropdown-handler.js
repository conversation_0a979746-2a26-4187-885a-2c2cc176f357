/**
 * Touch Dropdown Handler - Minimal version
 * Chỉ xử lý logic cơ bản cho Cart và User Account trên màn hình cảm ứng
 */

class TouchDropdownHandler {
    constructor() {
        this.isTouchDevice = this.detectTouchDevice();
        this.activeDropdown = null;
        this.init();
    }

    /**
     * Ph<PERSON>t hiện thiết bị có màn hình cảm ứng
     */
    detectTouchDevice() {
        return (
            'ontouchstart' in window ||
            navigator.maxTouchPoints > 0 ||
            navigator.msMaxTouchPoints > 0 ||
            window.matchMedia('(hover: none)').matches
        );
    }

    /**
     * Khởi tạo event listeners
     */
    init() {
        if (!this.isTouchDevice) {
            return; // Không làm gì trên desktop, giữ nguyên hover behavior
        }

        // Xử lý cho Cart dropdown
        this.handleCartDropdown();
        
        // Xử lý cho User Account dropdown
        this.handleUserDropdown();

        // Xử lý cho nav link "Sản phẩm" trên touch devices với màn hình >= 1201px
        this.handleProductsNavLink();

        // Đóng dropdown khi click ra ngoài
        this.handleOutsideClick();
    }

    /**
     * Xử lý Cart dropdown trên màn hình cảm ứng
     */
    handleCartDropdown() {
        const cartBtn = document.querySelector('.cart-btn');
        const cartContainer = document.querySelector('.cart-container');
        const miniCart = document.querySelector('.mini-cart');

        if (!cartBtn || !cartContainer || !miniCart) return;

        cartBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Nếu đã mở thì đóng và chuyển trang
            if (cartContainer.classList.contains('dropdown-active')) {
                this.closeDropdown(cartContainer);
                // Chuyển trang ngay lập tức
                window.location.href = cartBtn.href;
                return;
            }

            // Đóng dropdown khác nếu có
            this.closeAllDropdowns();

            // Mở cart dropdown
            this.openDropdown(cartContainer);
        });

        // Xử lý click vào các nút trong mini cart
        const viewCartBtn = miniCart.querySelector('.view-cart');
        const checkoutBtn = miniCart.querySelector('.checkout');
        const miniCartViewMore = miniCart.querySelector('.mini-cart-view-more');

        if (viewCartBtn) {
            viewCartBtn.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        if (miniCartViewMore) {
            miniCartViewMore.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }

    /**
     * Xử lý User Account dropdown trên màn hình cảm ứng
     */
    handleUserDropdown() {
        const userBtn = document.querySelector('.user-account-btn');
        const userDropdown = document.querySelector('.user-dropdown');
        const userDropdownMenu = document.querySelector('.user-dropdown-menu');

        if (!userBtn || !userDropdown || !userDropdownMenu) return;

        userBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Nếu đã mở thì đóng
            if (userDropdown.classList.contains('dropdown-active')) {
                this.closeDropdown(userDropdown);
                return;
            }

            // Đóng dropdown khác nếu có
            this.closeAllDropdowns();

            // Mở user dropdown
            this.openDropdown(userDropdown);
        });

        // Xử lý click vào các menu item
        const menuItems = userDropdownMenu.querySelectorAll('.user-dropdown-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }

    /**
     * Xử lý nav link "Sản phẩm" trên touch devices với màn hình >= 1201px
     */
    handleProductsNavLink() {
        const productsNavLink = document.querySelector('.nav-item .nav-link[href*="products.php"]');
        
        if (!productsNavLink) return;

        productsNavLink.addEventListener('click', (e) => {
            // Chỉ xử lý khi màn hình >= 1201px
            if (window.innerWidth >= 1201) {
                e.preventDefault();
                e.stopPropagation();
                
                // Chuyển đến trang products.php
                window.location.href = productsNavLink.href;
            }
        });
    }

    /**
     * Mở dropdown
     */
    openDropdown(container) {
        container.classList.add('dropdown-active');
        
        // Update ARIA attributes
        const button = container.querySelector('.cart-btn, .user-account-btn');
        if (button) {
            button.setAttribute('aria-expanded', 'true');
        }

        this.activeDropdown = container;
    }

    /**
     * Đóng dropdown
     */
    closeDropdown(container) {
        if (!container) return;

        container.classList.remove('dropdown-active');
        
        // Update ARIA attributes
        const button = container.querySelector('.cart-btn, .user-account-btn');
        if (button) {
            button.setAttribute('aria-expanded', 'false');
        }

        if (this.activeDropdown === container) {
            this.activeDropdown = null;
        }
    }

    /**
     * Đóng tất cả dropdown
     */
    closeAllDropdowns() {
        const activeDropdowns = document.querySelectorAll('.dropdown-active');
        activeDropdowns.forEach(dropdown => {
            this.closeDropdown(dropdown);
        });
    }

    /**
     * Xử lý click ra ngoài để đóng dropdown
     */
    handleOutsideClick() {
        document.addEventListener('click', (e) => {
            if (this.activeDropdown && !this.activeDropdown.contains(e.target)) {
                this.closeDropdown(this.activeDropdown);
            }
        });

        // Xử lý ESC key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeDropdown) {
                this.closeDropdown(this.activeDropdown);
            }
        });
    }
}

// Khởi tạo khi DOM ready
document.addEventListener('DOMContentLoaded', () => {
    new TouchDropdownHandler();
}); 