/**
 * Mobile Dropdown Fix JavaScript for Nội Thất Bàng Vũ
 * Xử lý các tính năng tương tác của dropdown menu trong mobile menu
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Dropdown Fix loaded');
  
  // Xử lý dropdown menu
  function initMobileDropdowns() {
    // L<PERSON>y tất cả các dropdown toggle
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    console.log('Mobile dropdown toggles:', mobileDropdownToggles.length);
    
    if (mobileDropdownToggles.length > 0) {
      // Xử lý sự kiện click cho các dropdown toggle
      mobileDropdownToggles.forEach(function (toggle) {
        toggle.addEventListener('click', function (e) {
          e.preventDefault();
          e.stopPropagation();
          
          const parent = this.parentElement;
          console.log('Toggle clicked:', parent);
          
          // Toggle class active cho parent
          parent.classList.toggle('active');
        });
      });
    }
    
    // Xử lý nút quay lại
    const mobileBackButtons = document.querySelectorAll('.mobile-menu-back');
    console.log('Mobile back buttons:', mobileBackButtons.length);
    
    if (mobileBackButtons.length > 0) {
      mobileBackButtons.forEach(function (button) {
        button.addEventListener('click', function (e) {
          e.preventDefault();
          e.stopPropagation();
          
          // Tìm submenu cha
          const submenu = this.closest('.mobile-submenu');
          const parentItem = submenu.closest('.mobile-menu-item, .mobile-submenu-item');
          
          if (parentItem) {
            parentItem.classList.remove('active');
            console.log('Back button clicked:', parentItem);
          }
        });
      });
    }
  }
  
  // Khởi tạo dropdown menu sau khi DOM đã tải hoàn toàn
  setTimeout(function() {
    initMobileDropdowns();
  }, 500);
});
