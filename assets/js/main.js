/**
 * Main JavaScript file
 */

$(document).ready(function () {
  /**
   * Add to cart functionality
   *
   * Lưu ý: Chức năng thêm vào giỏ hàng đã được xử lý trong cart-realtime.js
   * Không thêm event handler ở đây để tránh xử lý trùng lặp
   */

  /**
   * Update cart item quantity
   */
  $('.update-cart-btn').on('click', function (e) {
    e.preventDefault();

    const productId = $(this).data('product-id');
    const quantity = $(this).closest('tr').find('.quantity-input').val();

    $.ajax({
      url: 'ajax/update_cart.php',
      type: 'POST',
      data: {
        product_id: productId,
        quantity: quantity,
      },
      dataType: 'json',
      success: function (response) {
        if (response.success) {
          // Reload the page to update cart
          location.reload();
        } else {
          // Show error message
          showNotification(response.message, 'error');
        }
      },
      error: function () {
        showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');
      },
    });
  });

  /**
   * Remove cart item
   *
   * Lưu ý: Chức năng xóa sản phẩm khỏi giỏ hàng đã được xử lý trong cart-realtime.js
   * Không thêm event handler ở đây để tránh xử lý trùng lặp
   */

  /**
   * Quantity input controls
   *
   * Lưu ý: Chức năng điều khiển số lượng đã được xử lý trong cart-realtime.js
   * Không thêm event handler ở đây để tránh xử lý trùng lặp
   */

  /**
   * Product image gallery
   */
  $('.product-thumbnail').on('click', function () {
    const mainImage = $('#main-product-image');
    const newSrc = $(this).data('image');

    mainImage.fadeOut(300, function () {
      mainImage.attr('src', newSrc);
      mainImage.fadeIn(300);
    });
  });

  /**
   * Show notification
   *
   * Lưu ý: Chức năng hiển thị thông báo đã được xử lý trong cart-realtime.js
   * Hàm này chỉ được giữ lại để tương thích với các chức năng khác trong main.js
   */
  function showNotification(message, type) {
    // Kiểm tra xem hàm showNotification đã được định nghĩa trong cart-realtime.js chưa
    if (typeof window.showNotificationRealtime === 'function') {
      // Sử dụng hàm từ cart-realtime.js
      window.showNotificationRealtime(message, type);
    } else {
      // Fallback nếu hàm từ cart-realtime.js không tồn tại
      console.log('Thông báo:', message, 'Loại:', type);
    }
  }

  /**
   * Update cart count
   *
   * Lưu ý: Chức năng cập nhật số lượng giỏ hàng đã được xử lý trong cart-realtime.js
   * Hàm này chỉ được giữ lại để tương thích với các chức năng khác trong main.js
   */
  function updateCartCount() {
    // Kiểm tra xem hàm updateCartCount đã được định nghĩa trong cart-realtime.js chưa
    if (typeof window.updateCartCountRealtime === 'function') {
      // Sử dụng hàm từ cart-realtime.js
      window.updateCartCountRealtime();
    } else {
      // Fallback nếu hàm từ cart-realtime.js không tồn tại
      $.ajax({
        url: 'ajax/get_cart_count.php',
        type: 'GET',
        dataType: 'json',
        success: function (response) {
          const cartCount = $('.cart-count');
          if (response.count > 0) {
            cartCount.text(response.count).show();
          } else {
            cartCount.hide();
          }
        },
      });
    }
  }

  /**
   * Form validation
   */
  $('.validate-form').on('submit', function (e) {
    let isValid = true;

    // Check required fields
    $(this)
      .find('[required]')
      .each(function () {
        if ($(this).val().trim() === '') {
          isValid = false;
          $(this).addClass('border-red-500');

          // Add error message if not exists
          if ($(this).siblings('.error-message').length === 0) {
            $(
              '<div class="error-message text-red-500 text-sm mt-1">Vui lòng nhập trường này</div>'
            ).insertAfter($(this));
          }
        } else {
          $(this).removeClass('border-red-500');
          $(this).siblings('.error-message').remove();
        }
      });

    // Check email format
    $(this)
      .find('[type="email"]')
      .each(function () {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if ($(this).val().trim() !== '' && !emailRegex.test($(this).val())) {
          isValid = false;
          $(this).addClass('border-red-500');

          // Add error message if not exists
          if ($(this).siblings('.error-message').length === 0) {
            $(
              '<div class="error-message text-red-500 text-sm mt-1">Email không hợp lệ</div>'
            ).insertAfter($(this));
          }
        }
      });

    if (!isValid) {
      e.preventDefault();
    }
  });

  // Remove error on input
  $('input, textarea, select').on('input', function () {
    $(this).removeClass('border-red-500');
    $(this).siblings('.error-message').remove();
  });

  /**
   * Dropdown menu
   */
  // Add a delay before hiding dropdown menu
  let dropdownTimeout;

  $('.dropdown-menu').on('mouseenter', function () {
    clearTimeout(dropdownTimeout);
    $('.dropdown-content').hide();
    $(this).find('.dropdown-content').show();
  });

  $('.dropdown-menu').on('mouseleave', function () {
    const $dropdown = $(this);
    dropdownTimeout = setTimeout(function () {
      $dropdown.find('.dropdown-content').hide();
    }, 300); // 300ms delay before hiding
  });

  // Prevent dropdown from closing when moving from button to content
  $('.dropdown-content').on('mouseenter', function () {
    clearTimeout(dropdownTimeout);
  });

  $('.dropdown-content').on('mouseleave', function () {
    const $dropdown = $(this).closest('.dropdown-menu');
    dropdownTimeout = setTimeout(function () {
      $dropdown.find('.dropdown-content').hide();
    }, 300); // 300ms delay before hiding
  });
});
