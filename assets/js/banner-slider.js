/**
 * Banner Slider JavaScript
 * Initializes and controls the banner slider on the homepage
 */

document.addEventListener('DOMContentLoaded', function () {
  // Khởi tạo Swiper cho banner - sử dụng selector phù hợp với HTML
  const bannerSlider = document.querySelector('.banner-swiper');

  if (bannerSlider) {
    // Biến để theo dõi trạng thái animation và user interaction
    let isAnimating = false;
    let animationQueue = [];
    let userInteractionTimer = null;
    let isUserInteracting = false;

    const swiper = new Swiper(bannerSlider, {
      // Tham số chung
      slidesPerView: 1,
      spaceBetween: 0,
      loop: true,
      effect: 'fade',
      fadeEffect: {
        crossFade: true,
      },
      autoplay: false, // Tắt autoplay ban đầu
      speed: 600, // Tốc độ tối ưu cho fade effect
      grabCursor: true,
      watchSlidesProgress: true,
      preventInteractionOnTransition: true, // Ngăn click spam
      allowTouchMove: true,

      // Navigation tùy chỉnh
      navigation: {
        nextEl: '.swiper-button-next-custom',
        prevEl: '.swiper-button-prev-custom',
      },

      // Pagination tùy chỉnh - sử dụng selector đúng với HTML
      pagination: {
        el: '.banner-pagination-container',
        clickable: true,
        renderBullet: function (index, className) {
          return '<span class="' + className + ' banner-bullet"></span>';
        },
      },

      // Hiệu ứng được tối ưu hóa
      on: {
        init: function () {
          console.log('Banner swiper đã khởi tạo.');

          // Khởi tạo slide đầu tiên ngay lập tức
          initializeFirstSlide();
        },
        slideChangeTransitionStart: function () {
          // Ngăn animation mới khi đang có animation
          if (isAnimating) return;
          isAnimating = true;

          // Ẩn tất cả content ngay lập tức
          hideAllSlideContentImmediate();
        },
        slideChangeTransitionEnd: function () {
          // Hiển thị content của slide mới với hiệu ứng
          showActiveSlideContentWithAnimation();

          // Cập nhật pagination
          updatePaginationBullets(this);

          // Reset và áp dụng lại parallax cho tất cả slide để đảm bảo hiển thị đúng
          resetAndApplyParallaxToAllSlides();

          // Reset trạng thái animation
          isAnimating = false;
        },
        slideChange: function () {
          // Chỉ cập nhật pagination nếu không đang animate
          if (!isAnimating) {
            updatePaginationBullets(this);
          }
        },
      },
    });

    // Hàm cập nhật pagination bullets
    function updatePaginationBullets(swiperInstance) {
      const bullets = document.querySelectorAll('.banner-bullet');

      if (bullets.length > 0) {
        bullets.forEach((bullet, index) => {
          bullet.classList.remove('swiper-pagination-bullet-active');
          if (index === swiperInstance.realIndex) {
            bullet.classList.add('swiper-pagination-bullet-active');
          }
        });
      }
    }

    // Hàm khởi tạo slide đầu tiên
    function initializeFirstSlide() {
      // Reset và áp dụng parallax cho tất cả slide ngay từ đầu
      resetAndApplyParallaxToAllSlides();

      // Đảm bảo tất cả content bắt đầu ở trạng thái ẩn
      const allContents = document.querySelectorAll('.banner-content');
      allContents.forEach(content => {
        content.style.opacity = '0';
        content.style.transform = 'translateY(30px)';
        content.style.transition = 'opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      });

      // Hiển thị nội dung slide đầu tiên với delay nhỏ
      setTimeout(() => {
        showActiveSlideContentWithAnimation();
      }, 300);
    }

    // Hàm ẩn tất cả nội dung slide ngay lập tức (cho transition)
    function hideAllSlideContentImmediate() {
      const allContents = document.querySelectorAll('.banner-content');
      allContents.forEach(content => {
        // Sử dụng transition nhanh để ẩn
        content.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
        content.style.opacity = '0';
        content.style.transform = 'translateY(30px)';
      });
    }

    // Hàm hiển thị nội dung slide đang active với animation từ dưới lên
    function showActiveSlideContentWithAnimation() {
      // Delay nhỏ để đảm bảo fade effect của Swiper hoàn thành
      setTimeout(() => {
        const activeContent = document.querySelector('.swiper-slide-active .banner-content');
        if (activeContent) {
          // Thiết lập transition mượt mà cho hiệu ứng từ dưới lên
          activeContent.style.transition = 'opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

          // Trigger animation từ dưới lên trên
          requestAnimationFrame(() => {
            activeContent.style.opacity = '1';
            activeContent.style.transform = 'translateY(0)';
          });
        }
      }, 100); // Delay ngắn để đồng bộ với Swiper fade
    }

    // Hàm áp dụng parallax cho slide đang active
    function applyParallaxToActiveSlide() {
      const scrollPosition = window.pageYOffset;
      const bannerSection = document.querySelector('.banner-section');

      if (bannerSection) {
        const bannerHeight = bannerSection.offsetHeight;

        if (scrollPosition <= bannerHeight) {
          const activeSlide = document.querySelector('.swiper-slide-active');
          if (activeSlide) {
            const parallaxBg = activeSlide.querySelector('.banner-slide-inner img');
            const parallaxContent = activeSlide.querySelector('.banner-content');

            if (parallaxBg) {
              // Áp dụng parallax cho ảnh nền
              parallaxBg.style.transform = `translateY(${scrollPosition * 0.4}px)`;
            }

            if (parallaxContent) {
              // Hiệu ứng fade out cho nội dung khi scroll (chỉ khi không đang animate)
              if (!isAnimating) {
                const opacity = 1 - (scrollPosition / bannerHeight) * 1.8;
                parallaxContent.style.opacity = opacity > 0 ? opacity : 0;
                parallaxContent.style.transform = `translateY(${scrollPosition * 0.15}px)`;
              }
            }
          }
        }
      }
    }

    // Hàm reset và áp dụng lại parallax cho tất cả slide
    function resetAndApplyParallaxToAllSlides() {
      const scrollPosition = window.pageYOffset;
      const bannerSection = document.querySelector('.banner-section');

      if (bannerSection) {
        const bannerHeight = bannerSection.offsetHeight;

        // Reset tất cả ảnh trước
        resetAllBannerImages();

        // Nếu đang scroll, áp dụng parallax cho tất cả slide
        if (scrollPosition > 0 && scrollPosition <= bannerHeight) {
          const allSlides = document.querySelectorAll('.banner-swiper .swiper-slide');
          allSlides.forEach(slide => {
            const parallaxBg = slide.querySelector('.banner-slide-inner img');
            if (parallaxBg) {
              parallaxBg.style.transform = `translateY(${scrollPosition * 0.4}px)`;
            }
          });
        }
      }
    }

    // Hàm xử lý khi người dùng bắt đầu tương tác
    function handleUserInteractionStart() {
      isUserInteracting = true;

      // Clear timer cũ nếu có
      if (userInteractionTimer) {
        clearTimeout(userInteractionTimer);
        userInteractionTimer = null;
      }

      // Dừng autoplay nếu đang chạy
      if (swiper && swiper.autoplay && swiper.autoplay.running) {
        swiper.autoplay.stop();
        console.log('Autoplay đã dừng do người dùng tương tác');
      }
    }

    // Hàm xử lý khi người dùng kết thúc tương tác
    function handleUserInteractionEnd() {
      // Clear timer cũ nếu có
      if (userInteractionTimer) {
        clearTimeout(userInteractionTimer);
      }

      // Đặt timer 5 giây để kích hoạt lại autoplay
      userInteractionTimer = setTimeout(() => {
        isUserInteracting = false;

        // Kiểm tra xem banner có trong viewport không
        const bannerSection = document.querySelector('.banner-section');
        if (bannerSection) {
          const rect = bannerSection.getBoundingClientRect();
          const isInViewport = (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
          );

          // Chỉ kích hoạt autoplay nếu banner trong viewport và swiper tồn tại
          if (isInViewport && swiper && swiper.autoplay && !swiper.autoplay.running) {
            swiper.autoplay.start();
            console.log('Autoplay đã được kích hoạt lại sau 5 giây không tương tác');
          }
        }

        userInteractionTimer = null;
      }, 5000); // 5 giây

      console.log('Timer 5 giây đã được đặt để kích hoạt lại autoplay');
    }

    // Sử dụng Intersection Observer để theo dõi khi banner xuất hiện trong viewport
    const bannerSection = document.querySelector('.banner-section');
    if (bannerSection) {
      const bannerObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Khi banner nằm trong viewport, kích hoạt autoplay (chỉ khi không có user interaction)
            if (swiper && !swiper.autoplay.running && !isUserInteracting) {
              // Cấu hình autoplay
              swiper.params.autoplay = {
                delay: 7000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              };
              swiper.autoplay.start();
              console.log('Đã kích hoạt autoplay cho banner');
            }
          } else {
            // Khi banner không nằm trong viewport, dừng autoplay và clear timer
            if (swiper && swiper.autoplay.running) {
              swiper.autoplay.stop();
              console.log('Đã dừng autoplay cho banner');
            }

            // Clear user interaction timer khi banner ra khỏi viewport
            if (userInteractionTimer) {
              clearTimeout(userInteractionTimer);
              userInteractionTimer = null;
              isUserInteracting = false;
            }
          }
        });
      }, {
        root: null,
        rootMargin: '0px',
        threshold: 0.1 // Kích hoạt khi 10% phần tử xuất hiện
      });

      bannerObserver.observe(bannerSection);
    }

    // Xử lý cuộn xuống khi click vào biểu tượng cuộn xuống (chỉ một handler)
    const scrollDownIcon = document.querySelector('.scroll-down-icon');
    if (scrollDownIcon) {
      scrollDownIcon.addEventListener('click', () => {
        const bannerHeight = document.querySelector('.banner-section').offsetHeight;
        window.scrollTo({
          top: bannerHeight - 50, // Trừ đi 50px để có overlap nhẹ
          behavior: 'smooth'
        });
      });
    }

    // Thêm event listeners cho navigation buttons
    const prevButton = document.querySelector('.swiper-button-prev-custom');
    const nextButton = document.querySelector('.swiper-button-next-custom');

    if (prevButton) {
      prevButton.addEventListener('click', () => {
        handleUserInteractionStart();
        handleUserInteractionEnd();
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => {
        handleUserInteractionStart();
        handleUserInteractionEnd();
      });
    }

    // Thêm event listeners cho pagination bullets
    const paginationContainer = document.querySelector('.banner-pagination-container');
    if (paginationContainer) {
      paginationContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('banner-bullet')) {
          handleUserInteractionStart();
          handleUserInteractionEnd();
        }
      });
    }

    // Thêm event listeners cho touch/swipe gestures
    bannerSlider.addEventListener('touchstart', () => {
      handleUserInteractionStart();
    });

    bannerSlider.addEventListener('touchend', () => {
      handleUserInteractionEnd();
    });

    // Thêm event listeners cho mouse drag
    bannerSlider.addEventListener('mousedown', () => {
      handleUserInteractionStart();
    });

    bannerSlider.addEventListener('mouseup', () => {
      handleUserInteractionEnd();
    });
  }

  // Hiệu ứng parallax khi scroll - tối ưu hóa và khắc phục vấn đề hiển thị
  let ticking = false;

  window.addEventListener('scroll', function () {
    if (!ticking) {
      requestAnimationFrame(function() {
        const bannerSection = document.querySelector('.banner-section');

        if (bannerSection) {
          const scrollPosition = window.pageYOffset;
          const bannerHeight = bannerSection.offsetHeight;

          if (scrollPosition <= bannerHeight) {
            // Áp dụng parallax cho TẤT CẢ slide để đảm bảo hiển thị đúng
            const allSlides = document.querySelectorAll('.banner-swiper .swiper-slide');
            allSlides.forEach(slide => {
              const parallaxBg = slide.querySelector('.banner-slide-inner img');
              if (parallaxBg) {
                parallaxBg.style.transform = `translateY(${scrollPosition * 0.4}px)`;
              }
            });

            // Chỉ áp dụng hiệu ứng content cho slide active
            const activeSlide = document.querySelector('.swiper-slide-active');
            if (activeSlide) {
              const parallaxContent = activeSlide.querySelector('.banner-content');
              if (parallaxContent) {
                // Hiệu ứng fade out cho nội dung khi scroll
                const opacity = 1 - (scrollPosition / bannerHeight) * 1.8;
                parallaxContent.style.opacity = opacity > 0 ? opacity : 0;
                parallaxContent.style.transform = `translateY(${scrollPosition * 0.15}px)`;
              }
            }
          }
        }
        ticking = false;
      });
      ticking = true;
    }
  });
});

// Hàm reset tất cả ảnh banner về vị trí ban đầu
function resetAllBannerImages() {
  const allBannerImages = document.querySelectorAll('.banner-slide-inner img');
  allBannerImages.forEach(image => {
    image.style.transform = 'translateY(0px)';
  });
}

// Tối ưu hóa hoàn tất - tất cả logic đã được tích hợp vào main function
