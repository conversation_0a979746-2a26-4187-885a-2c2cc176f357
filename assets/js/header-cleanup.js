/**
 * Header Cleanup Script
 * Tạm thời disable các file JavaScript duplicate để test performance
 */

class HeaderCleanup {
    constructor() {
        this.disabledScripts = [];
        this.originalFunctions = {};
        this.cleanupActive = false;
    }

    /**
     * Bắt đầu cleanup process
     */
    startCleanup() {
        console.log('🧹 Bắt đầu header cleanup process...');
        
        this.disableConflictingScripts();
        this.disableConflictingEventListeners();
        this.cleanupActive = true;
        
        console.log('✅ Header cleanup completed');
        this.showCleanupStatus();
    }

    /**
     * Disable các script files gây conflict
     */
    disableConflictingScripts() {
        const conflictingScripts = [
            'premium-header.js',
            'header-scroll.js', 
            'three-tier-header.js',
            'modern-header.js',
            'luxury-header.js',
            'elegant-header.js'
        ];

        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            const src = script.src;
            const isConflicting = conflictingScripts.some(conflictScript => 
                src.includes(conflictScript)
            );
            
            if (isConflicting && !src.includes('premium-header-optimized.js')) {
                console.log(`🚫 Disabling script: ${src}`);
                
                // Tạo comment node thay thế
                const comment = document.createComment(`DISABLED: ${script.outerHTML}`);
                script.parentNode.insertBefore(comment, script);
                
                // Remove script
                script.remove();
                
                this.disabledScripts.push(src);
            }
        });
    }

    /**
     * Disable các event listeners gây conflict
     */
    disableConflictingEventListeners() {
        console.log('🔇 Disabling conflicting event listeners...');
        
        // Override các global functions có thể gây conflict
        const conflictingGlobals = [
            'handleScroll',
            'updateHeader',
            'headerScrollHandler',
            'stickyHeader'
        ];

        conflictingGlobals.forEach(funcName => {
            if (window[funcName] && typeof window[funcName] === 'function') {
                console.log(`🚫 Disabling global function: ${funcName}`);
                this.originalFunctions[funcName] = window[funcName];
                window[funcName] = function() {
                    console.log(`⚠️ Blocked call to disabled function: ${funcName}`);
                };
            }
        });

        // Remove existing scroll listeners (aggressive approach)
        this.removeScrollListeners();
    }

    /**
     * Remove existing scroll listeners
     */
    removeScrollListeners() {
        // Clone window object để remove tất cả listeners
        const newWindow = window.cloneNode ? window.cloneNode(true) : window;
        
        // Tạo array để track listeners
        const listeners = [];
        
        // Override addEventListener để track
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const originalRemoveEventListener = EventTarget.prototype.removeEventListener;
        
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type === 'scroll' && this === window) {
                listeners.push({ type, listener, options, target: this });
                console.log('📝 Tracked scroll listener');
            }
            return originalAddEventListener.call(this, type, listener, options);
        };

        // Method để remove tất cả tracked listeners
        this.removeAllScrollListeners = () => {
            listeners.forEach(({ type, listener, options, target }) => {
                try {
                    target.removeEventListener(type, listener, options);
                    console.log('🗑️ Removed scroll listener');
                } catch (e) {
                    console.log('⚠️ Could not remove listener:', e);
                }
            });
            listeners.length = 0;
        };
    }

    /**
     * Restore original state
     */
    restoreOriginalState() {
        console.log('🔄 Restoring original header state...');
        
        // Restore disabled functions
        Object.entries(this.originalFunctions).forEach(([funcName, originalFunc]) => {
            window[funcName] = originalFunc;
            console.log(`✅ Restored function: ${funcName}`);
        });

        // Note: Scripts cannot be easily restored without page reload
        console.log('⚠️ Note: Disabled scripts require page reload to restore');
        
        this.cleanupActive = false;
        console.log('✅ Restoration completed (reload page for full restoration)');
    }

    /**
     * Show cleanup status
     */
    showCleanupStatus() {
        console.log('\n🎯 ===== CLEANUP STATUS =====');
        console.log(`Disabled Scripts: ${this.disabledScripts.length}`);
        console.log(`Disabled Functions: ${Object.keys(this.originalFunctions).length}`);
        console.log(`Cleanup Active: ${this.cleanupActive ? '✅ Yes' : '❌ No'}`);
        
        if (this.disabledScripts.length > 0) {
            console.log('\n📄 Disabled Scripts:');
            this.disabledScripts.forEach(script => {
                console.log(`  - ${script.split('/').pop()}`);
            });
        }
        
        if (Object.keys(this.originalFunctions).length > 0) {
            console.log('\n🔧 Disabled Functions:');
            Object.keys(this.originalFunctions).forEach(func => {
                console.log(`  - ${func}()`);
            });
        }
        
        console.log('\n💡 Commands:');
        console.log('  - headerCleanup.restoreOriginalState() // Restore functions');
        console.log('  - location.reload() // Full restore');
        console.log('===== END STATUS =====\n');
    }

    /**
     * Test performance after cleanup
     */
    testPerformance() {
        console.log('🧪 Testing performance after cleanup...');
        
        let scrollEvents = 0;
        let startTime = performance.now();
        
        const testHandler = () => {
            scrollEvents++;
        };
        
        window.addEventListener('scroll', testHandler, { passive: true });
        
        // Test scroll performance
        setTimeout(() => {
            const duration = performance.now() - startTime;
            const eventsPerSecond = Math.round((scrollEvents * 1000) / duration);
            
            console.log(`📊 Performance Test Results:`);
            console.log(`  - Scroll events: ${scrollEvents}`);
            console.log(`  - Events per second: ${eventsPerSecond}`);
            console.log(`  - Test duration: ${Math.round(duration)}ms`);
            
            window.removeEventListener('scroll', testHandler);
            
            // Compare with baseline
            if (eventsPerSecond < 50) {
                console.log('✅ Good: Low scroll event frequency');
            } else if (eventsPerSecond < 100) {
                console.log('⚠️ Medium: Moderate scroll event frequency');
            } else {
                console.log('🚨 High: High scroll event frequency - needs optimization');
            }
        }, 3000);
        
        console.log('⏳ Running 3-second performance test...');
    }
}

// Initialize cleanup
document.addEventListener('DOMContentLoaded', () => {
    // Delay để đảm bảo tất cả scripts đã load
    setTimeout(() => {
        window.headerCleanup = new HeaderCleanup();
        
        // Auto-start cleanup if URL contains cleanup parameter
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('header_cleanup') === 'true') {
            window.headerCleanup.startCleanup();
            
            // Auto-test performance
            setTimeout(() => {
                window.headerCleanup.testPerformance();
            }, 1000);
        }
        
        console.log('🧹 Header Cleanup ready. Use headerCleanup.startCleanup() to begin');
    }, 1000);
});

// Export for manual usage
window.HeaderCleanup = HeaderCleanup;
