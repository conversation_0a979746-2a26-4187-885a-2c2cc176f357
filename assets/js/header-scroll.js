/**
 * Header Scroll Effects
 * Xử lý hiệu ứng cuộn cho header
 */

document.addEventListener('DOMContentLoaded', function() {
    // Các phần tử header
    const premiumHeader = document.querySelector('.premium-header');
    const topBar = document.querySelector('.top-bar');
    const mobileHeader = document.querySelector('.mobile-header');
    const mobileBottomNav = document.querySelector('.mobile-bottom-nav');

    // Biến lưu trạng thái cuộn trước đó
    let lastScrollTop = 0;
    let isScrollingDown = false;
    let scrollTimer = null;
    let ticking = false; // Biến để tối ưu hiệu suất với requestAnimationFrame

    // Thêm class transition sau khi trang đã tải xong
    setTimeout(() => {
        if (premiumHeader) premiumHeader.classList.add('smooth-transition');
        if (mobileHeader) mobileHeader.classList.add('smooth-transition');
        if (mobileBottomNav) mobileBottomNav.classList.add('smooth-transition');
    }, 300);

    // Xử lý sự kiện cuộn
    window.addEventListener('scroll', function() {
        // Lấy vị trí cuộn hiện tại
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Xác định hướng cuộn
        isScrollingDown = scrollTop > lastScrollTop;

        // Sử dụng requestAnimationFrame để tối ưu hiệu suất
        if (!ticking) {
            window.requestAnimationFrame(function() {
                // Xử lý hiệu ứng cho desktop header
                if (premiumHeader) {
                    handleDesktopHeaderScroll(scrollTop, isScrollingDown);
                }

                // Xử lý hiệu ứng cho mobile header
                if (mobileHeader) {
                    handleMobileHeaderScroll(scrollTop, isScrollingDown);
                }

                // Xử lý hiệu ứng cho mobile bottom nav
                if (mobileBottomNav) {
                    handleMobileBottomNavScroll(scrollTop, isScrollingDown);
                }

                // Đặt lại trạng thái ticking
                ticking = false;
            });

            ticking = true;
        }

        // Cập nhật vị trí cuộn trước đó
        lastScrollTop = scrollTop;

        // Đặt lại timer để phát hiện khi người dùng dừng cuộn
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(() => {
            handleScrollStop();
        }, 150);
    });

    /**
     * Xử lý hiệu ứng cuộn cho desktop header
     */
    function handleDesktopHeaderScroll(scrollTop, isScrollingDown) {
        // Sử dụng hysteresis để tránh hiệu ứng nhấp nháy khi cuộn gần ngưỡng
        const scrollThreshold = 10;
        const scrollHysteresis = 5;

        // Thêm class scrolled khi cuộn xuống
        if (scrollTop > scrollThreshold) {
            if (!premiumHeader.classList.contains('scrolled')) {
                // Thêm class với hiệu ứng mượt mà
                requestAnimationFrame(() => {
                    premiumHeader.classList.add('scrolled');
                });
            }

            // Thêm hiệu ứng scale nhẹ cho header khi cuộn xuống
            const scrollProgress = Math.min(1, scrollTop / 100);
            const scale = 0.98 + (0.02 * (1 - scrollProgress));

            // Không sử dụng biến opacity nữa để tránh tạo thanh màu đen
            premiumHeader.style.setProperty('--header-scale', scale.toFixed(3));
        } else if (scrollTop < (scrollThreshold - scrollHysteresis)) {
            // Chỉ xóa class khi cuộn lên đủ xa để tránh nhấp nháy
            if (premiumHeader.classList.contains('scrolled')) {
                // Xóa class với hiệu ứng mượt mà
                requestAnimationFrame(() => {
                    premiumHeader.classList.remove('scrolled');
                });

                // Thêm class để kích hoạt hiệu ứng trượt xuống mượt mà khi top bar hiện lại
                if (topBar) {
                    // Đảm bảo animation chỉ chạy một lần khi hiện lại
                    topBar.style.animation = 'none';
                    // Force reflow
                    void topBar.offsetWidth;
                    // Kích hoạt animation
                    topBar.style.animation = '';
                }
            }

            // Khôi phục scale bình thường
            premiumHeader.style.setProperty('--header-scale', '1');
        }
    }

    /**
     * Xử lý hiệu ứng cuộn cho mobile header
     */
    function handleMobileHeaderScroll(scrollTop, isScrollingDown) {
        // Thêm class scrolled khi cuộn xuống
        if (scrollTop > 10) {
            if (!mobileHeader.classList.contains('scrolled')) {
                mobileHeader.classList.add('scrolled');
            }

            // Ẩn/hiện header dựa vào hướng cuộn với hiệu ứng mượt mà
            if (isScrollingDown && scrollTop > 100) {
                // Cuộn xuống và đã cuộn quá 100px - ẩn header
                mobileHeader.style.transform = 'translateY(-100%)';
                mobileHeader.style.boxShadow = 'none';
            } else {
                // Cuộn lên - hiện header với hiệu ứng nhẹ
                mobileHeader.style.transform = 'translateY(0)';
                mobileHeader.style.boxShadow = '';
            }
        } else {
            if (mobileHeader.classList.contains('scrolled')) {
                mobileHeader.classList.remove('scrolled');
            }
            mobileHeader.style.transform = 'translateY(0)';
            mobileHeader.style.boxShadow = '';
        }
    }

    /**
     * Xử lý hiệu ứng cuộn cho mobile bottom nav
     */
    function handleMobileBottomNavScroll(scrollTop, isScrollingDown) {
        // Thêm class scrolled khi cuộn xuống
        if (scrollTop > 50) {
            if (!mobileBottomNav.classList.contains('scrolled')) {
                mobileBottomNav.classList.add('scrolled');
            }

            // Ẩn/hiện bottom nav dựa vào hướng cuộn với hiệu ứng mượt mà
            if (isScrollingDown && scrollTop > 100) {
                // Cuộn xuống và đã cuộn quá 100px - ẩn bottom nav
                mobileBottomNav.style.transform = 'translateY(100%) translateX(-50%)';
                mobileBottomNav.style.opacity = '0';
            } else {
                // Cuộn lên - hiện bottom nav
                mobileBottomNav.style.transform = 'translateY(0) translateX(-50%)';
                mobileBottomNav.style.opacity = '1';
            }
        } else {
            if (mobileBottomNav.classList.contains('scrolled')) {
                mobileBottomNav.classList.remove('scrolled');
            }
            mobileBottomNav.style.transform = 'translateY(0) translateX(-50%)';
            mobileBottomNav.style.opacity = '1';
        }
    }

    /**
     * Xử lý khi người dùng dừng cuộn
     */
    function handleScrollStop() {
        // Hiển thị lại các thanh điều hướng sau khi dừng cuộn với hiệu ứng mượt mà
        if (mobileHeader) {
            // Sử dụng requestAnimationFrame để đảm bảo hiệu ứng mượt mà
            requestAnimationFrame(() => {
                mobileHeader.style.transform = 'translateY(0)';
                mobileHeader.style.boxShadow = '';
            });
        }

        if (mobileBottomNav) {
            // Sử dụng requestAnimationFrame để đảm bảo hiệu ứng mượt mà
            requestAnimationFrame(() => {
                mobileBottomNav.style.transform = 'translateY(0) translateX(-50%)';
                mobileBottomNav.style.opacity = '1';
            });
        }

        // Thêm class để đánh dấu đã dừng cuộn
        if (premiumHeader) {
            // Kiểm tra vị trí cuộn hiện tại
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Thêm class để đánh dấu đã dừng cuộn
            premiumHeader.classList.add('scroll-pause');

            // Xóa class sau 300ms để có thể áp dụng lại hiệu ứng
            setTimeout(() => {
                premiumHeader.classList.remove('scroll-pause');
            }, 300);

            // Nếu đang ở đầu trang và top bar đang ẩn, hiển thị lại với hiệu ứng trượt xuống mượt mà
            if (scrollTop < 5 && topBar && premiumHeader.classList.contains('scrolled')) {
                // Xóa class scrolled để hiển thị top bar
                requestAnimationFrame(() => {
                    premiumHeader.classList.remove('scrolled');

                    // Đảm bảo animation chỉ chạy một lần khi hiện lại
                    topBar.style.animation = 'none';
                    // Force reflow
                    void topBar.offsetWidth;
                    // Kích hoạt animation
                    topBar.style.animation = '';
                });
            }
        }
    }
});
