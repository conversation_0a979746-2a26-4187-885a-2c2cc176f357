/**
 * Hover Reset Handler
 * Xử lý việc tắt hover effect sau khi click vào nav link và cart
 */
document.addEventListener('DOMContentLoaded', function() {
    // Lấy các elements cần xử lý - sử dụng cách an toàn hơn
    const productNavItem = document.querySelector('.nav-item .mega-menu')?.closest('.nav-item');
    const cartContainer = document.querySelector('.cart-container');

    // Class để tắt hover effect tạm thời
    const HOVER_DISABLED_CLASS = 'hover-disabled';

    // Phát hiện trang hiện tại
    const currentPage = window.location.pathname;
    const isProductPage = currentPage.includes('products.php') || currentPage.includes('/products');
    const isCartPage = currentPage.includes('cart.php') || currentPage.includes('/cart');

    // EXTREME MODE: Thêm class vào body ngay lập tức để CSS có thể ẩn hover box
    if (isProductPage) {
        document.body.classList.add('page-products');
    }

    if (isCartPage) {
        document.body.classList.add('page-cart');
    }



    // Biến lưu vị trí chuột cuối cùng
    let lastMouseX = 0;
    let lastMouseY = 0;

    // Biến theo dõi element nào đang được hover
    let isMouseOverProduct = false;
    let isMouseOverCart = false;

    // Biến theo dõi xem đã có lần hover "thật" chưa (từ ngoài vào trong)
    let hasRealProductHover = false;
    let hasRealCartHover = false;

    // Biến theo dõi xem chuột có đang ở trong nav link khi trang load không
    let wasMouseOverProductOnLoad = false;
    let wasMouseOverCartOnLoad = false;

    // Biến theo dõi xem đã kiểm tra initial position chưa
    let hasCheckedInitialPosition = false;

    // Lắng nghe vị trí chuột toàn cục
    document.addEventListener('mousemove', function(event) {
        lastMouseX = event.clientX;
        lastMouseY = event.clientY;
    });

    // Theo dõi khi chuột vào/ra khỏi các elements
    if (productNavItem) {
        productNavItem.addEventListener('mouseenter', function() {
            isMouseOverProduct = true;

            // Kiểm tra initial position nếu chưa kiểm tra
            if (!hasCheckedInitialPosition) {
                checkInitialMousePosition();
            }

            // Logic cho trang sản phẩm
            if (isProductPage) {
                // Cho phép hover nếu:
                // 1. Đã có lần hover thật, HOẶC
                // 2. Chuột không ở trong nav link khi load trang (tức là di chuyển từ ngoài vào)
                if (hasRealProductHover || !wasMouseOverProductOnLoad) {
                    enableHover(productNavItem);
                    productNavItem.classList.add('real-hover-allowed');
                }
                // Nếu chuột đã ở sẵn trong nav link khi load trang, giữ nguyên trạng thái disabled
            } else {
                // Trang khác: cho phép hover bình thường
                enableHover(productNavItem);
            }
        });

        productNavItem.addEventListener('mouseleave', function() {
            isMouseOverProduct = false;

            // Đánh dấu đã có lần hover thật khi chuột rời khỏi element
            if (isProductPage) {
                hasRealProductHover = true;
                // EXTREME MODE: Thêm class để cho phép hover lần tiếp theo
                productNavItem.classList.add('real-hover-allowed');
            }

            // Luôn enable hover khi chuột ra ngoài (để chuẩn bị cho lần hover tiếp theo)
            enableHover(productNavItem);
        });
    }

    if (cartContainer) {
        cartContainer.addEventListener('mouseenter', function() {
            isMouseOverCart = true;

            // Kiểm tra initial position nếu chưa kiểm tra
            if (!hasCheckedInitialPosition) {
                checkInitialMousePosition();
            }

            // Logic cho trang giỏ hàng
            if (isCartPage) {
                // Cho phép hover nếu:
                // 1. Đã có lần hover thật, HOẶC
                // 2. Chuột không ở trong cart container khi load trang (tức là di chuyển từ ngoài vào)
                if (hasRealCartHover || !wasMouseOverCartOnLoad) {
                    enableHover(cartContainer);
                    cartContainer.classList.add('real-hover-allowed');
                }
                // Nếu chuột đã ở sẵn trong cart container khi load trang, giữ nguyên trạng thái disabled
            } else {
                // Trang khác: cho phép hover bình thường
                enableHover(cartContainer);
            }
        });

        cartContainer.addEventListener('mouseleave', function() {
            isMouseOverCart = false;

            // Đánh dấu đã có lần hover thật khi chuột rời khỏi element
            if (isCartPage) {
                hasRealCartHover = true;
                // EXTREME MODE: Thêm class để cho phép hover lần tiếp theo
                cartContainer.classList.add('real-hover-allowed');
            }

            // Luôn enable hover khi chuột ra ngoài (để chuẩn bị cho lần hover tiếp theo)
            enableHover(cartContainer);
        });
    }
    
    /**
     * Tắt hover effect cho element
     */
    function disableHover(element) {
        if (element) {
            element.classList.add(HOVER_DISABLED_CLASS);
        }
    }
    
    /**
     * Bật lại hover effect cho element
     */
    function enableHover(element) {
        if (element) {
            element.classList.remove(HOVER_DISABLED_CLASS);
        }
    }
    
    /**
     * Kiểm tra xem chuột có đang nằm trong element không
     */
    function isMouseOver(element, event) {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return (
            event.clientX >= rect.left &&
            event.clientX <= rect.right &&
            event.clientY >= rect.top &&
            event.clientY <= rect.bottom
        );
    }
    
    /**
     * Kiểm tra vị trí chuột hiện tại bằng nhiều phương pháp
     * Chỉ áp dụng cho các trang không phải product/cart
     */
    function checkCurrentMousePosition() {
        // Không kiểm tra nếu đang ở trang product/cart
        if (isProductPage || isCartPage) {
            return;
        }

        // Phương pháp 0: Sử dụng biến theo dõi (chính xác nhất)
        if (isMouseOverProduct) {
            disableHover(productNavItem);
            return;
        }

        if (isMouseOverCart) {
            disableHover(cartContainer);
            return;
        }

        // Phương pháp 1: Sử dụng :hover pseudo-class
        if (productNavItem && productNavItem.matches(':hover')) {
            disableHover(productNavItem);
            return;
        }

        if (cartContainer && cartContainer.matches(':hover')) {
            disableHover(cartContainer);
            return;
        }

        // Phương pháp 2: Sử dụng elementFromPoint với vị trí chuột cuối cùng
        if (lastMouseX > 0 && lastMouseY > 0) {
            const elementUnderMouse = document.elementFromPoint(lastMouseX, lastMouseY);

            if (elementUnderMouse) {
                // Kiểm tra xem element có nằm trong nav item sản phẩm không
                if (productNavItem && (productNavItem.contains(elementUnderMouse) || productNavItem === elementUnderMouse)) {
                    disableHover(productNavItem);
                    return;
                }

                // Kiểm tra xem element có nằm trong cart container không
                if (cartContainer && (cartContainer.contains(elementUnderMouse) || cartContainer === elementUnderMouse)) {
                    disableHover(cartContainer);
                    return;
                }
            }
        }

        // Phương pháp 3: Kiểm tra bằng getBoundingClientRect nếu có vị trí chuột
        if (lastMouseX > 0 && lastMouseY > 0) {
            if (productNavItem && isMouseOverElement(productNavItem, lastMouseX, lastMouseY)) {
                disableHover(productNavItem);
            }

            if (cartContainer && isMouseOverElement(cartContainer, lastMouseX, lastMouseY)) {
                disableHover(cartContainer);
            }
        }
    }

    /**
     * Kiểm tra xem tọa độ có nằm trong element không
     */
    function isMouseOverElement(element, x, y) {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        return (
            x >= rect.left &&
            x <= rect.right &&
            y >= rect.top &&
            y <= rect.bottom
        );
    }

    /**
     * Kiểm tra vị trí chuột ban đầu khi trang load
     */
    function checkInitialMousePosition() {
        if (hasCheckedInitialPosition) return;
        hasCheckedInitialPosition = true;

        // Phương pháp 1: Sử dụng :hover pseudo-class
        if (productNavItem && productNavItem.matches(':hover')) {
            wasMouseOverProductOnLoad = true;
        }

        if (cartContainer && cartContainer.matches(':hover')) {
            wasMouseOverCartOnLoad = true;
        }

        // Phương pháp 2: Sử dụng elementFromPoint nếu có vị trí chuột
        if (lastMouseX > 0 && lastMouseY > 0) {
            const elementUnderMouse = document.elementFromPoint(lastMouseX, lastMouseY);

            if (elementUnderMouse) {
                // Kiểm tra xem element có nằm trong nav item sản phẩm không
                if (productNavItem && (productNavItem.contains(elementUnderMouse) || productNavItem === elementUnderMouse)) {
                    wasMouseOverProductOnLoad = true;
                }

                // Kiểm tra xem element có nằm trong cart container không
                if (cartContainer && (cartContainer.contains(elementUnderMouse) || cartContainer === elementUnderMouse)) {
                    wasMouseOverCartOnLoad = true;
                }
            }
        }

        // Phương pháp 3: Kiểm tra bằng getBoundingClientRect
        if (lastMouseX > 0 && lastMouseY > 0) {
            if (productNavItem && isMouseOverElement(productNavItem, lastMouseX, lastMouseY)) {
                wasMouseOverProductOnLoad = true;
            }

            if (cartContainer && isMouseOverElement(cartContainer, lastMouseX, lastMouseY)) {
                wasMouseOverCartOnLoad = true;
            }
        }


    }

    /**
     * Tắt hover ngay lập tức cho trang tương ứng
     */
    function disableHoverForCurrentPage() {
        if (isProductPage && productNavItem) {
            disableHover(productNavItem);
        }

        if (isCartPage && cartContainer) {
            disableHover(cartContainer);
        }
    }

    /**
     * Xử lý khi trang được load
     */
    function handlePageLoad() {
        // Tắt hover ngay lập tức cho trang hiện tại
        disableHoverForCurrentPage();

        // Kiểm tra vị trí chuột ban đầu cho trang product/cart
        if (isProductPage || isCartPage) {
            // Kiểm tra sau một khoảng thời gian ngắn để đảm bảo layout đã ổn định
            setTimeout(function() {
                checkInitialMousePosition();
            }, 100);

            setTimeout(function() {
                checkInitialMousePosition();
            }, 300);
        }

        // Kiểm tra vị trí chuột cho các trang khác
        if (!isProductPage && !isCartPage) {
            // Kiểm tra ngay lập tức
            checkCurrentMousePosition();

            // Kiểm tra lại sau 100ms
            setTimeout(function() {
                checkCurrentMousePosition();
            }, 100);

            // Kiểm tra lại sau 300ms để đảm bảo
            setTimeout(function() {
                checkCurrentMousePosition();
            }, 300);

            // Kiểm tra lại sau 500ms (cuối cùng)
            setTimeout(function() {
                checkCurrentMousePosition();
            }, 500);
        }

        // Backup: Lắng nghe sự kiện mousemove đầu tiên
        document.addEventListener('mousemove', function(event) {
            // Chỉ kiểm tra cho các trang không phải product/cart
            if (!isProductPage && !isCartPage) {
                // Kiểm tra nav item sản phẩm
                if (productNavItem && isMouseOver(productNavItem, event)) {
                    disableHover(productNavItem);
                }

                // Kiểm tra cart container
                if (cartContainer && isMouseOver(cartContainer, event)) {
                    disableHover(cartContainer);
                }
            }
        }, { once: true }); // Chỉ chạy một lần
    }
    

    
    /**
     * Xử lý click events
     */
    function setupClickHandlers() {
        // Xử lý click vào nav link sản phẩm
        if (productNavItem) {
            const productLink = productNavItem.querySelector('.nav-link');
            if (productLink) {
                productLink.addEventListener('click', function() {
                    // Tắt hover effect ngay lập tức
                    disableHover(productNavItem);
                });
            }
        }
        
        // Xử lý click vào cart button
        if (cartContainer) {
            const cartButton = cartContainer.querySelector('.cart-btn');
            if (cartButton) {
                cartButton.addEventListener('click', function() {
                    // Tắt hover effect ngay lập tức
                    disableHover(cartContainer);
                });
            }
        }
    }
    
    // Khởi tạo
    handlePageLoad();
    setupClickHandlers();
});
