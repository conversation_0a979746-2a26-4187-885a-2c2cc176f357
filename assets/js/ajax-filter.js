/**
 * AJAX Filter Module cho trang products.php
 * Xử lý việc filter sản phẩm không cần reload trang
 */

class AjaxFilter {
    constructor() {
        console.log('🚀 AJAX Filter: Constructor called');
        this.apiUrl = 'api/filter-products.php';
        this.isLoading = false;
        this.currentPage = 1;
        this.keywordRemoved = false; // Track if keyword was manually removed

        // Flags để phân biệt loại request cho việc áp dụng delay 300ms
        this.isPaginationRequest = false; // Flag để phân biệt pagination request
        this.isSearchRequest = false; // Flag để phân biệt search request
        this.isFilterRequest = false; // Flag để phân biệt filter request
        this.clickedPageNumber = null; // Lưu số trang được click
        this.clickedPageLink = null; // Lưu link được click

        console.log('✅ AJAX Filter: Properties initialized');
        this.init();
    }

    init() {
        console.log('AJAX Filter: Init called');

        // Khởi tạo keywordRemoved flag dựa trên URL hiện tại
        const urlParams = new URLSearchParams(window.location.search);
        const urlKeyword = urlParams.get('keyword');
        if (urlKeyword && urlKeyword.trim()) {
            this.keywordRemoved = false;
            console.log('🔄 Initialize keywordRemoved = false (URL has keyword)');
        } else {
            this.keywordRemoved = false; // Default false
            console.log('🔄 Initialize keywordRemoved = false (no URL keyword)');
        }

        this.bindEvents();
        this.setupHistoryHandling();
        console.log('AJAX Filter: Init completed');
    }

    bindEvents() {
        console.log('AJAX Filter: bindEvents called');

        // Đánh dấu rằng AJAX filter đã được khởi tạo
        window.ajaxFilterActive = true;
        console.log('AJAX Filter: ajaxFilterActive flag set');

        // Skip window.location override - not needed and causes errors

        // Override tất cả event handlers cho nút apply filters
        this.overrideApplyFiltersButton();

        // Bind main search form events
        this.bindMainSearchEvents();

        // Bind sort and items per page events
        this.bindSortAndPaginationEvents();

        console.log('AJAX Filter: bindEvents completed');

        // Bind event cho pagination với priority cao để override ajax-pagination.js
        document.addEventListener('click', (e) => {
            // Tìm pagination link (có thể click vào icon hoặc text bên trong)
            const paginationLink = e.target.closest('.ajax-pagination-link');
            if (paginationLink) {
                console.log('🔥 AJAX Filter: Pagination link clicked - taking control!', paginationLink);
                e.preventDefault();
                e.stopPropagation(); // Ngăn không cho ajax-pagination.js xử lý

                const page = parseInt(paginationLink.dataset.page);
                if (page && !this.isLoading) {
                    console.log('🚀 AJAX Filter: Loading page', page, 'with skeleton loading');

                    // Đánh dấu đây là pagination request
                    this.isPaginationRequest = true;
                    // Lưu thông tin trang đích để cập nhật active state sau
                    this.clickedPageNumber = page;
                    // Không lưu clickedPageLink nữa vì sẽ tìm theo page number
                    this.clickedPageLink = null;
                    // Thêm loading state cho nút được click (Previous/Next hoặc số trang)
                    this.addPaginationLoadingState(paginationLink);
                    this.loadProducts(this.collectFilterData(), page);
                } else if (this.isLoading) {
                    console.log('⏳ AJAX Filter: Already loading, ignoring pagination click');
                }
            }
        }, true); // Use capture phase để chạy trước ajax-pagination.js
    }

    bindMainSearchEvents() {
        console.log('🔍 AJAX Filter: bindMainSearchEvents called');

        // Tìm form tìm kiếm chính
        const mainSearchForm = document.querySelector('form.search-container-solution1');
        const mainSearchInput = document.getElementById('main-search-input');
        const searchSubmitBtn = document.getElementById('search-submit-btn');

        console.log('🔍 AJAX Filter: Elements found:', {
            mainSearchForm: !!mainSearchForm,
            mainSearchInput: !!mainSearchInput,
            searchSubmitBtn: !!searchSubmitBtn
        });

        if (!mainSearchForm) {
            console.warn('❌ AJAX Filter: Main search form not found');
            return;
        }

        console.log('✅ AJAX Filter: Found main search form, binding events');

        const self = this;

        // Override form submission
        mainSearchForm.addEventListener('submit', function(e) {
            console.log('AJAX Filter: Main search form submitted');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            if (!self.isLoading) {
                self.handleMainSearchSubmit();
            }
            return false;
        }, true);

        // Override search button click
        if (searchSubmitBtn) {
            searchSubmitBtn.addEventListener('click', function(e) {
                console.log('AJAX Filter: Search submit button clicked');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                if (!self.isLoading) {
                    self.handleMainSearchSubmit();
                }
                return false;
            }, true);
        }

        // Handle Enter key in search input
        if (mainSearchInput) {
            mainSearchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    console.log('AJAX Filter: Enter key pressed in search input');
                    e.preventDefault();
                    e.stopPropagation();

                    if (!self.isLoading) {
                        self.handleMainSearchSubmit();
                    }
                    return false;
                }
            });
        }

        console.log('AJAX Filter: Main search events bound successfully');
    }

    overrideApplyFiltersButton() {
        const applyFiltersBtn = document.getElementById('applyFilters');
        if (!applyFiltersBtn) {
            console.warn('AJAX Filter: Apply filters button not found');
            return;
        }

        console.log('AJAX Filter: Overriding apply filters button');

        // Thêm attribute để đánh dấu
        applyFiltersBtn.setAttribute('data-ajax-filter', 'true');

        // Override onclick property để chặn tất cả handlers khác
        const self = this;
        applyFiltersBtn.onclick = function(e) {
            console.log('AJAX Filter: onclick handler called');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            self.handleFilterSubmit();
            return false;
        };

        // Thêm event listener với capture phase
        applyFiltersBtn.addEventListener('click', function(e) {
            console.log('AJAX Filter: addEventListener handler called');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            self.handleFilterSubmit();
            return false;
        }, true);

        // Override form submission nếu button nằm trong form
        const form = applyFiltersBtn.closest('form');
        if (form) {
            console.log('AJAX Filter: Overriding form submission');
            form.addEventListener('submit', (e) => {
                console.log('AJAX Filter: Form submit prevented');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }, true);
        }
    }



    setupHistoryHandling() {
        // Xử lý back/forward button của browser
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.isAjaxFilter) {
                this.loadProducts(e.state.filterData, e.state.page, false);
            }
        });
    }

    collectFilterData(ignoreUrlKeyword = false) {
        const data = {};

        // Keyword - ưu tiên main search input, sau đó sidebar search, cuối cùng là URL
        const mainSearchInput = document.getElementById('main-search-input');
        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
        const urlParams = new URLSearchParams(window.location.search);
        const urlKeyword = urlParams.get('keyword');

        console.log('🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword:', ignoreUrlKeyword, 'keywordRemoved:', this.keywordRemoved);

        let keyword = '';

        // Chỉ lấy keyword nếu chưa bị remove manually
        if (!this.keywordRemoved) {
            // Ưu tiên 1: Main search input
            if (mainSearchInput && mainSearchInput.value.trim()) {
                keyword = mainSearchInput.value.trim();
                console.log('✅ Using keyword from main search input:', keyword);
            }
            // Ưu tiên 2: Sidebar search input (nếu khác với main search)
            else if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput && sidebarSearchInput.value.trim()) {
                keyword = sidebarSearchInput.value.trim();
                console.log('✅ Using keyword from sidebar search input:', keyword);
            }
            // Ưu tiên 3: URL keyword (chỉ khi không bị ignore)
            else if (!ignoreUrlKeyword && urlKeyword && urlKeyword.trim()) {
                keyword = urlKeyword.trim();
                console.log('✅ Using keyword from URL:', keyword);
            }
        } else {
            console.log('🚫 Keyword was manually removed, ignoring search inputs');
        }

        if (keyword) {
            data.keyword = keyword;
        }

        console.log('🔍 AJAX Filter: Final keyword in data:', data.keyword || 'None');

        // Categories
        const selectedCategories = Array.from(document.querySelectorAll('input[name="category[]"]:checked'))
            .map(input => parseInt(input.value))
            .filter(id => id > 0);
        if (selectedCategories.length > 0) {
            data.categories = selectedCategories;
        }

        // Price range
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');
        
        if (priceMinInput && priceMinInput.value.trim()) {
            const minPrice = this.getPriceNumericValue(priceMinInput);
            if (minPrice > 0) {
                data.price_min = minPrice;
            }
        }
        
        if (priceMaxInput && priceMaxInput.value.trim()) {
            const maxPrice = this.getPriceNumericValue(priceMaxInput);
            if (maxPrice > 0) {
                data.price_max = maxPrice;
            }
        }

        // Promotions
        const selectedPromotions = Array.from(document.querySelectorAll('input[name="promotion[]"]:checked'))
            .map(input => input.value);
        if (selectedPromotions.length > 0) {
            data.promotions = selectedPromotions;
        }

        // Sort
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect && sortSelect.value) {
            data.sort = sortSelect.value;
        } else {
            // Default sort
            data.sort = 'newest';
        }

        // Items per page
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect && itemsPerPageSelect.value) {
            data.items_per_page = parseInt(itemsPerPageSelect.value);
        } else {
            // Default items per page
            data.items_per_page = 12;
        }

        return data;
    }

    getPriceNumericValue(input) {
        if (!input || !input.value) return 0;
        
        // Loại bỏ tất cả ký tự không phải số
        const numericValue = input.value.replace(/[^\d]/g, '');
        return parseInt(numericValue) || 0;
    }

    hasActiveFilters(data) {
        const result = !!(data.keyword ||
                 (data.categories && data.categories.length > 0) ||
                 data.price_min ||
                 data.price_max ||
                 (data.promotions && data.promotions.length > 0));

        console.log('🔍 hasActiveFilters result:', result, 'for data:', data.keyword ? `keyword:"${data.keyword}"` : 'no keyword');

        return result;
    }

    hasActiveFiltersExceptKeyword(data) {
        return !!((data.categories && data.categories.length > 0) ||
                 data.price_min ||
                 data.price_max ||
                 (data.promotions && data.promotions.length > 0));
    }

    handleMainSearchSubmit() {
        console.log('🔍 AJAX Filter: handleMainSearchSubmit called');

        // Ngăn chặn multiple clicks khi đang loading
        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, ignoring search submit');
            return;
        }

        // Đánh dấu đây là search request để áp dụng delay 300ms
        this.isSearchRequest = true;

        // Ẩn search suggestions nếu có
        this.hideSuggestions();

        // Lấy từ khóa tìm kiếm từ input
        const mainSearchInput = document.getElementById('main-search-input');
        if (!mainSearchInput) {
            console.warn('AJAX Filter: Main search input not found');
            return;
        }

        const keyword = mainSearchInput.value.trim();
        console.log('AJAX Filter: Search keyword:', keyword);

        // Nếu không có từ khóa, hiển thị thông báo
        if (!keyword) {
            this.showNotification('Vui lòng nhập từ khóa tìm kiếm!', 'warning');
            mainSearchInput.focus();
            return;
        }

        // Reset keyword removed flag khi user thực sự submit search mới
        this.keywordRemoved = false;
        console.log('🔄 Reset keywordRemoved flag on new search submit');

        // Hiển thị loading state cho nút tìm kiếm
        this.showSearchButtonLoadingState();

        // Tạo filter data với keyword mới và preserve các filter hiện tại (additive filtering)
        // Đây là cách tiếp cận đúng theo UX chuẩn e-commerce: search trong context hiện tại
        const currentFilterData = this.collectFilterData(true); // ignore URL keyword
        const searchData = {
            ...currentFilterData, // Giữ nguyên tất cả filters hiện tại (categories, price, promotions)
            keyword: keyword // Chỉ cập nhật keyword mới
        };

        console.log('AJAX Filter: Loading products with search data (preserving current filters):', searchData);
        this.loadProducts(searchData, 1);

        // KHÔNG clear sidebar filters - giữ nguyên để maintain context

        // Đồng bộ keyword với sidebar search input nếu có
        this.syncSearchInputs(keyword);
    }

    handleFilterSubmit() {
        console.log('AJAX Filter: handleFilterSubmit called');

        // Ngăn chặn multiple clicks khi đang loading
        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, ignoring click');
            return;
        }

        // Đánh dấu đây là filter request để áp dụng delay 300ms
        this.isFilterRequest = true;

        const filterData = this.collectFilterData();
        console.log('AJAX Filter: collected data', filterData);

        // Kiểm tra xem có filter nào được chọn không
        if (!this.hasActiveFilters(filterData)) {
            console.log('AJAX Filter: No active filters');
            this.showNotification('Vui lòng chọn ít nhất một bộ lọc trước khi áp dụng!', 'warning');
            return;
        }

        console.log('AJAX Filter: Loading products...');
        this.loadProducts(filterData, 1);

        // Đồng bộ search inputs để đảm bảo consistency
        this.syncSearchInputs(filterData.keyword || '');
    }

    handleFilterSubmitWithoutKeyword() {
        console.log('AJAX Filter: handleFilterSubmitWithoutKeyword called');

        const filterData = this.collectFilterData();
        console.log('AJAX Filter: collected data without keyword', filterData);

        // Xóa keyword khỏi filterData
        delete filterData.keyword;

        // Kiểm tra xem có filter nào khác được chọn không
        if (!this.hasActiveFilters(filterData)) {
            console.log('AJAX Filter: No active filters after removing keyword, loading all products with AJAX');
            // Thay vì redirect, sử dụng AJAX để load tất cả sản phẩm
            this.loadAllProducts();
            return;
        }

        console.log('AJAX Filter: Loading products without keyword...');
        this.loadProducts(filterData, 1);
    }

    handleRemoveFilter() {
        console.log('🗑️ AJAX Filter: handleRemoveFilter called');

        // Đánh dấu đây là filter request để áp dụng delay 300ms
        this.isFilterRequest = true;

        // Đợi một chút để UI được cập nhật trước khi collect data
        setTimeout(() => {
            // Ignore URL keyword khi remove filter để tránh lấy keyword từ URL cũ
            const filterData = this.collectFilterData(true);
            console.log('🔍 AJAX Filter: collected data after filter removal:', filterData);

            // Kiểm tra xem có filter nào được chọn không
            const hasActiveFilters = this.hasActiveFilters(filterData);

            if (!hasActiveFilters) {
                console.log('📭 AJAX Filter: No active filters after removal, loading all products');
                // Thay vì redirect, sử dụng AJAX để load tất cả sản phẩm
                this.loadAllProducts();
                return;
            }

            console.log('🔄 AJAX Filter: Loading products after filter removal...');
            this.loadProducts(filterData, 1);

            // Đồng bộ search inputs để đảm bảo consistency
            this.syncSearchInputs(filterData.keyword || '');
        }, 50); // Delay ngắn để đảm bảo UI đã được clear
    }

    loadAllProducts() {
        console.log('📭 AJAX Filter: Loading all products');

        // Đánh dấu đây là filter request để áp dụng delay 300ms
        this.isFilterRequest = true;

        // Reset keyword removed flag khi load all products
        this.keywordRemoved = false;
        console.log('🔄 Reset keywordRemoved flag when loading all products');

        // Lấy items_per_page hiện tại từ select
        const itemsPerPageSelect = document.getElementById('items-per-page');
        const currentItemsPerPage = itemsPerPageSelect ? parseInt(itemsPerPageSelect.value) : 12;

        // Tạo empty filter data nhưng giữ nguyên items_per_page
        const emptyFilterData = {
            categories: [],
            promotions: [],
            sort: 'newest',
            items_per_page: currentItemsPerPage
        };

        console.log('📭 AJAX Filter: Loading all products with empty filters');

        // Load products với empty filters
        this.loadProducts(emptyFilterData, 1);

        // Cập nhật URL về trang products.php sạch
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.pushState({}, '', cleanUrl);

        console.log('📭 AJAX Filter: URL updated to clean products page');
    }

    handleResetFilters() {
        console.log('AJAX Filter: Resetting all filters');

        // Clear tất cả UI elements
        this.clearAllUIElements();

        // Load tất cả sản phẩm với AJAX - KHÔNG có delay cho Reset Badge
        this.loadAllProductsWithoutDelay();

        // Cập nhật mobile filter button ngay sau khi reset
        if (typeof MobileFilterModal !== 'undefined' && MobileFilterModal.updateMobileFilterButton) {
            setTimeout(() => {
                MobileFilterModal.updateMobileFilterButton();
            }, 100);
        }

        console.log('AJAX Filter: Reset filters completed');
    }

    loadAllProductsWithoutDelay() {
        console.log('📭 AJAX Filter: Loading all products without delay (Reset Badge)');

        // Reset keyword removed flag khi load all products
        this.keywordRemoved = false;
        console.log('🔄 Reset keywordRemoved flag when loading all products');

        // Lấy items_per_page hiện tại từ select
        const itemsPerPageSelect = document.getElementById('items-per-page');
        const currentItemsPerPage = itemsPerPageSelect ? parseInt(itemsPerPageSelect.value) : 12;

        // Tạo empty filter data nhưng giữ nguyên items_per_page
        const emptyFilterData = {
            categories: [],
            promotions: [],
            sort: 'newest',
            items_per_page: currentItemsPerPage
        };

        console.log('📭 AJAX Filter: Loading all products with empty filters (no delay)');

        // Load products với empty filters - KHÔNG set flag để không có delay
        this.loadProducts(emptyFilterData, 1);

        // Cập nhật URL về trang products.php sạch
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.pushState({}, '', cleanUrl);

        console.log('📭 AJAX Filter: URL updated to clean products page');
    }

    // Show loading state cho nút reset nhỏ
    showResetLoadingState(resetBtn) {
        if (!resetBtn) return;

        console.log('AJAX Filter: Showing reset loading state');

        // Disable nút và thêm class loading
        resetBtn.disabled = true;
        resetBtn.classList.add('loading');

        // Lấy các elements
        const btnSpinner = resetBtn.querySelector('.btn-spinner');
        const btnResetIcon = resetBtn.querySelector('.btn-reset-icon');
        const btnText = resetBtn.querySelector('.btn-text');
        const btnArrowContainer = resetBtn.querySelector('.btn-arrow-container');

        // Ẩn icon và text cũ
        if (btnResetIcon) {
            btnResetIcon.style.display = 'none';
        }

        if (btnText) {
            btnText.style.display = 'none';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.display = 'none';
        }

        // Hiện spinner ngay lập tức
        if (btnSpinner) {
            btnSpinner.innerHTML = '<i class="fas fa-spinner fa-spin text-orange-500"></i>';
            btnSpinner.style.display = 'inline-flex';
            btnSpinner.style.alignItems = 'center';
            btnSpinner.style.opacity = '1';
            btnSpinner.style.transform = 'scale(1)';
            btnSpinner.style.transition = 'none'; // Loại bỏ transition để hiển thị ngay lập tức
        }
    }

    // Hide loading state cho nút reset
    hideResetLoadingState(resetBtn) {
        if (!resetBtn) return;

        console.log('AJAX Filter: Hiding reset loading state');

        // Enable nút và remove class loading
        resetBtn.disabled = false;
        resetBtn.classList.remove('loading');

        // Lấy các elements
        const btnSpinner = resetBtn.querySelector('.btn-spinner');
        const btnResetIcon = resetBtn.querySelector('.btn-reset-icon');
        const btnText = resetBtn.querySelector('.btn-text');
        const btnArrowContainer = resetBtn.querySelector('.btn-arrow-container');

        // Ẩn spinner
        if (btnSpinner) {
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiện lại icon và text
        if (btnResetIcon) {
            btnResetIcon.style.display = '';
        }

        if (btnText) {
            btnText.style.display = '';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.display = '';
        }

        // Xóa loading text nếu có
        const loadingText = resetBtn.querySelector('.loading-text');
        if (loadingText) {
            loadingText.remove();
        }
    }

    clearSidebarFilters() {
        console.log('AJAX Filter: Clearing sidebar filters only');

        // Clear tất cả checkboxes trong sidebar
        document.querySelectorAll('input[name="category[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('input[name="promotion[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Clear price inputs
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');

        if (priceMinInput) {
            priceMinInput.value = '';
        }
        if (priceMaxInput) {
            priceMaxInput.value = '';
        }

        // Clear active state từ tất cả price preset buttons
        document.querySelectorAll('.price-preset').forEach(btn => {
            btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
        });

        console.log('AJAX Filter: Sidebar filters cleared');
    }

    syncSearchInputs(keyword = '') {
        console.log('AJAX Filter: Syncing search inputs with keyword:', keyword);

        // Reset keyword removed flag nếu có keyword được sync
        if (keyword && keyword.trim()) {
            this.keywordRemoved = false;
            console.log('🔄 Reset keywordRemoved flag when syncing with keyword:', keyword);
        }

        const mainSearchInput = document.getElementById('main-search-input');
        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');

        // Đồng bộ main search input
        if (mainSearchInput && mainSearchInput.value.trim() !== keyword) {
            mainSearchInput.value = keyword;
        }

        // Đồng bộ sidebar search input (nếu khác với main search)
        if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput && sidebarSearchInput.value.trim() !== keyword) {
            sidebarSearchInput.value = keyword;
        }

        console.log('AJAX Filter: Search inputs synced');
    }

    hideSuggestions() {
        console.log('🔍 AJAX Filter: Hiding search suggestions');

        // Tìm và ẩn suggestions container từ enhanced-search.js
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
            console.log('✅ Search suggestions hidden');
        }

        // Tìm và ẩn suggestions container từ search.js (nếu có)
        const searchSuggestions = document.querySelectorAll('.search-suggestions');
        searchSuggestions.forEach(container => {
            container.classList.add('hidden');
        });

        // Reset selected index nếu có
        if (window.selectedIndex !== undefined) {
            window.selectedIndex = -1;
        }
    }

    showSearchButtonLoadingState() {
        console.log('🔍 AJAX Filter: Showing search button loading state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear tất cả animations trước khi bắt đầu
        this.clearSearchButtonAnimations(searchBtn);

        // Disable nút và thêm class loading
        searchBtn.disabled = true;
        searchBtn.classList.add('loading');
        searchBtn.classList.remove('success');

        // Lấy tất cả các elements trong nút
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnSearchIcon = searchBtn.querySelector('.btn-search-icon');
        const btnText = searchBtn.querySelector('.btn-text');

        // Ẩn success icon và search icon
        if (btnSuccess) {
            btnSuccess.style.display = 'none';
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
        }

        if (btnSearchIcon) {
            btnSearchIcon.style.transition = 'all 0.2s ease-out';
            btnSearchIcon.style.opacity = '0';
            btnSearchIcon.style.transform = 'scale(0.8)';

            setTimeout(() => {
                btnSearchIcon.style.display = 'none';
            }, 200);
        }

        // Hiển thị spinner với smooth animation
        if (btnSpinner) {
            btnSpinner.style.display = 'flex';
            btnSpinner.innerHTML = `
                <div style="
                    width: 16px;
                    height: 16px;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-top: 2px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                "></div>
            `;
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0.8)';
            btnSpinner.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

            // Delay để tránh conflict với search icon animation
            setTimeout(() => {
                btnSpinner.style.opacity = '1';
                btnSpinner.style.transform = 'scale(1)';
            }, 150);
        }

        // Thay đổi text với smooth transition
        if (btnText) {
            btnText.style.transition = 'all 0.2s ease-out';
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Đang tìm kiếm...';
                btnText.style.transition = 'all 0.3s ease-in';
                btnText.style.opacity = '1';
            }, 200);
        }
    }

    hideSearchButtonLoadingState() {
        console.log('🔍 AJAX Filter: Hiding search button loading state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear tất cả animations trước khi chuyển state
        this.clearSearchButtonAnimations(searchBtn);

        // Lấy tất cả các elements trong nút
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnText = searchBtn.querySelector('.btn-text');

        // Ẩn spinner ngay lập tức
        if (btnSpinner) {
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiển thị success state
        this.showSearchButtonSuccessState();
    }

    showSearchButtonSuccessState() {
        console.log('🔍 AJAX Filter: Showing search button success state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Thêm success class
        searchBtn.classList.remove('loading');
        searchBtn.classList.add('success');

        // Lấy elements
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnText = searchBtn.querySelector('.btn-text');

        // Hiển thị success icon
        if (btnSuccess) {
            btnSuccess.style.display = 'flex';
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';

            setTimeout(() => {
                btnSuccess.style.transition = 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                btnSuccess.style.opacity = '1';
                btnSuccess.style.transform = 'scale(1)';
            }, 100);
        }

        // Đổi text thành "Tìm kiếm thành công!"
        if (btnText) {
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Tìm kiếm thành công!';
                btnText.style.transition = 'opacity 0.3s ease';
                btnText.style.opacity = '1';
            }, 100);
        }

        // Tự động reset về trạng thái ban đầu sau 2 giây
        setTimeout(() => {
            this.resetSearchButtonToOriginalState();
        }, 2000);
    }

    resetSearchButtonToOriginalState() {
        console.log('🔍 AJAX Filter: Resetting search button to original state');

        const searchBtn = document.getElementById('search-submit-btn');
        if (!searchBtn) return;

        // Clear animations
        this.clearSearchButtonAnimations(searchBtn);

        // Lấy elements
        const btnSpinner = searchBtn.querySelector('.btn-spinner');
        const btnSuccess = searchBtn.querySelector('.btn-success');
        const btnSearchIcon = searchBtn.querySelector('.btn-search-icon');
        const btnText = searchBtn.querySelector('.btn-text');

        // Enable nút và remove classes
        searchBtn.disabled = false;
        searchBtn.classList.remove('loading', 'success');

        // Ẩn success icon và spinner
        if (btnSuccess) {
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
            btnSuccess.style.display = 'none';
        }

        if (btnSpinner) {
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
        }

        // Hiển thị lại search icon
        if (btnSearchIcon) {
            btnSearchIcon.style.display = 'inline-block';
            btnSearchIcon.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                btnSearchIcon.style.opacity = '1';
                btnSearchIcon.style.transform = 'scale(1)';
            }, 100);
        }

        // Reset text
        if (btnText) {
            btnText.style.opacity = '0';

            setTimeout(() => {
                btnText.textContent = 'Tìm kiếm';
                btnText.style.transition = 'opacity 0.3s ease';
                btnText.style.opacity = '1';
            }, 100);
        }
    }

    clearSearchButtonAnimations(searchBtn) {
        if (!searchBtn) return;

        // Clear tất cả inline styles và animations
        const elements = searchBtn.querySelectorAll('*');
        elements.forEach(el => {
            el.style.animation = 'none';
            el.style.transition = 'none';
            // Reset transform và opacity để tránh layout jump
            el.style.transform = '';
            el.style.opacity = '';
        });

        // Clear button's own styles
        searchBtn.style.animation = 'none';
        searchBtn.style.transition = 'none';

        // Force reflow để đảm bảo changes được apply
        searchBtn.offsetHeight;

        // Re-enable transitions sau khi clear
        setTimeout(() => {
            elements.forEach(el => {
                el.style.transition = '';
            });
            searchBtn.style.transition = '';
        }, 10);
    }

    clearAllUIElements() {
        console.log('AJAX Filter: Clearing all UI elements');

        // Reset keyword removed flag khi clear all
        this.keywordRemoved = false;
        console.log('🔄 Reset keywordRemoved flag when clearing all UI elements');

        // Clear sidebar filters
        this.clearSidebarFilters();

        // Clear keyword input (cả main search và sidebar search nếu có)
        const mainSearchInput = document.getElementById('main-search-input');
        if (mainSearchInput) {
            mainSearchInput.value = '';
        }

        const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
        if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
            sidebarSearchInput.value = '';
        }

        // Reset sort về mặc định nếu có
        const sortSelect = document.querySelector('select[name="sort"]');
        if (sortSelect) {
            sortSelect.value = 'newest';
        }

        console.log('AJAX Filter: All UI elements cleared');
    }

    async loadProducts(filterData, page = 1, updateHistory = true, fastMode = false) {
        console.log('AJAX Filter: loadProducts called', { filterData, page, updateHistory, fastMode });

        if (this.isLoading) {
            console.log('AJAX Filter: Already loading, skipping');
            return;
        }

        this.isLoading = true;
        this.currentPage = page;

        // Ghi nhận thời gian bắt đầu để đảm bảo loading tối thiểu
        const startTime = Date.now();
        // Thời gian loading để người dùng thấy rõ feedback
        const minLoadingTime = fastMode ? 600 : 1000;

        try {
            // HIỂN thị loading skeleton NGAY LẬP TỨC
            this.showImmediateLoadingSkeleton();

            // Hiển thị loading state cho buttons để người dùng thấy rõ
            this.showLoadingState();

            // Chuẩn bị data để gửi
            const requestData = {
                ...filterData,
                page: page
            };

            console.log('AJAX Filter: Sending request to', this.apiUrl, 'with data', requestData);

            // Gửi AJAX request
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            console.log('AJAX Filter: Response received', response.status, response.ok);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AJAX Filter: HTTP error', response.status, errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('AJAX Filter: API response', result);

            if (result.success) {
                // Tính toán thời gian đã trôi qua
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

                console.log(`AJAX Filter: Processing completed in ${elapsedTime}ms, waiting additional ${remainingTime}ms`);

                // Luôn đợi thời gian loading tối thiểu để người dùng thấy rõ hiệu ứng
                console.log('⏳ AJAX Filter: Ensuring minimum loading time for better UX');
                await new Promise(resolve => setTimeout(resolve, remainingTime));

                // Reset keyword removed flag nếu load thành công với keyword
                if (result.data.filters && result.data.filters.keyword) {
                    this.keywordRemoved = false;
                    console.log('🔄 Reset keywordRemoved flag after successful load with keyword:', result.data.filters.keyword);
                }

                // Áp dụng cùng UX mượt mà cho cả Filter và Pagination
                // Scroll trước, sau đó mới hiển thị content để tạo trải nghiệm nhất quán
                this.handleSmoothUX(result.data, filterData, page, updateHistory);

                // Không hiển thị thông báo - đã xóa theo yêu cầu
                // this.showSuccessNotification(result.data);

            } else {
                throw new Error(result.error || 'Có lỗi xảy ra khi lọc sản phẩm');
            }

        } catch (error) {
            console.error('AJAX Filter Error:', error);

            // Đảm bảo thời gian loading tối thiểu ngay cả khi có lỗi
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            if (remainingTime > 0) {
                await new Promise(resolve => setTimeout(resolve, remainingTime));
            }

            this.showNotification('Có lỗi xảy ra. Vui lòng thử lại!', 'error');
        } finally {
            this.isLoading = false;

            // LUÔN ẩn loading state sau khi hoàn thành (bất kể fast mode hay không)
            this.hideLoadingState();

            // Ẩn loading state của search button nếu có
            const searchBtn = document.getElementById('search-submit-btn');
            if (searchBtn && searchBtn.classList.contains('loading')) {
                this.hideSearchButtonLoadingState();
            }

            // Ẩn loading state của pagination links
            this.removePaginationLoadingState();

            // Cập nhật mobile filter button sau khi load xong
            if (typeof MobileFilterModal !== 'undefined' && MobileFilterModal.updateMobileFilterButton) {
                MobileFilterModal.updateMobileFilterButton();
            }

            // Reset tất cả request flags
            this.isPaginationRequest = false;
            this.isSearchRequest = false;
            this.isFilterRequest = false;
        }
    }

    // Helper function để clear tất cả animation states ngay lập tức
    clearAllButtonAnimations(applyBtn) {
        const elements = applyBtn.querySelectorAll('.btn-spinner, .btn-success, .btn-filter-icon, .btn-text, .ml-2');
        elements.forEach(el => {
            if (el) {
                el.style.transition = 'none';
                el.style.animation = 'none';
                // Force reflow để đảm bảo styles được apply ngay lập tức
                el.offsetHeight;
            }
        });
    }

    showLoadingState() {
        console.log('AJAX Filter: Showing sophisticated loading state');

        const applyBtn = document.getElementById('applyFilters');
        if (!applyBtn) return;

        // Clear tất cả animations trước khi bắt đầu
        this.clearAllButtonAnimations(applyBtn);

        // Disable nút và thêm class loading
        applyBtn.disabled = true;
        applyBtn.classList.add('loading');
        // Đảm bảo remove success class nếu có
        applyBtn.classList.remove('success');

        // Lấy tất cả các elements trong nút
        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnFilterIcon = applyBtn.querySelector('.btn-filter-icon');
        const btnText = applyBtn.querySelector('.btn-text');
        const btnArrowContainer = applyBtn.querySelector('.ml-2');

        // Bước 1: ẨN NGAY LẬP TỨC tất cả elements cũ - KHÔNG có animation
        if (btnSuccess) {
            btnSuccess.style.transition = 'none';
            btnSuccess.style.animation = 'none';
            btnSuccess.style.display = 'none';
        }

        if (btnArrowContainer) {
            btnArrowContainer.style.transition = 'none';
            btnArrowContainer.style.display = 'none';
        }

        if (btnFilterIcon) {
            btnFilterIcon.style.transition = 'none';
            btnFilterIcon.style.display = 'none';
        }

        // Bước 2: Hiện spinner NGAY LẬP TỨC
        if (btnSpinner) {
            // Reset mọi style trước đó
            btnSpinner.style.transition = 'none';
            btnSpinner.style.animation = 'none';
            btnSpinner.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>';
            btnSpinner.style.display = 'inline-flex';
            btnSpinner.style.alignItems = 'center';
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0.8)';

            // Force reflow
            btnSpinner.offsetHeight;

            // Bật lại transition cho smooth animation
            btnSpinner.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            // Fade in spinner
            setTimeout(() => {
                btnSpinner.style.opacity = '1';
                btnSpinner.style.transform = 'scale(1)';
            }, 50);
        }

        // Đổi text NGAY LẬP TỨC
        if (btnText) {
            // Ẩn ngay text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và hiện ngay
            btnText.textContent = 'Đang lọc sản phẩm...';
            btnText.style.transition = 'opacity 0.3s ease';

            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Products grid overlay
        this.showProductsGridOverlay();
    }

    showProductsGridOverlay() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        let overlay = productsGrid.querySelector('.ajax-loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'ajax-loading-overlay';
            overlay.innerHTML = `
                <div style="text-align: center; padding: 2rem;">
                    <div style="margin-bottom: 1rem;">
                        <i class="fas fa-search text-2xl text-orange-500 mb-2" style="animation: pulse 1.5s ease-in-out infinite;"></i>
                    </div>
                    <div class="text-gray-700 font-medium">Đang tìm kiếm sản phẩm...</div>
                    <div class="text-gray-500 text-sm mt-1">Vui lòng chờ trong giây lát</div>
                </div>
            `;
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(1px);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            if (getComputedStyle(productsGrid).position === 'static') {
                productsGrid.style.position = 'relative';
            }

            productsGrid.appendChild(overlay);

            // Fade in overlay
            setTimeout(() => {
                overlay.style.opacity = '1';
            }, 50);
        }
    }

    hideLoadingState() {
        console.log('🎯 AJAX Filter: Hiding sophisticated loading state for Apply Filters button');

        const applyBtn = document.getElementById('applyFilters');
        if (!applyBtn) {
            console.warn('⚠️ AJAX Filter: Apply Filters button not found, cannot hide loading state');
            return;
        }

        console.log('✅ AJAX Filter: Apply Filters button found, proceeding to hide loading state');

        // Clear tất cả animations trước khi chuyển state
        this.clearAllButtonAnimations(applyBtn);

        // Lấy tất cả các elements trong nút
        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnText = applyBtn.querySelector('.btn-text');

        // Bước 1: ẨN NGAY LẬP TỨC spinner và loading state - KHÔNG có animation fade out
        if (btnSpinner) {
            // Ẩn ngay lập tức và clear nội dung
            btnSpinner.style.opacity = '0';
            btnSpinner.style.transform = 'scale(0)';
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Thêm success class cho nút để trigger CSS animations
        applyBtn.classList.add('success');

        // Hiện success icon với animation NGAY LẬP TỨC - không dùng scale để tránh jump
        if (btnSuccess) {
            // Reset mọi style trước đó
            btnSuccess.style.transition = 'none';
            btnSuccess.style.animation = 'none';
            btnSuccess.style.display = 'inline-flex';
            btnSuccess.style.alignItems = 'center';
            btnSuccess.style.marginRight = '8px';
            btnSuccess.style.opacity = '0';
            // Không dùng scale transform để tránh jump
            btnSuccess.style.transform = '';

            // Force reflow để đảm bảo styles được apply
            btnSuccess.offsetHeight;

            // Bật lại transition cho success state - chỉ opacity
            btnSuccess.style.transition = 'opacity 0.3s ease';
            btnSuccess.style.animation = 'successCheckmark 0.5s cubic-bezier(0.4, 0, 0.2, 1)'; // Easing mượt hơn

            // Hiện success icon ngay lập tức
            setTimeout(() => {
                btnSuccess.style.opacity = '1';
            }, 50); // Delay rất ngắn để animation mượt
        }

        // Success text với animation - ẨN NGAY text cũ, hiện text mới
        if (btnText) {
            // Ẩn ngay lập tức text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và hiện ngay
            btnText.textContent = 'Lọc thành công!';
            btnText.style.transition = 'opacity 0.3s ease';
            btnText.style.animation = 'successTextSlide 0.4s ease-out';
            btnText.style.fontWeight = '600';
            btnText.style.color = 'white';

            // Hiện text mới
            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Bước 2: Sau 1.2s, reset về trạng thái ban đầu
        setTimeout(() => {
            this.resetButtonToOriginalState(applyBtn);
        }, 1200);

        // Loại bỏ products grid overlay
        this.hideProductsGridOverlay();
    }

    resetButtonToOriginalState(applyBtn) {
        // Clear tất cả animations trước khi reset
        this.clearAllButtonAnimations(applyBtn);

        const btnSpinner = applyBtn.querySelector('.btn-spinner');
        const btnSuccess = applyBtn.querySelector('.btn-success');
        const btnFilterIcon = applyBtn.querySelector('.btn-filter-icon');
        const btnText = applyBtn.querySelector('.btn-text');
        const btnArrowContainer = applyBtn.querySelector('.ml-2');

        // Enable nút và remove loading + success classes
        applyBtn.disabled = false;
        applyBtn.classList.remove('loading', 'success');

        // QUAN TRỌNG: Đảm bảo không có transform scale để tránh jump
        applyBtn.style.transform = '';

        // ẨN NGAY LẬP TỨC success icon - KHÔNG có animation fade out
        if (btnSuccess) {
            btnSuccess.style.opacity = '0';
            btnSuccess.style.transform = 'scale(0)';
            btnSuccess.style.display = 'none';
        }

        // Đảm bảo spinner ẩn hoàn toàn và clean
        if (btnSpinner) {
            btnSpinner.style.display = 'none';
            btnSpinner.innerHTML = '';
        }

        // Hiện lại filter icon NGAY LẬP TỨC - không dùng scale để tránh jump
        if (btnFilterIcon) {
            // Reset mọi style
            btnFilterIcon.style.transition = 'none';
            btnFilterIcon.style.animation = 'none';
            btnFilterIcon.style.display = 'inline-block';
            btnFilterIcon.style.opacity = '0';
            // Không dùng scale transform để tránh jump
            btnFilterIcon.style.transform = '';

            // Force reflow
            btnFilterIcon.offsetHeight;

            // Bật lại transition cho smooth animation - chỉ opacity
            btnFilterIcon.style.transition = 'opacity 0.3s ease';

            // Hiện icon ngay
            setTimeout(() => {
                btnFilterIcon.style.opacity = '1';
            }, 50);
        }

        // Reset text NGAY LẬP TỨC
        if (btnText) {
            // Ẩn ngay text cũ
            btnText.style.transition = 'none';
            btnText.style.animation = 'none';
            btnText.style.opacity = '0';

            // Force reflow
            btnText.offsetHeight;

            // Đổi text và style ngay
            btnText.textContent = 'Áp dụng bộ lọc';
            btnText.style.fontWeight = '500'; // Reset font weight
            btnText.style.color = ''; // Reset color
            btnText.style.transition = 'opacity 0.3s ease';

            // Hiện text mới
            setTimeout(() => {
                btnText.style.opacity = '1';
            }, 50);
        }

        // Hiện lại arrow container NGAY LẬP TỨC - không dùng transform
        if (btnArrowContainer) {
            btnArrowContainer.style.transition = 'none';
            btnArrowContainer.style.animation = 'none';
            btnArrowContainer.style.display = 'block';
            btnArrowContainer.style.opacity = '0'; // Ẩn, chỉ hiện khi hover
            // Không dùng transform để tránh jump
            btnArrowContainer.style.transform = '';

            // Force reflow
            btnArrowContainer.offsetHeight;

            // Bật lại transition - chỉ opacity
            btnArrowContainer.style.transition = 'opacity 0.3s ease';
        }
    }

    hideProductsGridOverlay() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        const overlay = productsGrid.querySelector('.ajax-loading-overlay');
        if (overlay) {
            overlay.style.transition = 'opacity 0.3s ease';
            overlay.style.opacity = '0';

            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }

    showNotification(message, type = 'info') {
        // Sử dụng hàm showFilterNotification có sẵn nếu có
        if (typeof showFilterNotification === 'function') {
            showFilterNotification(message, type, false);
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }



    scrollToProductsGrid(callback = null) {
        console.log('AJAX Filter: Auto-scrolling to products section');



        // Scroll ngay lập tức để tránh hiện tượng cuộn lên cuộn xuống
        console.log('AJAX Filter: Scrolling immediately for smooth UX');

        // Tìm products-section thay vì productsGrid để scroll chính xác hơn
        const productsSection = document.getElementById('products-section');
        if (!productsSection) {
            console.warn('AJAX Filter: Products section not found for scrolling');
            if (callback) callback();
            return;
        }

        // Đợi 2 frames để đảm bảo layout hoàn toàn ổn định
        requestAnimationFrame(() => {
            requestAnimationFrame(() => {


        // Tính toán chính xác vị trí scroll (sử dụng logic giống products.php)
        let finalHeaderHeight = 0;

        // Kiểm tra mobile/desktop
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // Mobile: scroll thêm 60px để đến đúng vị trí danh sách sản phẩm
            const mobileHeader = document.querySelector('.mobile-header');
            if (mobileHeader && window.getComputedStyle(mobileHeader).display !== 'none') {
                finalHeaderHeight = -60; // Giá trị âm để cuộn thêm 60px
                console.log('AJAX Filter: Mobile header calculation:', {
                    mobileHeaderHeight: mobileHeader.offsetHeight,
                    finalHeaderHeight: finalHeaderHeight,
                    screenWidth: window.innerWidth,
                    note: 'Scroll additional 60px for mobile'
                });
            } else {
                finalHeaderHeight = -60; // Fallback cho mobile
            }
        } else {
            // Desktop: tính header height sau khi top bar ẩn
            const premiumHeader = document.querySelector('.premium-header');
            if (premiumHeader && window.getComputedStyle(premiumHeader).display !== 'none') {
                const topBar = premiumHeader.querySelector('.top-bar');

                if (topBar) {
                    // Header height = total height - top bar height (vì top bar sẽ ẩn khi scroll)
                    const totalHeight = premiumHeader.offsetHeight;
                    const topBarHeight = topBar.offsetHeight;
                    finalHeaderHeight = totalHeight - topBarHeight;

                    console.log('AJAX Filter: Desktop header calculation:', {
                        totalHeight,
                        topBarHeight,
                        finalHeaderHeight,
                        calculation: `${totalHeight} - ${topBarHeight} = ${finalHeaderHeight}`
                    });
                } else {
                    // Fallback nếu không tìm thấy top bar
                    finalHeaderHeight = premiumHeader.offsetHeight;
                }
            } else {
                // Fallback cho desktop
                const mainHeader = document.querySelector('.main-header, header');
                if (mainHeader) {
                    finalHeaderHeight = mainHeader.offsetHeight;
                }
            }
        }

        // Tính toán vị trí target
        const productsOffset = productsSection.offsetTop;
        const targetPosition = productsOffset - finalHeaderHeight;

        console.log('AJAX Filter: Final scroll calculation:', {
            isMobile,
            productsOffset,
            finalHeaderHeight,
            targetPosition,
            calculation: `${productsOffset} - ${finalHeaderHeight} = ${targetPosition}`
        });



                // Scroll ngay lập tức với animation mượt mà
                this.smoothScrollTo(targetPosition, 400, callback); // Tăng duration lên 400ms để mượt hơn
            });
        });
    }

    smoothScrollTo(targetPosition, duration = 400, callback = null) {
        const startPosition = window.pageYOffset || document.documentElement.scrollTop;
        const distance = targetPosition - startPosition;

        // Nếu khoảng cách quá nhỏ, không cần scroll
        if (Math.abs(distance) < 10) {
            if (callback) callback();
            return;
        }

        console.log('AJAX Filter: Starting smooth scroll', {
            from: startPosition,
            to: targetPosition,
            distance: distance,
            duration: duration
        });

        // Dừng bất kỳ scroll animation nào đang chạy
        if (this.scrollAnimation) {
            cancelAnimationFrame(this.scrollAnimation);
        }

        let startTime = null;
        const self = this;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);

            // Easing function mượt mà hơn (ease-out-cubic với điều chỉnh)
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);

            const currentPos = startPosition + distance * easeOutCubic;
            window.scrollTo(0, currentPos);

            if (timeElapsed < duration) {
                self.scrollAnimation = requestAnimationFrame(animation);
            } else {
                // Animation hoàn thành
                self.scrollAnimation = null;
                console.log('AJAX Filter: Scroll animation completed');

                if (callback) {
                    // Đợi một chút để đảm bảo scroll đã hoàn toàn ổn định
                    setTimeout(callback, 50);
                }
            }
        }

        this.scrollAnimation = requestAnimationFrame(animation);
    }

    addPaginationLoadingState(clickedLink) {
        console.log('AJAX Filter: Adding enhanced loading state to pagination link');

        // Lưu nội dung gốc
        if (!clickedLink.dataset.originalContent) {
            clickedLink.dataset.originalContent = clickedLink.innerHTML;
        }

        // Thêm loading state với animation mượt mà
        clickedLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        clickedLink.style.transform = 'scale(0.95)';
        clickedLink.style.pointerEvents = 'none';
        clickedLink.classList.add('loading');

        // Tạo loading content với animation đẹp hơn
        const originalContent = clickedLink.dataset.originalContent;
        let loadingHTML = '';

        if (originalContent.includes('page-text')) {
            // Nút Previous/Next có text
            if (originalContent.includes('Trước')) {
                loadingHTML = `
                    <div class="pagination-loading-content">
                        <div class="loading-spinner-container">
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                        <span class="page-text loading-text">Đang tải</span>
                    </div>
                `;
            } else if (originalContent.includes('Sau')) {
                loadingHTML = `
                    <div class="pagination-loading-content">
                        <span class="page-text loading-text">Đang tải</span>
                        <div class="loading-spinner-container">
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                    </div>
                `;
            }
        } else {
            // Nút số trang - loading dots đẹp hơn
            loadingHTML = `
                <div class="pagination-loading-content">
                    <div class="loading-dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                </div>
            `;
        }

        // Fade out content cũ, sau đó fade in content mới
        clickedLink.style.opacity = '0.3';

        setTimeout(() => {
            clickedLink.innerHTML = loadingHTML;
            clickedLink.style.opacity = '0.8';
        }, 150);
    }

    removePaginationLoadingState() {
        console.log('AJAX Filter: Removing enhanced loading state from pagination links');

        // Tìm tất cả pagination links có loading state
        const loadingLinks = document.querySelectorAll('.ajax-pagination-link.loading');
        let processedCount = 0;
        const totalLinks = loadingLinks.length;

        loadingLinks.forEach(link => {
            // Fade out loading content
            link.style.opacity = '0.3';

            setTimeout(() => {
                // Khôi phục nội dung gốc
                if (link.dataset.originalContent) {
                    link.innerHTML = link.dataset.originalContent;
                    delete link.dataset.originalContent;
                }

                // Fade in và restore styles với animation mượt mà
                link.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                link.style.opacity = '1';
                link.style.transform = 'scale(1)';
                link.style.pointerEvents = '';
                link.classList.remove('loading');

                // Reset styles sau khi animation hoàn thành
                setTimeout(() => {
                    link.style.transition = '';
                    link.style.transform = '';

                    // Chỉ đếm processed count, không cập nhật active state ở đây nữa
                    processedCount++;
                    // Active state sẽ được cập nhật sớm hơn trong handleSmoothUX
                }, 300);
            }, 150);
        });
    }

    updatePaginationActiveState() {
        console.log('AJAX Filter: Updating pagination active state immediately');

        // Chỉ cập nhật nếu có thông tin trang đích và là pagination request
        if (!this.clickedPageNumber || !this.isPaginationRequest) {
            console.log('AJAX Filter: No clicked page number or not pagination request, skipping active state update');
            return;
        }

        console.log(`AJAX Filter: Setting page ${this.clickedPageNumber} as active`);

        // Tìm tất cả pagination items
        const allPageItems = document.querySelectorAll('.page-item');

        // Xóa active state từ tất cả các trang (bao gồm cả Previous/Next)
        allPageItems.forEach(item => {
            item.classList.remove('active');
            const link = item.querySelector('.ajax-pagination-link');
            if (link) {
                link.classList.remove('active');
            }
        });

        // Tìm tất cả nút có data-page = clickedPageNumber (có thể có nhiều nút cùng page)
        const targetPageLinks = document.querySelectorAll(`.ajax-pagination-link[data-page="${this.clickedPageNumber}"]`);

        // Tìm nút số trang (không phải Previous/Next) để set active
        let numberPageLink = null;
        targetPageLinks.forEach(link => {
            const linkText = link.textContent.trim();
            // Chỉ chọn nút có text là số (không phải "Trước" hoặc "Sau")
            if (/^\d+$/.test(linkText)) {
                numberPageLink = link;
            }
        });

        if (numberPageLink) {
            const targetPageItem = numberPageLink.closest('.page-item');
            if (targetPageItem) {
                targetPageItem.classList.add('active');
                numberPageLink.classList.add('active');

                console.log(`AJAX Filter: Successfully set page ${this.clickedPageNumber} as active (number button)`);
            }
        } else {
            console.warn(`AJAX Filter: Could not find number button for page ${this.clickedPageNumber}`);
        }

        // Reset thông tin click
        this.clickedPageNumber = null;
        this.clickedPageLink = null;
    }

    handleSmoothUX(data, filterData, page, updateHistory) {
        console.log('AJAX Filter: Handling smooth UX - scroll first, then show content');

        // Bước 1: Cập nhật active state ngay sau loading (chỉ cho pagination)
        if (this.isPaginationRequest) {
            console.log('AJAX Filter: Updating active state immediately after loading');
            this.updatePaginationActiveState();
        }

        // Bước 2: Scroll to products section với delay để người dùng thấy loading
        setTimeout(() => {
            this.scrollToProductsGrid(() => {
                // Bước 3: Đợi một chút sau khi scroll xong để tránh giật
                setTimeout(() => {
                    console.log('AJAX Filter: Scroll completed, updating content');

                    // Cập nhật các thành phần phụ
                    this.updatePagination(data.pagination);
                    this.updateProductsStats(data.pagination);
                    this.updateFilterBadge(data.filters);

                    // Đồng bộ UI sidebar
                    this.syncSidebarUI();

                    // Cập nhật URL và history
                    if (updateHistory) {
                        this.updateUrlAndHistory(filterData, page);

                        // Đồng bộ UI với URL state mới
                        if (typeof window.filterStateManager !== 'undefined') {
                            window.filterStateManager.syncUIWithURL();
                            console.log('AJAX Filter: UI synced with URL state');
                        }
                    }

                    // Bước 4: Cập nhật nội dung
                    this.updateContentAfterSkeleton(data);

                }, 150); // Delay để tránh giật sau scroll
            });
        }, 300); // Delay để người dùng thấy loading state trước khi scroll
    }

    updateContentAfterSkeleton(data) {
        console.log('AJAX Filter: Updating content after skeleton is already shown');

        const productsContent = document.querySelector('.products-content');
        const existingHeader = productsContent ? productsContent.querySelector('.filter-results-header') : null;

        // Bước 1: Cập nhật Filter Results Header với animation
        if (existingHeader) {
            // Fade out header cũ
            existingHeader.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
            existingHeader.style.opacity = '0.3';
            existingHeader.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                // Cập nhật nội dung header
                this.updateFilterResultsHeaderContent(data);

                // Fade in header mới
                setTimeout(() => {
                    const newHeader = productsContent ? productsContent.querySelector('.filter-results-header') : null;
                    if (newHeader) {
                        newHeader.style.opacity = '1';
                        newHeader.style.transform = 'translateY(0)';

                        // Reset transition
                        setTimeout(() => {
                            newHeader.style.transition = '';
                            newHeader.style.transform = '';
                        }, 300);
                    }
                }, 50);
            }, 300);
        } else {
            // Không có header cũ, chỉ cần thêm header mới
            this.updateFilterResultsHeaderContent(data);

            setTimeout(() => {
                const newHeader = productsContent ? productsContent.querySelector('.filter-results-header') : null;
                if (newHeader) {
                    newHeader.style.opacity = '1';
                    newHeader.style.transform = 'translateY(0)';
                }
            }, 50);
        }

        // Bước 2: Thay thế skeleton bằng nội dung thực - để người dùng thấy rõ loading
        setTimeout(() => {
            this.updateProductsGridContent(data);
        }, 400); // Tăng thời gian để người dùng thấy rõ skeleton loading
    }

    updateContentSimultaneously(data) {
        console.log('AJAX Filter: Updating Filter Results Header and Products Grid simultaneously');

        const productsGrid = document.getElementById('productsGrid');
        const productsContent = document.querySelector('.products-content');
        const existingHeader = productsContent ? productsContent.querySelector('.filter-results-header') : null;

        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found for simultaneous update');
            return;
        }

        // Bước 1: Fade out cả hai thành phần cùng lúc
        const elementsToFade = [productsGrid];
        if (existingHeader) {
            elementsToFade.push(existingHeader);
        }

        elementsToFade.forEach(element => {
            element.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
            element.style.opacity = '0.3';
            if (element === existingHeader) {
                element.style.transform = 'translateY(-10px)';
            }
        });

        setTimeout(() => {
            // Bước 2: Hiển thị loading skeleton cho Products Grid
            this.showProductsLoadingSkeleton(productsGrid);

            // Cập nhật Filter Results Header
            this.updateFilterResultsHeaderContent(data);

            // Bước 3: Fade in header và loading skeleton
            setTimeout(() => {
                // Fade in existing header hoặc new header
                const currentHeader = productsContent ? productsContent.querySelector('.filter-results-header') : null;
                if (currentHeader) {
                    currentHeader.style.opacity = '1';
                    currentHeader.style.transform = 'translateY(0)';
                }

                // Fade in products grid với loading skeleton
                productsGrid.style.opacity = '1';
                productsGrid.style.transform = 'translateY(0)';

                // Reset transition cho products grid
                setTimeout(() => {
                    productsGrid.style.transition = '';
                    productsGrid.style.transform = '';
                }, 300);

                // Bước 4: Sau khi hiển thị loading skeleton, cập nhật nội dung thực
                setTimeout(() => {
                    this.updateProductsGridContent(data);
                }, 800); // Hiển thị loading skeleton trong 800ms

            }, 50);
        }, 300);
    }

    showImmediateLoadingSkeleton() {
        console.log('AJAX Filter: Showing immediate loading skeleton');

        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found for immediate skeleton');
            return;
        }

        // LƯU CHIỀU CAO HIỆN TẠI để đảm bảo pagination không bị dịch chuyển
        const currentHeight = productsGrid.offsetHeight;
        console.log('AJAX Filter: Current products grid height:', currentHeight + 'px');

        // Đặt min-height cố định để giữ vị trí pagination
        // Thêm một chút buffer để đảm bảo không bị thiếu chiều cao
        const minHeight = Math.max(currentHeight, 400); // Tối thiểu 400px
        productsGrid.style.minHeight = minHeight + 'px';
        console.log('AJAX Filter: Set min-height to', minHeight + 'px', 'to preserve pagination position');

        // Fade out nội dung hiện tại với transition mượt mà
        productsGrid.style.transition = 'opacity 0.3s ease-out';
        productsGrid.style.opacity = '0.4';

        setTimeout(() => {
            // Hiển thị skeleton với số lượng phù hợp
            this.showProductsLoadingSkeleton(productsGrid);

            // Fade in skeleton mượt mà
            setTimeout(() => {
                productsGrid.style.opacity = '1';

                // Reset transition sau khi hoàn thành
                setTimeout(() => {
                    productsGrid.style.transition = '';
                }, 300);
            }, 100);
        }, 300); // Tăng delay để người dùng thấy rõ quá trình loading
    }

    showProductsLoadingSkeleton(productsGrid) {
        console.log('AJAX Filter: Showing products loading skeleton');

        // Tính số lượng skeleton cards dựa trên số sản phẩm hiện tại
        const currentProducts = productsGrid.querySelectorAll('.product-card:not(.skeleton-card)');
        let skeletonCount = currentProducts.length;

        // Nếu không có sản phẩm hiện tại, sử dụng số mặc định
        if (skeletonCount === 0) {
            // Lấy items per page từ select hoặc URL
            const itemsPerPageSelect = document.getElementById('items-per-page');
            if (itemsPerPageSelect) {
                skeletonCount = parseInt(itemsPerPageSelect.value) || 12;
            } else {
                // Fallback: lấy từ URL parameter
                const urlParams = new URLSearchParams(window.location.search);
                skeletonCount = parseInt(urlParams.get('items_per_page')) || 12;
            }
        } else {
            // Nếu có ít sản phẩm (có thể là trang cuối), đảm bảo có đủ skeleton để giữ chiều cao
            // Lấy items per page để so sánh
            const itemsPerPageSelect = document.getElementById('items-per-page');
            const expectedItemsPerPage = itemsPerPageSelect ? parseInt(itemsPerPageSelect.value) || 12 : 12;

            // Nếu số sản phẩm hiện tại ít hơn expected (trang cuối), sử dụng expected để giữ chiều cao
            if (skeletonCount < expectedItemsPerPage) {
                console.log('AJAX Filter: Detected last page with', skeletonCount, 'products, using', expectedItemsPerPage, 'skeletons to maintain height');
                skeletonCount = expectedItemsPerPage;
            }
        }

        console.log('AJAX Filter: Creating', skeletonCount, 'skeleton cards to match current layout');
        console.log('AJAX Filter: Current products grid min-height:', productsGrid.style.minHeight);

        // Debug: Log chiều cao của từng sản phẩm hiện tại
        const currentProductCards = productsGrid.querySelectorAll('.product-card:not(.skeleton-card)');
        if (currentProductCards.length > 0) {
            console.log('AJAX Filter: Sample product card height:', currentProductCards[0].offsetHeight + 'px');
        }

        let skeletonHTML = '';
        for (let i = 0; i < skeletonCount; i++) {
            skeletonHTML += this.createSkeletonCard();
        }

        productsGrid.innerHTML = skeletonHTML;

        // Thêm class để styling
        productsGrid.classList.add('loading-skeleton');
    }

    createSkeletonCard() {
        return `
            <div class="product-card skeleton-card group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 transition-all duration-500">
                <!-- Skeleton Image - Khối đơn giản -->
                <div class="product-image-wrapper relative aspect-square bg-gray-200 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
                    <!-- Skeleton Sale Badge - Góc phải trên -->
                    <div class="absolute top-3 right-3 w-12 h-6 bg-gray-300 rounded animate-pulse"></div>
                </div>

                <!-- Skeleton Content - Match chính xác với sản phẩm thực -->
                <div class="product-info-wrapper flex flex-col flex-grow" style="padding: 1.25rem 1.25rem 1rem 1.25rem;">
                    <!-- Skeleton Title - Match product-title -->
                    <div class="product-title" style="margin-bottom: 0;">
                        <div class="space-y-2">
                            <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                        </div>
                    </div>

                    <!-- Skeleton Price - Match premium-price-section -->
                    <div class="premium-price-section" style="margin-top: auto; display: flex; align-items: center; justify-content: flex-start; margin-bottom: 0;">
                        <div class="space-y-1">
                            <div class="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                            <div class="h-5 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                    </div>

                    <!-- Skeleton Rating - Match product-rating-sales -->
                    <div class="product-rating-sales" style="margin-top: 0.5rem; display: flex; justify-content: space-between; align-items: center;">
                        <div class="flex items-center space-x-2">
                            <div class="flex space-x-1">
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                            </div>
                            <div class="h-3 bg-gray-200 rounded w-8 animate-pulse"></div>
                        </div>
                        <div class="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                    </div>
                </div>
            </div>
        `;
    }

    updateProductsGridContent(data) {
        console.log('AJAX Filter: Updating products grid content after skeleton');

        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found for content update');
            return;
        }

        // Remove skeleton class
        productsGrid.classList.remove('loading-skeleton');

        // Fade out skeleton mượt mà
        productsGrid.style.transition = 'opacity 0.25s ease-out';
        productsGrid.style.opacity = '0.5';

        setTimeout(() => {
            // Cập nhật nội dung thực
            this.updateProductsGrid(data);

            // Fade in với nội dung mới
            setTimeout(() => {
                productsGrid.style.opacity = '1';

                // Reset min-height sau khi nội dung thực đã được hiển thị
                setTimeout(() => {
                    // Kiểm tra xem nội dung mới có chiều cao hợp lý không trước khi reset
                    const newHeight = productsGrid.offsetHeight;
                    console.log('AJAX Filter: New content height:', newHeight + 'px');

                    // Chỉ reset min-height nếu nội dung mới có chiều cao hợp lý
                    if (newHeight > 100) { // Đảm bảo có nội dung thực sự
                        productsGrid.style.minHeight = '';
                        console.log('AJAX Filter: Reset min-height after content loaded');
                    } else {
                        console.warn('AJAX Filter: New content height too small, keeping min-height');
                    }
                }, 250); // Tăng thời gian chờ để ổn định
            }, 50);

            setTimeout(() => {
                productsGrid.style.opacity = '1';
                productsGrid.style.transform = 'translateY(0)';

                // Reset transition sau khi hoàn thành
                setTimeout(() => {
                    productsGrid.style.transition = '';
                    productsGrid.style.transform = '';
                }, 200); // Giảm từ 400ms xuống 200ms
            }, 30); // Giảm từ 50ms xuống 30ms
        }, 200); // Giảm từ 400ms xuống 200ms
    }

    updateProductsGridWithAnimation(data) {
        console.log('AJAX Filter: Updating products grid with fade-in animation');

        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found for animation');
            return;
        }

        // Fade out hiện tại
        productsGrid.style.transition = 'opacity 0.3s ease-out';
        productsGrid.style.opacity = '0.3';

        setTimeout(() => {
            // Cập nhật nội dung
            this.updateProductsGrid(data);

            // Fade in với nội dung mới
            setTimeout(() => {
                productsGrid.style.opacity = '1';

                // Reset transition sau khi hoàn thành
                setTimeout(() => {
                    productsGrid.style.transition = '';
                }, 300);
            }, 50);
        }, 300);
    }

    updateUrlAndHistory(filterData, page) {
        const url = new URL(window.location);

        // Xóa các tham số cũ
        url.searchParams.delete('keyword');
        url.searchParams.delete('category');
        url.searchParams.delete('category[]');
        url.searchParams.delete('price_min');
        url.searchParams.delete('price_max');
        url.searchParams.delete('promotion');
        url.searchParams.delete('promotion[]');
        url.searchParams.delete('sort');
        url.searchParams.delete('page');
        url.searchParams.delete('items_per_page');

        // Thêm các tham số mới
        if (filterData.keyword && filterData.keyword.trim() !== '') {
            url.searchParams.set('keyword', filterData.keyword.trim());
            // Reset keyword removed flag khi URL có keyword
            this.keywordRemoved = false;
            console.log('🔄 Reset keywordRemoved flag when updating URL with keyword:', filterData.keyword.trim());
        }

        if (filterData.categories && filterData.categories.length > 0) {
            filterData.categories.forEach(catId => {
                url.searchParams.append('category[]', catId);
            });
        }

        if (filterData.price_min) {
            url.searchParams.set('price_min', filterData.price_min);
        }

        if (filterData.price_max) {
            url.searchParams.set('price_max', filterData.price_max);
        }

        if (filterData.promotions && filterData.promotions.length > 0) {
            filterData.promotions.forEach(promo => {
                url.searchParams.append('promotion[]', promo);
            });
        }

        // Luôn luôn include sort để đảm bảo consistency
        if (filterData.sort) {
            url.searchParams.set('sort', filterData.sort);
        }

        if (page > 1) {
            url.searchParams.set('page', page);
        }

        // Luôn luôn include items_per_page để đảm bảo consistency
        if (filterData.items_per_page) {
            url.searchParams.set('items_per_page', filterData.items_per_page);
        }

        // Cập nhật history
        const state = {
            isAjaxFilter: true,
            filterData: filterData,
            page: page
        };

        history.pushState(state, '', url.toString());
    }

    updateProductsGrid(data) {
        console.log('AJAX Filter: updateProductsGrid called', data);

        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) {
            console.error('AJAX Filter: productsGrid element not found');
            return;
        }

        console.log('AJAX Filter: productsGrid element found', productsGrid);

        // Tạo nội dung mới
        let newContent = '';
        if (data.products && data.products.length > 0) {
            console.log('AJAX Filter: Rendering', data.products.length, 'products');
            newContent = data.products.map(product => this.renderProductCard(product)).join('');
        } else {
            console.log('AJAX Filter: No products, rendering empty state');
            newContent = this.renderEmptyState(data.filters);
        }

        // Hiệu ứng chuyển đổi mượt mà
        this.animateProductsTransition(productsGrid, newContent);

        // Nếu là empty state, load popular categories
        if (!data.products || data.products.length === 0) {
            setTimeout(() => {
                this.loadPopularCategories();
            }, 500); // Delay để animation hoàn thành
        }
    }

    animateProductsTransition(productsGrid, newContent) {
        // Lấy tất cả sản phẩm hiện tại (trừ loading overlay)
        const currentProducts = Array.from(productsGrid.children).filter(
            child => !child.classList.contains('ajax-loading-overlay')
        );

        if (currentProducts.length === 0) {
            // Không có sản phẩm cũ, chỉ fade in sản phẩm mới
            productsGrid.innerHTML = newContent;
            this.fadeInNewProducts(productsGrid);
            return;
        }

        // Fade out sản phẩm cũ nhanh hơn
        console.log('AJAX Filter: Fading out old products');
        currentProducts.forEach((product, index) => {
            product.style.transition = 'opacity 0.2s ease, transform 0.2s ease'; // Giảm từ 0.3s xuống 0.2s
            product.style.opacity = '0';
            product.style.transform = 'translateY(-8px)'; // Giảm từ -10px xuống -8px
        });

        // Sau khi fade out xong, thay thế nội dung và fade in nhanh hơn
        setTimeout(() => {
            productsGrid.innerHTML = newContent;
            this.fadeInNewProducts(productsGrid);
            console.log('AJAX Filter: Products transition completed');
        }, 200); // Giảm từ 300ms xuống 200ms
    }

    fadeInNewProducts(productsGrid) {
        const newProducts = Array.from(productsGrid.children);

        // Đặt opacity ban đầu là 0 và transform
        newProducts.forEach(product => {
            product.style.opacity = '0';
            product.style.transform = 'translateY(15px)'; // Giảm từ 20px xuống 15px
            product.style.transition = 'opacity 0.3s ease, transform 0.3s ease'; // Giảm từ 0.4s xuống 0.3s
        });

        // Fade in từng sản phẩm với delay staggered nhanh hơn
        newProducts.forEach((product, index) => {
            setTimeout(() => {
                product.style.opacity = '1';
                product.style.transform = 'translateY(0)';
            }, index * 30); // Giảm delay từ 50ms xuống 30ms
        });

        // Reset styles sau khi animation hoàn thành
        setTimeout(() => {
            newProducts.forEach(product => {
                product.style.transition = '';
                product.style.transform = '';
            });
        }, 300 + (newProducts.length * 30)); // Tối ưu thời gian reset
    }

    renderProductCard(product) {
        const discountPercent = product.sale_price > 0 && product.price > product.sale_price
            ? Math.round(((product.price - product.sale_price) / product.price) * 100)
            : 0;

        const saleBadge = discountPercent > 0 ? `
            <div class="premium-sale-badge">
                <div class="badge-content">
                    <span class="discount-percent">-${discountPercent}%</span>
                    <span class="sale-text">SALE</span>
                </div>
            </div>
        ` : '';

        const priceSection = this.renderPriceSection(product);
        const stars = this.renderStars(product.rating || 5);

        return `
            <div class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                <div class="product-image-wrapper relative">
                    <a href="${product.url}" class="block product-image">
                        ${product.image ?
                            `<img src="${window.BASE_URL}/uploads/products/${product.image}" alt="${product.name}" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">` :
                            `<div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                                <i class="fas fa-image text-gray-500 text-4xl"></i>
                            </div>`
                        }
                    </a>
                    ${saleBadge}
                </div>
                <div class="product-info-wrapper flex flex-col flex-grow">
                    <div class="product-title mb-3">
                        <a href="${product.url}" class="block">
                            <h3 class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                ${product.name}
                            </h3>
                        </a>
                    </div>
                    ${priceSection}
                    <div class="product-rating-sales">
                        <div class="rating-section">
                            <div class="stars">${stars}</div>
                            <span class="rating-text">${parseFloat(product.rating || 5).toFixed(1)}</span>
                        </div>
                        <div class="sales-section">
                            <i class="fas fa-shopping-cart"></i>
                            <span>${(product.sold || 0).toLocaleString()} đã bán</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPriceSection(product) {
        if (product.price_type === 'contact') {
            return `
                <div class="premium-price-section">
                    <div class="contact-price-container">
                        <div class="contact-price-main">GỌI NGAY</div>
                        <div class="contact-price-subtitle">Liên hệ báo giá</div>
                    </div>
                </div>
            `;
        } else if (product.sale_price > 0) {
            return `
                <div class="premium-price-section">
                    <div class="price-container">
                        <div class="original-price">${this.formatCurrency(product.price)}</div>
                        <div class="sale-price">${this.formatCurrency(product.sale_price)}</div>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="premium-price-section">
                    <div class="regular-price-container">
                        <div class="price-label">Giá bán</div>
                        <div class="main-price">${this.formatCurrency(product.price)}</div>
                    </div>
                </div>
            `;
        }
    }

    renderStars(rating) {
        const numRating = parseFloat(rating) || 5;
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            stars += `<i class="fas fa-star${i <= numRating ? '' : '-o'}"></i>`;
        }
        return stars;
    }

    formatCurrency(amount) {
        // Sử dụng format giống như PHP format_currency function
        if (!amount || isNaN(amount)) return '0 đ';
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' đ';
    }

    renderEmptyState(filters) {
        const hasKeyword = filters.keyword && filters.keyword.trim() !== '';

        return `
            <div class="col-span-full py-16 text-center">
                <div class="max-w-2xl mx-auto">
                    <!-- Enhanced Empty State -->
                    <div class="bg-gradient-to-br from-orange-50/50 to-amber-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-sm">
                        <!-- Animated Icon -->
                        <div class="relative mb-6">
                            <div class="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-lg">
                                ${hasKeyword ?
                                    '<i class="fas fa-search-minus text-orange-400 text-2xl"></i>' :
                                    '<i class="fas fa-box-open text-orange-400 text-2xl"></i>'
                                }
                            </div>
                            <!-- Pulse animation -->
                            <div class="absolute inset-0 w-20 h-20 mx-auto bg-orange-200/30 rounded-full animate-ping"></div>
                        </div>

                        <!-- Main Message -->
                        <h3 class="text-xl font-bold text-gray-800 mb-3">
                            ${hasKeyword ? 'Không tìm thấy sản phẩm phù hợp' : 'Không có sản phẩm trong bộ lọc này'}
                        </h3>

                        <!-- Detailed Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            ${hasKeyword ?
                                `Không có sản phẩm nào khớp với từ khóa <span class="inline-flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded font-medium">"${filters.keyword}"</span>` :
                                'Không có sản phẩm nào phù hợp với các tiêu chí lọc hiện tại'
                            }
                        </p>

                        <!-- Helpful Suggestions -->
                        <div class="bg-white/60 rounded-xl p-6 mb-6 border border-orange-100/50">
                            <h4 class="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                                <i class="fas fa-lightbulb text-amber-500 mr-2"></i>
                                Gợi ý để tìm thấy sản phẩm:
                            </h4>
                            <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-3 text-sm text-gray-600">
                                ${hasKeyword ? `
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử từ khóa ngắn gọn hơn</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Kiểm tra chính tả từ khóa</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Sử dụng từ đồng nghĩa</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử bỏ bớt bộ lọc</span>
                                    </div>
                                ` : `
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Mở rộng khoảng giá</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Thử danh mục khác</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Bỏ bớt tiêu chí lọc</span>
                                    </div>
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                        <span>Xem tất cả sản phẩm</span>
                                    </div>
                                `}
                            </div>
                        </div>

                        <!-- Popular Categories -->
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-700 mb-3">Danh mục phổ biến:</h4>
                            <div class="flex flex-wrap justify-center gap-2" id="popular-categories-ajax">
                                <!-- Categories will be loaded here -->
                            </div>
                        </div>

                        <!-- Contact Support -->
                        <div class="text-center">
                            <p class="text-sm text-gray-500 mb-3">Không tìm thấy sản phẩm bạn cần?</p>
                            <div class="flex flex-col sm:flex-row gap-2 justify-center">
                                <a href="${window.BASE_URL}/contact.php" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                    <i class="fas fa-headset mr-2"></i>
                                    Liên hệ tư vấn
                                </a>
                                <a href="${window.BASE_URL}/products.php" class="inline-flex items-center px-4 py-2 bg-white border-2 border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 font-medium rounded-lg transition-all duration-200 hover:bg-orange-50 text-sm">
                                    <i class="fas fa-th-large mr-2"></i>
                                    Xem tất cả sản phẩm
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    loadPopularCategories() {
        console.log('AJAX Filter: Loading popular categories for empty state');

        const categoriesContainer = document.getElementById('popular-categories-ajax');
        if (!categoriesContainer) {
            console.warn('AJAX Filter: Popular categories container not found');
            return;
        }

        // Show loading state
        categoriesContainer.innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                <span class="ml-2 text-sm text-gray-500">Đang tải danh mục...</span>
            </div>
        `;

        // Make AJAX request to get popular categories
        fetch(`${window.BASE_URL}/ajax/get_popular_categories.php`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('AJAX Filter: Popular categories loaded', data);

            if (data.success && data.categories && data.categories.length > 0) {
                const categoriesHtml = data.categories.map(cat => `
                    <a href="${window.BASE_URL}/products.php?category=${cat.id}"
                       class="inline-flex items-center px-3 py-2 bg-white border border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 rounded-lg text-xs font-medium transition-all duration-200 hover:bg-orange-50 hover:shadow-sm">
                        <i class="fas fa-tag mr-1.5 text-xs"></i>
                        ${cat.name}
                        <span class="ml-1 text-gray-400">(${cat.product_count})</span>
                    </a>
                `).join('');

                categoriesContainer.innerHTML = categoriesHtml;
            } else {
                // Fallback if no categories
                categoriesContainer.innerHTML = `
                    <div class="text-sm text-gray-500">Không có danh mục phổ biến</div>
                `;
            }
        })
        .catch(error => {
            console.error('AJAX Filter: Error loading popular categories:', error);
            // Hide the container on error
            categoriesContainer.innerHTML = `
                <div class="text-sm text-gray-400">Không thể tải danh mục</div>
            `;
        });
    }

    updateFilterResultsHeader(data) {
        console.log('AJAX Filter: updateFilterResultsHeader called', data);

        // Tìm container chứa Filter Results Header
        const productsContent = document.querySelector('.products-content');
        if (!productsContent) {
            console.warn('AJAX Filter: Products content container not found');
            return;
        }

        // Tìm vị trí để chèn/cập nhật Filter Results Header
        // Nó nằm ngay sau phần header của products content và trước products grid
        const existingHeader = productsContent.querySelector('.filter-results-header');
        const productsGrid = document.getElementById('productsGrid');

        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found');
            return;
        }

        // Nếu có HTML từ API, sử dụng nó
        if (data.filter_results_header_html && data.filter_results_header_html.trim() !== '') {
            console.log('AJAX Filter: Using HTML from API for filter results header');

            if (existingHeader) {
                // Thay thế header hiện tại bằng animation
                this.replaceFilterResultsHeader(existingHeader, data.filter_results_header_html);
            } else {
                // Tạo mới header và chèn vào vị trí phù hợp
                this.insertFilterResultsHeader(productsGrid, data.filter_results_header_html);
            }
        } else {
            console.log('AJAX Filter: No filter results header HTML from API, hiding existing header');

            // Nếu không có HTML từ API (không có filter), ẩn header hiện tại
            if (existingHeader) {
                this.hideFilterResultsHeader(existingHeader);
            }
        }

        console.log('AJAX Filter: Filter results header updated successfully');
    }

    replaceFilterResultsHeader(existingHeader, newHtml) {
        console.log('AJAX Filter: Replacing existing filter results header');

        // Fade out header cũ
        existingHeader.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        existingHeader.style.opacity = '0';
        existingHeader.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            // Tạo element mới từ HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newHtml;
            const newHeader = tempDiv.firstElementChild;

            if (newHeader) {
                // Đặt opacity ban đầu là 0 cho animation
                newHeader.style.opacity = '0';
                newHeader.style.transform = 'translateY(10px)';
                newHeader.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

                // Thay thế element
                existingHeader.parentNode.replaceChild(newHeader, existingHeader);

                // Fade in header mới
                setTimeout(() => {
                    newHeader.style.opacity = '1';
                    newHeader.style.transform = 'translateY(0)';
                }, 50);

                // Reset styles sau animation
                setTimeout(() => {
                    newHeader.style.transition = '';
                    newHeader.style.transform = '';
                }, 450);
            }
        }, 300);
    }

    insertFilterResultsHeader(productsGrid, newHtml) {
        console.log('AJAX Filter: Inserting new filter results header');

        // Tạo element mới từ HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newHtml;
        const newHeader = tempDiv.firstElementChild;

        if (newHeader) {
            // Đặt opacity ban đầu là 0 cho animation
            newHeader.style.opacity = '0';
            newHeader.style.transform = 'translateY(20px)';
            newHeader.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

            // Chèn trước products grid
            productsGrid.parentNode.insertBefore(newHeader, productsGrid);

            // Fade in header mới
            setTimeout(() => {
                newHeader.style.opacity = '1';
                newHeader.style.transform = 'translateY(0)';
            }, 50);

            // Reset styles sau animation
            setTimeout(() => {
                newHeader.style.transition = '';
                newHeader.style.transform = '';
            }, 450);
        }
    }

    hideFilterResultsHeader(existingHeader) {
        console.log('AJAX Filter: Hiding filter results header');

        // Fade out và remove
        existingHeader.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        existingHeader.style.opacity = '0';
        existingHeader.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            if (existingHeader.parentNode) {
                existingHeader.parentNode.removeChild(existingHeader);
            }
        }, 300);
    }

    updateFilterResultsHeaderContent(data) {
        console.log('AJAX Filter: updateFilterResultsHeaderContent called (for simultaneous update)', data);

        // Tìm container chứa Filter Results Header
        const productsContent = document.querySelector('.products-content');
        if (!productsContent) {
            console.warn('AJAX Filter: Products content container not found');
            return;
        }

        // Tìm vị trí để chèn/cập nhật Filter Results Header
        const existingHeader = productsContent.querySelector('.filter-results-header');
        const productsGrid = document.getElementById('productsGrid');

        if (!productsGrid) {
            console.warn('AJAX Filter: Products grid not found');
            return;
        }

        // Nếu có HTML từ API, sử dụng nó
        if (data.filter_results_header_html && data.filter_results_header_html.trim() !== '') {
            console.log('AJAX Filter: Using HTML from API for filter results header (simultaneous)');

            if (existingHeader) {
                // Thay thế header hiện tại mà không có animation riêng biệt
                this.replaceFilterResultsHeaderContent(existingHeader, data.filter_results_header_html);
            } else {
                // Tạo mới header và chèn vào vị trí phù hợp
                this.insertFilterResultsHeaderContent(productsGrid, data.filter_results_header_html);
            }
        } else {
            console.log('AJAX Filter: No filter results header HTML from API, removing existing header');

            // Nếu không có HTML từ API (không có filter), xóa header hiện tại
            if (existingHeader && existingHeader.parentNode) {
                existingHeader.parentNode.removeChild(existingHeader);
            }
        }

        console.log('AJAX Filter: Filter results header content updated successfully (simultaneous)');
    }

    replaceFilterResultsHeaderContent(existingHeader, newHtml) {
        console.log('AJAX Filter: Replacing existing filter results header content (no animation)');

        // Tạo element mới từ HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newHtml;
        const newHeader = tempDiv.firstElementChild;

        if (newHeader) {
            // Đặt opacity và transform giống với existingHeader để đồng bộ animation
            newHeader.style.opacity = '0.3';
            newHeader.style.transform = 'translateY(-10px)';
            newHeader.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';

            // Thay thế element
            existingHeader.parentNode.replaceChild(newHeader, existingHeader);
        }
    }

    insertFilterResultsHeaderContent(productsGrid, newHtml) {
        console.log('AJAX Filter: Inserting new filter results header content (no animation)');

        // Tạo element mới từ HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newHtml;
        const newHeader = tempDiv.firstElementChild;

        if (newHeader) {
            // Đặt opacity ban đầu để đồng bộ với animation chung
            newHeader.style.opacity = '0.3';
            newHeader.style.transform = 'translateY(-10px)';
            newHeader.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';

            // Chèn trước products grid
            productsGrid.parentNode.insertBefore(newHeader, productsGrid);
        }
    }

    updateFilterBadge(filters) {
        console.log('AJAX Filter: updateFilterBadge called', filters);

        // Tính số lượng filter active
        let activeFiltersCount = 0;
        let filterDetails = [];

        if (filters.keyword && filters.keyword.trim() !== '') {
            activeFiltersCount++;
            filterDetails.push('từ khóa');
        }

        if (filters.categories && filters.categories.length > 0) {
            activeFiltersCount++;
            filterDetails.push('danh mục');
        }

        if (filters.price_min || filters.price_max) {
            activeFiltersCount++;
            filterDetails.push('khoảng giá');
        }

        if (filters.promotions && filters.promotions.length > 0) {
            activeFiltersCount++;
            filterDetails.push('khuyến mãi');
        }

        console.log('AJAX Filter: Active filters count:', activeFiltersCount);

        // Tìm nút reset filters
        const resetButton = document.getElementById('resetFilters');
        if (!resetButton) {
            console.warn('AJAX Filter: Reset button not found');
            return;
        }

        // Tìm badge hiện tại
        let badge = resetButton.querySelector('.absolute.-top-1.-right-1');

        if (activeFiltersCount > 0) {
            // Có filter active - hiển thị/cập nhật badge
            if (!badge) {
                // Tạo badge mới
                badge = document.createElement('div');
                badge.className = 'absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse';
                resetButton.appendChild(badge);

                // Animation xuất hiện
                badge.style.opacity = '0';
                badge.style.transform = 'scale(0)';
                badge.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

                setTimeout(() => {
                    badge.style.opacity = '1';
                    badge.style.transform = 'scale(1)';
                }, 50);
            }

            // Cập nhật số lượng với animation
            if (badge.textContent !== activeFiltersCount.toString()) {
                badge.style.transform = 'scale(1.2)';
                badge.textContent = activeFiltersCount;

                setTimeout(() => {
                    badge.style.transform = 'scale(1)';
                }, 150);
            }

            // Cập nhật classes của button
            resetButton.className = resetButton.className.replace(
                /text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200/g,
                'text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300'
            );

            // Cập nhật tooltip
            const tooltipText = `Xóa tất cả bộ lọc (${activeFiltersCount} bộ lọc: ${filterDetails.join(', ')})`;
            resetButton.setAttribute('title', tooltipText);

        } else {
            // Không có filter active - ẩn badge
            if (badge) {
                // Animation biến mất
                badge.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                badge.style.opacity = '0';
                badge.style.transform = 'scale(0)';

                setTimeout(() => {
                    if (badge.parentNode) {
                        badge.parentNode.removeChild(badge);
                    }
                }, 300);
            }

            // Cập nhật classes của button về trạng thái inactive
            resetButton.className = resetButton.className.replace(
                /text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300/g,
                'text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200'
            );

            // Cập nhật tooltip
            resetButton.setAttribute('title', 'Đặt lại bộ lọc');
        }

        console.log('AJAX Filter: Filter badge updated successfully');
    }

    syncSidebarUI() {
        console.log('AJAX Filter: Syncing sidebar UI with current filter state');

        // Lấy filter data hiện tại
        const filterData = this.collectFilterData();

        // Đồng bộ search inputs
        this.syncSearchInputs(filterData.keyword || '');

        // Đồng bộ price preset buttons
        this.syncPricePresetButtons(filterData.price_min, filterData.price_max);

        // Đồng bộ category checkboxes
        this.syncCategoryCheckboxes(filterData.categories || []);

        // Đồng bộ promotion checkboxes
        this.syncPromotionCheckboxes(filterData.promotions || []);

        // Đồng bộ price inputs
        this.syncPriceInputs(filterData.price_min, filterData.price_max);

        console.log('AJAX Filter: Sidebar UI sync completed');
    }

    syncPricePresetButtons(priceMin, priceMax) {
        console.log('AJAX Filter: Syncing price preset buttons', { priceMin, priceMax });

        // Tìm tất cả price preset buttons
        const pricePresets = document.querySelectorAll('.price-preset');

        pricePresets.forEach(button => {
            const buttonMin = parseInt(button.getAttribute('data-min')) || 0;
            const buttonMax = button.getAttribute('data-max') ? parseInt(button.getAttribute('data-max')) : null;

            // Kiểm tra xem button này có match với filter hiện tại không
            let isActive = false;

            if (buttonMax === null) {
                // "Trên X triệu" case
                isActive = (priceMin == buttonMin && !priceMax);
            } else {
                // Range case
                isActive = (priceMin == buttonMin && priceMax == buttonMax);
            }

            // Cập nhật trạng thái
            if (isActive) {
                // Activate button
                button.className = button.className.replace(/bg-white|text-gray-700|border-gray-200/g, '');
                button.classList.add('from-orange-500', 'bg-white', 'text-gray-700', 'border-gray-200');
            } else {
                // Deactivate button
                button.className = button.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                button.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
            }
        });

        console.log('AJAX Filter: Price preset buttons synced');
    }

    syncCategoryCheckboxes(activeCategories) {
        console.log('AJAX Filter: Syncing category checkboxes', activeCategories);

        // Tìm tất cả category checkboxes
        const categoryCheckboxes = document.querySelectorAll('input[name="category[]"]');

        categoryCheckboxes.forEach(checkbox => {
            const categoryId = parseInt(checkbox.value);
            checkbox.checked = activeCategories.includes(categoryId);
        });

        console.log('AJAX Filter: Category checkboxes synced');
    }

    syncPromotionCheckboxes(activePromotions) {
        console.log('AJAX Filter: Syncing promotion checkboxes', activePromotions);

        // Tìm tất cả promotion checkboxes
        const promotionCheckboxes = document.querySelectorAll('input[name="promotion[]"]');

        promotionCheckboxes.forEach(checkbox => {
            const promotionValue = checkbox.value;
            checkbox.checked = activePromotions.includes(promotionValue);
        });

        console.log('AJAX Filter: Promotion checkboxes synced');
    }

    syncPriceInputs(priceMin, priceMax) {
        console.log('AJAX Filter: Syncing price inputs', { priceMin, priceMax });

        // Tìm price inputs
        const priceMinInput = document.getElementById('price-min');
        const priceMaxInput = document.getElementById('price-max');

        if (priceMinInput) {
            priceMinInput.value = priceMin || '';
        }

        if (priceMaxInput) {
            priceMaxInput.value = priceMax || '';
        }

        console.log('AJAX Filter: Price inputs synced');
    }

    updatePagination(pagination) {
        // Tìm container pagination
        const paginationContainer = document.querySelector('.pagination-container, .pagination-section');
        if (!paginationContainer) return;

        if (pagination.total_pages <= 1) {
            // Ẩn pagination nếu chỉ có 1 trang
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';

        // Render pagination HTML
        const paginationHTML = this.renderPagination(pagination);

        // Tìm phần pagination links để thay thế
        const paginationLinks = paginationContainer.querySelector('.pagination-list, .pagination-links, .flex.justify-center');
        if (paginationLinks) {
            paginationLinks.innerHTML = paginationHTML;
        }
    }

    renderPagination(pagination) {
        const { current_page, total_pages, has_prev, has_next } = pagination;
        let html = '';

        // Previous button
        if (has_prev) {
            html += `
                <li class="page-item">
                    <a class="page-link ajax-pagination-link" href="#" data-page="${current_page - 1}" aria-label="Trang trước">
                        <i class="fas fa-chevron-left"></i>
                        <span class="page-text">Trước</span>
                    </a>
                </li>
            `;
        } else {
            html += `
                <li class="page-item disabled">
                    <span class="page-link" aria-disabled="true">
                        <i class="fas fa-chevron-left"></i>
                        <span class="page-text">Trước</span>
                    </span>
                </li>
            `;
        }

        // Page numbers logic
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(total_pages, current_page + 2);

        // First page + ellipsis if needed
        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link ajax-pagination-link" href="#" data-page="1" aria-label="Trang 1">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link ellipsis">...</span>
                    </li>
                `;
            }
        }

        // Visible page range
        for (let i = startPage; i <= endPage; i++) {
            if (i === current_page) {
                html += `
                    <li class="page-item active">
                        <a class="page-link ajax-pagination-link" href="#" data-page="${i}" aria-label="Trang ${i}">${i}</a>
                    </li>
                `;
            } else {
                html += `
                    <li class="page-item">
                        <a class="page-link ajax-pagination-link" href="#" data-page="${i}" aria-label="Trang ${i}">${i}</a>
                    </li>
                `;
            }
        }

        // Last page + ellipsis if needed
        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link ellipsis">...</span>
                    </li>
                `;
            }
            html += `
                <li class="page-item">
                    <a class="page-link ajax-pagination-link" href="#" data-page="${total_pages}" aria-label="Trang ${total_pages}">${total_pages}</a>
                </li>
            `;
        }

        // Next button
        if (has_next) {
            html += `
                <li class="page-item">
                    <a class="page-link ajax-pagination-link" href="#" data-page="${current_page + 1}" aria-label="Trang sau">
                        <span class="page-text">Sau</span>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        } else {
            html += `
                <li class="page-item disabled">
                    <span class="page-link" aria-disabled="true">
                        <span class="page-text">Sau</span>
                        <i class="fas fa-chevron-right"></i>
                    </span>
                </li>
            `;
        }

        return html;
    }

    bindSortAndPaginationEvents() {
        console.log('AJAX Filter: Binding sort and pagination events');

        // Bind sort select
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                console.log('AJAX Filter: Sort changed to', e.target.value);
                this.handleSortChange(e.target.value);
            });
            console.log('AJAX Filter: Sort select bound');
        }

        // Bind items per page select
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                console.log('AJAX Filter: Items per page changed to', e.target.value);
                this.handleItemsPerPageChange(e.target.value);
            });
            console.log('AJAX Filter: Items per page select bound');
        }
    }

    handleSortChange(sortValue) {
        console.log('AJAX Filter: handleSortChange called with', sortValue);

        // Đánh dấu đây là sort request để debug
        this.isFilterRequest = true;

        // Collect current filter data
        const filterData = this.collectFilterData();

        // Update sort value
        filterData.sort = sortValue;

        // Load products with new sort (reset to page 1) - sử dụng fast mode
        console.log('🔄 AJAX Filter: Loading products with new sort, will hide loading state after completion');
        this.loadProducts(filterData, 1, true, true);
    }

    handleItemsPerPageChange(itemsPerPage) {
        console.log('AJAX Filter: handleItemsPerPageChange called with', itemsPerPage);

        // Đánh dấu đây là filter request để debug
        this.isFilterRequest = true;

        // Lưu giá trị vào Local Storage và Cookie (giống logic cũ)
        localStorage.setItem('products_items_per_page', itemsPerPage);

        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);
        document.cookie = `products_items_per_page=${itemsPerPage}; expires=${expiryDate.toUTCString()}; path=/`;

        // Collect current filter data
        const filterData = this.collectFilterData();

        // Update items per page
        filterData.items_per_page = parseInt(itemsPerPage);

        // Load products with new items per page (reset to page 1) - sử dụng fast mode
        console.log('🔄 AJAX Filter: Loading products with new items per page, will hide loading state after completion');
        this.loadProducts(filterData, 1, true, true);
    }

    updateProductsStats(pagination) {
        console.log('AJAX Filter: updateProductsStats called', pagination);

        const productsStatsContainer = document.getElementById('products-stats');
        const productsShowingElement = document.getElementById('products-showing');
        const productsTotalElement = document.getElementById('products-total');

        if (!productsStatsContainer || !productsShowingElement || !productsTotalElement) {
            console.warn('AJAX Filter: Products stats elements not found');
            return;
        }

        // Tính toán số sản phẩm đang hiển thị
        const currentPage = pagination.current_page || 1;
        const itemsPerPage = pagination.items_per_page || 12;
        const totalProducts = pagination.total_products || 0;

        // Tính số sản phẩm hiển thị trên trang hiện tại
        const startItem = (currentPage - 1) * itemsPerPage + 1;
        const endItem = Math.min(currentPage * itemsPerPage, totalProducts);
        const showingCount = totalProducts > 0 ? endItem : 0;

        console.log('AJAX Filter: Products stats calculation', {
            currentPage,
            itemsPerPage,
            totalProducts,
            startItem,
            endItem,
            showingCount
        });

        // Cập nhật số liệu với animation
        this.animateNumberChange(productsShowingElement, showingCount);
        this.animateNumberChange(productsTotalElement, totalProducts);

        console.log('AJAX Filter: Products stats updated successfully');
    }

    animateNumberChange(element, newValue) {
        if (!element) return;

        const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
        const formattedNewValue = new Intl.NumberFormat('vi-VN').format(newValue);

        if (currentValue === newValue) {
            return; // Không thay đổi
        }

        // Fade out
        element.style.transition = 'opacity 0.2s ease';
        element.style.opacity = '0.5';

        // Update value và fade in
        setTimeout(() => {
            element.textContent = formattedNewValue;
            element.style.opacity = '1';
        }, 200);
    }
}

// Khởi tạo AJAX Filter sau khi tất cả script đã load
document.addEventListener('DOMContentLoaded', function() {
    // Delay để đảm bảo tất cả script khác đã chạy xong
    setTimeout(() => {
        console.log('Initializing AJAX Filter...');
        window.ajaxFilter = new AjaxFilter();
        window.ajaxFilterActive = true; // Đánh dấu AJAX Filter đang hoạt động
        console.log('AJAX Filter initialized successfully');

        // Thêm một delay nữa để override lại sau khi tất cả script khác đã chạy
        setTimeout(() => {
            console.log('AJAX Filter: Final override...');
            if (window.ajaxFilter) {
                window.ajaxFilter.overrideApplyFiltersButton();
            }

            // Đảm bảo hàm removeKeywordFilter được định nghĩa lại
            console.log('🔧 AJAX Filter: Re-defining removeKeywordFilter...');
            window.removeKeywordFilter = function() {
                console.log('🗑️ AJAX Filter: Removing keyword filter');

                // Set flag để không lấy keyword từ search input nữa
                if (window.ajaxFilter) {
                    window.ajaxFilter.keywordRemoved = true;
                    console.log('🚫 Set keywordRemoved flag to true');
                }

                // Xóa keyword từ TẤT CẢ search inputs
                const mainSearchInput = document.getElementById('main-search-input');
                if (mainSearchInput) {
                    mainSearchInput.value = '';
                    console.log('✅ Main search input cleared');
                }

                const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
                if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
                    sidebarSearchInput.value = '';
                    console.log('✅ Sidebar search input cleared');
                }

                // Trigger AJAX filter với data mới
                if (window.ajaxFilter) {
                    console.log('🔄 Calling handleRemoveFilter()');
                    window.ajaxFilter.handleRemoveFilter();
                } else {
                    console.log('⚠️ AJAX Filter not available, using fallback');
                    // Fallback: reload trang
                    const url = new URL(window.location);
                    url.searchParams.delete('keyword');
                    window.location.href = url.toString();
                }
            };

            // Thêm event delegation để bắt click trên nút remove keyword (backup)
            document.addEventListener('click', function(e) {
                // Kiểm tra nhiều cách khác nhau
                const isKeywordRemoveBtn =
                    e.target.closest('button[onclick*="removeKeywordFilter"]') ||
                    e.target.closest('.remove-btn') && e.target.closest('.filter-tag') &&
                    e.target.closest('.filter-tag').textContent.includes('Từ khóa:');

                if (isKeywordRemoveBtn) {
                    console.log('🎯 AJAX Filter: Detected click on keyword remove button via delegation');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    // Gọi hàm remove keyword
                    if (typeof window.removeKeywordFilter === 'function') {
                        window.removeKeywordFilter();
                    }
                    return false;
                }
            }, true); // Use capture phase

            // Override onclick handler trực tiếp
            console.log('🔧 AJAX Filter: Overriding onclick handler for keyword remove button...');
            const keywordRemoveBtn = document.querySelector('button[onclick*="removeKeywordFilter"]');
            if (keywordRemoveBtn) {
                console.log('✅ Found keyword remove button, overriding onclick');
                keywordRemoveBtn.onclick = function(e) {
                    console.log('🎯 AJAX Filter: Keyword remove button clicked (overridden)');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    if (typeof window.removeKeywordFilter === 'function') {
                        window.removeKeywordFilter();
                    }
                    return false;
                };
            } else {
                console.warn('❌ Keyword remove button not found for override');
            }

            console.log('✅ removeKeywordFilter re-defined and event delegation set up successfully');
        }, 500);
    }, 200);
});

// Global functions để xử lý remove filter từ Filter Results Header với AJAX
window.removeKeywordFilter = function() {
    console.log('🗑️ AJAX Filter: Removing keyword filter');

    // Set flag để không lấy keyword từ search input nữa
    if (window.ajaxFilter) {
        window.ajaxFilter.keywordRemoved = true;
        console.log('🚫 Set keywordRemoved flag to true');
    }

    // Xóa keyword từ TẤT CẢ search inputs
    const mainSearchInput = document.getElementById('main-search-input');
    if (mainSearchInput) {
        console.log('🔍 Main search input before clear:', mainSearchInput.value);
        mainSearchInput.value = '';
        console.log('✅ Cleared main search input');
    } else {
        console.warn('❌ Main search input not found');
    }

    const sidebarSearchInput = document.querySelector('input[name="keyword"], #search-input');
    if (sidebarSearchInput && sidebarSearchInput !== mainSearchInput) {
        console.log('🔍 Sidebar search input before clear:', sidebarSearchInput.value);
        sidebarSearchInput.value = '';
        console.log('✅ Cleared sidebar search input');
    } else {
        console.log('ℹ️ No separate sidebar search input found');
    }

    // Uncheck keyword checkbox nếu có
    const keywordCheckbox = document.querySelector('input[type="checkbox"][value*="keyword"]');
    if (keywordCheckbox) {
        keywordCheckbox.checked = false;
        console.log('✅ Unchecked keyword checkbox');
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        console.log('🔄 Triggering AJAX filter after keyword removal');
        window.ajaxFilter.handleRemoveFilter();
    } else {
        console.log('⚠️ AJAX Filter not available, using fallback');
        // Fallback: reload trang
        const url = new URL(window.location);
        url.searchParams.delete('keyword');
        window.location.href = url.toString();
    }
};

window.removeCategoryFilter = function(categoryId) {
    console.log('AJAX Filter: Removing category filter', categoryId);

    // Uncheck category checkbox
    const categoryCheckbox = document.querySelector(`input[name="category[]"][value="${categoryId}"]`);
    if (categoryCheckbox) {
        categoryCheckbox.checked = false;
        console.log('AJAX Filter: Unchecked category checkbox for ID:', categoryId);
    } else {
        console.warn('AJAX Filter: Category checkbox not found for ID:', categoryId);
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        const categories = url.searchParams.getAll('category[]');
        const newCategories = categories.filter(id => parseInt(id) !== parseInt(categoryId));

        url.searchParams.delete('category[]');
        newCategories.forEach(id => {
            url.searchParams.append('category[]', id);
        });

        window.location.href = url.toString();
    }
};

window.removePriceFilter = function() {
    console.log('AJAX Filter: Removing price filter');

    // Clear price inputs
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');

    if (priceMinInput) {
        priceMinInput.value = '';
    }
    if (priceMaxInput) {
        priceMaxInput.value = '';
    }

    // Clear active state từ tất cả price preset buttons
    document.querySelectorAll('.price-preset').forEach(btn => {
        btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
        btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
    });

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        url.searchParams.delete('price_min');
        url.searchParams.delete('price_max');
        window.location.href = url.toString();
    }
};

window.removePromotionFilter = function(promotion) {
    console.log('AJAX Filter: Removing promotion filter', promotion);

    // Uncheck promotion checkbox
    const promotionCheckbox = document.querySelector(`input[name="promotion[]"][value="${promotion}"]`);
    if (promotionCheckbox) {
        promotionCheckbox.checked = false;
        console.log('AJAX Filter: Unchecked promotion checkbox for:', promotion);
    } else {
        console.warn('AJAX Filter: Promotion checkbox not found for:', promotion);
    }

    // Trigger AJAX filter với data mới
    if (window.ajaxFilter) {
        window.ajaxFilter.handleRemoveFilter();
    } else {
        // Fallback: reload trang
        const url = new URL(window.location);
        const promotions = url.searchParams.getAll('promotion[]');
        const newPromotions = promotions.filter(p => p !== promotion);

        url.searchParams.delete('promotion[]');
        newPromotions.forEach(p => {
            url.searchParams.append('promotion[]', p);
        });

        window.location.href = url.toString();
    }
};
