/**
 * Unified Optimized Header Handler
 * Single source of truth cho tất cả header interactions
 * Tối ưu performance với RAF, throttling, và smart state management
 */

class UnifiedHeaderHandler {
    constructor(options = {}) {
        // ===== CONFIGURATION =====
        this.config = {
            // Performance settings
            THROTTLE_DELAY: options.throttleDelay || 16, // ~60fps
            DEBOUNCE_DELAY: options.debounceDelay || 150,
            RAF_TIMEOUT: options.rafTimeout || 1000 / 60,
            
            // Scroll thresholds
            SCROLL_THRESHOLD: options.scrollThreshold || 10,
            SCROLL_HYSTERESIS: options.scrollHysteresis || 5,
            COMPACT_THRESHOLD: options.compactThreshold || 200,
            TOP_BAR_HIDE_THRESHOLD: options.topBarHideThreshold || 50,
            
            // Transition settings
            TRANSITION_DURATION: options.transitionDuration || 300,
            EASING_FUNCTION: options.easingFunction || 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            
            // Feature flags
            ENABLE_HARDWARE_ACCELERATION: options.enableHardwareAcceleration !== false,
            ENABLE_LOGO_TRANSITION: options.enableLogoTransition !== false,
            ENABLE_VELOCITY_TRACKING: options.enableVelocityTracking !== false,
            ENABLE_PERFORMANCE_MONITORING: options.enablePerformanceMonitoring !== false
        };

        // ===== STATE MANAGEMENT =====
        this.state = {
            // Scroll tracking
            currentScrollTop: 0,
            lastScrollTop: 0,
            scrollDelta: 0,
            scrollVelocity: 0,
            velocityHistory: [],
            
            // Direction and behavior
            isScrollingDown: false,
            isScrolled: false,
            isCompact: false,
            isTopBarHidden: false,
            
            // Performance tracking
            lastUpdateTime: 0,
            frameId: null,
            isThrottled: false,
            
            // Timers
            debounceTimer: null,
            scrollStopTimer: null,
            
            // Logo sources
            originalLogoSrc: '',
            darkLogoSrc: ''
        };

        // ===== DOM ELEMENTS =====
        this.elements = {};
        
        // ===== PERFORMANCE METRICS =====
        this.metrics = {
            scrollEvents: 0,
            frameDrops: 0,
            averageFPS: 0,
            lastFPSCheck: 0
        };

        // Initialize
        this.init();
    }

    /**
     * Initialize handler
     */
    init() {
        console.log('🚀 Initializing Unified Header Handler...');
        
        this.cacheElements();
        this.setupInitialState();
        this.setupEventListeners();
        this.setupPerformanceMonitoring();
        this.checkInitialScrollPosition();
        
        console.log('✅ Unified Header Handler initialized');
    }

    /**
     * Cache DOM elements
     */
    cacheElements() {
        this.elements = {
            header: document.querySelector('.premium-header') || 
                   document.querySelector('.three-tier-header') ||
                   document.querySelector('.mobile-header'),
            topBar: document.querySelector('.top-bar'),
            midHeader: document.querySelector('.mid-header'),
            bottomHeader: document.querySelector('.bottom-header'),
            logoImage: document.querySelector('.premium-logo-image img') ||
                      document.querySelector('.luxury-logo-image img'),
            searchContainer: document.querySelector('.search-container'),
            mobileMenu: document.querySelector('.mobile-menu'),
            body: document.body,
            html: document.documentElement
        };

        // Setup logo sources
        if (this.elements.logoImage) {
            this.state.originalLogoSrc = this.elements.logoImage.src;
            this.state.darkLogoSrc = this.state.originalLogoSrc.replace('.svg', '-chu-trang.svg');
        }

        console.log('📦 DOM elements cached');
    }

    /**
     * Setup initial state
     */
    setupInitialState() {
        if (!this.elements.header) {
            console.warn('⚠️ No header element found');
            return;
        }

        // Add optimization classes
        this.elements.header.classList.add('unified-optimized');
        
        // Setup hardware acceleration if enabled
        if (this.config.ENABLE_HARDWARE_ACCELERATION) {
            this.elements.header.style.willChange = 'transform';
            this.elements.header.style.transform = 'translateZ(0)';
        }

        // Setup CSS custom properties for smooth transitions
        this.elements.header.style.setProperty('--transition-duration', `${this.config.TRANSITION_DURATION}ms`);
        this.elements.header.style.setProperty('--easing-function', this.config.EASING_FUNCTION);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Main scroll handler with passive listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { 
            passive: true,
            capture: false 
        });

        // Resize handler with debouncing
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250), {
            passive: true
        });

        // Visibility change handler
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        console.log('👂 Event listeners setup');
    }

    /**
     * Main scroll handler
     */
    handleScroll() {
        const now = performance.now();
        
        // Throttle for performance
        if (now - this.state.lastUpdateTime < this.config.THROTTLE_DELAY) {
            return;
        }

        this.state.lastUpdateTime = now;
        this.metrics.scrollEvents++;

        // Cancel previous frame if pending
        if (this.state.frameId) {
            cancelAnimationFrame(this.state.frameId);
        }

        // Schedule update on next frame
        this.state.frameId = requestAnimationFrame(() => {
            this.updateScrollState();
            this.applyScrollEffects();
            this.handleScrollStop();
            this.state.frameId = null;
        });
    }

    /**
     * Update scroll state
     */
    updateScrollState() {
        const scrollTop = window.pageYOffset || this.elements.html.scrollTop;
        
        // Update scroll tracking
        this.state.lastScrollTop = this.state.currentScrollTop;
        this.state.currentScrollTop = scrollTop;
        this.state.scrollDelta = scrollTop - this.state.lastScrollTop;
        
        // Update direction
        this.state.isScrollingDown = this.state.scrollDelta > 0;
        
        // Update velocity if enabled
        if (this.config.ENABLE_VELOCITY_TRACKING) {
            this.updateScrollVelocity();
        }
        
        // Update states with hysteresis
        this.updateScrollStates();
    }

    /**
     * Update scroll velocity
     */
    updateScrollVelocity() {
        const now = performance.now();
        const velocity = Math.abs(this.state.scrollDelta) / (now - this.state.lastUpdateTime) * 1000;
        
        this.state.velocityHistory.push(velocity);
        if (this.state.velocityHistory.length > 5) {
            this.state.velocityHistory.shift();
        }
        
        // Calculate average velocity
        this.state.scrollVelocity = this.state.velocityHistory.reduce((a, b) => a + b, 0) / this.state.velocityHistory.length;
    }

    /**
     * Update scroll states with hysteresis
     */
    updateScrollStates() {
        const { currentScrollTop } = this.state;
        const { SCROLL_THRESHOLD, SCROLL_HYSTERESIS, COMPACT_THRESHOLD, TOP_BAR_HIDE_THRESHOLD } = this.config;
        
        // Update scrolled state
        if (currentScrollTop > SCROLL_THRESHOLD) {
            if (!this.state.isScrolled) {
                this.state.isScrolled = true;
                this.onScrolledStateChange(true);
            }
        } else if (currentScrollTop < (SCROLL_THRESHOLD - SCROLL_HYSTERESIS)) {
            if (this.state.isScrolled) {
                this.state.isScrolled = false;
                this.onScrolledStateChange(false);
            }
        }
        
        // Update compact state
        if (currentScrollTop > COMPACT_THRESHOLD) {
            if (!this.state.isCompact) {
                this.state.isCompact = true;
                this.onCompactStateChange(true);
            }
        } else if (currentScrollTop < (COMPACT_THRESHOLD - SCROLL_HYSTERESIS)) {
            if (this.state.isCompact) {
                this.state.isCompact = false;
                this.onCompactStateChange(false);
            }
        }
        
        // Update top bar visibility
        const shouldHideTopBar = this.state.isScrollingDown && currentScrollTop > TOP_BAR_HIDE_THRESHOLD;
        if (shouldHideTopBar !== this.state.isTopBarHidden) {
            this.state.isTopBarHidden = shouldHideTopBar;
            this.onTopBarVisibilityChange(!shouldHideTopBar);
        }
    }

    /**
     * Apply scroll effects
     */
    applyScrollEffects() {
        if (!this.elements.header) return;
        
        const { currentScrollTop } = this.state;
        
        // Batch DOM updates
        this.elements.header.style.setProperty('--scroll-progress', Math.min(1, currentScrollTop / 100));
        
        // Apply transform for smooth scrolling effect
        if (this.config.ENABLE_HARDWARE_ACCELERATION) {
            const translateY = this.state.isTopBarHidden ? '-100%' : '0%';
            this.elements.topBar?.style.setProperty('transform', `translateY(${translateY})`);
        }
    }

    /**
     * Handle scroll state changes
     */
    onScrolledStateChange(isScrolled) {
        if (!this.elements.header) return;
        
        this.elements.header.classList.toggle('scrolled', isScrolled);
        
        // Handle logo transition
        if (this.config.ENABLE_LOGO_TRANSITION && this.elements.logoImage) {
            this.elements.logoImage.src = isScrolled ? this.state.darkLogoSrc : this.state.originalLogoSrc;
        }
        
        console.log(`🔄 Scrolled state: ${isScrolled}`);
    }

    /**
     * Handle compact state changes
     */
    onCompactStateChange(isCompact) {
        if (!this.elements.header) return;
        
        this.elements.header.classList.toggle('compact', isCompact);
        console.log(`🔄 Compact state: ${isCompact}`);
    }

    /**
     * Handle top bar visibility changes
     */
    onTopBarVisibilityChange(isVisible) {
        if (!this.elements.topBar) return;
        
        this.elements.topBar.classList.toggle('hidden', !isVisible);
        console.log(`🔄 Top bar visible: ${isVisible}`);
    }

    /**
     * Handle scroll stop
     */
    handleScrollStop() {
        clearTimeout(this.state.scrollStopTimer);
        this.state.scrollStopTimer = setTimeout(() => {
            this.onScrollStop();
        }, this.config.DEBOUNCE_DELAY);
    }

    /**
     * Handle scroll stop event
     */
    onScrollStop() {
        if (this.elements.header) {
            this.elements.header.classList.add('scroll-stopped');
            setTimeout(() => {
                this.elements.header?.classList.remove('scroll-stopped');
            }, this.config.TRANSITION_DURATION);
        }
        
        console.log('⏸️ Scroll stopped');
    }

    /**
     * Handle resize
     */
    handleResize() {
        this.checkInitialScrollPosition();
        console.log('📐 Window resized');
    }

    /**
     * Handle visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Pause performance monitoring when tab is hidden
            this.pausePerformanceMonitoring();
        } else {
            // Resume performance monitoring when tab is visible
            this.resumePerformanceMonitoring();
        }
    }

    /**
     * Check initial scroll position
     */
    checkInitialScrollPosition() {
        const scrollTop = window.pageYOffset || this.elements.html.scrollTop;
        if (scrollTop > 0) {
            this.state.currentScrollTop = scrollTop;
            this.updateScrollStates();
            this.applyScrollEffects();
        }
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        if (!this.config.ENABLE_PERFORMANCE_MONITORING) return;
        
        this.monitorFPS();
        this.monitorLayoutShifts();
        
        console.log('📊 Performance monitoring enabled');
    }

    /**
     * Monitor FPS
     */
    monitorFPS() {
        let frames = 0;
        let lastTime = performance.now();
        
        const measureFrame = (currentTime) => {
            frames++;
            if (currentTime - lastTime >= 1000) {
                this.metrics.averageFPS = Math.round((frames * 1000) / (currentTime - lastTime));
                frames = 0;
                lastTime = currentTime;
                
                if (this.metrics.averageFPS < 50) {
                    this.metrics.frameDrops++;
                    console.warn(`⚠️ Low FPS detected: ${this.metrics.averageFPS}`);
                }
            }
            
            if (!document.hidden) {
                requestAnimationFrame(measureFrame);
            }
        };
        
        requestAnimationFrame(measureFrame);
    }

    /**
     * Monitor layout shifts
     */
    monitorLayoutShifts() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.value > 0.1) {
                        console.warn(`⚠️ Layout shift: ${entry.value}`);
                    }
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    /**
     * Pause performance monitoring
     */
    pausePerformanceMonitoring() {
        console.log('⏸️ Performance monitoring paused');
    }

    /**
     * Resume performance monitoring
     */
    resumePerformanceMonitoring() {
        console.log('▶️ Performance monitoring resumed');
    }

    /**
     * Utility: Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            scrollEventsPerSecond: this.metrics.scrollEvents / ((performance.now() - this.state.lastUpdateTime) / 1000),
            currentFPS: this.metrics.averageFPS,
            scrollVelocity: this.state.scrollVelocity
        };
    }

    /**
     * Destroy handler
     */
    destroy() {
        // Cancel pending frames
        if (this.state.frameId) {
            cancelAnimationFrame(this.state.frameId);
        }
        
        // Clear timers
        clearTimeout(this.state.debounceTimer);
        clearTimeout(this.state.scrollStopTimer);
        
        // Remove event listeners
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        console.log('🗑️ Unified Header Handler destroyed');
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Check if we should use unified handler
    const useUnified = new URLSearchParams(window.location.search).get('unified_header') === 'true' ||
                      localStorage.getItem('use_unified_header') === 'true';
    
    if (useUnified) {
        window.unifiedHeaderHandler = new UnifiedHeaderHandler({
            enablePerformanceMonitoring: true,
            enableVelocityTracking: true
        });
        
        console.log('🎯 Unified Header Handler active');
    } else {
        console.log('💡 Add ?unified_header=true to URL to enable unified handler');
    }
});

// Export for manual usage
window.UnifiedHeaderHandler = UnifiedHeaderHandler;

/**
 * CSS Injection for Unified Header Optimization
 */
function injectOptimizedCSS() {
    const css = `
        /* Unified Header Optimizations */
        .unified-optimized {
            contain: layout style paint;
            will-change: transform;
            transform: translateZ(0);
        }

        .unified-optimized * {
            box-sizing: border-box;
        }

        /* Smooth transitions */
        .unified-optimized {
            transition:
                transform var(--transition-duration, 300ms) var(--easing-function, ease-out),
                background-color var(--transition-duration, 300ms) var(--easing-function, ease-out),
                box-shadow var(--transition-duration, 300ms) var(--easing-function, ease-out);
        }

        /* Top bar optimizations */
        .unified-optimized .top-bar {
            will-change: transform;
            transform: translateZ(0);
            transition: transform var(--transition-duration, 300ms) var(--easing-function, ease-out);
        }

        .unified-optimized .top-bar.hidden {
            transform: translateY(-100%) translateZ(0);
        }

        /* Scroll states */
        .unified-optimized.scrolled {
            background-color: rgba(32, 40, 52, 0.95);
            backdrop-filter: blur(10px);
        }

        .unified-optimized.compact {
            transform: scale(0.98) translateZ(0);
        }

        .unified-optimized.scroll-stopped {
            transition-duration: calc(var(--transition-duration, 300ms) * 1.5);
        }

        /* Performance optimizations */
        .unified-optimized .premium-logo-image img,
        .unified-optimized .luxury-logo-image img {
            will-change: auto;
            transition: opacity var(--transition-duration, 300ms) var(--easing-function, ease-out);
        }

        /* Disable animations on low-end devices */
        @media (prefers-reduced-motion: reduce) {
            .unified-optimized,
            .unified-optimized * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* GPU acceleration hints */
        .unified-optimized .search-container,
        .unified-optimized .user-actions {
            transform: translateZ(0);
        }
    `;

    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);

    console.log('🎨 Optimized CSS injected');
}

// Inject CSS when handler is initialized
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectOptimizedCSS);
} else {
    injectOptimizedCSS();
}
