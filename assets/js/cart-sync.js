/**
 * Cart Synchronization JavaScript
 * Đảm bảo giỏ hàng được đồng bộ hóa giữa session và localStorage
 */

// Biến toàn cục để theo dõi trạng thái đồng bộ hóa
window.cartSyncInProgress = false;
window.cartSyncInitialized = false;
window.lastKnownCartCount = -1; // -1 nghĩa là chưa biết

// Đảm bảo DOM đã được tải
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart sync initialized');

    // Đồng bộ hóa giỏ hàng khi trang được tải
    initializeCartSync();

    // Thiết lập interval để kiểm tra và đồng bộ hóa giỏ hàng mỗi 2 giây
    setInterval(syncCartBadges, 2000);

    // Đăng ký sự kiện cho các nút thêm vào giỏ hàng
    registerAddToCartEvents();

    // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
    // document.addEventListener('click', function(e) {
    //     if (e.target && (e.target.classList.contains('add-to-cart-btn') ||
    //                     e.target.classList.contains('quick-add-to-cart-btn') ||
    //                     e.target.closest('.add-to-cart-btn') ||
    //                     e.target.closest('.quick-add-to-cart-btn'))) {
    //         // Đảm bảo giỏ hàng được đồng bộ hóa sau khi thêm sản phẩm
    //         setTimeout(syncCartBadges, 500);
    //     }
    // });
});

/**
 * Khởi tạo đồng bộ hóa giỏ hàng
 */
function initializeCartSync() {
    if (window.cartSyncInitialized) {
        return;
    }

    window.cartSyncInitialized = true;

    // Đồng bộ hóa giỏ hàng ngay lập tức
    syncCartBadges();

    // Đồng bộ hóa lại sau 1 giây để đảm bảo dữ liệu được cập nhật
    setTimeout(syncCartBadges, 1000);
}

/**
 * Đồng bộ hóa số lượng giỏ hàng giữa session và localStorage
 */
function syncCartBadges() {
    // Tránh đồng bộ hóa trùng lặp
    if (window.cartSyncInProgress) {
        return;
    }

    window.cartSyncInProgress = true;

    // Lấy số lượng giỏ hàng từ server
    fetch(`${BASE_URL}/ajax/get_cart_count.php`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Lấy số lượng giỏ hàng từ localStorage
                const localCartCount = parseInt(localStorage.getItem('cartCount') || '0');

                // Nếu số lượng khác nhau hoặc chưa biết số lượng trước đó, cập nhật localStorage và UI
                if (data.count !== localCartCount || window.lastKnownCartCount !== data.count) {
                    console.log('Syncing cart count:', {
                        serverCount: data.count,
                        localCount: localCartCount,
                        lastKnownCount: window.lastKnownCartCount
                    });

                    // Cập nhật localStorage
                    localStorage.setItem('cartCount', data.count.toString());

                    // Cập nhật biến toàn cục
                    window.lastKnownCartCount = data.count;

                    // Cập nhật UI
                    updateAllCartBadges(data.count);

                    // Cập nhật thời gian cập nhật cuối cùng
                    localStorage.setItem('cartLastUpdated', Date.now().toString());
                }
            }

            window.cartSyncInProgress = false;
        })
        .catch(error => {
            console.error('Error syncing cart count:', error);
            window.cartSyncInProgress = false;
        });
}

/**
 * Cập nhật tất cả các badge giỏ hàng trên trang
 */
function updateAllCartBadges(count) {
    // Tìm tất cả các badge giỏ hàng
    const cartBadges = document.querySelectorAll('.cart-badge');
    const mobileBadge = document.querySelector('.mobile-badge');
    const mobileNavBadge = document.querySelector('.mobile-nav-badge');

    console.log('Updating all cart badges:', {
        count: count,
        cartBadges: cartBadges.length,
        hasMobileBadge: !!mobileBadge,
        hasMobileNavBadge: !!mobileNavBadge
    });

    // Cập nhật tất cả các badge desktop
    cartBadges.forEach(badge => {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'flex';

            // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
            // badge.classList.add('badge-pulse');
            setTimeout(() => {
                // badge.classList.remove('badge-pulse');
            }, 1000);
        } else {
            badge.style.display = 'none';
        }
    });

    // Cập nhật badge trên mobile menu
    if (mobileBadge) {
        if (count > 0) {
            mobileBadge.textContent = count;
            mobileBadge.style.display = 'inline-block';

            // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
            // mobileBadge.classList.add('badge-pulse');
            setTimeout(() => {
                // mobileBadge.classList.remove('badge-pulse');
            }, 1000);
        } else {
            mobileBadge.style.display = 'none';
        }
    }

    // Cập nhật badge trên mobile bottom navigation
    // Sử dụng hàm updateMobileCartBadge từ mobile-cart-badge-fix.js nếu có
    if (typeof window.updateMobileCartBadge === 'function') {
        window.updateMobileCartBadge(count);
    }
    // Nếu không có, sử dụng cách cập nhật thông thường
    else if (mobileNavBadge) {
        if (count > 0) {
            mobileNavBadge.textContent = count > 99 ? '99+' : count;
            mobileNavBadge.setAttribute('data-count', count > 99 ? '99+' : count);
            mobileNavBadge.style.display = 'flex';

            // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
            // mobileNavBadge.classList.add('badge-pulse');
            setTimeout(() => {
                // mobileNavBadge.classList.remove('badge-pulse');
            }, 1000);
        } else {
            mobileNavBadge.style.display = 'none';
        }
    }

    // Kiểm tra nếu không có badge nào được tìm thấy, tạo mới
    if ((cartBadges.length === 0 || Array.from(cartBadges).every(badge => badge.style.display === 'none')) && count > 0) {
        console.log('No visible cart badge found, creating new one');
        const cartContainer = document.querySelector('.cart-container');
        if (cartContainer) {
            // Xóa badge cũ nếu có
            const oldBadge = cartContainer.querySelector('.cart-badge');
            if (oldBadge) {
                oldBadge.remove();
            }

            // Tạo badge mới
            const badge = document.createElement('span');
            badge.className = 'cart-badge';
            badge.textContent = count;
            badge.style.display = 'flex';

            // Thêm badge vào cart-container
            cartContainer.appendChild(badge);

            // Thêm hiệu ứng nhấp nháy - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
            // badge.classList.add('badge-pulse');
            setTimeout(() => {
                // badge.classList.remove('badge-pulse');
            }, 1000);

            console.log('Created new cart badge in cart-container');
        }
    }
}

/**
 * Đăng ký sự kiện cho các nút thêm vào giỏ hàng
 */
function registerAddToCartEvents() {
    // Tìm tất cả các nút thêm vào giỏ hàng
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn, .quick-add-to-cart-btn');

    console.log('Registering add to cart events for', addToCartButtons.length, 'buttons');

    // LOẠI BỎ event listener trùng lặp - đã được xử lý trong cart-realtime.js
    // addToCartButtons.forEach(button => {
    //     // Kiểm tra xem nút đã được đăng ký sự kiện chưa
    //     if (!button.dataset.cartSyncRegistered) {
    //         button.addEventListener('click', function(e) {
    //             console.log('Add to cart button clicked', this);
    //             // Đảm bảo giỏ hàng được đồng bộ hóa sau khi thêm sản phẩm
    //             setTimeout(syncCartBadges, 300);
    //             setTimeout(syncCartBadges, 800);
    //             setTimeout(syncCartBadges, 1500);
    //             setTimeout(function() {
    //                 const cartCount = parseInt(localStorage.getItem('cartCount') || '0');
    //                 if (cartCount > 0) {
    //                     updateAllCartBadges(cartCount);
    //                 }
    //             }, 1000);
    //         });
    //         // Đánh dấu nút đã được đăng ký sự kiện
    //         button.dataset.cartSyncRegistered = 'true';
    //     }
    // });
}

/**
 * Xử lý sự kiện thêm vào giỏ hàng từ AJAX
 * Hàm này được gọi từ các file JavaScript khác sau khi thêm sản phẩm vào giỏ hàng thành công
 * @param {number} count - Số lượng sản phẩm trong giỏ hàng
 * @param {Object} data - Dữ liệu trả về từ API add_to_cart.php
 */
function handleAddToCartSuccess(count, data) {
    console.log('handleAddToCartSuccess called with count:', count, 'and data:', data);

    // Kiểm tra tham số
    if (count === undefined || count === null) {
        console.error('Invalid count provided to handleAddToCartSuccess');
        // Sử dụng data.count nếu có
        if (data && data.count !== undefined) {
            count = data.count;
            console.log('Using count from data:', count);
        } else {
            // Fallback to current cart count
            count = parseInt(localStorage.getItem('cartCount') || '0');
            console.log('Using fallback count from localStorage:', count);
        }
    }

    // Cập nhật localStorage (chỉ nếu chưa được cập nhật)
    const currentStoredCount = localStorage.getItem('cartCount');
    if (currentStoredCount !== count.toString()) {
        localStorage.setItem('cartCount', count.toString());
    }

    // Cập nhật biến toàn cục
    window.lastKnownCartCount = count;

    // KHÔNG cập nhật UI badges ở đây nữa vì đã được cập nhật trong cart-realtime.js
    // Điều này tránh việc cập nhật trùng lặp và đảm bảo badge được cập nhật ngay lập tức
    console.log('handleAddToCartSuccess: Badge update skipped to avoid duplication');

    // Cập nhật mini cart với dữ liệu trả về từ API
    if (data) {
        updateMiniCartAfterAdd(data);
    } else {
        console.error('No data provided to handleAddToCartSuccess for mini cart update');
        // Fallback to AJAX request
        updateMiniCartAfterAdd(null);
    }

    // Cập nhật thời gian cập nhật cuối cùng
    localStorage.setItem('cartLastUpdated', Date.now().toString());
}

/**
 * Cập nhật mini cart sau khi thêm sản phẩm vào giỏ hàng
 * @param {Object} data - Dữ liệu trả về từ API add_to_cart.php
 */
function updateMiniCartAfterAdd(data) {
    console.log('updateMiniCartAfterAdd called with data:', data);

    // Kiểm tra xem hàm updateMiniCartFromAddToCartResponse có tồn tại không
    if (typeof window.updateMiniCartFromAddToCartResponse === 'function') {
        try {
            // Sử dụng dữ liệu trả về trực tiếp từ add_to_cart.php
            const updated = window.updateMiniCartFromAddToCartResponse(data);

            // Nếu cập nhật thành công, không cần gửi yêu cầu AJAX mới
            if (updated) {
                console.log('Mini cart updated directly from response data');
                return;
            }
        } catch (error) {
            console.error('Error updating mini cart from response:', error);
        }
    }

    console.log('Falling back to AJAX request for cart data');

    // Nếu không có dữ liệu hoặc không thể cập nhật trực tiếp, gửi yêu cầu AJAX mới
    if (typeof window.updateMiniCart === 'function') {
        // Gửi yêu cầu AJAX để lấy thông tin giỏ hàng hiện tại
        fetch(`${BASE_URL}/ajax/get_cart_data.php`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Cart data received:', data);
                if (data.success) {
                    // Cập nhật mini cart
                    window.updateMiniCart(data.count, data.items, data.total);
                } else {
                    console.error('Cart data response not successful:', data);
                }
            })
            .catch(error => console.error('Error updating mini cart:', error));
    } else {
        console.error('updateMiniCart function not available');
    }
}

// Đảm bảo các hàm có thể được gọi từ các file JavaScript khác
window.syncCartBadges = syncCartBadges;
window.updateAllCartBadges = updateAllCartBadges;
window.handleAddToCartSuccess = handleAddToCartSuccess;
window.updateMiniCartAfterAdd = updateMiniCartAfterAdd;
