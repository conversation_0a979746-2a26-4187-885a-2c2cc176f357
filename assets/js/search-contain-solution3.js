/**
 * <PERSON><PERSON><PERSON>i pháp 3: CSS Contain Property
 * JavaScript để hỗ trợ CSS contain và fallback cho browsers cũ
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        headerSelector: '.header-section-solution3',
        searchInputSelector: '#main-search-input',
        suggestionsSelector: '#search-suggestions',
        containerSelector: '.search-container-solution3',
        showClass: 'show',
        debugClass: 'debug',
        debounceDelay: 150
    };

    // State management
    let isInitialized = false;
    let headerElement = null;
    let searchInput = null;
    let suggestionsElement = null;
    let containerElement = null;
    let debounceTimer = null;
    let supportsContain = false;
    let supportsIsolation = false;

    /**
     * Initialize the solution
     */
    function init() {
        if (isInitialized) return;

        // Check browser support
        checkBrowserSupport();

        // Find elements
        headerElement = document.querySelector(CONFIG.headerSelector);
        searchInput = document.querySelector(CONFIG.searchInputSelector);
        suggestionsElement = document.querySelector(CONFIG.suggestionsSelector);
        containerElement = document.querySelector(CONFIG.containerSelector);

        if (!headerElement || !searchInput || !suggestionsElement) {
            console.warn('Search Contain Solution 3: Required elements not found');
            return;
        }

        // Setup elements
        setupElements();

        // Setup event listeners
        setupEventListeners();

        // Apply fallbacks if needed
        applyFallbacks();

        isInitialized = true;
        console.log('Search Contain Solution 3: Initialized successfully', {
            supportsContain,
            supportsIsolation
        });
    }

    /**
     * Check browser support for CSS features
     */
    function checkBrowserSupport() {
        // Check CSS contain support
        supportsContain = CSS.supports('contain', 'layout') ||
                         CSS.supports('contain', 'layout style');

        // Check CSS isolation support
        supportsIsolation = CSS.supports('isolation', 'isolate');

        console.log('Browser support:', {
            contain: supportsContain,
            isolation: supportsIsolation
        });
    }

    /**
     * Setup elements with solution-specific classes
     */
    function setupElements() {
        // Add solution-specific classes
        if (headerElement) {
            headerElement.classList.add('header-section-solution3');
        }

        if (containerElement) {
            containerElement.classList.add('search-container-solution3');
        }

        if (suggestionsElement) {
            suggestionsElement.classList.add('search-suggestions-solution3');
        }

        if (searchInput) {
            searchInput.classList.add('search-input-solution3');
        }
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Search input events
        searchInput.addEventListener('focus', handleSearchFocus);
        searchInput.addEventListener('blur', handleSearchBlur);
        searchInput.addEventListener('input', handleSearchInput);

        // Suggestions events
        suggestionsElement.addEventListener('mouseenter', handleSuggestionsMouseEnter);
        suggestionsElement.addEventListener('mouseleave', handleSuggestionsMouseLeave);

        // Global events
        document.addEventListener('click', handleGlobalClick);
        document.addEventListener('keydown', handleKeyDown);

        // Performance monitoring
        if (window.PerformanceObserver) {
            setupPerformanceMonitoring();
        }
    }

    /**
     * Apply fallbacks for unsupported browsers
     */
    function applyFallbacks() {
        if (!supportsContain) {
            console.log('CSS contain not supported, applying fallback');

            // Fallback to overflow visible approach
            if (headerElement) {
                headerElement.style.overflow = 'visible';
                headerElement.style.contain = 'none';
            }

            if (suggestionsElement) {
                suggestionsElement.style.zIndex = '9999';
            }
        }

        if (!supportsIsolation) {
            console.log('CSS isolation not supported, applying fallback');

            // Fallback to higher z-index
            if (containerElement) {
                containerElement.style.zIndex = '9998';
            }

            if (suggestionsElement) {
                suggestionsElement.style.zIndex = '9999';
            }
        }
    }

    /**
     * Setup performance monitoring
     */
    function setupPerformanceMonitoring() {
        try {
            const observer = new PerformanceObserver(function(list) {
                const entries = list.getEntries();
                entries.forEach(function(entry) {
                    if (entry.entryType === 'layout-shift' && entry.value > 0.1) {
                        console.warn('Layout shift detected:', entry.value);
                    }
                });
            });

            observer.observe({ entryTypes: ['layout-shift'] });
        } catch (error) {
            console.log('Performance monitoring not available');
        }
    }

    /**
     * Handle search input focus
     */
    function handleSearchFocus() {
        debounce(function() {
            if (shouldShowSuggestions()) {
                showSuggestions();
            }
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle search input blur
     */
    function handleSearchBlur() {
        debounce(function() {
            if (!isMouseOverSuggestions()) {
                hideSuggestions();
            }
        }, 200);
    }

    /**
     * Handle search input
     */
    function handleSearchInput() {
        debounce(function() {
            // Trigger suggestions update if needed
            if (shouldShowSuggestions()) {
                showSuggestions();
            } else {
                hideSuggestions();
            }
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle suggestions mouse enter
     */
    function handleSuggestionsMouseEnter() {
        clearTimeout(debounceTimer);
    }

    /**
     * Handle suggestions mouse leave
     */
    function handleSuggestionsMouseLeave() {
        if (!searchInput.matches(':focus')) {
            debounce(hideSuggestions, 200);
        }
    }

    /**
     * Handle global click
     */
    function handleGlobalClick(event) {
        if (!searchInput.contains(event.target) &&
            !suggestionsElement.contains(event.target)) {
            hideSuggestions();
        }
    }

    /**
     * Handle keyboard events
     */
    function handleKeyDown(event) {
        if (event.key === 'Escape') {
            hideSuggestions();
            searchInput.blur();
        }
    }

    /**
     * Show suggestions
     */
    function showSuggestions() {
        if (!suggestionsElement) return;

        suggestionsElement.classList.add(CONFIG.showClass);
        suggestionsElement.classList.remove('hidden');

        // Set ARIA attributes
        suggestionsElement.setAttribute('aria-hidden', 'false');
        searchInput.setAttribute('aria-expanded', 'true');
    }

    /**
     * Hide suggestions
     */
    function hideSuggestions() {
        if (!suggestionsElement) return;

        suggestionsElement.classList.remove(CONFIG.showClass);
        suggestionsElement.classList.add('hidden');

        // Set ARIA attributes
        suggestionsElement.setAttribute('aria-hidden', 'true');
        searchInput.setAttribute('aria-expanded', 'false');
    }

    /**
     * Check if suggestions should be shown
     */
    function shouldShowSuggestions() {
        return searchInput.value.trim().length > 0 || searchInput.matches(':focus');
    }

    /**
     * Check if mouse is over suggestions
     */
    function isMouseOverSuggestions() {
        return suggestionsElement.matches(':hover');
    }

    /**
     * Debounce function
     */
    function debounce(func, delay) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(func, delay);
    }

    /**
     * Enable debug mode
     */
    function enableDebugMode() {
        if (headerElement) headerElement.classList.add(CONFIG.debugClass);
        if (containerElement) containerElement.classList.add(CONFIG.debugClass);
        if (suggestionsElement) suggestionsElement.classList.add(CONFIG.debugClass);

        console.log('Debug mode enabled for Search Contain Solution 3');
    }

    /**
     * Disable debug mode
     */
    function disableDebugMode() {
        if (headerElement) headerElement.classList.remove(CONFIG.debugClass);
        if (containerElement) containerElement.classList.remove(CONFIG.debugClass);
        if (suggestionsElement) suggestionsElement.classList.remove(CONFIG.debugClass);

        console.log('Debug mode disabled for Search Contain Solution 3');
    }

    /**
     * Get browser support info
     */
    function getBrowserSupport() {
        return {
            contain: supportsContain,
            isolation: supportsIsolation,
            userAgent: navigator.userAgent
        };
    }

    /**
     * Public API
     */
    window.SearchContainSolution3 = {
        init: init,
        showSuggestions: showSuggestions,
        hideSuggestions: hideSuggestions,
        enableDebugMode: enableDebugMode,
        disableDebugMode: disableDebugMode,
        getBrowserSupport: getBrowserSupport
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();