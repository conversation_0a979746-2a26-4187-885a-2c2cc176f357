/**
 * Simple Notification Modal Enhancement
 * Thiết kế lại #simple-notification giống delete-confirmation-modal
 * Sửa lỗi màu sắc và cải thiện UX
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple Notification Modal Enhancement loaded');

    // Observer để theo dõi khi #simple-notification được thêm vào DOM
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE && node.id === 'simple-notification') {
                    enhanceNotification(node);
                }
            });
        });
    });

    // Bắt đầu observe
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Enhance notification đã có sẵn (nếu có)
    const existingNotification = document.getElementById('simple-notification');
    if (existingNotification) {
        enhanceNotification(existingNotification);
    }

    /**
     * Enhance notification với cấu trúc mới giống delete-confirmation-modal - Optimized
     */
    function enhanceNotification(notification) {
        // Lấy nội dung gốc một lần
        const originalText = notification.querySelector('span');
        const messageText = originalText ? originalText.textContent.trim() : 'Thông báo';

        // Xác định loại thông báo từ nội dung hoặc style
        const notificationType = detectNotificationType(notification, null, originalText);

        // Reset styles và tạo structure trong một lần DOM update
        resetNotificationStyles(notification);

        // Clear existing content và tạo DOM elements thay vì innerHTML để tránh parsing overhead
        notification.innerHTML = '';
        const structure = createOptimizedModalStructure(notificationType, messageText);

        // Single DOM update
        notification.appendChild(structure);

        // Batch DOM updates
        requestAnimationFrame(() => {
            notification.classList.add(`notification-${notificationType}`);
            ensureNotificationInBody(notification);
            document.body.classList.add('notification-active');

            // Immediate show without nested timeouts
            notification.style.opacity = '1';
            notification.style.visibility = 'visible';
            notification.classList.add('show');
        });

        // Setup events và auto-close
        setupEventListeners(notification);
        setupAutoClose(notification);
    }

    /**
     * Reset tất cả style có thể gây conflict
     */
    function resetNotificationStyles(notification) {
        // Xóa tất cả style inline
        notification.removeAttribute('style');

        // Xóa tất cả class có thể gây conflict
        const conflictClasses = [
            'fixed', 'absolute', 'relative', 'static', 'sticky',
            'top-0', 'left-0', 'right-0', 'bottom-0', 'inset-0',
            'w-full', 'h-full', 'w-screen', 'h-screen',
            'flex', 'grid', 'block', 'inline-block', 'inline',
            'container', 'row', 'col'
        ];

        conflictClasses.forEach(cls => {
            notification.classList.remove(cls);
        });

        // Đặt lại các style cần thiết bằng JavaScript
        notification.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 2147483647 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            margin: 0 !important;
            padding: 0 !important;
            transform: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: opacity 0.3s ease, visibility 0.3s ease !important;
        `;
    }

    /**
     * Đảm bảo notification được append vào body
     */
    function ensureNotificationInBody(notification) {
        // Nếu notification không phải là con trực tiếp của body
        if (notification.parentNode !== document.body) {
            // Di chuyển nó vào body
            document.body.appendChild(notification);
        }
    }

    /**
     * Xác định loại thông báo từ nội dung và icon
     */
    function detectNotificationType(notification, icon, textElement) {
        // Kiểm tra từ inline style (cách cũ)
        const bgColor = notification.style.backgroundColor;
        if (bgColor) {
            if (bgColor.includes('#EF4444') || bgColor.includes('rgb(239, 68, 68)')) {
                return 'error';
            }
            if (bgColor.includes('#F59E0B') || bgColor.includes('rgb(245, 158, 11)')) {
                return 'warning';
            }
            if (bgColor.includes('#3B82F6') || bgColor.includes('rgb(59, 130, 246)')) {
                return 'info';
            }
        }

        // Kiểm tra từ icon
        if (icon) {
            const iconClass = icon.className;
            if (iconClass.includes('fa-times') || iconClass.includes('fa-exclamation') || iconClass.includes('fa-ban')) {
                return 'error';
            }
            if (iconClass.includes('fa-warning') || iconClass.includes('fa-exclamation-triangle')) {
                return 'warning';
            }
            if (iconClass.includes('fa-info')) {
                return 'info';
            }
            if (iconClass.includes('fa-check') || iconClass.includes('fa-success')) {
                return 'success';
            }
        }

        // Kiểm tra từ nội dung text
        if (textElement) {
            const text = textElement.textContent.toLowerCase();
            if (text.includes('lỗi') || text.includes('thất bại') || text.includes('không thể') || text.includes('hết hàng')) {
                return 'error';
            }
            if (text.includes('cảnh báo') || text.includes('chú ý')) {
                return 'warning';
            }
            if (text.includes('thông tin')) {
                return 'info';
            }
        }

        // Mặc định là success
        return 'success';
    }

    /**
     * Tạo cấu trúc DOM optimized thay vì innerHTML
     */
    function createOptimizedModalStructure(type, messageText) {
        const config = getNotificationConfig(type);

        // Tạo elements một cách tối ưu
        const content = document.createElement('div');
        content.className = 'notification-content';

        const header = document.createElement('div');
        header.className = 'notification-header';

        const iconContainer = document.createElement('div');
        iconContainer.className = 'notification-icon';

        const icon = document.createElement('i');
        icon.className = config.icon;

        const body = document.createElement('div');
        body.className = 'notification-body';

        const title = document.createElement('h3');
        title.className = 'notification-title';
        title.textContent = config.title;

        const message = document.createElement('p');
        message.className = 'notification-message';
        message.textContent = messageText;

        const closeButton = document.createElement('button');
        closeButton.type = 'button';
        closeButton.className = 'notification-close';

        const closeIcon = document.createElement('i');
        closeIcon.className = 'fas fa-times';

        const closeText = document.createElement('span');
        closeText.textContent = 'Đóng';

        // Assemble structure efficiently
        iconContainer.appendChild(icon);
        header.appendChild(iconContainer);

        closeButton.appendChild(closeIcon);
        closeButton.appendChild(closeText);

        body.appendChild(title);
        body.appendChild(message);
        body.appendChild(closeButton);

        content.appendChild(header);
        content.appendChild(body);

        return content;
    }

    /**
     * Lấy config cho từng loại thông báo
     */
    function getNotificationConfig(type) {
        const configs = {
            success: {
                icon: 'fas fa-check',
                title: 'Thành công!'
            },
            error: {
                icon: 'fas fa-times',
                title: 'Có lỗi xảy ra!'
            },
            warning: {
                icon: 'fas fa-exclamation-triangle',
                title: 'Cảnh báo!'
            },
            info: {
                icon: 'fas fa-info-circle',
                title: 'Thông tin'
            }
        };

        return configs[type] || configs.success;
    }

    /**
     * Thiết lập event listeners - Optimized
     */
    function setupEventListeners(notification) {
        // Optimized event handlers
        const handleClose = (e) => {
            e.preventDefault();
            closeNotificationWithAnimation(notification);
        };

        const handleEscKey = (e) => {
            if (e.key === 'Escape') {
                closeNotificationWithAnimation(notification);
                document.removeEventListener('keydown', handleEscKey);
            }
        };

        const handleBackdropClick = (e) => {
            if (e.target === notification) {
                closeNotificationWithAnimation(notification);
            }
        };

        const handleContentClick = (e) => {
            e.stopPropagation();
        };

        // Single query và attach events
        const closeButton = notification.querySelector('.notification-close');
        const content = notification.querySelector('.notification-content');

        if (closeButton) {
            closeButton.addEventListener('click', handleClose, { passive: false });
        }
        if (content) {
            content.addEventListener('click', handleContentClick, { passive: true });
        }

        document.addEventListener('keydown', handleEscKey, { passive: true });
        notification.addEventListener('click', handleBackdropClick, { passive: true });

        // Store references for cleanup
        notification._eventHandlers = { handleEscKey, handleClose, handleBackdropClick, handleContentClick };
    }

    /**
     * Đóng notification với animation mượt - Optimized
     */
    function closeNotificationWithAnimation(notification) {
        if (!notification || notification.classList.contains('closing')) {
            return;
        }

        // Cleanup event listeners trước khi đóng
        if (notification._eventHandlers) {
            document.removeEventListener('keydown', notification._eventHandlers.handleEscKey);
        }

        // Batch DOM updates
        requestAnimationFrame(() => {
            notification.classList.add('closing');
            notification.classList.remove('show');
            notification.style.opacity = '0';
            notification.style.visibility = 'hidden';
            document.body.classList.remove('notification-active');
        });

        // Optimized cleanup với requestAnimationFrame
        setTimeout(() => {
            if (notification && notification.parentNode) {
                notification.remove();
            }
        }, 400);
    }

    /**
     * Thiết lập auto-close
     */
    function setupAutoClose(notification) {
        // Auto-close sau 5 giây
        setTimeout(() => {
            if (notification && notification.parentNode && !notification.classList.contains('closing')) {
                closeNotificationWithAnimation(notification);
            }
        }, 5000);
    }

    // Audio function removed for performance optimization

});

// Override hàm showSimpleNotification toàn cục nếu có - Optimized
if (typeof window.showSimpleNotification === 'function') {
    const originalShowSimpleNotification = window.showSimpleNotification;

    window.showSimpleNotification = function(message, type, duration) {
        // Gọi hàm gốc
        const result = originalShowSimpleNotification.call(this, message, type, duration);

        // Optimized notification enhancement với requestAnimationFrame
        requestAnimationFrame(() => {
            const notification = document.getElementById('simple-notification');
            if (notification && !notification.classList.contains('notification-enhanced')) {
                notification.classList.add('notification-enhanced');
                // Enhance sẽ được thực hiện bởi MutationObserver
            }
        });

        return result;
    };
}
