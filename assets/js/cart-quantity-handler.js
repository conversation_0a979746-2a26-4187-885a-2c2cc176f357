/**
 * Cart Quantity Handler - Optimized Version
 * Xử lý tăng giảm số lượng sản phẩm trong giỏ hàng
 *
 * Cải tiến UX:
 * - Giảm thiểu thông báo không cần thiết cho thao tác thường (tăng/giảm số lượng)
 * - Chỉ hiển thị thông báo cho lỗi, cảnh báo và thành công quan trọng
 * - Giảm thời gian hiệu ứng highlight để nhẹ nhàng hơn
 * - Ưu tiên visual feedback thay vì text notification
 */

// Hàm cập nhật giỏ hàng thủ công (được gọi từ onclick)
function updateCartItemManual(productId, button) {
    // Tìm card chứa sản phẩm
    const card = button.closest('.bg-white.rounded-xl');

    // Tìm tất cả input số lượng dựa trên product_id
    const inputs = card.querySelectorAll(`.quantity-input[data-product-id="${productId}"]`);

    if (inputs.length === 0) {
        console.error('Không tìm thấy input số lượng cho sản phẩm ID:', productId);
        return false;
    }

    // Lấy input đầu tiên để lấy giá trị
    const input = inputs[0];
    const quantity = parseInt(input.value);
    if (isNaN(quantity) || quantity < 1) {
        console.error('Số lượng không hợp lệ:', quantity);
        showCartInlineNotification('Lỗi cập nhật', 'Số lượng sản phẩm không hợp lệ', 'error');
        return false;
    }

    // Kiểm tra số lượng tồn kho
    const maxQuantity = parseInt(input.dataset.maxQuantity || 0);
    if (maxQuantity > 0 && quantity > maxQuantity) {
        console.error('Số lượng vượt quá tồn kho:', quantity, 'max:', maxQuantity);
        const productName = input.closest('.bg-white.rounded-xl').querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();
        showCartInlineNotification('Số lượng tối đa', `Sản phẩm "${productName}" chỉ còn ${maxQuantity} trong kho`, 'warning');
        // Đặt lại giá trị tối đa
        input.value = maxQuantity;
        return false;
    }

    // Hiển thị hiệu ứng loading cho nút
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    // Bắt đầu skeleton loading cho order summary (không bao gồm nút checkout)
    startOrderSummarySkeletonLoading();

    // Hiển thị loading riêng cho nút checkout
    startCheckoutButtonUpdateLoading();

    // Lưu thời điểm bắt đầu để đảm bảo loading tối thiểu 1.2 giây
    const startTime = Date.now();
    const minLoadingTime = 1200; // 1.2 giây
    let updateSuccess = false; // Biến theo dõi trạng thái cập nhật thành công

    // Log để debug
    console.log('Cập nhật sản phẩm:', { productId, quantity });

    // Phát sự kiện để ngăn cart-item-selection can thiệp
    window.dispatchEvent(new CustomEvent('cart-updating-from-server'));

    // Gửi yêu cầu cập nhật đến server
    fetch(`${BASE_URL}/ajax/update_cart.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}&quantity=${quantity}`,
        cache: 'no-store' // Không sử dụng cache
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Update response:', data);

        if (data.success) {
            updateSuccess = true; // Đánh dấu cập nhật thành công
            // Sử dụng updateTotalPrice để cập nhật tổng tiền
            if (data.total) {
                // Thêm delay nhỏ để đảm bảo DOM đã sẵn sàng
                setTimeout(() => {
                    updateTotalPrice(data.total);
                }, 100);

                // Highlight toàn bộ phần tổng đơn hàng
                const orderSummary = document.querySelector('.bg-white.rounded-xl.shadow-sm.border.border-gray-100.overflow-hidden.sticky');
                if (orderSummary) {
                    orderSummary.classList.add('order-summary-updated');
                    setTimeout(() => orderSummary.classList.remove('order-summary-updated'), 2000);
                }
            }

            // Cập nhật số lượng giỏ hàng
            if (data.count !== undefined) {
                // Lưu vào localStorage
                localStorage.setItem('cartCount', data.count.toString());

                // Cập nhật badge
                document.querySelectorAll('.cart-badge').forEach(badge => {
                    badge.textContent = data.count;
                    badge.style.display = data.count > 0 ? 'flex' : 'none';

                    // Thêm hiệu ứng pulse nhẹ nhàng hơn
                    badge.classList.add('badge-pulse');
                    setTimeout(() => badge.classList.remove('badge-pulse'), 800);
                });

                // Cập nhật số lượng hiển thị trên trang (sử dụng count cho tổng số lượng sản phẩm)
                const countBadge = document.querySelector('.text-sm.text-gray-500.bg-white .font-medium.text-primary');
                if (countBadge && data.count !== undefined) {
                    countBadge.textContent = data.count;

                    // Thêm hiệu ứng highlight nhẹ nhàng hơn
                    countBadge.classList.add('highlight-change');
                    setTimeout(() => countBadge.classList.remove('highlight-change'), 1500);
                }

                // Phát sự kiện cập nhật giỏ hàng để các thành phần khác có thể lắng nghe
                const cartUpdatedEvent = new CustomEvent('cart:updated', {
                    detail: {
                        total: data.total,
                        count: data.count
                    },
                    bubbles: true
                });
                document.dispatchEvent(cartUpdatedEvent);
            }

            // Lưu thông tin thông báo để hiển thị sau khi loading hoàn thành
            const productName = input.closest('.bg-white.rounded-xl').querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();

            // Lưu thông tin thông báo vào biến tạm để hiển thị sau
            window.pendingSuccessNotification = {
                message: 'Cập nhật thành công',
                detail: `Đã cập nhật số lượng sản phẩm "${productName}" thành ${quantity}`,
                type: 'success'
            };

            // Cập nhật tổng tiền sản phẩm
            const card = input.closest('.bg-white.rounded-xl');
            if (card) {
                // Đồng bộ hóa tất cả input số lượng của sản phẩm này
                const allInputs = card.querySelectorAll(`.quantity-input[data-product-id="${productId}"]`);
                allInputs.forEach(inp => {
                    if (inp !== input) {
                        inp.value = quantity;
                    }
                });

                // Cập nhật dataset quantity cho tất cả checkbox của sản phẩm này
                const checkboxes = card.querySelectorAll('.cart-item-checkbox');
                checkboxes.forEach(checkbox => {
                    if (checkbox.dataset.productId === productId.toString()) {
                        checkbox.dataset.quantity = quantity;
                    }
                });

                // Cập nhật tổng tiền sản phẩm realtime
                updateProductTotalDisplay(card, productId, quantity);

                const priceElement = card.querySelector('.text-primary.font-medium');
                const totalElement = card.querySelector('.text-lg.font-semibold.text-primary-dark');

                if (priceElement && totalElement) {
                    // Lấy giá từ phần tử hiển thị đơn giá
                    const priceText = priceElement.textContent.trim();
                    const price = parseFloat(priceText.replace(/[^\d]/g, ''));

                    // Tính tổng tiền mới
                    const total = price * quantity;

                    // Cập nhật tổng tiền sản phẩm
                    totalElement.textContent = formatCurrency(total);

                    // Highlight phần tử đã thay đổi
                    totalElement.classList.add('highlight-change');
                    setTimeout(() => totalElement.classList.remove('highlight-change'), 2000);

                    // Thêm hiệu ứng scale
                    totalElement.classList.add('scale-animation');
                    setTimeout(() => totalElement.classList.remove('scale-animation'), 1000);
                }

                // Highlight card sản phẩm nhẹ nhàng hơn
                card.classList.add('highlight-change');
                setTimeout(() => {
                    card.classList.remove('highlight-change');
                }, 1500);

                // Thêm hiệu ứng nhẹ cho nút cập nhật
                button.classList.add('update-success');
                setTimeout(() => button.classList.remove('update-success'), 800);
            }
        } else {
            // Hiển thị thông báo lỗi ngay lập tức (không cần chờ loading)
            showCartInlineNotification('Lỗi cập nhật', data.message || 'Có lỗi xảy ra khi cập nhật giỏ hàng', 'error');

            // Dừng skeleton loading ngay khi có lỗi
            stopOrderSummarySkeletonLoading();
            // Dừng loading cho nút checkout
            stopCheckoutButtonUpdateLoading();
        }
    })
    .catch(error => {
        console.error('Error updating cart:', error);
        // Hiển thị thông báo lỗi
        showCartInlineNotification('Lỗi cập nhật', 'Có lỗi xảy ra khi kết nối đến máy chủ', 'error');

        // Dừng skeleton loading khi có lỗi kết nối
        stopOrderSummarySkeletonLoading();
        // Dừng loading cho nút checkout
        stopCheckoutButtonUpdateLoading();
    })
    .finally(() => {
        // Tính thời gian đã trôi qua
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

        // Đảm bảo loading hiển thị tối thiểu 1.2 giây
        setTimeout(() => {
            // Khôi phục nút cập nhật
            button.classList.remove('loading');
            button.innerHTML = originalText;
            button.disabled = false;

            // Dừng skeleton loading cho order summary
            stopOrderSummarySkeletonLoading();
            // Dừng loading cho nút checkout
            stopCheckoutButtonUpdateLoading();

            // Hiển thị thông báo thành công sau khi loading hoàn thành
            if (updateSuccess && window.pendingSuccessNotification) {
                const notificationData = window.pendingSuccessNotification;

                // Hiển thị thông báo với màu xanh lá cây
                const notification = document.getElementById('cart-inline-notification');
                const messageElement = document.getElementById('cart-notification-message');
                const detailElement = document.getElementById('cart-notification-detail');
                const iconElement = document.getElementById('cart-notification-icon');

                if (notification && messageElement && detailElement && iconElement) {
                    // Cập nhật nội dung
                    messageElement.textContent = notificationData.message;
                    detailElement.textContent = notificationData.detail;

                    // Cập nhật màu sắc và icon (xanh lá cây)
                    iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-green-500';
                    iconElement.innerHTML = '<i class="fas fa-check"></i>';

                    // Hiển thị thông báo
                    notification.classList.remove('hidden');
                    notification.classList.remove('hiding');

                    // Thêm hiệu ứng xuất hiện
                    notification.style.animation = 'none';
                    notification.offsetHeight; // Trigger reflow
                    notification.style.animation = 'slideDown 0.3s ease-out forwards';

                    // Tự động ẩn sau 5 giây
                    clearTimeout(window.cartNotificationTimeout);
                    window.cartNotificationTimeout = setTimeout(hideCartNotification, 5000);
                }

                // Xóa thông báo tạm
                delete window.pendingSuccessNotification;

                // Cuộn lên đầu trang để người dùng thấy thông báo
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });

                // Phát sự kiện để thông báo cập nhật hoàn thành
                setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('cart-update-completed'));

                    // Cập nhật tổng đơn hàng thông qua cart-item-selection.js
                    if (typeof window.updateTotal === 'function') {
                        window.updateTotal();
                    }
                }, 500);
            }
        }, remainingTime);
    });

    return false;
}

/**
 * Bắt đầu skeleton loading cho order summary
 */
function startOrderSummarySkeletonLoading() {
    console.log('Starting skeleton loading for order summary...');

    // Các selector cho các phần tử cần skeleton loading (5 phần được yêu cầu)
    const skeletonSelectors = [
        '.font-medium.cart-subtotal',
        '.text-green-600.font-medium',
        '.text-primary.font-medium',
        '.text-xl.font-bold.text-primary.cart-total'
    ];

    skeletonSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            // Kiểm tra để không áp dụng cho các phần tử không mong muốn
            const parentClasses = element.parentElement ? element.parentElement.className : '';
            const elementClasses = element.className;

            // Loại trừ các phần tử có class không mong muốn
            if (elementClasses.includes('text-sm text-gray-500 bg-white') ||
                parentClasses.includes('text-sm text-gray-500 bg-white')) {
                console.log(`Skipping skeleton for excluded element: ${selector}`);
                return;
            }

            // Lưu nội dung gốc
            if (!element.dataset.originalContent) {
                element.dataset.originalContent = element.textContent;
            }

            // Thêm class skeleton loading
            element.classList.add('skeleton-loading');

            console.log(`Applied skeleton loading to: ${selector}`);
        });
    });

    // KHÔNG xử lý nút checkout ở đây nữa - sẽ được xử lý riêng
    console.log('Skeleton loading applied to order summary elements (excluding checkout button)');
}

/**
 * Dừng skeleton loading cho order summary
 */
function stopOrderSummarySkeletonLoading() {
    console.log('Stopping skeleton loading for order summary...');

    // Các selector cho các phần tử cần dừng skeleton loading (4 phần chính)
    const skeletonSelectors = [
        '.font-medium.cart-subtotal',
        '.text-green-600.font-medium',
        '.text-primary.font-medium',
        '.text-xl.font-bold.text-primary.cart-total'
    ];

    skeletonSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            // Kiểm tra để không xử lý các phần tử không mong muốn
            const parentClasses = element.parentElement ? element.parentElement.className : '';
            const elementClasses = element.className;

            // Loại trừ các phần tử có class không mong muốn
            if (elementClasses.includes('text-sm text-gray-500 bg-white') ||
                parentClasses.includes('text-sm text-gray-500 bg-white')) {
                console.log(`Skipping skeleton removal for excluded element: ${selector}`);
                return;
            }

            // Loại bỏ class skeleton loading
            element.classList.remove('skeleton-loading');

            // Khôi phục nội dung gốc nếu có
            if (element.dataset.originalContent) {
                // Không khôi phục nội dung gốc vì đã được cập nhật với giá trị mới
                delete element.dataset.originalContent;
            }

            console.log(`Removed skeleton loading from: ${selector}`);
        });
    });

    // KHÔNG xử lý nút checkout ở đây nữa - sẽ được xử lý riêng
    console.log('Skeleton loading removed from order summary elements (excluding checkout button)');
}

/**
 * Bắt đầu hiệu ứng loading cho nút checkout khi cập nhật giỏ hàng
 */
function startCheckoutButtonUpdateLoading() {
    const checkoutButton = document.querySelector('#checkout-btn');
    if (!checkoutButton) {
        console.log('Checkout button not found for update loading');
        return;
    }

    // Lưu nội dung gốc nếu chưa có
    if (!checkoutButton.dataset.originalContent) {
        checkoutButton.dataset.originalContent = checkoutButton.innerHTML;
    }

    // Thêm class loading và thay đổi nội dung
    checkoutButton.classList.add('loading');
    checkoutButton.innerHTML = '<span class="checkout-spinner"></span> Đang cập nhật';

    console.log('Started update loading for checkout button');
}

/**
 * Dừng hiệu ứng loading cho nút checkout và khôi phục nội dung gốc
 */
function stopCheckoutButtonUpdateLoading() {
    const checkoutButton = document.querySelector('#checkout-btn');
    if (!checkoutButton) {
        console.log('Checkout button not found for stopping update loading');
        return;
    }

    // Loại bỏ class loading
    checkoutButton.classList.remove('loading');

    // Khôi phục nội dung gốc
    if (checkoutButton.dataset.originalContent) {
        checkoutButton.innerHTML = checkoutButton.dataset.originalContent;
        delete checkoutButton.dataset.originalContent;
    }

    console.log('Stopped update loading for checkout button');
}

/**
 * Kiểm tra xem CSS đã load xong chưa
 */
function isCSSLoaded() {
    const checkoutBtn = document.getElementById('checkout-btn');
    if (!checkoutBtn) return false;

    // Kiểm tra xem CSS của premium-checkout-btn đã được áp dụng chưa
    const computedStyle = window.getComputedStyle(checkoutBtn);
    const background = computedStyle.background || computedStyle.backgroundImage;

    // Nếu có gradient background thì CSS đã load
    return background.includes('gradient') || background.includes('linear-gradient');
}

/**
 * Tự động remove skeleton loading sau khi trang load xong
 */
function autoRemoveSkeletonLoading() {
    // Kiểm tra CSS đã load chưa
    function checkAndRemove() {
        if (isCSSLoaded()) {
            console.log('CSS loaded, removing skeleton loading...');
            stopOrderSummarySkeletonLoading();
            // Cũng dừng loading cho nút checkout nếu có
            stopCheckoutButtonUpdateLoading();
        } else {
            console.log('CSS not fully loaded yet, waiting...');
            // Thử lại sau 100ms
            setTimeout(checkAndRemove, 100);
        }
    }

    // Bắt đầu kiểm tra sau 200ms
    setTimeout(checkAndRemove, 200);
}

/**
 * Cập nhật tổng tiền giỏ hàng
 */
function updateTotalPrice(total) {
    if (total) {
        console.log('updateTotalPrice called with:', total);

        // Phát sự kiện để ngăn cart-item-selection can thiệp
        window.dispatchEvent(new CustomEvent('cart-updating-from-server'));

        // Cách đơn giản nhất: Force update tất cả
        const success = forceUpdateAllTotalElements(total);

        // Nếu vẫn không thành công, thử reload phần tổng đơn hàng
        if (!success) {
            console.log('Force update failed, trying to reload order summary...');
            reloadOrderSummary();
        }

        // Debug: Liệt kê tất cả các phần tử có class chứa 'cart'
        const allCartElements = document.querySelectorAll('[class*="cart"]');
        console.log('All elements with "cart" in class:', allCartElements.length);
        allCartElements.forEach((el, index) => {
            console.log(`Element ${index}:`, el.className, '|', el.textContent.trim().substring(0, 50));
        });

        // Debug: Tìm tất cả các phần tử có thể chứa tổng tiền
        const allSpanElements = document.querySelectorAll('span');
        console.log('All span elements:', allSpanElements.length);
        allSpanElements.forEach((el, index) => {
            const text = el.textContent.trim();
            if (text.includes('đ') || text.includes('₫')) {
                console.log(`Span ${index} with currency:`, el.className, '|', text);
            }
        });

        // Cập nhật tạm tính - thử tất cả các selector có thể
        const subtotalSelectors = [
            '.cart-subtotal',
            '.space-y-3 .flex.justify-between.items-center:first-child .font-medium',
            'span.font-medium.cart-subtotal'
        ];

        let subtotalUpdated = false;
        subtotalSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`Found subtotal element with selector: ${selector}`);
                console.log('Current subtotal text:', element.textContent);
                element.textContent = total;
                console.log('Updated subtotal to:', total);

                // Không thêm highlight vì đã có skeleton loading
                subtotalUpdated = true;
            }
        });

        if (!subtotalUpdated) {
            console.error('NO subtotal element found with any selector!');
        }

        // Cập nhật tổng cộng - thử tất cả các selector có thể
        const totalSelectors = [
            '.cart-total',
            '.text-xl.font-bold.text-primary',
            'span.text-xl.font-bold.text-primary.cart-total'
        ];

        let totalUpdated = false;
        totalSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`Found total element with selector: ${selector}`);
                console.log('Current total text:', element.textContent);
                element.textContent = total;
                console.log('Updated total to:', total);

                // Không thêm highlight vì đã có skeleton loading
                totalUpdated = true;
            }
        });

        if (!totalUpdated) {
            console.error('NO total element found with any selector!');

            // Thử cách cuối cùng: tìm tất cả span có chứa tiền và cập nhật
            console.log('Trying last resort: find all spans with currency...');
            const allSpans = document.querySelectorAll('span');
            let foundAndUpdated = 0;

            allSpans.forEach((span, index) => {
                const text = span.textContent.trim();
                // Nếu span chứa tiền và có class liên quan đến cart
                if ((text.includes('đ') || text.includes('₫')) &&
                    (span.className.includes('cart') ||
                     span.className.includes('total') ||
                     span.className.includes('subtotal'))) {
                    console.log(`Updating span ${index}:`, span.className, 'from', text, 'to', total);
                    span.textContent = total;
                    // Không thêm highlight vì đã có skeleton loading
                    foundAndUpdated++;
                }
            });

            console.log(`Updated ${foundAndUpdated} spans with currency`);
        }

        // Phát sự kiện hoàn thành cập nhật
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('cart-update-completed'));
        }, 100);
    }
}

/**
 * Force update tất cả các phần tử tổng tiền
 */
function forceUpdateAllTotalElements(total) {
    console.log('forceUpdateAllTotalElements called with:', total);

    let updated = 0;

    // Cách 1: Tìm tất cả span có chứa tiền và cập nhật
    const allSpans = document.querySelectorAll('span');
    console.log(`Found ${allSpans.length} span elements`);

    allSpans.forEach((span, index) => {
        const text = span.textContent.trim();
        if (text.includes('đ') || text.includes('₫')) {
            console.log(`Span ${index} with currency:`, span.className, '|', text);

            // Nếu có class liên quan hoặc nằm trong phần tổng đơn hàng
            if (span.className.includes('cart') ||
                span.className.includes('total') ||
                span.className.includes('subtotal') ||
                span.closest('.space-y-3')) {

                console.log(`Updating span ${index}:`, text, '->', total);

                // Thử cả textContent và innerHTML
                span.textContent = total;
                span.innerHTML = total;

                // Không thêm highlight và style vì đã có skeleton loading
                updated++;
            }
        }
    });

    console.log(`Total elements updated: ${updated}`);
    return updated > 0;
}

/**
 * Reload phần tổng đơn hàng từ server
 */
function reloadOrderSummary() {
    console.log('Reloading order summary from server...');

    fetch(`${BASE_URL}/ajax/get_cart_data.php`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Got cart data from server:', data);

                // Cập nhật tổng tiền với dữ liệu từ server
                if (data.total) {
                    forceUpdateAllTotalElements(data.total);
                }

                // Cập nhật số lượng nếu có
                if (data.count !== undefined) {
                    updateCartCount(data.count);
                }
            } else {
                console.error('Failed to get cart data:', data);
            }
        })
        .catch(error => {
            console.error('Error reloading order summary:', error);
        });
}

/**
 * Cập nhật số lượng giỏ hàng
 */
function updateCartCount(count) {
    // Lưu vào localStorage
    localStorage.setItem('cartCount', count.toString());

    // Cập nhật badge
    document.querySelectorAll('.cart-badge').forEach(badge => {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'flex' : 'none';
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart quantity handler loaded');

    // Đảm bảo không có xung đột với các file JavaScript khác
    if (window.cartHandlerInitialized) {
        console.log('Cart handler đã được khởi tạo trước đó, bỏ qua');
        return;
    }

    // Đánh dấu đã khởi tạo
    window.cartHandlerInitialized = true;

    // Không khởi tạo tổng tiền realtime - để cart-item-selection.js xử lý
    console.log('Cart quantity handler initialized');

    // Kiểm tra xem có thông báo cập nhật giỏ hàng không
    checkCartUpdateNotification();

    // Ghi đè hàm showNotification toàn cục nếu cần thiết
    overrideDefaultNotification();

    // Xử lý nút xóa tất cả sản phẩm trong giỏ hàng (mobile)
    const clearCartBtnMobile = document.getElementById('clear-cart-btn-mobile');
    if (clearCartBtnMobile) {
        clearCartBtnMobile.addEventListener('click', function() {
            // Kiểm tra xem GlobalModal đã được khởi tạo chưa
            if (window.GlobalModal) {
                // Sử dụng modal toàn cục
                window.GlobalModal.confirmClearCart({
                    onConfirm: () => clearCart()
                });
            } else {
                // Fallback về modal cũ nếu GlobalModal chưa được khởi tạo
                showDeleteConfirmationModal({
                    type: 'all'
                });
            }
        });
    }

    // Xử lý nút xóa tất cả sản phẩm trong giỏ hàng (desktop)
    const clearCartBtnDesktop = document.getElementById('clear-cart-btn-desktop');
    if (clearCartBtnDesktop) {
        clearCartBtnDesktop.addEventListener('click', function() {
            // Kiểm tra xem GlobalModal đã được khởi tạo chưa
            if (window.GlobalModal) {
                // Sử dụng modal toàn cục
                window.GlobalModal.confirmClearCart({
                    onConfirm: () => clearCart()
                });
            } else {
                // Fallback về modal cũ nếu GlobalModal chưa được khởi tạo
                showDeleteConfirmationModal({
                    type: 'all'
                });
            }
        });
    }

    // Xử lý nút giảm số lượng
    document.querySelectorAll('.quantity-decrease').forEach(function(button) {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('.quantity-input');
            const currentValue = parseInt(input.value);

            if (currentValue > 1) {
                // Thêm hiệu ứng cho nút
                this.classList.add('active-control');
                setTimeout(() => this.classList.remove('active-control'), 200);

                // Giảm số lượng
                input.value = currentValue - 1;

                // Thêm hiệu ứng nhẹ cho input
                input.classList.add('highlight-change');
                setTimeout(() => input.classList.remove('highlight-change'), 1000);

                // Cập nhật hiển thị tổng tiền sản phẩm
                updateProductTotal(input);

                // Không hiển thị thông báo cho thao tác thường - chỉ dùng visual feedback
            } else {
                // Hiển thị thông báo khi không thể giảm thêm
                showCartInlineNotification('Không thể giảm', 'Số lượng tối thiểu là 1', 'warning');
            }
        });
    });

    // Xử lý nút tăng số lượng
    document.querySelectorAll('.quantity-increase').forEach(function(button) {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('.quantity-input');
            const currentValue = parseInt(input.value);
            const maxQuantity = parseInt(input.dataset.maxQuantity || 0);

            // Kiểm tra nếu đã đạt số lượng tối đa
            if (maxQuantity > 0 && currentValue >= maxQuantity) {
                // Hiển thị thông báo cảnh báo
                const productName = input.closest('.bg-white.rounded-xl').querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();
                showCartInlineNotification('Số lượng tối đa', `Sản phẩm "${productName}" chỉ còn ${maxQuantity} trong kho`, 'warning');
                return;
            }

            // Thêm hiệu ứng cho nút
            this.classList.add('active-control');
            setTimeout(() => this.classList.remove('active-control'), 200);

            // Tăng số lượng
            input.value = currentValue + 1;

            // Thêm hiệu ứng nhẹ cho input
            input.classList.add('highlight-change');
            setTimeout(() => input.classList.remove('highlight-change'), 1000);

            // Cập nhật hiển thị tổng tiền sản phẩm
            updateProductTotal(input);

            // Không hiển thị thông báo cho thao tác thường - chỉ dùng visual feedback
        });
    });

    // Không cần xử lý sự kiện click cho nút cập nhật vì đã sử dụng onclick="updateCartItemManual()" trong HTML

    // Xử lý nút xóa sản phẩm
    document.querySelectorAll('.remove-cart-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;

            // Lấy thông tin sản phẩm để hiển thị trong modal
            const card = this.closest('.bg-white.rounded-xl');
            const productName = card.querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();
            const productPrice = card.querySelector('.text-primary.font-medium').textContent.trim();
            const productQuantity = card.querySelector('.quantity-input').value;
            const productImage = card.querySelector('img') ? card.querySelector('img').src : '';

            // Kiểm tra xem GlobalModal đã được khởi tạo chưa
            if (window.GlobalModal) {
                // Sử dụng modal toàn cục
                window.GlobalModal.confirmDeleteProduct({
                    productId: productId,
                    productName: productName,
                    productPrice: productPrice,
                    productQuantity: productQuantity,
                    productImage: productImage,
                    onConfirm: () => removeCartItem(productId, this, () => {
                        // Callback khi xóa thành công - GlobalModal sẽ tự đóng
                        console.log('Product removed successfully via GlobalModal');
                    })
                });
            } else {
                // Fallback về modal cũ nếu GlobalModal chưa được khởi tạo
                showDeleteConfirmationModal({
                    type: 'single',
                    productId: productId,
                    productName: productName,
                    productPrice: productPrice,
                    productQuantity: productQuantity,
                    productImage: productImage,
                    button: this
                });
            }
        });
    });

    /**
     * Hiển thị modal xác nhận xóa
     * @param {Object} options - Các tùy chọn cho modal
     */
    function showDeleteConfirmationModal(options) {
        const modal = document.getElementById('delete-confirmation-modal');
        const title = document.getElementById('delete-modal-title');
        const message = document.getElementById('delete-modal-message');
        const productInfo = document.getElementById('delete-product-info');
        const productImage = document.getElementById('delete-product-image').querySelector('img');
        const productName = document.getElementById('delete-product-name');
        const productQuantity = document.getElementById('delete-product-quantity');
        const productPrice = document.getElementById('delete-product-price');
        const confirmBtn = document.getElementById('delete-confirm-btn');
        const cancelBtn = document.getElementById('delete-cancel-btn');
        const closeBtn = document.getElementById('delete-close-btn');
        const btnText = document.getElementById('delete-btn-text');

        // Đặt nội dung modal dựa trên loại xóa
        if (options.type === 'single') {
            title.textContent = 'Xác nhận xóa sản phẩm';
            message.textContent = 'Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?';
            btnText.textContent = 'Xác nhận xóa';

            // Hiển thị thông tin sản phẩm
            productInfo.classList.remove('hidden');
            productImage.src = options.productImage;
            productImage.alt = options.productName;
            productName.textContent = options.productName;
            productQuantity.textContent = options.productQuantity;
            productPrice.textContent = options.productPrice;
        } else if (options.type === 'all') {
            title.textContent = 'Xác nhận xóa tất cả';
            message.textContent = 'Bạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?';
            btnText.textContent = 'Xóa tất cả';

            // Ẩn thông tin sản phẩm
            productInfo.classList.add('hidden');
        }

        // Hiển thị modal
        modal.classList.add('active');

        // Thêm class modal-active vào body để vô hiệu hóa các stacking context khác
        document.body.classList.add('modal-active');

        // Đảm bảo modal nằm ở cấp cao nhất trong DOM
        document.body.appendChild(modal);

        // Đảm bảo modal có z-index cao nhất
        modal.style.zIndex = '2147483647';

        // Đảm bảo các phần tử con của modal có z-index cao
        const modalBox = modal.querySelector('.bg-white');
        if (modalBox) {
            modalBox.style.zIndex = '2147483647';
        }

        // Xử lý sự kiện nút xác nhận
        const handleConfirm = function() {
            // Hiển thị hiệu ứng loading ngay lập tức
            confirmBtn.classList.add('loading');
            confirmBtn.disabled = true;

            if (options.type === 'single') {
                // Xóa một sản phẩm với callback để đóng modal
                removeCartItem(options.productId, options.button, () => {
                    // Đóng modal sau khi xóa thành công
                    modal.classList.remove('active');
                    document.body.classList.remove('modal-active');

                    // Reset trạng thái nút
                    setTimeout(() => {
                        confirmBtn.classList.remove('loading');
                        confirmBtn.disabled = false;
                    }, 300);
                });
            } else if (options.type === 'all') {
                // Xóa tất cả sản phẩm với callback để đóng modal
                clearCart(() => {
                    // Modal sẽ được đóng khi chuyển hướng
                });
            }
        };

        // Xử lý sự kiện đóng modal
        const handleClose = function() {
            modal.classList.remove('active');

            // Xóa class modal-active khỏi body
            document.body.classList.remove('modal-active');
        };

        // Gán sự kiện cho các nút
        confirmBtn.onclick = handleConfirm;
        cancelBtn.onclick = handleClose;
        closeBtn.onclick = handleClose;

        // Đóng modal khi click bên ngoài
        modal.onclick = function(e) {
            if (e.target === modal) {
                handleClose();
            }
        };
    }

    /**
     * Xóa một sản phẩm khỏi giỏ hàng
     * @param {string} productId - ID sản phẩm cần xóa
     * @param {HTMLElement} button - Nút xóa được click
     * @param {Function} onSuccess - Callback khi xóa thành công
     */
    function removeCartItem(productId, button, onSuccess) {
        console.log('🗑️ removeCartItem called:', productId);

        // Thêm delay 1 giây để hiển thị loading effect
        setTimeout(() => {
            console.log('⏰ Starting remove request after 1s delay');
            // Gửi yêu cầu xóa
            fetch(`${BASE_URL}/ajax/remove_from_cart.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}`,
                cache: 'no-store'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Remove response:', data);

                if (data.success) {
                    // Xóa card sản phẩm
                    const card = button.closest('.bg-white.rounded-xl');
                    if (card) {
                        card.remove();
                    }

                    // Cập nhật tổng tiền giỏ hàng
                    if (data.total) {
                        updateTotalPrice(data.total);
                    }

                    // Cập nhật số lượng giỏ hàng
                    if (data.count !== undefined) {
                        updateCartCount(data.count);

                        // Cập nhật hiển thị tổng số lượng sản phẩm
                        const countBadge = document.querySelector('.text-sm.text-gray-500.bg-white .font-medium.text-primary');
                        if (countBadge) {
                            countBadge.textContent = data.count;
                        }

                        // Nếu giỏ hàng trống, tải lại trang
                        if (data.count === 0) {
                            location.reload();
                            return;
                        }
                    }

                    // Phát sự kiện cập nhật giỏ hàng để các thành phần khác có thể lắng nghe
                    const cartUpdatedEvent = new CustomEvent('cart:updated', {
                        detail: {
                            total: data.total,
                            count: data.count
                        },
                        bubbles: true
                    });
                    document.dispatchEvent(cartUpdatedEvent);

                    // Đảm bảo thông báo thành công luôn hiển thị
                    // Tạm thời xóa cờ để thông báo có thể hiển thị
                    const wasUsingCustomNotification = sessionStorage.getItem('useCustomNotification') === 'true';
                    if (wasUsingCustomNotification) {
                        sessionStorage.removeItem('useCustomNotification');
                    }

                    // Hiển thị thông báo thành công bằng simple-notification
                    showSimpleNotification(data.message || 'Đã xóa sản phẩm khỏi giỏ hàng thành công', 'success');

                    // Khôi phục cờ nếu cần
                    if (wasUsingCustomNotification) {
                        setTimeout(() => {
                            sessionStorage.setItem('useCustomNotification', 'true');
                        }, 100);
                    }

                    // Gọi callback để đóng modal
                    if (typeof onSuccess === 'function') {
                        onSuccess();
                    }
                } else {
                    // Hiển thị thông báo lỗi
                    showNotification(data.message || 'Có lỗi xảy ra khi xóa sản phẩm', 'error');

                    // Hiệu ứng shake cho modal nếu còn hiển thị
                    const modal = document.getElementById('delete-confirmation-modal');
                    if (modal.classList.contains('active')) {
                        modal.classList.add('shake');
                        setTimeout(() => modal.classList.remove('shake'), 600);
                    }
                }
            })
            .catch(error => {
                console.error('Error removing cart item:', error);
                showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');

                // Hiệu ứng shake cho modal nếu còn hiển thị
                const modal = document.getElementById('delete-confirmation-modal');
                if (modal.classList.contains('active')) {
                    modal.classList.add('shake');
                    setTimeout(() => modal.classList.remove('shake'), 600);
                }
            });
        }, 1000); // Delay 1 giây
    }

    /**
     * Xóa tất cả sản phẩm trong giỏ hàng
     * @param {Function} onComplete - Callback khi hoàn thành
     */
    function clearCart(onComplete) {
        // Đặt cờ để ngăn thông báo mặc định hiển thị
        sessionStorage.setItem('useCustomNotification', 'true');

        // Thêm delay 1 giây để hiển thị loading effect
        setTimeout(() => {
            // Chuyển hướng đến trang xóa giỏ hàng
            window.location.href = `${BASE_URL}/ajax/clear_cart.php`;
        }, 1000); // Delay 1 giây
    }

    // Không cập nhật tổng đơn hàng realtime khi thay đổi checkbox
    // Tổng đơn hàng chỉ được cập nhật thông qua cart-item-selection.js khi cần thiết

    // Xử lý khi nhập số lượng và nhấn Enter
    document.querySelectorAll('.quantity-input').forEach(function(input) {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Tìm nút cập nhật và kích hoạt sự kiện click
                const card = this.closest('.bg-white.rounded-xl');
                const updateButton = card.querySelector('.update-cart-btn');
                if (updateButton) {
                    updateButton.click();
                }
            }
        });

        // Lưu giá trị ban đầu khi focus
        input.addEventListener('focus', function() {
            // Lưu giá trị hiện tại làm giá trị ban đầu
            const currentValue = parseInt(this.value);
            if (!isNaN(currentValue) && currentValue > 0) {
                this.dataset.originalValue = currentValue.toString();
            }
        });

        // Kiểm tra giá trị hợp lệ khi blur
        input.addEventListener('blur', function() {
            const value = parseInt(this.value);
            const maxQuantity = parseInt(this.dataset.maxQuantity || 0);

            if (isNaN(value) || value < 1) {
                // Nếu giá trị không hợp lệ, khôi phục giá trị ban đầu
                this.value = this.dataset.originalValue || 1;
                showCartInlineNotification('Số lượng không hợp lệ', 'Số lượng sản phẩm phải lớn hơn 0', 'warning');
            } else if (maxQuantity > 0 && value > maxQuantity) {
                // Nếu vượt quá số lượng tồn kho
                this.value = maxQuantity;
                const productName = this.closest('.bg-white.rounded-xl').querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();
                showCartInlineNotification('Số lượng tối đa', `Sản phẩm "${productName}" chỉ còn ${maxQuantity} trong kho`, 'warning');
                // Cập nhật hiển thị tổng tiền sản phẩm
                updateProductTotal(this);
            } else {
                // Nếu giá trị hợp lệ, cập nhật giá trị ban đầu
                this.dataset.originalValue = value.toString();
                // Cập nhật hiển thị tổng tiền sản phẩm
                updateProductTotal(this);
            }
        });
    });

    /**
     * Cập nhật hiển thị tổng tiền sản phẩm mà không gửi yêu cầu đến server
     */
    function updateProductTotal(input) {
        const quantity = parseInt(input.value);
        if (isNaN(quantity) || quantity < 1) return;

        // Tìm card chứa sản phẩm
        const card = input.closest('.bg-white.rounded-xl');
        if (!card) return;

        // Đồng bộ hóa tất cả input số lượng của sản phẩm này
        const allInputs = card.querySelectorAll(`.quantity-input[data-product-id="${input.dataset.productId}"]`);
        allInputs.forEach(inp => {
            if (inp !== input) {
                inp.value = quantity;
            }
        });

        // Cập nhật dataset quantity cho tất cả checkbox của sản phẩm này
        const checkboxes = card.querySelectorAll('.cart-item-checkbox');
        checkboxes.forEach(checkbox => {
            if (checkbox.dataset.productId === input.dataset.productId) {
                checkbox.dataset.quantity = quantity;
            }
        });

        // Cập nhật tổng tiền sản phẩm cho cả mobile và desktop layout
        updateProductTotalDisplay(card, input.dataset.productId, quantity);

        // Không cập nhật tổng đơn hàng realtime - chỉ cập nhật khi nhấn nút "Cập nhật"
    }

    /**
     * Cập nhật hiển thị tổng tiền sản phẩm cho cả mobile và desktop
     */
    function updateProductTotalDisplay(card, productId, quantity) {
        // Tìm phần tử hiển thị đơn giá (có thể ở mobile hoặc desktop)
        const priceElements = card.querySelectorAll('.text-primary.font-medium, .product-price-professional');

        if (priceElements.length === 0) return;

        // Lấy giá từ phần tử đầu tiên
        const priceText = priceElements[0].textContent.trim();
        const price = parseFloat(priceText.replace(/[^\d]/g, ''));

        if (isNaN(price) || price <= 0) return;

        // Tính tổng tiền mới
        const total = price * quantity;

        // Cập nhật tổng tiền sản phẩm cho mobile layout
        const mobileTotalElement = card.querySelector('.text-lg.font-semibold.text-primary-dark');
        if (mobileTotalElement) {
            mobileTotalElement.textContent = formatCurrency(total);

            // Thêm hiệu ứng highlight nhẹ
            mobileTotalElement.classList.add('highlight-change');
            setTimeout(() => mobileTotalElement.classList.remove('highlight-change'), 1200);
        }

        // Cập nhật tổng tiền sản phẩm cho desktop layout (professional)
        const desktopTotalElement = card.querySelector('.total-amount-professional');
        if (desktopTotalElement) {
            desktopTotalElement.textContent = formatCurrency(total);

            // Thêm hiệu ứng highlight nhẹ
            desktopTotalElement.classList.add('highlight-change');
            setTimeout(() => desktopTotalElement.classList.remove('highlight-change'), 1200);
        }

        // Cập nhật phần tiết kiệm cho mobile layout
        updateSavingsDisplay(card, price, quantity, 'mobile');

        // Cập nhật phần tiết kiệm cho desktop layout
        updateSavingsDisplay(card, price, quantity, 'desktop');

        console.log(`Updated product total for ID ${productId}: ${formatCurrency(total)}`);
    }

    /**
     * Cập nhật hiển thị phần tiết kiệm
     */
    function updateSavingsDisplay(card, price, quantity, layout) {
        let savingsElement;

        if (layout === 'mobile') {
            savingsElement = card.querySelector('.mobile-cart-layout .bg-green-100.text-green-700 i.fa-piggy-bank');
        } else {
            savingsElement = card.querySelector('.desktop-cart-layout .savings-badge-professional i.fa-piggy-bank');
        }

        if (savingsElement) {
            const savingsSpan = savingsElement.parentElement;

            // Tính toán lại số tiền tiết kiệm (giả sử giá gốc cao hơn 20%)
            const originalPrice = price * 1.2;
            const savingsAmount = (originalPrice - price) * quantity;

            // Cập nhật text hiển thị tiết kiệm
            if (layout === 'mobile') {
                savingsSpan.innerHTML = `<i class="fas fa-piggy-bank mr-1"></i>Tiết kiệm ${formatCurrency(savingsAmount)}`;
            } else {
                savingsSpan.innerHTML = `<i class="fas fa-piggy-bank"></i>Tiết kiệm ${formatCurrency(savingsAmount)}`;
            }

            // Thêm hiệu ứng nhảy nhẹ
            savingsSpan.classList.add('savings-bounce');
            setTimeout(() => savingsSpan.classList.remove('savings-bounce'), 600);
        }
    }

    /**
     * Cập nhật tổng tiền toàn bộ giỏ hàng realtime
     */
    function updateCartTotalRealtime() {
        let cartSubtotal = 0;

        // Tính tổng tiền từ tất cả sản phẩm được chọn
        const allCartItems = document.querySelectorAll('.bg-white.rounded-xl');

        allCartItems.forEach(card => {
            const checkbox = card.querySelector('.cart-item-checkbox');
            if (checkbox && checkbox.checked) {
                const quantityInput = card.querySelector('.quantity-input');
                const priceElement = card.querySelector('.text-primary.font-medium, .product-price-professional');

                if (quantityInput && priceElement) {
                    const quantity = parseInt(quantityInput.value) || 0;
                    const priceText = priceElement.textContent.trim();
                    const price = parseFloat(priceText.replace(/[^\d]/g, '')) || 0;

                    cartSubtotal += price * quantity;
                }
            }
        });

        // Cập nhật hiển thị tạm tính (không highlight vì có skeleton loading)
        const subtotalElements = document.querySelectorAll('.cart-subtotal');
        subtotalElements.forEach(element => {
            element.textContent = formatCurrency(cartSubtotal);
        });

        // Cập nhật hiển thị tổng cộng (không highlight vì có skeleton loading)
        const totalElements = document.querySelectorAll('.cart-total');
        totalElements.forEach(element => {
            element.textContent = formatCurrency(cartSubtotal);
        });

        console.log(`Updated cart total realtime: ${formatCurrency(cartSubtotal)}`);

        // Phát sự kiện để các component khác có thể lắng nghe
        const event = new CustomEvent('cart-total-updated', {
            detail: {
                subtotal: cartSubtotal,
                total: cartSubtotal,
                formatted: formatCurrency(cartSubtotal)
            }
        });
        document.dispatchEvent(event);
    }





    /**
     * Format số tiền
     */
    function formatCurrency(amount) {
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'đ';
    }

    /**
     * Hiển thị thông báo toàn trang
     */
    function showNotification(message, type) {
        console.log('Showing notification:', message, type); // Debug log

        // Xóa thông báo cũ
        const oldNotification = document.getElementById('cart-notification');
        if (oldNotification) {
            oldNotification.remove();
        }

        // Tạo thông báo mới
        const notification = document.createElement('div');
        notification.id = 'cart-notification';
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.zIndex = '999999'; // Z-index cao hơn
        notification.style.backgroundColor = type === 'success' ? '#10B981' : (type === 'warning' ? '#F59E0B' : (type === 'info' ? '#3B82F6' : '#EF4444'));
        notification.style.color = 'white';
        notification.style.padding = '12px 20px';
        notification.style.borderRadius = '8px';
        notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.minWidth = '300px';
        notification.style.maxWidth = '80%';
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.3s ease';

        // Chọn icon phù hợp với loại thông báo
        let icon = 'fa-exclamation-circle';
        if (type === 'success') icon = 'fa-check-circle';
        else if (type === 'warning') icon = 'fa-exclamation-triangle';
        else if (type === 'info') icon = 'fa-info-circle';

        // Tạo nội dung thông báo
        notification.innerHTML = `
            <i class="fas ${icon}" style="margin-right: 10px;"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.remove();" style="background: none; border: none; color: white; cursor: pointer; margin-left: 10px;">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Thêm vào body
        document.body.appendChild(notification);

        // Hiệu ứng fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Tự động đóng sau 5 giây (thời gian dài hơn)
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }



    /**
     * Kiểm tra xem có nên hiển thị thông báo hay không
     * Chỉ hiển thị thông báo cho các trường hợp quan trọng
     */
    function shouldShowNotification(type, message) {
        // Luôn hiển thị lỗi và cảnh báo
        if (type === 'error' || type === 'warning') {
            return true;
        }

        // Hiển thị thông báo thành công quan trọng (cập nhật giỏ hàng)
        if (type === 'success' && (message.includes('cập nhật') || message.includes('Cập nhật'))) {
            return true;
        }

        // Không hiển thị thông báo info cho thao tác thường (tăng/giảm số lượng)
        if (type === 'info' && (message.includes('tăng số lượng') || message.includes('giảm số lượng'))) {
            return false;
        }

        return true;
    }

    /**
     * Hiển thị thông báo trong giỏ hàng (đã được tối ưu hóa)
     */
    function showCartInlineNotification(message, detail, type = 'success') {
        // Kiểm tra xem có nên hiển thị thông báo không
        if (!shouldShowNotification(type, message)) {
            return;
        }
        // Lấy phần tử thông báo
        const notification = document.getElementById('cart-inline-notification');
        const messageElement = document.getElementById('cart-notification-message');
        const detailElement = document.getElementById('cart-notification-detail');
        const iconElement = document.getElementById('cart-notification-icon');

        if (!notification || !messageElement || !detailElement || !iconElement) {
            console.error('Không tìm thấy phần tử thông báo');
            return;
        }

        // Xóa class hiding nếu có
        notification.classList.remove('hiding');

        // Cập nhật nội dung
        messageElement.textContent = message;
        detailElement.textContent = detail;

        // Cập nhật màu sắc và icon dựa trên loại thông báo
        if (type === 'success') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-green-500';
            iconElement.innerHTML = '<i class="fas fa-check"></i>';
        } else if (type === 'error') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-red-500';
            iconElement.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
        } else if (type === 'warning') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-yellow-500';
            iconElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
        } else if (type === 'info') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-blue-500';
            iconElement.innerHTML = '<i class="fas fa-info-circle"></i>';
        }

        // Hiển thị thông báo
        notification.classList.remove('hidden');

        // Tự động ẩn sau 5 giây
        clearTimeout(window.cartNotificationTimeout);
        window.cartNotificationTimeout = setTimeout(hideCartNotification, 5000);
    }

    /**
     * Ẩn thông báo trong giỏ hàng
     */
    function hideCartNotification() {
        const notification = document.getElementById('cart-inline-notification');
        if (notification) {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.classList.add('hidden');
                notification.classList.remove('hiding');
            }, 300);
        }
    }

    // Thêm hàm vào window để có thể gọi từ HTML
    window.hideCartNotification = hideCartNotification;
    window.updateProductTotal = updateProductTotal;

    /**
     * Kiểm tra và hiển thị thông báo cập nhật giỏ hàng sau khi trang đã tải lại
     */
    function checkCartUpdateNotification() {
        // Kiểm tra xem có kết quả cập nhật giỏ hàng không
        const updateResultJson = sessionStorage.getItem('cartUpdateResult');
        if (updateResultJson) {
            try {
                const updateResult = JSON.parse(updateResultJson);

                // Kiểm tra xem thông báo có mới không (trong vòng 5 giây)
                const now = new Date().getTime();
                const messageTime = updateResult.timestamp || 0;

                if (now - messageTime < 5000) { // Chỉ hiển thị thông báo nếu mới hơn 5 giây
                    // Hiển thị thông báo
                    showCartInlineNotification(
                        updateResult.message || 'Giỏ hàng đã được cập nhật',
                        updateResult.detail || '',
                        updateResult.success ? 'success' : 'error'
                    );

                    // Highlight các phần tử liên quan
                    highlightUpdatedElements();
                }

                // Xóa thông báo đã hiển thị
                sessionStorage.removeItem('cartUpdateResult');
            } catch (error) {
                console.error('Lỗi khi xử lý thông báo cập nhật giỏ hàng:', error);
                sessionStorage.removeItem('cartUpdateResult');
            }
        }

        // Kiểm tra và xóa cờ thông báo tùy chỉnh
        if (sessionStorage.getItem('useCustomNotification') === 'true') {
            // Xóa cờ để lần sau có thể hiển thị thông báo mặc định
            sessionStorage.removeItem('useCustomNotification');

            // Xóa thông báo mặc định nếu có
            const defaultNotification = document.getElementById('simple-notification');
            if (defaultNotification) {
                defaultNotification.remove();
            }

            // Xóa thông báo mặc định khác nếu có
            const otherNotifications = document.querySelectorAll('.notification');
            otherNotifications.forEach(notification => {
                notification.remove();
            });
        }
    }

    /**
     * Ghi đè hàm thông báo mặc định để ngăn hiển thị khi sử dụng thông báo tùy chỉnh
     */
    function overrideDefaultNotification() {
        // Lưu trữ hàm showNotification gốc nếu có
        if (typeof window.originalShowNotification === 'undefined' && typeof window.showNotification === 'function') {
            window.originalShowNotification = window.showNotification;

            // Ghi đè hàm showNotification
            window.showNotification = function(message, type) {
                // Kiểm tra xem có đang sử dụng thông báo tùy chỉnh không
                if (sessionStorage.getItem('useCustomNotification') === 'true') {
                    console.log('Đã chặn thông báo mặc định:', message);
                    return; // Không hiển thị thông báo mặc định
                }

                // Nếu không, sử dụng hàm gốc
                return window.originalShowNotification(message, type);
            };
        }

        // Lưu trữ hàm showSimpleNotification gốc nếu có
        if (typeof window.originalShowSimpleNotification === 'undefined' && typeof window.showSimpleNotification === 'function') {
            window.originalShowSimpleNotification = window.showSimpleNotification;

            // Ghi đè hàm showSimpleNotification
            window.showSimpleNotification = function(message, type, duration) {
                // Kiểm tra xem có đang sử dụng thông báo tùy chỉnh không
                if (sessionStorage.getItem('useCustomNotification') === 'true') {
                    console.log('Đã chặn thông báo đơn giản mặc định:', message);
                    return; // Không hiển thị thông báo mặc định
                }

                // Nếu không, sử dụng hàm gốc
                return window.originalShowSimpleNotification(message, type, duration);
            };
        }

        // Lưu trữ hàm showNotificationRealtime gốc nếu có
        if (typeof window.originalShowNotificationRealtime === 'undefined' && typeof window.showNotificationRealtime === 'function') {
            window.originalShowNotificationRealtime = window.showNotificationRealtime;

            // Ghi đè hàm showNotificationRealtime
            window.showNotificationRealtime = function(message, type) {
                // Kiểm tra xem có đang sử dụng thông báo tùy chỉnh không
                if (sessionStorage.getItem('useCustomNotification') === 'true') {
                    console.log('Đã chặn thông báo realtime mặc định:', message);
                    return; // Không hiển thị thông báo mặc định
                }

                // Nếu không, sử dụng hàm gốc
                return window.originalShowNotificationRealtime(message, type);
            };
        }
    }

    /**
     * Highlight các phần tử liên quan đến cập nhật giỏ hàng
     */
    function highlightUpdatedElements() {
        // Không cần highlight cho order summary vì đã có skeleton loading

        // Chỉ giữ lại highlight cho số lượng giỏ hàng (không có skeleton)
        // Lưu ý: Phần này sẽ được cập nhật trong updateCartItemManual với items_count
        const countBadge = document.querySelector('.text-sm.text-gray-500.bg-white .font-medium.text-primary');
        if (countBadge) {
            countBadge.classList.add('highlight-change');
            setTimeout(() => countBadge.classList.remove('highlight-change'), 1200);
        }

        // Chỉ giữ lại highlight cho badge giỏ hàng (không có skeleton)
        document.querySelectorAll('.cart-badge').forEach(badge => {
            badge.classList.add('badge-pulse');
            setTimeout(() => badge.classList.remove('badge-pulse'), 1000);
        });

        // Kiểm tra thông tin cập nhật để highlight sản phẩm cụ thể
        const updateInfoJson = sessionStorage.getItem('cartUpdateInfo');
        if (updateInfoJson) {
            try {
                const updateInfo = JSON.parse(updateInfoJson);
                const productId = updateInfo.productId;

                // Tìm sản phẩm và highlight
                if (productId) {
                    const input = document.querySelector(`.quantity-input[data-product-id="${productId}"]`);
                    if (input) {
                        // Không cần highlight cho các phần tử order summary vì đã có skeleton loading
                        // Chỉ giữ lại highlight cho input và card sản phẩm (không có skeleton)
                        input.classList.add('highlight-change');
                        setTimeout(() => input.classList.remove('highlight-change'), 1200);

                        const card = input.closest('.bg-white.rounded-xl');
                        if (card) {
                            card.classList.add('highlight-change');
                            setTimeout(() => card.classList.remove('highlight-change'), 1200);

                            // Highlight tổng tiền sản phẩm (không có skeleton)
                            const productTotalElement = card.querySelector('.text-lg.font-semibold.text-primary-dark');
                            if (productTotalElement) {
                                productTotalElement.classList.add('highlight-change');
                                setTimeout(() => productTotalElement.classList.remove('highlight-change'), 1200);
                            }
                        }
                    }
                }

                // Xóa thông tin cập nhật
                sessionStorage.removeItem('cartUpdateInfo');
            } catch (error) {
                console.error('Lỗi khi xử lý thông tin cập nhật giỏ hàng:', error);
                sessionStorage.removeItem('cartUpdateInfo');
            }
        }
    }
});

// Tự động remove skeleton loading khi trang load xong
document.addEventListener('DOMContentLoaded', function() {
    autoRemoveSkeletonLoading();
});

// Fallback: đảm bảo skeleton loading được remove ngay cả khi có vấn đề
window.addEventListener('load', function() {
    // Đợi thêm một chút để đảm bảo tất cả resources đã load
    setTimeout(() => {
        console.log('Fallback: checking CSS and removing skeleton loading after window load...');
        // Force remove skeleton loading sau khi window load
        stopOrderSummarySkeletonLoading();
        // Cũng dừng loading cho nút checkout
        stopCheckoutButtonUpdateLoading();
    }, 100);
});

// Fallback cuối cùng: force remove sau 2 giây
setTimeout(() => {
    console.log('Final fallback: force removing skeleton loading after 2 seconds...');
    stopOrderSummarySkeletonLoading();
    // Cũng dừng loading cho nút checkout
    stopCheckoutButtonUpdateLoading();
}, 2000);
