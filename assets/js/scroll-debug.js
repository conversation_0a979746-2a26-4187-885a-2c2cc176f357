/**
 * Scroll Debug Tool
 * Công cụ debug để tìm nguyên nhân gây giật khi scroll
 */

class ScrollDebugger {
    constructor() {
        this.scrollEvents = [];
        this.layoutEvents = [];
        this.animationEvents = [];
        this.isDebugging = false;
        this.debugPanel = null;
        
        this.init();
    }

    init() {
        // Tạo debug panel
        this.createDebugPanel();
        
        // Hook vào các scroll events
        this.hookScrollEvents();
        
        // Hook vào layout events
        this.hookLayoutEvents();
        
        // Hook vào animation events
        this.hookAnimationEvents();
        
        console.log('🔍 Scroll Debugger initialized');
    }

    createDebugPanel() {
        this.debugPanel = document.createElement('div');
        this.debugPanel.id = 'scroll-debug-panel';
        this.debugPanel.innerHTML = `
            <div style="
                position: fixed;
                top: 10px;
                left: 10px;
                width: 350px;
                max-height: 400px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                z-index: 99999;
                font-family: monospace;
                font-size: 12px;
                overflow-y: auto;
                display: none;
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h3 style="margin: 0; color: #4CAF50;">Scroll Debugger</h3>
                    <button id="clear-debug" style="background: #f44336; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">Clear</button>
                </div>
                <div id="debug-content"></div>
            </div>
        `;
        
        document.body.appendChild(this.debugPanel);
        
        // Event listeners
        document.getElementById('clear-debug').addEventListener('click', () => {
            this.clearLogs();
        });
    }

    startDebugging() {
        this.isDebugging = true;
        if (this.debugPanel && this.debugPanel.firstElementChild) {
            this.debugPanel.firstElementChild.style.display = 'block';
        }
        this.clearLogs();
        this.log('🚀 Debug started', 'info');
    }

    stopDebugging() {
        this.isDebugging = false;
        this.debugPanel.firstElementChild.style.display = 'none';
    }

    log(message, type = 'info', data = null) {
        if (!this.isDebugging) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            info: '#4CAF50',
            warning: '#FF9800',
            error: '#f44336',
            scroll: '#2196F3',
            layout: '#9C27B0',
            animation: '#FF5722'
        };
        
        const logEntry = {
            timestamp,
            message,
            type,
            data
        };
        
        const debugContent = document.getElementById('debug-content');
        if (!debugContent) return;

        const logElement = document.createElement('div');
        logElement.style.cssText = `
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid ${colors[type] || '#4CAF50'};
            background: rgba(255, 255, 255, 0.1);
        `;
        
        let content = `<strong>[${timestamp}]</strong> ${message}`;
        if (data) {
            content += `<br><small style="color: #ccc;">${JSON.stringify(data, null, 2)}</small>`;
        }
        
        logElement.innerHTML = content;
        debugContent.appendChild(logElement);
        
        // Auto scroll to bottom
        debugContent.scrollTop = debugContent.scrollHeight;
        
        // Keep only last 50 entries
        while (debugContent.children.length > 50) {
            debugContent.removeChild(debugContent.firstChild);
        }
    }

    clearLogs() {
        const debugContent = document.getElementById('debug-content');
        debugContent.innerHTML = '';
        this.scrollEvents = [];
        this.layoutEvents = [];
        this.animationEvents = [];
    }

    hookScrollEvents() {
        // Hook window.scrollTo
        const originalScrollTo = window.scrollTo;
        window.scrollTo = (...args) => {
            this.log('window.scrollTo called', 'scroll', {
                args: args,
                currentPosition: window.pageYOffset,
                stack: new Error().stack.split('\n').slice(1, 4)
            });
            return originalScrollTo.apply(window, args);
        };

        // Hook scroll events
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.log('Scroll event', 'scroll', {
                    position: window.pageYOffset,
                    direction: this.lastScrollPosition > window.pageYOffset ? 'up' : 'down'
                });
                this.lastScrollPosition = window.pageYOffset;
            }, 10);
        });

        // Hook requestAnimationFrame
        const originalRAF = window.requestAnimationFrame;
        window.requestAnimationFrame = (callback) => {
            this.log('requestAnimationFrame called', 'animation', {
                stack: new Error().stack.split('\n').slice(1, 3)
            });
            return originalRAF.call(window, callback);
        };
    }

    hookLayoutEvents() {
        // Hook DOM mutations
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    this.log('DOM mutation detected', 'layout', {
                        target: mutation.target.id || mutation.target.className,
                        addedNodes: mutation.addedNodes.length
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Hook style changes
        const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
        CSSStyleDeclaration.prototype.setProperty = function(property, value, priority) {
            if (property.includes('height') || property.includes('opacity') || property.includes('transform')) {
                window.scrollDebugger?.log('Style change', 'layout', {
                    property,
                    value,
                    element: this.parentRule?.selectorText || 'inline'
                });
            }
            return originalSetProperty.call(this, property, value, priority);
        };
    }

    hookAnimationEvents() {
        // Hook setTimeout
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = (callback, delay) => {
            this.log('setTimeout called', 'animation', {
                delay,
                stack: new Error().stack.split('\n').slice(1, 3)
            });
            return originalSetTimeout.call(window, callback, delay);
        };
    }

    // Phương thức để test scroll
    testScroll() {
        this.startDebugging();
        this.log('🧪 Testing scroll to products section', 'info');
        
        const productsSection = document.getElementById('products-section');
        if (productsSection) {
            const targetPosition = productsSection.offsetTop - 100;
            this.log('Target position calculated', 'info', {
                offsetTop: productsSection.offsetTop,
                targetPosition
            });
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        } else {
            this.log('Products section not found!', 'error');
        }
    }
}

// Khởi tạo debugger
window.scrollDebugger = new ScrollDebugger();

// Thêm controls vào console
console.log(`
🔍 Scroll Debugger Commands:
- scrollDebugger.startDebugging() - Bắt đầu debug
- scrollDebugger.stopDebugging() - Dừng debug  
- scrollDebugger.testScroll() - Test scroll
- scrollDebugger.clearLogs() - Xóa logs
`);
