/**
 * Premium Header JavaScript for Nội Thất Bàng Vũ
 * Handles interactive features of the premium header
 */

document.addEventListener('DOMContentLoaded', function () {
  // Tablet Navigation Touch Scroll
  const navMenu = document.querySelector('.nav-menu');
  if (navMenu) {
    let isDown = false;
    let startX;
    let scrollLeft;

    // Mouse events
    navMenu.addEventListener('mousedown', (e) => {
      isDown = true;
      startX = e.pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
      navMenu.style.cursor = 'grabbing';
    });

    navMenu.addEventListener('mouseleave', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mouseup', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });

    // Touch events for mobile/tablet
    navMenu.addEventListener('touchstart', (e) => {
      startX = e.touches[0].pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
    });

    navMenu.addEventListener('touchmove', (e) => {
      const x = e.touches[0].pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });
  }

  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuClose = document.querySelector('.mobile-menu-close');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      mobileMenu.classList.add('active');
      document.body.classList.add('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.add('active');
      }
    });
  }

  if (mobileMenuClose && mobileMenu) {
    mobileMenuClose.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.remove('active');
      }
    });
  }

  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Mobile dropdown toggles
  const mobileDropdownToggles = document.querySelectorAll(
    '.mobile-dropdown-toggle'
  );

  mobileDropdownToggles.forEach(function (toggle) {
    toggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.parentElement;
      const submenu = parent.querySelector('.mobile-submenu');

      if (parent.classList.contains('active')) {
        parent.classList.remove('active');
        submenu.style.maxHeight = '0px';
        this.querySelector('i').classList.remove('fa-chevron-up');
        this.querySelector('i').classList.add('fa-chevron-down');
      } else {
        parent.classList.add('active');
        submenu.style.maxHeight = submenu.scrollHeight + 'px';
        this.querySelector('i').classList.remove('fa-chevron-down');
        this.querySelector('i').classList.add('fa-chevron-up');
      }
    });
  });

  // Sticky header behavior
  const header = document.querySelector('.premium-header');
  const topBar = document.querySelector('.top-bar');
  const logoImage = document.querySelector('.premium-logo-image img');
  const originalLogoSrc = logoImage ? logoImage.src : '';
  const darkLogoSrc = BASE_URL + '/assets/images/logo/logo-chu-trang.svg';
  let lastScrollTop = 0;
  let scrollTimer;
  let ticking = false;
  let lastScrollUpdate = 0; // Thời điểm cập nhật cuối cùng
  const scrollThreshold = 10; // Ngưỡng để thêm/xóa class scrolled
  const throttleDelay = 10; // Thời gian tối thiểu giữa các lần cập nhật (ms)

  if (header) {
    // Đảm bảo header có z-index cao
    header.style.zIndex = '1020';

    // Thêm class để kích hoạt hiệu ứng transition mượt mà
    header.classList.add('smooth-transition');

    // Sử dụng requestAnimationFrame để tối ưu hiệu suất
    window.addEventListener('scroll', function () {
      const now = Date.now();

      // Throttle: chỉ xử lý nếu đã qua đủ thời gian từ lần cập nhật trước
      if (now - lastScrollUpdate > throttleDelay) {
        lastScrollUpdate = now;

        if (!ticking) {
          window.requestAnimationFrame(function () {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollProgress = Math.min(1, scrollTop / (scrollThreshold * 2));

            // Thay vì thêm/xóa class ngay lập tức, áp dụng style trực tiếp để có hiệu ứng mượt mà
            if (scrollTop > 0) {
              // Áp dụng hiệu ứng mờ dần khi cuộn xuống
              const opacity = Math.min(scrollProgress * 2, 1);
              const scale = 0.98 + (0.02 * opacity);
              const translateY = Math.min(scrollProgress * 2, 1) * -2;

              // Thêm class scrolled khi vượt qua ngưỡng
              if (scrollTop > scrollThreshold) {
                if (!header.classList.contains('scrolled')) {
                  header.classList.add('scrolled');
                  if (logoImage) logoImage.src = darkLogoSrc;
                }
              } else {
                if (header.classList.contains('scrolled')) {
                  header.classList.remove('scrolled');
                  if (logoImage) logoImage.src = originalLogoSrc;
                }
              }

              // Áp dụng hiệu ứng transform mượt mà - Tạm thời bỏ qua để đảm bảo sticky header hoạt động
              header.style.setProperty('--header-opacity', opacity);
              // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
              // header.style.setProperty('--header-scale', scale);
              // header.style.setProperty('--header-translate-y', `${translateY}px`);
            } else {
              // Reset về trạng thái ban đầu khi ở đầu trang
              header.classList.remove('scrolled');
              if (logoImage) logoImage.src = originalLogoSrc;
              header.style.setProperty('--header-opacity', 0);
              // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
              // header.style.setProperty('--header-scale', 1);
              // header.style.setProperty('--header-translate-y', '0px');
            }

            // Add compact mode when scrolling down more
            if (scrollTop > 200) {
              header.classList.add('compact');
            } else {
              header.classList.remove('compact');
            }

            // Hide/show top bar based on scroll direction with smoother transition
            if (topBar) {
              if (scrollTop > lastScrollTop && scrollTop > topBar.offsetHeight) {
                // Scrolling down - hide top bar
                topBar.style.transform = 'translateY(-100%)';
              } else {
                // Scrolling up - show top bar
                topBar.style.transform = 'translateY(0)';
              }
            }

            // Add a subtle animation when user stops scrolling
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(function () {
              header.classList.add('scroll-pause');

              // Remove the class after animation completes
              setTimeout(function () {
                header.classList.remove('scroll-pause');
              }, 300);
            }, 150);

            lastScrollTop = scrollTop;
            ticking = false;
          });

          ticking = true;
        }
      }
    }, { passive: true }); // Thêm passive: true để cải thiện hiệu suất

    // Kiểm tra trạng thái ban đầu khi trang tải
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (initialScrollTop > scrollThreshold) {
      header.classList.add('scrolled');
      if (logoImage) logoImage.src = darkLogoSrc;

      // Thiết lập các biến CSS cho hiệu ứng mượt mà
      const scrollProgress = Math.min(1, initialScrollTop / (scrollThreshold * 2));
      const opacity = Math.min(scrollProgress * 2, 1);
      const scale = 0.98 + (0.02 * opacity);
      const translateY = Math.min(scrollProgress * 2, 1) * -2;

      header.style.setProperty('--header-opacity', opacity);
      // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
      // header.style.setProperty('--header-scale', scale);
      // header.style.setProperty('--header-translate-y', `${translateY}px`);
    }
  }

  // Search input focus effect
  const searchInput = document.querySelector('.search-input');
  const searchForm = document.querySelector('.search-form');

  if (searchInput && searchForm) {
    searchInput.addEventListener('focus', function () {
      searchForm.classList.add('focused');

      // Thêm hiệu ứng ripple khi focus
      const ripple = document.createElement('span');
      ripple.classList.add('search-ripple');
      searchForm.appendChild(ripple);

      // Xóa ripple sau khi hiệu ứng hoàn thành
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });

    searchInput.addEventListener('blur', function () {
      searchForm.classList.remove('focused');
    });
  }

  // Live search functionality - VÔ HIỆU HÓA để tránh xung đột với search-improved.js
  if (false && searchInput) {
    // Tạo container cho kết quả tìm kiếm nếu chưa có
    let searchSuggestions = document.querySelector('.search-suggestions');
    if (!searchSuggestions) {
      searchSuggestions = document.createElement('div');
      searchSuggestions.className = 'search-suggestions';
      searchForm.appendChild(searchSuggestions);
    }

    // Thêm CSS inline cho search-suggestions nếu chưa có
    searchSuggestions.style.position = 'absolute';
    searchSuggestions.style.left = '0';
    searchSuggestions.style.right = '0';
    searchSuggestions.style.top = 'calc(100% + 5px)';
    searchSuggestions.style.zIndex = '9999';
    searchSuggestions.style.border = '1px solid #e2e8f0';
    searchSuggestions.style.borderRadius = '0.375rem';
    searchSuggestions.style.backgroundColor = 'white';
    searchSuggestions.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
    searchSuggestions.style.maxHeight = '80vh';
    searchSuggestions.style.overflowY = 'auto';
    searchSuggestions.style.padding = '10px';
    searchSuggestions.style.display = 'none';

    // Xử lý sự kiện input
    searchInput.addEventListener('input', function () {
      const searchValue = this.value.trim();

      if (searchValue.length > 0) {
        // Tạo dữ liệu mẫu để hiển thị
        const sampleData = [
          {
            id: 1,
            name: 'Sofa da cao cấp đẳng cấp dân chơi',
            category: 'Phòng khách',
            price: '15.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-1.jpg',
          },
          {
            id: 2,
            name: 'Sofa da cao cấp Milano',
            category: 'Phòng khách',
            price: '12.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-2.jpg',
          },
          {
            id: 3,
            name: 'Sofa da cao cấp Milanoooooo',
            category: 'Phòng khách',
            price: '12.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-3.jpg',
          },
          {
            id: 4,
            name: 'Tủ bếp hiện đại',
            category: 'Phòng bếp',
            price: '15.000.000 đ',
            image: BASE_URL + '/uploads/products/sample-product-4.jpg',
          },
        ];

        // Lọc dữ liệu mẫu theo từ khóa tìm kiếm
        const filteredData = sampleData.filter(
          (item) =>
            item.name.toLowerCase().includes(searchValue.toLowerCase()) ||
            item.category.toLowerCase().includes(searchValue.toLowerCase())
        );

        // Tạo HTML cho kết quả tìm kiếm
        let resultsHTML = '';

        if (filteredData.length > 0) {
          filteredData.forEach((item) => {
            resultsHTML += `
              <div class="search-item" onclick="window.location.href='${BASE_URL}/san-pham/${
              item.slug
            }'">
                <div class="search-item-image">
                  <div class="skeleton-loading skeleton-wave" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 0.5rem; z-index: 1;"></div>
                  <img
                    src="${item.image}"
                    alt="${item.name}"
                    style="width: 100%; height: 100%; object-fit: cover; border-radius: 0.5rem; position: relative; z-index: 2;"
                    onerror="this.style.display='none';"
                    onload="this.previousElementSibling.style.display='none';"
                  >
                </div>
                <div class="search-item-info">
                  <div class="search-item-name">${item.name}</div>
                  <div class="search-item-meta">
                    <div class="search-item-rating">
                      <i class="fas fa-star"></i>
                      <span>${item.rating || '0.0'}</span>
                    </div>
                    <div class="search-item-sales">
                      <span>${item.sales || '0'} đã bán</span>
                    </div>
                  </div>
                  <div class="search-item-category">${item.category}</div>
                </div>
                <div class="search-item-price">${item.price}</div>
              </div>
            `;
          });
        } else {
          resultsHTML = `
            <div class="search-no-results">
              <i class="fas fa-search" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #999;"></i>
              <p>Không tìm thấy sản phẩm nào phù hợp</p>
            </div>
          `;
        }

        // Cập nhật nội dung và hiển thị kết quả
        searchSuggestions.innerHTML = resultsHTML;
        searchSuggestions.style.display = 'block';
      } else {
        // Ẩn container kết quả nếu input quá ngắn
        searchSuggestions.style.display = 'none';
      }
    });

    // Xử lý sự kiện click bên ngoài để ẩn kết quả
    document.addEventListener('click', function (e) {
      if (!searchForm.contains(e.target)) {
        searchSuggestions.style.display = 'none';
      }
    });

    // Đảm bảo kết quả tìm kiếm hiển thị đúng khi focus vào input
    searchInput.addEventListener('focus', function () {
      if (this.value.trim().length > 0) {
        // Kích hoạt sự kiện input để hiển thị kết quả
        const event = new Event('input', { bubbles: true });
        this.dispatchEvent(event);
      }
    });
  }

  // Tablet touch-friendly dropdowns
  function initTabletDropdowns() {
    const userDropdownToggle = document.querySelector('.user-dropdown .action-btn');
    const userDropdownMenu = document.querySelector('.user-dropdown-menu');
    const cartBtn = document.querySelector('.cart-btn');
    const miniCart = document.querySelector('.mini-cart');



    // Kiểm tra xem có phải tablet không (768px - 1024px)
    function isTablet() {
      return window.innerWidth >= 768 && window.innerWidth <= 1024;
    }

    // Xử lý user dropdown
    if (userDropdownToggle && userDropdownMenu) {
      userDropdownToggle.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation(); // Ngăn event bubbling

          // Toggle dropdown
          const isActive = userDropdownMenu.classList.contains('active');
          const userDropdown = userDropdownToggle.closest('.user-dropdown');

          // Đóng tất cả dropdown khác và reset icon
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded về false để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle dropdown hiện tại
          if (!isActive) {
            userDropdownMenu.classList.add('active');
            userDropdown.classList.add('has-active');
            userDropdownToggle.setAttribute('aria-expanded', 'true');
          } else {
            userDropdown.classList.remove('has-active');
            userDropdownToggle.setAttribute('aria-expanded', 'false');
          }
        }
      });
    }

    // Xử lý cart dropdown
    if (cartBtn && miniCart) {
      cartBtn.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation(); // Ngăn event bubbling

          // Toggle dropdown
          const isActive = miniCart.classList.contains('active');
          const cartContainer = cartBtn.closest('.cart-container');

          // Đóng tất cả dropdown khác và reset icon
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded về false để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle dropdown hiện tại
          if (!isActive) {
            miniCart.classList.add('active');
            cartContainer.classList.add('has-active');
          } else {
            cartContainer.classList.remove('has-active');
          }
        }
      });
    }

    // Đóng dropdown khi click bên ngoài
    document.addEventListener('click', function (e) {
      if (isTablet()) {
        const isClickInsideDropdown = e.target.closest('.user-dropdown, .cart-container');

        if (!isClickInsideDropdown) {
          // Xóa tất cả active states
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });

          // Reset tất cả aria-expanded để icon quay xuống
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });
        }
      }
    });

    // Xử lý khi resize window
    window.addEventListener('resize', function() {
      if (!isTablet()) {
        // Nếu không phải tablet, xóa tất cả active class
        document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
          container.classList.remove('has-active');
        });
        document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
          dropdown.classList.remove('active');
        });

        // Reset tất cả aria-expanded để icon quay xuống
        document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
          btn.setAttribute('aria-expanded', 'false');
        });
      }
    });
  }

  // Khởi tạo tablet dropdowns
  initTabletDropdowns();
});
