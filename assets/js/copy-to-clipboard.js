/**
 * <PERSON><PERSON> chép văn bản vào clipboard
 * 
 * @param {string} text Văn bản cần sao chép
 */
function copyToClipboard(text) {
    // Tạo một phần tử input tạm thời
    const tempInput = document.createElement('input');
    tempInput.value = text;
    document.body.appendChild(tempInput);
    
    // Chọn và sao chép văn bản
    tempInput.select();
    document.execCommand('copy');
    
    // Xóa phần tử input tạm thời
    document.body.removeChild(tempInput);
    
    // Hiển thị thông báo
    const toast = document.createElement('div');
    toast.className = 'copy-toast';
    toast.innerHTML = '<i class="fas fa-check-circle"></i> Đã sao chép liên kết';
    document.body.appendChild(toast);
    
    // Hiển thị toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // Ẩn toast sau 2 giây
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}
