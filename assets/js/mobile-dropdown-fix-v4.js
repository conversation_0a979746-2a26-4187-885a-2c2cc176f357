/**
 * Mobile Dropdown Fix V4 JavaScript for Nội Thất Bàng Vũ
 * Xử lý các tính năng tương tác của dropdown menu trong mobile menu
 * Phiên bản cải tiến với xử lý chiều cao động và sửa lỗi event listener
 * Hỗ trợ tốt hơn cho submenu lồng nhau
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Dropdown Fix V4 loaded');

  // Hàm xử lý sự kiện click cho dropdown toggle
  function handleDropdownToggle(e) {
    e.preventDefault();
    e.stopPropagation();

    // Xác định parent item dựa vào cấu trúc HTML
    let parent;
    if (this.classList.contains('mobile-menu-link')) {
      // Trường hợp menu cấp 1
      parent = this.parentElement;
    } else if (this.classList.contains('mobile-submenu-link')) {
      // Trường hợp submenu
      parent = this.parentElement;
    } else {
      // Trường hợp khác
      parent = this.closest('.mobile-menu-item, .mobile-submenu-item');
    }

    if (!parent) {
      console.error('Không tìm thấy parent item');
      return;
    }

    const submenu = parent.querySelector('.mobile-submenu');
    if (!submenu) {
      console.error('Không tìm thấy submenu');
      return;
    }

    console.log('Toggle clicked:', parent);

    // Kiểm tra trạng thái hiện tại
    if (parent.classList.contains('active')) {
      // Đang mở, cần đóng lại
      closeSubmenu(parent, submenu);
    } else {
      // Đang đóng, cần mở ra

      // Đóng tất cả các submenu cùng cấp khác
      const parentList = parent.parentElement;
      if (parentList) {
        const siblings = parentList.querySelectorAll(
          '.mobile-menu-item.active, .mobile-submenu-item.active'
        );

        siblings.forEach(function (sibling) {
          if (
            sibling !== parent &&
            !parent.contains(sibling) &&
            !sibling.contains(parent)
          ) {
            const siblingSubmenu = sibling.querySelector('.mobile-submenu');
            if (siblingSubmenu) {
              closeSubmenu(sibling, siblingSubmenu);
            }
          }
        });
      }

      // Mở submenu hiện tại
      openSubmenu(parent, submenu);
    }
  }

  // Hàm xử lý sự kiện click cho nút quay lại
  function handleBackButton(e) {
    e.preventDefault();
    e.stopPropagation();

    // Tìm submenu cha
    const submenu = this.closest('.mobile-submenu');
    const parentItem = submenu.closest(
      '.mobile-menu-item, .mobile-submenu-item'
    );

    if (parentItem) {
      console.log('Back button clicked:', parentItem);
      closeSubmenu(parentItem, submenu);
    }
  }

  // Hàm mở submenu
  function openSubmenu(parent, submenu) {
    if (!submenu) return;

    // Thêm class active cho parent
    parent.classList.add('active');

    // Thêm hiệu ứng mượt mà khi mở submenu
    submenu.style.opacity = '0';

    // Đặt chiều cao cho submenu
    setTimeout(function () {
      // Đảm bảo tính toán chính xác scrollHeight
      submenu.style.maxHeight = 'none'; // Tạm thời bỏ giới hạn chiều cao để tính toán chính xác
      const scrollHeight = submenu.scrollHeight;

      // Thêm hiệu ứng mượt mà
      submenu.style.transition =
        'max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease';
      submenu.style.maxHeight = scrollHeight + 'px';
      submenu.style.marginBottom = '1rem';
      submenu.style.opacity = '1';

      console.log('Opening submenu, height:', scrollHeight);

      // Nếu parent là submenu-item và nằm trong một submenu khác,
      // cập nhật chiều cao của submenu cha
      updateParentSubmenuHeight(parent, submenu);
    }, 10);
  }

  // Hàm đệ quy để cập nhật chiều cao của tất cả submenu cha
  function updateParentSubmenuHeight(parent, submenu) {
    const parentSubmenu = parent.closest('.mobile-submenu');
    if (parentSubmenu && parentSubmenu !== submenu) {
      // Tạm thời bỏ giới hạn chiều cao để tính toán chính xác
      parentSubmenu.style.maxHeight = 'none';

      // Đợi một chút để DOM cập nhật
      setTimeout(function () {
        // Lấy chiều cao thực tế của submenu cha
        const newParentHeight = parentSubmenu.scrollHeight;

        // Thêm hiệu ứng mượt mà khi cập nhật chiều cao
        parentSubmenu.style.transition =
          'max-height 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        parentSubmenu.style.maxHeight = newParentHeight + 'px';
        console.log('Updating parent submenu height:', newParentHeight);

        // Đệ quy lên các cấp cha cao hơn
        const grandParent = parentSubmenu.closest(
          '.mobile-menu-item, .mobile-submenu-item'
        );
        if (grandParent) {
          updateParentSubmenuHeight(grandParent, parentSubmenu);
        }
      }, 30); // Tăng thời gian chờ để đảm bảo animation mượt mà
    }
  }

  // Hàm đóng submenu
  function closeSubmenu(parent, submenu) {
    if (!submenu) return;

    // Xóa class active cho parent
    parent.classList.remove('active');

    // Thêm hiệu ứng mượt mà khi đóng submenu
    submenu.style.transition =
      'max-height 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53), opacity 0.2s ease, margin-bottom 0.2s ease';
    submenu.style.opacity = '0.5';

    // Đặt chiều cao về 0
    submenu.style.maxHeight = '0';
    submenu.style.marginBottom = '0';

    console.log('Closing submenu');

    // Đóng tất cả các submenu con
    const childItems = submenu.querySelectorAll(
      '.mobile-menu-item.active, .mobile-submenu-item.active'
    );
    childItems.forEach(function (item) {
      const childSubmenu = item.querySelector('.mobile-submenu');
      if (childSubmenu) {
        closeSubmenu(item, childSubmenu);
      }
    });

    // Nếu parent là submenu-item và nằm trong một submenu khác,
    // cập nhật chiều cao của submenu cha
    const parentSubmenu = parent.closest('.mobile-submenu');
    if (
      parentSubmenu &&
      parentSubmenu !== submenu &&
      parentSubmenu.classList.contains('active')
    ) {
      // Cập nhật chiều cao của submenu cha
      setTimeout(function () {
        // Tạm thời bỏ giới hạn chiều cao để tính toán chính xác
        parentSubmenu.style.maxHeight = 'none';
        const newParentHeight = parentSubmenu.scrollHeight;

        // Thêm hiệu ứng mượt mà khi cập nhật chiều cao
        parentSubmenu.style.transition =
          'max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        parentSubmenu.style.maxHeight = newParentHeight + 'px';
        console.log(
          'Updating parent submenu height after closing:',
          newParentHeight
        );

        // Đệ quy lên các cấp cha cao hơn
        const grandParent = parentSubmenu.closest(
          '.mobile-menu-item, .mobile-submenu-item'
        );
        if (grandParent) {
          updateParentSubmenuHeight(grandParent, parentSubmenu);
        }
      }, 30); // Tăng thời gian chờ để đảm bảo animation mượt mà
    }
  }

  // Thiết lập chiều cao ban đầu cho tất cả submenu
  function setupSubmenuHeights() {
    const allSubmenus = document.querySelectorAll('.mobile-submenu');

    // Đầu tiên, đặt tất cả chiều cao về 0
    allSubmenus.forEach(function (submenu) {
      submenu.style.maxHeight = '0';
      submenu.style.marginBottom = '0';
    });

    // Sau đó, mở các submenu đang active từ cấp cao nhất xuống
    // Lấy các menu item cấp 1 đang active
    const activeTopItems = document.querySelectorAll(
      '.mobile-menu-item.active'
    );

    activeTopItems.forEach(function (item) {
      const submenu = item.querySelector('.mobile-submenu');
      if (submenu) {
        openSubmenu(item, submenu);

        // Xử lý các submenu con đang active
        processActiveChildren(submenu);
      }
    });
  }

  // Hàm đệ quy xử lý các submenu con đang active
  function processActiveChildren(parentSubmenu) {
    const activeChildren = parentSubmenu.querySelectorAll(
      ':scope > .mobile-submenu-item.active'
    );

    activeChildren.forEach(function (child) {
      const childSubmenu = child.querySelector('.mobile-submenu');
      if (childSubmenu) {
        // Đảm bảo mở submenu con
        setTimeout(function () {
          openSubmenu(child, childSubmenu);
          // Đệ quy xuống các cấp con tiếp theo
          processActiveChildren(childSubmenu);
        }, 50);
      }
    });
  }

  // Xử lý dropdown menu
  function initMobileDropdowns() {
    // Lấy tất cả các dropdown toggle
    const mobileDropdownToggles = document.querySelectorAll(
      '.mobile-dropdown-toggle'
    );
    console.log('Mobile dropdown toggles:', mobileDropdownToggles.length);

    if (mobileDropdownToggles.length > 0) {
      // Xử lý sự kiện click cho các dropdown toggle
      mobileDropdownToggles.forEach(function (toggle) {
        // Xóa tất cả event listener cũ bằng cách clone và thay thế
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);

        // Thêm event listener mới
        newToggle.addEventListener('click', handleDropdownToggle);
      });
    }

    // Xử lý nút quay lại
    const mobileBackButtons = document.querySelectorAll('.mobile-menu-back');
    console.log('Mobile back buttons:', mobileBackButtons.length);

    if (mobileBackButtons.length > 0) {
      mobileBackButtons.forEach(function (button) {
        // Xóa tất cả event listener cũ bằng cách clone và thay thế
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Thêm event listener mới
        newButton.addEventListener('click', handleBackButton);
      });
    }

    // Thiết lập chiều cao ban đầu cho tất cả submenu
    setupSubmenuHeights();
  }

  // Xử lý khi cửa sổ thay đổi kích thước
  window.addEventListener('resize', function () {
    // Sử dụng debounce để tránh gọi quá nhiều lần
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(function () {
      console.log('Window resized, updating submenu heights');

      // Cập nhật lại chiều cao cho tất cả submenu
      // Bắt đầu từ cấp cao nhất
      const activeTopItems = document.querySelectorAll(
        '.mobile-menu-item.active'
      );

      activeTopItems.forEach(function (item) {
        const submenu = item.querySelector('.mobile-submenu');
        if (submenu) {
          // Tạm thời bỏ giới hạn chiều cao để tính toán chính xác
          submenu.style.maxHeight = 'none';
          const scrollHeight = submenu.scrollHeight;
          submenu.style.maxHeight = scrollHeight + 'px';
          console.log(
            'Resize: updating top level submenu height:',
            scrollHeight
          );

          // Xử lý các submenu con đang active
          updateActiveChildrenOnResize(submenu);
        }
      });
    }, 250); // Đợi 250ms sau khi resize kết thúc
  });

  // Hàm đệ quy cập nhật chiều cao submenu con khi resize
  function updateActiveChildrenOnResize(parentSubmenu) {
    const activeChildren = parentSubmenu.querySelectorAll(
      ':scope > .mobile-submenu-item.active'
    );

    activeChildren.forEach(function (child) {
      const childSubmenu = child.querySelector('.mobile-submenu');
      if (childSubmenu) {
        // Tạm thời bỏ giới hạn chiều cao để tính toán chính xác
        childSubmenu.style.maxHeight = 'none';
        const scrollHeight = childSubmenu.scrollHeight;
        childSubmenu.style.maxHeight = scrollHeight + 'px';
        console.log('Resize: updating child submenu height:', scrollHeight);

        // Đệ quy xuống các cấp con tiếp theo
        updateActiveChildrenOnResize(childSubmenu);
      }
    });

    // Cập nhật lại chiều cao của submenu cha sau khi đã cập nhật tất cả con
    parentSubmenu.style.maxHeight = 'none';
    const newHeight = parentSubmenu.scrollHeight;
    parentSubmenu.style.maxHeight = newHeight + 'px';
    console.log('Resize: updating parent after children:', newHeight);
  }

  // Khởi tạo dropdown menu sau khi DOM đã tải hoàn toàn
  setTimeout(function () {
    initMobileDropdowns();
  }, 500);
});
