/*
 * Enhanced Search JavaScript for Nội Thất Băng <PERSON>ũ
 * <PERSON><PERSON> lý auto-suggestions, clear search, và filter interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('main-search-input');
    const suggestionsContainer = document.getElementById('search-suggestions');
    const clearBtn = document.querySelector('.clear-search-btn');
    
    let searchTimeout;
    let currentSuggestions = [];
    let selectedIndex = -1;
    let isFromQuickSearch = false; // Flag to track if input is from Quick Search Tags
    let enterKeyPressed = false; // Flag to prevent suggestions after Enter key

    // Auto-suggestions functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const keyword = this.value.trim();

            // Clear previous timeout
            clearTimeout(searchTimeout);

            // Reset Enter key flag when user starts typing
            enterKeyPressed = false;

            // Don't show suggestions if input is from Quick Search Tags
            if (isFromQuickSearch) {
                hideSuggestions();
                return;
            }

            // Don't show suggestions if Enter key was just pressed
            if (enterKeyPressed) {
                hideSuggestions();
                return;
            }

            if (keyword.length < 1) {
                hideSuggestions();
                return;
            }

            // Debounce search requests - adaptive timing
            let debounceTime;
            if (keyword.length === 1) {
                debounceTime = 600; // Longer delay for single character
            } else if (keyword.length === 2) {
                debounceTime = 400; // Medium delay for 2 characters
            } else {
                debounceTime = 200; // Fast for 3+ characters
            }

            searchTimeout = setTimeout(() => {
                fetchSuggestions(keyword);
            }, debounceTime);
        });

        // Reset Quick Search flag when user starts typing manually
        searchInput.addEventListener('keydown', function(e) {
            // Reset flags when user types any character (not navigation keys)
            if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Enter', 'Escape', 'Tab'].includes(e.key)) {
                isFromQuickSearch = false;
                enterKeyPressed = false;
            }
        });

        // Keyboard navigation with wrap-around and auto-scroll
        searchInput.addEventListener('keydown', function(e) {
            // Xử lý Enter key trước - luôn ưu tiên
            if (e.key === 'Enter') {
                e.preventDefault();
                // Set flag để ngăn hiển thị suggestions
                enterKeyPressed = true;
                // Luôn ẩn suggestions trước
                hideSuggestions();

                // Reset flag sau một khoảng thời gian ngắn
                setTimeout(() => {
                    enterKeyPressed = false;
                }, 500);

                // Nếu có suggestion được chọn thì chọn nó
                if (selectedIndex >= 0 && currentSuggestions[selectedIndex]) {
                    selectSuggestion(currentSuggestions[selectedIndex]);
                } else {
                    // Không có suggestion được chọn - trigger AJAX search
                    if (searchInput.value.trim().length > 0) {
                        triggerAjaxSearch();
                    }
                }
                return;
            }

            // Xử lý Escape key
            if (e.key === 'Escape') {
                hideSuggestions();
                // Clear search if has content
                if (searchInput.value.trim().length > 0) {
                    searchInput.value = '';
                    searchInput.focus();
                }
                return;
            }

            // Chỉ xử lý navigation keys khi suggestions đang hiển thị
            if (!suggestionsContainer.classList.contains('hidden')) {
                switch(e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        navigateDown();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        navigateUp();
                        break;
                }
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions();
            }
        });
    }

    // Clear search functionality
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            hideSuggestions();
        });
    }

    // Fetch suggestions from server with minimum loading time
    function fetchSuggestions(keyword) {
        // Don't fetch if Enter key was just pressed
        if (enterKeyPressed) {
            return;
        }

        // Show loading state
        showLoadingState();

        // Record start time for minimum loading duration
        const startTime = Date.now();
        // Adaptive minimum loading time based on keyword length - Tăng thời gian để người dùng kịp nhận biết
        const minLoadingTime = keyword.length === 1 ? 800 :
                              keyword.length === 2 ? 700 : 600;

        fetch(`${BASE_URL}/api/search_suggestions.php?keyword=${encodeURIComponent(keyword)}`)
            .then(response => response.json())
            .then(data => {
                // Calculate elapsed time
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

                // Delay showing results to ensure minimum loading time
                setTimeout(() => {
                    // Add fade transition effect
                    suggestionsContainer.style.opacity = '0.5';

                    setTimeout(() => {
                        if (data.suggestions && data.suggestions.length > 0) {
                            currentSuggestions = data.suggestions;
                            displaySuggestions(data.suggestions);
                        } else {
                            showNoResultsSuggestion();
                        }

                        // Fade in the results
                        suggestionsContainer.style.opacity = '1';
                    }, 100);
                }, remainingTime);
            })
            .catch(error => {
                console.error('Error fetching suggestions:', error);

                // Calculate elapsed time for error case too
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

                setTimeout(() => {
                    showErrorSuggestion();
                }, remainingTime);
            });
    }

    // Show error suggestion
    function showErrorSuggestion() {
        suggestionsContainer.innerHTML = `
            <div class="search-suggestion-item no-results">
                <div class="flex flex-col items-center p-4 text-center">
                    <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                    <div class="text-gray-500 text-sm">
                        Có lỗi xảy ra khi tìm kiếm
                    </div>
                </div>
            </div>
        `;
        suggestionsContainer.classList.remove('hidden');
    }

    // Display suggestions with rich product data
    function displaySuggestions(suggestions) {
        if (suggestions.length === 0) {
            showNoResultsSuggestion();
            return;
        }

        let html = '';
        suggestions.forEach((suggestion, index) => {
            // Check if suggestion is a product object or simple string
            if (typeof suggestion === 'object' && suggestion.id) {
                // Rich product suggestion
                const salePrice = suggestion.price_type === 'contact' ? 'Liên hệ' : suggestion.price;
                const rating = parseFloat(suggestion.rating);
                const stars = generateStarRating(rating);

                html += `
                    <div class="search-suggestion-item product-suggestion" data-index="${index}" onclick="window.location.href='${suggestion.url}'">
                        <div class="flex items-center p-3 hover:bg-orange-50 transition-colors">
                            <div class="flex-shrink-0 w-12 h-12 mr-3">
                                <img src="${suggestion.image}"
                                     alt="${suggestion.name}"
                                     class="w-full h-full object-cover rounded-lg border border-gray-200"
                                     onerror="this.src='${BASE_URL}/assets/img/no-image.jpg'">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="font-medium text-gray-900 truncate">
                                    ${highlightMatch(suggestion.name, searchInput.value)}
                                </div>
                                <div class="text-sm text-gray-500 truncate">
                                    ${suggestion.category}
                                </div>
                                <div class="flex items-center mt-1">
                                    <div class="text-sm font-semibold text-primary mr-2">
                                        ${salePrice}
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400">
                                        ${stars}
                                        <span class="ml-1">(${suggestion.sales} đã bán)</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ml-2">
                                <i class="fas fa-arrow-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // Simple text suggestion (fallback)
                const suggestionText = typeof suggestion === 'string' ? suggestion : suggestion.name;
                html += `
                    <div class="search-suggestion-item simple-suggestion" data-index="${index}" onclick="selectSuggestion('${suggestionText.replace(/'/g, "\\'")}')">
                        <div class="flex items-center p-3 hover:bg-orange-50 transition-colors">
                            <i class="fas fa-search text-gray-400 mr-3"></i>
                            <span class="text-gray-700">${highlightMatch(suggestionText, searchInput.value)}</span>
                        </div>
                    </div>
                `;
            }
        });

        suggestionsContainer.innerHTML = html;
        suggestionsContainer.classList.remove('hidden');
        selectedIndex = -1;
    }

    // Show no results suggestion
    function showNoResultsSuggestion() {
        const keyword = searchInput.value.trim();
        suggestionsContainer.innerHTML = `
            <div class="search-suggestion-item no-results">
                <div class="flex flex-col items-center p-4 text-center">
                    <i class="fas fa-search text-gray-300 text-2xl mb-2"></i>
                    <div class="text-gray-500 text-sm mb-2">
                        Không tìm thấy sản phẩm cho "${keyword}"
                    </div>
                    <button onclick="submitSearch('${keyword}')" class="text-primary text-sm hover:underline">
                        Tìm kiếm tất cả kết quả
                    </button>
                </div>
            </div>
        `;
        suggestionsContainer.classList.remove('hidden');
    }

    // Generate star rating HTML
    function generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let stars = '';

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars += '<i class="fas fa-star text-yellow-400"></i>';
            } else if (i === fullStars && hasHalfStar) {
                stars += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
            } else {
                stars += '<i class="far fa-star text-gray-300"></i>';
            }
        }

        return stars;
    }

    // Show enhanced loading state with multiple loading items
    function showLoadingState() {
        const loadingMessages = [
            'Đang tìm kiếm sản phẩm...',
            'Đang phân tích từ khóa...',
            'Đang tải kết quả...'
        ];

        const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];

        suggestionsContainer.innerHTML = `
            <div class="search-suggestion-item loading-item">
                <div class="flex items-center p-3">
                    <div class="flex-shrink-0 w-12 h-12 mr-3">
                        <div class="loading-skeleton-image"></div>
                    </div>
                    <div class="flex-1">
                        <div class="loading-skeleton-text mb-2"></div>
                        <div class="loading-skeleton-text-small mb-1"></div>
                        <div class="loading-skeleton-text-small"></div>
                    </div>
                    <div class="flex-shrink-0 ml-2">
                        <i class="fas fa-spinner fa-spin text-primary"></i>
                    </div>
                </div>
            </div>
            <div class="search-suggestion-item loading-item">
                <div class="flex items-center p-3">
                    <div class="flex-shrink-0 w-12 h-12 mr-3">
                        <div class="loading-skeleton-image"></div>
                    </div>
                    <div class="flex-1">
                        <div class="loading-skeleton-text mb-2"></div>
                        <div class="loading-skeleton-text-small mb-1"></div>
                        <div class="loading-skeleton-text-small"></div>
                    </div>
                    <div class="flex-shrink-0 ml-2">
                        <i class="fas fa-spinner fa-spin text-primary"></i>
                    </div>
                </div>
            </div>
            <div class="search-suggestion-item loading-item">
                <div class="flex items-center p-3">
                    <div class="flex-shrink-0 w-12 h-12 mr-3">
                        <div class="loading-skeleton-image"></div>
                    </div>
                    <div class="flex-1">
                        <div class="loading-skeleton-text mb-2"></div>
                        <div class="loading-skeleton-text-small mb-1"></div>
                        <div class="loading-skeleton-text-small"></div>
                    </div>
                    <div class="flex-shrink-0 ml-2">
                        <i class="fas fa-spinner fa-spin text-primary"></i>
                    </div>
                </div>
            </div>
            <div class="search-loading-message">
                <div class="flex items-center justify-center p-3" style="display: flex !important; align-items: center !important; justify-content: center !important; gap: 0.5rem !important;">
                    <i class="fas fa-search text-primary" style="display: inline-flex !important; align-items: center !important; vertical-align: middle !important; line-height: 1 !important;"></i>
                    <span class="text-gray-600 text-sm" style="display: inline-flex !important; align-items: center !important; line-height: 1 !important;">Đang tìm kiếm sản phẩm...</span>
                </div>
            </div>
        `;
        suggestionsContainer.classList.remove('hidden');
    }

    // Hide suggestions
    function hideSuggestions() {
        suggestionsContainer.classList.add('hidden');
        selectedIndex = -1;

        // Hủy timeout đang chờ để tránh hiển thị suggestions sau khi đã ẩn
        if (searchTimeout) {
            clearTimeout(searchTimeout);
            searchTimeout = null;
        }
    }

    // Trigger AJAX search instead of form submit
    function triggerAjaxSearch() {
        // Kiểm tra xem AJAX filter có active không
        if (window.ajaxFilterActive && window.ajaxFilter && window.ajaxFilter.handleMainSearchSubmit) {
            // Sử dụng AJAX search
            window.ajaxFilter.handleMainSearchSubmit();
        } else {
            // Fallback: submit form thông thường
            const form = searchInput.closest('form');
            if (form) {
                form.submit();
            }
        }
    }

    // Navigation functions with wrap-around
    function navigateDown() {
        if (currentSuggestions.length === 0) return;

        // Wrap-around: từ cuối quay về đầu
        if (selectedIndex >= currentSuggestions.length - 1) {
            selectedIndex = 0;
        } else {
            selectedIndex++;
        }

        updateSelection();
        scrollToSelectedItem();
    }

    function navigateUp() {
        if (currentSuggestions.length === 0) return;

        // Wrap-around: từ đầu quay về cuối
        if (selectedIndex <= 0) {
            selectedIndex = currentSuggestions.length - 1;
        } else {
            selectedIndex--;
        }

        updateSelection();
        scrollToSelectedItem();
    }

    // Update selection highlight
    function updateSelection() {
        const items = suggestionsContainer.querySelectorAll('.search-suggestion-item');
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.style.backgroundColor = '#fff0e8';
                item.setAttribute('data-keyboard-selected', 'true');

                // Fix: Extract the correct text from suggestion object
                const suggestion = currentSuggestions[index];
                if (typeof suggestion === 'object' && suggestion.name) {
                    searchInput.value = suggestion.name;
                } else if (typeof suggestion === 'string') {
                    searchInput.value = suggestion;
                } else {
                    searchInput.value = suggestion.name || suggestion.toString();
                }
            } else {
                item.style.backgroundColor = '';
                item.removeAttribute('data-keyboard-selected');
            }
        });
    }

    // Auto-scroll to selected item
    function scrollToSelectedItem() {
        if (selectedIndex < 0) return;

        const items = suggestionsContainer.querySelectorAll('.search-suggestion-item');
        const selectedItem = items[selectedIndex];

        if (!selectedItem) return;

        const containerRect = suggestionsContainer.getBoundingClientRect();
        const itemRect = selectedItem.getBoundingClientRect();

        // Kiểm tra nếu item nằm ngoài viewport của container
        const isAboveViewport = itemRect.top < containerRect.top;
        const isBelowViewport = itemRect.bottom > containerRect.bottom;

        if (isAboveViewport || isBelowViewport) {
            // Smooth scroll đến item được chọn
            selectedItem.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'nearest'
            });
        }
    }

    // Select suggestion
    function selectSuggestion(suggestion) {
        // Fix: Handle both object and string suggestions
        if (typeof suggestion === 'object' && suggestion.name) {
            searchInput.value = suggestion.name;
        } else if (typeof suggestion === 'string') {
            searchInput.value = suggestion;
        } else {
            searchInput.value = suggestion.name || suggestion.toString();
        }
        hideSuggestions();
        // Optionally submit form automatically
        // searchInput.closest('form').submit();
    }

    // Highlight matching text
    function highlightMatch(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<strong class="text-primary">$1</strong>');
    }

    // Submit search function
    function submitSearch(keyword) {
        const form = searchInput.closest('form');
        if (form) {
            searchInput.value = keyword;
            form.submit();
        } else {
            // Fallback: redirect manually
            window.location.href = `${BASE_URL}/products.php?keyword=${encodeURIComponent(keyword)}`;
        }
    }

    // Make submitSearch globally available
    window.submitSearch = submitSearch;

    // Expose function to set Quick Search flag
    window.setQuickSearchFlag = function(value) {
        isFromQuickSearch = value;
    };

    // Filter change handlers
    const filterSelects = document.querySelectorAll('.filter-select');
    const filterInputs = document.querySelectorAll('.filter-input');

    // Auto-submit on filter change (optional)
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Add visual feedback
            this.style.borderColor = 'var(--primary)';
            setTimeout(() => {
                this.style.borderColor = '';
            }, 300);
        });
    });

    filterInputs.forEach(input => {
        input.addEventListener('blur', function() {
            // Validate price inputs (only for visible inputs, not hidden ones)
            if ((this.name === 'price_min' || this.name === 'price_max') && this.type !== 'hidden') {
                const value = parseFloat(this.value);
                if (value < 0) {
                    this.value = '';
                    showToast('Giá không thể âm', 'error');
                }
            }
        });
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'error' ? 'bg-red-500' : 'bg-primary'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Form validation
    const searchForm = document.querySelector('form[action*="search.php"]');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const keyword = searchInput.value.trim();
            const priceMinInput = document.querySelector('input[name="price_min"]');
            const priceMaxInput = document.querySelector('input[name="price_max"]');
            const priceMin = priceMinInput ? priceMinInput.value : '';
            const priceMax = priceMaxInput ? priceMaxInput.value : '';
            
            // Validate price range
            if (priceMin && priceMax && parseFloat(priceMin) > parseFloat(priceMax)) {
                e.preventDefault();
                showToast('Giá tối thiểu không thể lớn hơn giá tối đa', 'error');
                return;
            }
            
            // Require at least keyword or filters
            if (!keyword && !priceMin && !priceMax && 
                !document.querySelector('select[name="category"]').value &&
                !document.querySelector('select[name="promotion"]').value) {
                e.preventDefault();
                showToast('Vui lòng nhập từ khóa hoặc chọn bộ lọc', 'error');
                searchInput.focus();
                return;
            }
        });
    }
});

// Global function for suggestion selection (called from HTML)
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('main-search-input');
    const suggestionsContainer = document.getElementById('search-suggestions');

    if (searchInput) {
        // Fix: Handle both object and string suggestions
        if (typeof suggestion === 'object' && suggestion.name) {
            searchInput.value = suggestion.name;
        } else if (typeof suggestion === 'string') {
            searchInput.value = suggestion;
        } else {
            searchInput.value = suggestion.name || suggestion.toString();
        }
        suggestionsContainer.classList.add('hidden');
        searchInput.focus();
    }
}

// Global function for clearing search
function clearSearch() {
    const searchInput = document.getElementById('main-search-input');
    const suggestionsContainer = document.getElementById('search-suggestions');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
        suggestionsContainer.classList.add('hidden');
    }
}

// Custom Dropdown Functionality
document.addEventListener('DOMContentLoaded', function() {
    initCustomDropdowns();
    initPriceRangeModal();
});

function initCustomDropdowns() {
    const customDropdowns = document.querySelectorAll('.custom-dropdown');

    customDropdowns.forEach(dropdown => {
        const trigger = dropdown.querySelector('.custom-dropdown-trigger');
        const menu = dropdown.querySelector('.custom-dropdown-menu');
        const options = dropdown.querySelectorAll('.custom-dropdown-option');
        const hiddenInput = dropdown.querySelector('input[type="hidden"]');

        // Toggle dropdown
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Close other dropdowns
            customDropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('open');
                }
            });

            // Toggle current dropdown
            dropdown.classList.toggle('open');
        });

        // Select option
        options.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Check if this is the custom price option
                if (this.classList.contains('custom-price-option')) {
                    dropdown.classList.remove('open');
    
                    openPriceModal();
                    return;
                }

                // Handle price range options
                if (this.hasAttribute('data-price-min') || this.hasAttribute('data-price-max')) {
                    handlePriceRangeSelection(this, dropdown);
                    return;
                }

                // Regular dropdown option handling
                // Remove selected class from all options
                options.forEach(opt => opt.classList.remove('selected'));

                // Add selected class to clicked option
                this.classList.add('selected');

                // Update trigger text
                trigger.textContent = this.textContent;

                // Update hidden input value
                if (hiddenInput) {
                    hiddenInput.value = this.dataset.value;
                }

                // Close dropdown
                dropdown.classList.remove('open');

                // Trigger change event
                if (hiddenInput) {
                    hiddenInput.dispatchEvent(new Event('change'));
                }

                // Auto-submit form for category, promotion, and sort filters
                const form = dropdown.closest('form');
                if (form && hiddenInput) {
                    const inputName = hiddenInput.name;
                    // Auto-submit for category, promotion, and sort filters
                    if (inputName === 'category' || inputName === 'promotion' || inputName === 'sort') {
                        form.submit();
                    }
                }
            });
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        customDropdowns.forEach(dropdown => {
            dropdown.classList.remove('open');
        });
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            customDropdowns.forEach(dropdown => {
                dropdown.classList.remove('open');
            });
        }
    });
}

// Price Range Dropdown Handling
function handlePriceRangeSelection(option, dropdown) {
    const priceMin = option.dataset.priceMin;
    const priceMax = option.dataset.priceMax;
    const trigger = dropdown.querySelector('.custom-dropdown-trigger');
    const options = dropdown.querySelectorAll('.custom-dropdown-option');

    // Remove selected class from all options
    options.forEach(opt => opt.classList.remove('selected'));

    // Add selected class to clicked option
    option.classList.add('selected');

    // Update trigger text
    trigger.textContent = option.textContent;

    // Update hidden inputs
    const priceMinInput = document.querySelector('input[name="price_min"]');
    const priceMaxInput = document.querySelector('input[name="price_max"]');

    if (priceMinInput) priceMinInput.value = priceMin || '';
    if (priceMaxInput) priceMaxInput.value = priceMax || '';

    // Close dropdown
    dropdown.classList.remove('open');

    // Auto-submit form
    const form = dropdown.closest('form');
    if (form) {
        form.submit();
    }
}

// Price Range Modal Functionality
function initPriceRangeModal() {
    const modal = document.getElementById('custom-price-modal');
    const closeBtn = document.getElementById('close-price-modal');
    const cancelBtn = document.getElementById('cancel-custom-price');
    const applyBtn = document.getElementById('apply-custom-price');
    const customMinInput = document.getElementById('custom-price-min');
    const customMaxInput = document.getElementById('custom-price-max');

    if (!modal) return;

    // Initialize input formatting
    initPriceInputFormatting();

    // Close modal handlers
    [closeBtn, cancelBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', closePriceModal);
        }
    });

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closePriceModal();
        }
    });

    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            closePriceModal();
        }
    });

    // Apply custom price range
    if (applyBtn) {
        applyBtn.addEventListener('click', function() {
            // Get clean values (without dots) - ensure we always get the current value
            let minValue = customMinInput.value.replace(/\./g, '');
            let maxValue = customMaxInput.value.replace(/\./g, '');

            // Update data-clean-value attributes
            if (minValue) customMinInput.setAttribute('data-clean-value', minValue);
            if (maxValue) customMaxInput.setAttribute('data-clean-value', maxValue);

            // Validate inputs
            if (minValue && maxValue && parseFloat(minValue) > parseFloat(maxValue)) {
                showPriceWarning('Giá tối thiểu không thể lớn hơn giá tối đa');
                return;
            }

            if (minValue && parseFloat(minValue) < 0) {
                showPriceWarning('Giá không thể âm');
                return;
            }

            if (maxValue && parseFloat(maxValue) < 0) {
                showPriceWarning('Giá không thể âm');
                return;
            }

            // Validate length (max 12 digits = 999 billion)
            if (minValue && minValue.length > 12) {
                showPriceWarning('Giá tối thiểu không được vượt quá 999 tỷ đồng');
                return;
            }

            if (maxValue && maxValue.length > 12) {
                showPriceWarning('Giá tối đa không được vượt quá 999 tỷ đồng');
                return;
            }

            // Update hidden inputs with clean values
            const priceMinInput = document.querySelector('input[name="price_min"]');
            const priceMaxInput = document.querySelector('input[name="price_max"]');

            if (priceMinInput) {
                priceMinInput.value = minValue;
            }
            if (priceMaxInput) {
                priceMaxInput.value = maxValue;
            }

            // Update dropdown trigger text
            updatePriceDropdownText(minValue, maxValue);

            // Close modal
            closePriceModal();

            // Auto-submit form với nhiều cách tìm form
            let form = priceMinInput?.closest('form');
            if (!form) {
                form = document.querySelector('form[action*="search.php"]');
            }
            if (!form) {
                // Fallback: tìm form chứa input name="keyword"
                form = document.querySelector('form input[name="keyword"]')?.closest('form');
            }
            if (!form) {
                // Fallback: tìm form đầu tiên trong trang
                form = document.querySelector('form');
            }

            if (form) {
                // Show loading indicator
                showLoadingIndicator();

                // Submit form
                form.submit();
            } else {
                console.error('Could not find form to submit');
                // Fallback: reload page với parameters
                const url = new URL(window.location);
                url.searchParams.set('price_min', minValue || '');
                url.searchParams.set('price_max', maxValue || '');

                // Show loading indicator
                showLoadingIndicator();

                window.location.href = url.toString();
            }
        });
    }
}

function openPriceModal() {
    const modal = document.getElementById('custom-price-modal');
    const customMinInput = document.getElementById('custom-price-min');
    const customMaxInput = document.getElementById('custom-price-max');
    const priceMinInput = document.querySelector('input[name="price_min"]');
    const priceMaxInput = document.querySelector('input[name="price_max"]');

    if (!modal) {
        console.error('Custom price modal not found!');
        return;
    }

    // Pre-fill with current values and format them
    if (customMinInput && priceMinInput && priceMinInput.value) {
        const minValue = priceMinInput.value;
        customMinInput.value = formatPriceInput(minValue);
        customMinInput.setAttribute('data-clean-value', minValue);
    }
    if (customMaxInput && priceMaxInput && priceMaxInput.value) {
        const maxValue = priceMaxInput.value;
        customMaxInput.value = formatPriceInput(maxValue);
        customMaxInput.setAttribute('data-clean-value', maxValue);
    }

    // Đảm bảo modal nằm ở cấp cao nhất trong DOM
    document.body.appendChild(modal);

    // Đảm bảo modal có z-index cao nhất
    modal.style.zIndex = '2147483647';

    // Đảm bảo các phần tử con của modal có z-index cao
    const modalBox = modal.querySelector('.bg-white');
    if (modalBox) {
        modalBox.style.zIndex = '2147483647';
    }

    // Show modal với class active
    modal.classList.remove('hidden');

    // Force reflow để đảm bảo CSS được áp dụng
    modal.offsetHeight;

    modal.classList.add('active');

    // Thêm class modal-active vào body để vô hiệu hóa cuộn trang
    document.body.classList.add('modal-active');



    // Focus on first input
    if (customMinInput) {
        setTimeout(() => customMinInput.focus(), 100);
    }
}

function closePriceModal() {
    const modal = document.getElementById('custom-price-modal');
    if (modal) {
        modal.classList.remove('active');

        // Xóa class modal-active khỏi body
        document.body.classList.remove('modal-active');

        // Thêm lại class hidden sau khi animation hoàn thành
        setTimeout(() => {
            if (!modal.classList.contains('active')) {
                modal.classList.add('hidden');
            }
        }, 300);
    }
}

function updatePriceDropdownText(minValue, maxValue) {
    const dropdown = document.querySelector('.custom-dropdown');
    const trigger = dropdown?.querySelector('.custom-dropdown-trigger');

    if (!trigger) return;

    let displayText = 'Tất cả mức giá';

    if (minValue && maxValue) {
        displayText = `${formatPrice(minValue)} - ${formatPrice(maxValue)} VNĐ`;
    } else if (minValue) {
        displayText = `Từ ${formatPrice(minValue)} VNĐ`;
    } else if (maxValue) {
        displayText = `Đến ${formatPrice(maxValue)} VNĐ`;
    }

    trigger.textContent = displayText;

    // Update selected option
    const options = dropdown.querySelectorAll('.custom-dropdown-option');
    options.forEach(opt => opt.classList.remove('selected'));

    // If it matches a predefined range, select that option
    const matchingOption = findMatchingPriceOption(minValue, maxValue);
    if (matchingOption) {
        matchingOption.classList.add('selected');
    }
}

function findMatchingPriceOption(minValue, maxValue) {
    const dropdown = document.querySelector('.custom-dropdown');
    if (!dropdown) return null;

    const options = dropdown.querySelectorAll('.custom-dropdown-option[data-price-min]');

    for (const option of options) {
        const optionMin = option.dataset.priceMin;
        const optionMax = option.dataset.priceMax;

        if (optionMin === minValue && optionMax === maxValue) {
            return option;
        }
    }

    return null;
}

function formatPrice(price) {
    if (!price || price === '') return '';
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return '';
    return numPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Format price for input display (with dots)
function formatPriceInput(price) {
    if (!price || price === '') return '';
    const cleanPrice = price.toString().replace(/[^\d]/g, '');
    if (!cleanPrice) return '';
    return cleanPrice.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Show price warning message
function showPriceWarning(message) {
    // Remove existing warning if any
    const existingWarning = document.querySelector('.price-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create warning element
    const warning = document.createElement('div');
    warning.className = 'price-warning';
    warning.innerHTML = `
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                <span class="text-sm text-yellow-700">${message}</span>
            </div>
        </div>
    `;

    // Insert warning after the modal content
    const modalBody = document.querySelector('#custom-price-modal .space-y-4');
    if (modalBody) {
        modalBody.appendChild(warning);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (warning && warning.parentNode) {
                warning.remove();
            }
        }, 5000);
    }
}

// Show loading indicator when submitting form
function showLoadingIndicator() {
    // Remove existing loading indicator if any
    const existingLoading = document.querySelector('.price-loading');
    if (existingLoading) {
        existingLoading.remove();
    }

    // Create loading element
    const loading = document.createElement('div');
    loading.className = 'price-loading';
    loading.innerHTML = `
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2">
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                <span class="text-sm text-blue-700">Đang tìm kiếm sản phẩm...</span>
            </div>
        </div>
    `;

    // Insert loading after the modal content
    const modalBody = document.querySelector('#custom-price-modal .space-y-4');
    if (modalBody) {
        modalBody.appendChild(loading);
    }
}

// Add input formatting for better UX
function initPriceInputFormatting() {
    const customMinInput = document.getElementById('custom-price-min');
    const customMaxInput = document.getElementById('custom-price-max');

    [customMinInput, customMaxInput].forEach(input => {
        if (!input) return;

        // Format on input (real-time formatting)
        input.addEventListener('input', function() {
            // Remove all non-digits
            let value = this.value.replace(/[^\d]/g, '');

            // Giới hạn tối đa 12 chữ số (đơn vị tỷ đồng)
            if (value.length > 12) {
                // Hiển thị thông báo cảnh báo
                showPriceWarning('Số tiền không được vượt quá 999 tỷ đồng');
                // Cắt bớt về 12 chữ số
                value = value.substring(0, 12);
            }

            // Store clean value immediately for processing
            this.setAttribute('data-clean-value', value);

            // Format with dots
            if (value) {
                // Add dots every 3 digits from right
                value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
                this.value = value;
            }
        });

        // Clean value on blur for processing
        input.addEventListener('blur', function() {
            if (this.value) {
                // Store clean value for processing
                const cleanValue = this.value.replace(/\./g, '');
                this.setAttribute('data-clean-value', cleanValue);

                // Validate value - chỉ kiểm tra âm, không reset về 0
                const numValue = parseFloat(cleanValue);
                if (isNaN(numValue) || numValue < 0) {
                    showPriceWarning('Giá tiền không thể âm');
                    // Focus lại vào input để người dùng sửa
                    setTimeout(() => this.focus(), 100);
                } else if (cleanValue.length > 12) {
                    showPriceWarning('Số tiền không được vượt quá 999 tỷ đồng');
                    // Cắt bớt về 12 chữ số
                    const truncatedValue = cleanValue.substring(0, 12);
                    this.value = formatPriceInput(truncatedValue);
                    this.setAttribute('data-clean-value', truncatedValue);
                }
            }
        });

        // Prevent negative values and non-numeric input
        input.addEventListener('keydown', function(e) {
            // Allow: backspace, delete, tab, escape, enter
            if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                (e.keyCode === 65 && e.ctrlKey === true) ||
                (e.keyCode === 67 && e.ctrlKey === true) ||
                (e.keyCode === 86 && e.ctrlKey === true) ||
                (e.keyCode === 88 && e.ctrlKey === true)) {
                return;
            }

            // Check if adding this character would exceed 12 digits
            const currentValue = this.value.replace(/[^\d]/g, '');
            const isNumberKey = (e.keyCode >= 48 && e.keyCode <= 57) || (e.keyCode >= 96 && e.keyCode <= 105);

            if (isNumberKey && currentValue.length >= 12) {
                e.preventDefault();
                showPriceWarning('Số tiền không được vượt quá 999 tỷ đồng');
                return;
            }

            // Ensure that it is a number and stop the keypress
            if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
            }
        });
    });
}

// Filter Badge Management
function removeFilter(filterType) {
    const url = new URL(window.location);

    switch(filterType) {
        case 'keyword':
            url.searchParams.delete('keyword');
            break;
        case 'category':
            url.searchParams.delete('category');
            break;
        case 'price':
            url.searchParams.delete('price_min');
            url.searchParams.delete('price_max');
            break;
        case 'promotion':
            url.searchParams.delete('promotion');
            break;
    }

    // Reset to page 1 when removing filters
    url.searchParams.set('page', '1');

    // Redirect to new URL
    window.location.href = url.toString();
}



// Initialize filter badge interactions
document.addEventListener('DOMContentLoaded', function() {
    initFilterBadges();
});

function initFilterBadges() {
    const removeButtons = document.querySelectorAll('.remove-filter');

    removeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Add fade out animation
            const badge = this.closest('.filter-badge');
            if (badge) {
                badge.style.animation = 'fadeOutScale 0.2s ease-out forwards';

                // Get filter type from onclick attribute
                const onclickStr = this.getAttribute('onclick');
                const match = onclickStr.match(/removeFilter\('(.+?)'\)/);
                if (match) {
                    const filterType = match[1];

                    // Execute removal after animation
                    setTimeout(() => {
                        removeFilter(filterType);
                    }, 200);
                }
            }
        });
    });


}

// Add fade out animation CSS
if (!document.getElementById('filter-badge-animations')) {
    const style = document.createElement('style');
    style.id = 'filter-badge-animations';
    style.textContent = `
        @keyframes fadeOutScale {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function để focus vào search input - dùng cho nút "Tìm kiếm mới"
function focusSearchInput() {
    const searchInput = document.getElementById('main-search-input');
    if (searchInput) {
        // Scroll to search input
        searchInput.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Focus và select text sau khi scroll
        setTimeout(() => {
            searchInput.focus();
            searchInput.select();
        }, 500);
    }
}

// Function để tự động cuộn xuống no-results-card khi không có kết quả
function scrollToNoResults() {
    const noResultsCard = document.querySelector('.no-results-card');
    if (noResultsCard) {
        // Delay nhỏ để đảm bảo page đã load xong
        setTimeout(() => {
            // Tính toán chiều cao header cho từng thiết bị
            const headerHeight = calculateHeaderHeight();
            const viewportHeight = window.innerHeight;
            const cardRect = noResultsCard.getBoundingClientRect();
            const cardHeight = cardRect.height;

            // Tính toán vị trí cuộn tối ưu
            const cardTop = noResultsCard.offsetTop;
            const optimalScrollPosition = cardTop - headerHeight - ((viewportHeight - headerHeight - cardHeight) / 2);

            // Đảm bảo không cuộn quá đầu trang
            const finalScrollPosition = Math.max(0, optimalScrollPosition);

            // Cuộn mượt mà đến vị trí tối ưu
            window.scrollTo({
                top: finalScrollPosition,
                behavior: 'smooth'
            });
        }, 300);
    }
}

// Function để tính toán chiều cao header dựa trên thiết bị
function calculateHeaderHeight() {
    const screenWidth = window.innerWidth;
    let headerHeight = 0;

    // Mobile (≤ 576px) - Mobile header
    if (screenWidth <= 576) {
        const mobileHeader = document.querySelector('.mobile-header');
        if (mobileHeader) {
            headerHeight = mobileHeader.offsetHeight || 60; // Default mobile header height
        } else {
            headerHeight = 60; // Fallback mobile header height
        }
    }
    // Tablet (577px - 1024px) - Premium header
    else if (screenWidth <= 1024) {
        const premiumHeader = document.querySelector('.premium-header');
        const topBar = document.querySelector('.top-bar');
        const mainHeader = document.querySelector('.main-header-container');
        const bottomHeader = document.querySelector('.bottom-header-container');

        if (premiumHeader) {
            headerHeight = premiumHeader.offsetHeight;
        } else {
            // Tính toán từng phần nếu không có container chính
            headerHeight += topBar ? topBar.offsetHeight : 40;      // Top bar ~40px
            headerHeight += mainHeader ? mainHeader.offsetHeight : 80;  // Main header ~80px
            headerHeight += bottomHeader ? bottomHeader.offsetHeight : 50; // Bottom nav ~50px
        }
    }
    // Desktop (> 1024px) - Premium header
    else {
        const premiumHeader = document.querySelector('.premium-header');
        if (premiumHeader) {
            headerHeight = premiumHeader.offsetHeight;
        } else {
            // Fallback cho desktop - tổng các phần header
            headerHeight = 170; // Top bar (40px) + Main header (80px) + Navigation (50px)
        }
    }

    // Thêm buffer nhỏ để tránh dính header
    return headerHeight + 20;
}

// Tự động cuộn khi page load và có no-results-card
document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra nếu có no-results-card thì tự động cuộn xuống
    const noResultsCard = document.querySelector('.no-results-card');
    if (noResultsCard) {
        scrollToNoResults();
    }

    // Initialize search input enhancements
    initSearchInputEnhancements();
});

// Enhanced Search Input Functionality
function initSearchInputEnhancements() {
    const searchInput = document.getElementById('main-search-input');
    const searchActions = document.querySelector('.search-actions-right');
    const clearBtn = document.getElementById('search-clear-btn');
    const submitBtn = document.getElementById('search-submit-btn');

    if (!searchInput || !searchActions || !clearBtn || !submitBtn) {
        return;
    }

    // Function to toggle action buttons visibility and icon switching
    function toggleActionButtons() {
        const hasValue = searchInput.value.trim().length > 0;
        const container = searchInput.closest('.search-input-container');

        if (hasValue) {
            searchActions.classList.add('show');
            container.classList.add('has-content');
        } else {
            searchActions.classList.remove('show');
            container.classList.remove('has-content');
        }
    }

    // Initial check on page load
    toggleActionButtons();

    // Listen for input changes
    searchInput.addEventListener('input', function() {
        toggleActionButtons();

        // Hide suggestions if input is empty
        if (this.value.trim().length === 0) {
            const suggestionsContainer = document.getElementById('search-suggestions');
            if (suggestionsContainer) {
                suggestionsContainer.classList.add('hidden');
            }
        }
    });

    // Listen for paste events
    searchInput.addEventListener('paste', function() {
        // Small delay to ensure pasted content is processed
        setTimeout(toggleActionButtons, 10);
    });

    // Clear button functionality
    clearBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Clear the input
        searchInput.value = '';

        // Hide action buttons and reset icon
        const container = searchInput.closest('.search-input-container');
        searchActions.classList.remove('show');
        container.classList.remove('has-content');

        // Hide suggestions box
        const suggestionsContainer = document.getElementById('search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
        }

        // Focus back to input
        searchInput.focus();

        // Optional: Auto-submit to show all results
        // Uncomment if you want to auto-search when clearing
        // searchInput.closest('form').submit();
    });

    // Submit button functionality
    submitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Submit the form
        const form = searchInput.closest('form');
        if (form) {
            // Add loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            submitBtn.disabled = true;

            // Submit form
            form.submit();
        }
    });



    // Focus/blur effects for better UX
    searchInput.addEventListener('focus', function() {
        searchInput.parentElement.classList.add('focused');
    });

    searchInput.addEventListener('blur', function() {
        searchInput.parentElement.classList.remove('focused');
    });
}

// Clear All Filters Function
function clearAllFilters() {
    // Create URL without any filter parameters
    const url = new URL(window.location.origin + window.location.pathname);

    // Add loading effect to clear all badge
    const clearAllBadge = document.querySelector('.clear-all-badge');
    if (clearAllBadge) {
        clearAllBadge.style.opacity = '0.6';
        clearAllBadge.style.pointerEvents = 'none';
    }

    // Redirect to clean search page
    window.location.href = url.toString();
}
