/**
 * Mobile Dropdown Fix V3 JavaScript for Nội Thất Bàng Vũ
 * Xử lý các tính năng tương tác của dropdown menu trong mobile menu
 * Phiên bản cải tiến với xử lý chiều cao động và sửa lỗi event listener
 */

document.addEventListener('DOMContentLoaded', function () {
  console.log('Mobile Dropdown Fix V3 loaded');
  
  // Hàm xử lý sự kiện click cho dropdown toggle
  function handleDropdownToggle(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const parent = this.parentElement;
    const submenu = parent.querySelector('.mobile-submenu');
    
    console.log('Toggle clicked:', parent);
    
    // Kiểm tra trạng thái hiện tại
    if (parent.classList.contains('active')) {
      // Đang mở, cần đóng lại
      closeSubmenu(parent, submenu);
    } else {
      // <PERSON>ang đóng, cần mở ra
      
      // <PERSON><PERSON>g tất cả các submenu cùng cấp khác
      const siblings = parent.parentElement.querySelectorAll(
        '.mobile-menu-item.active, .mobile-submenu-item.active'
      );
      
      siblings.forEach(function (sibling) {
        if (sibling !== parent && !parent.contains(sibling) && !sibling.contains(parent)) {
          const siblingSubmenu = sibling.querySelector('.mobile-submenu');
          closeSubmenu(sibling, siblingSubmenu);
        }
      });
      
      // Mở submenu hiện tại
      openSubmenu(parent, submenu);
    }
  }
  
  // Hàm xử lý sự kiện click cho nút quay lại
  function handleBackButton(e) {
    e.preventDefault();
    e.stopPropagation();
    
    // Tìm submenu cha
    const submenu = this.closest('.mobile-submenu');
    const parentItem = submenu.closest('.mobile-menu-item, .mobile-submenu-item');
    
    if (parentItem) {
      console.log('Back button clicked:', parentItem);
      closeSubmenu(parentItem, submenu);
    }
  }
  
  // Hàm mở submenu
  function openSubmenu(parent, submenu) {
    if (!submenu) return;
    
    // Thêm class active cho parent
    parent.classList.add('active');
    
    // Đặt chiều cao cho submenu
    const scrollHeight = submenu.scrollHeight;
    submenu.style.maxHeight = scrollHeight + 'px';
    submenu.style.marginBottom = '1rem';
    
    console.log('Opening submenu, height:', scrollHeight);
    
    // Nếu parent là submenu-item và nằm trong một submenu khác,
    // cập nhật chiều cao của submenu cha
    const parentSubmenu = parent.closest('.mobile-submenu');
    if (parentSubmenu && parentSubmenu !== submenu) {
      // Cập nhật chiều cao của submenu cha để chứa cả submenu con
      const newParentHeight = parentSubmenu.scrollHeight + scrollHeight;
      parentSubmenu.style.maxHeight = newParentHeight + 'px';
      console.log('Updating parent submenu height:', newParentHeight);
    }
  }
  
  // Hàm đóng submenu
  function closeSubmenu(parent, submenu) {
    if (!submenu) return;
    
    // Xóa class active cho parent
    parent.classList.remove('active');
    
    // Đặt chiều cao về 0
    submenu.style.maxHeight = '0';
    submenu.style.marginBottom = '0';
    
    console.log('Closing submenu');
    
    // Đóng tất cả các submenu con
    const childItems = submenu.querySelectorAll('.mobile-menu-item.active, .mobile-submenu-item.active');
    childItems.forEach(function (item) {
      const childSubmenu = item.querySelector('.mobile-submenu');
      if (childSubmenu) {
        closeSubmenu(item, childSubmenu);
      }
    });
    
    // Nếu parent là submenu-item và nằm trong một submenu khác,
    // cập nhật chiều cao của submenu cha
    const parentSubmenu = parent.closest('.mobile-submenu');
    if (parentSubmenu && parentSubmenu !== submenu && parentSubmenu.classList.contains('active')) {
      // Cập nhật chiều cao của submenu cha
      parentSubmenu.style.maxHeight = parentSubmenu.scrollHeight + 'px';
      console.log('Updating parent submenu height after closing:', parentSubmenu.scrollHeight);
    }
  }
  
  // Thiết lập chiều cao ban đầu cho tất cả submenu
  function setupSubmenuHeights() {
    const allSubmenus = document.querySelectorAll('.mobile-submenu');
    
    allSubmenus.forEach(function (submenu) {
      // Đặt chiều cao ban đầu là 0
      submenu.style.maxHeight = '0';
      
      // Nếu parent đã active, mở submenu
      const parent = submenu.closest('.mobile-menu-item, .mobile-submenu-item');
      if (parent && parent.classList.contains('active')) {
        openSubmenu(parent, submenu);
      }
    });
  }
  
  // Xử lý dropdown menu
  function initMobileDropdowns() {
    // Lấy tất cả các dropdown toggle
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    console.log('Mobile dropdown toggles:', mobileDropdownToggles.length);
    
    if (mobileDropdownToggles.length > 0) {
      // Xử lý sự kiện click cho các dropdown toggle
      mobileDropdownToggles.forEach(function (toggle) {
        // Xóa tất cả event listener cũ bằng cách clone và thay thế
        const newToggle = toggle.cloneNode(true);
        toggle.parentNode.replaceChild(newToggle, toggle);
        
        // Thêm event listener mới
        newToggle.addEventListener('click', handleDropdownToggle);
      });
    }
    
    // Xử lý nút quay lại
    const mobileBackButtons = document.querySelectorAll('.mobile-menu-back');
    console.log('Mobile back buttons:', mobileBackButtons.length);
    
    if (mobileBackButtons.length > 0) {
      mobileBackButtons.forEach(function (button) {
        // Xóa tất cả event listener cũ bằng cách clone và thay thế
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Thêm event listener mới
        newButton.addEventListener('click', handleBackButton);
      });
    }
    
    // Thiết lập chiều cao ban đầu cho tất cả submenu
    setupSubmenuHeights();
  }
  
  // Xử lý khi cửa sổ thay đổi kích thước
  window.addEventListener('resize', function() {
    // Cập nhật lại chiều cao cho các submenu đang mở
    const activeItems = document.querySelectorAll('.mobile-menu-item.active, .mobile-submenu-item.active');
    activeItems.forEach(function(item) {
      const submenu = item.querySelector('.mobile-submenu');
      if (submenu) {
        submenu.style.maxHeight = submenu.scrollHeight + 'px';
      }
    });
  });
  
  // Khởi tạo dropdown menu sau khi DOM đã tải hoàn toàn
  setTimeout(function() {
    initMobileDropdowns();
  }, 500);
});
