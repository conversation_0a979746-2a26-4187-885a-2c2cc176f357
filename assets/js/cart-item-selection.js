/**
 * Cart Item Selection Handler
 * Xử lý chọn/bỏ chọn sản phẩm trong giỏ hàng
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart item selection handler loaded');

    // Biến flag để ngăn can thiệp khi đang cập nhật từ server
    let isUpdatingFromServer = false;

    // Lưu trữ tổng tiền gốc từ server
    let originalCartTotal = 0;

    // Lắng nghe sự kiện cập nhật từ server
    window.addEventListener('cart-updating-from-server', function() {
        console.log('Cart updating from server - blocking selection updates');
        isUpdatingFromServer = true;
        setTimeout(() => {
            isUpdatingFromServer = false;
            console.log('Cart update from server completed - allowing selection updates');
        }, 3000); // Ngăn can thiệp trong 3 giây
    });

    // Lắng nghe sự kiện hoàn thành cập nhật từ server
    window.addEventListener('cart-update-completed', function() {
        console.log('Cart update completed - extending block time');
        isUpdatingFromServer = true;
        setTimeout(() => {
            isUpdatingFromServer = false;
            console.log('Extended block time completed - allowing selection updates');
        }, 2000); // Ngăn can thiệp thêm 2 giây sau khi hoàn thành
    });

    // Lấy tất cả các checkbox sản phẩm
    const itemCheckboxes = document.querySelectorAll('.cart-item-checkbox');

    // Lấy checkbox chọn tất cả
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const selectAllCheckboxMobile = document.getElementById('select-all-checkbox-mobile');

    // Lấy nút chọn tất cả và bỏ chọn tất cả
    const selectAllBtn = document.getElementById('select-all-btn');
    const deselectAllBtn = document.getElementById('deselect-all-btn');

    // Lấy nút mobile select all
    const mobileSelectAllBtn = document.querySelector('.mobile-select-all-btn');

    // Lấy các phần tử hiển thị thông tin tổng
    const cartSubtotal = document.querySelector('.cart-subtotal');
    const cartTotal = document.querySelector('.cart-total');
    const selectedItemsCount = document.querySelector('.selected-items-count');
    const selectedItemsPercentage = document.querySelector('.selected-items-percentage');
    const selectedItemsBadge = document.querySelector('.selected-items-badge');
    const checkoutBtn = document.getElementById('checkout-btn');

    // Lưu trữ tổng tiền ban đầu
    const originalTotal = getOriginalTotal();

    /**
     * Lấy tổng tiền ban đầu từ tất cả sản phẩm
     */
    function getOriginalTotal() {
        let total = 0;
        itemCheckboxes.forEach(function(checkbox) {
            const price = parseFloat(checkbox.dataset.price);
            const quantity = parseInt(checkbox.dataset.quantity);
            total += price * quantity;
        });
        return total;
    }

    /**
     * Lấy danh sách checkbox duy nhất cho mỗi sản phẩm (tránh trùng lặp mobile/desktop)
     */
    function getUniqueCheckboxes() {
        const allCheckboxes = document.querySelectorAll('.cart-item-checkbox');
        const uniqueCheckboxes = [];
        const processedProductIds = new Set();

        allCheckboxes.forEach(function(checkbox) {
            const productId = checkbox.dataset.productId;
            if (!processedProductIds.has(productId)) {
                processedProductIds.add(productId);
                // Ưu tiên checkbox desktop nếu có, nếu không thì lấy mobile
                const desktopCheckbox = document.getElementById(`checkbox-desktop-${productId}`);
                const mobileCheckbox = document.getElementById(`checkbox-mobile-${productId}`);

                if (window.innerWidth >= 768 && desktopCheckbox) {
                    uniqueCheckboxes.push(desktopCheckbox);
                } else if (mobileCheckbox) {
                    uniqueCheckboxes.push(mobileCheckbox);
                } else {
                    uniqueCheckboxes.push(checkbox);
                }
            }
        });

        return uniqueCheckboxes;
    }

    /**
     * Đồng bộ hóa trạng thái checkbox giữa mobile và desktop
     */
    function syncCheckboxes(productId, checked) {
        const mobileCheckbox = document.getElementById(`checkbox-mobile-${productId}`);
        const desktopCheckbox = document.getElementById(`checkbox-desktop-${productId}`);

        if (mobileCheckbox && mobileCheckbox.checked !== checked) {
            mobileCheckbox.checked = checked;
        }
        if (desktopCheckbox && desktopCheckbox.checked !== checked) {
            desktopCheckbox.checked = checked;
        }
    }

    /**
     * Cập nhật tổng tiền dựa trên các sản phẩm được chọn
     */
    function updateTotal() {
        // Debug: Log khi hàm được gọi
        console.log('updateTotal called, isUpdatingFromServer:', isUpdatingFromServer);

        // Không can thiệp nếu đang cập nhật từ server
        if (isUpdatingFromServer) {
            console.log('Skipping updateTotal - server update in progress');
            return;
        }

        // Lấy danh sách checkbox hiện tại từ DOM - chỉ lấy mobile hoặc desktop, không lấy cả hai
        const currentCheckboxes = getUniqueCheckboxes();

        let selectedTotal = 0;
        let selectedCount = 0;
        let totalItems = currentCheckboxes.length;
        let selectedProductIds = [];

        // Tính tổng tiền của các sản phẩm được chọn
        currentCheckboxes.forEach(function(checkbox) {
            if (checkbox.checked) {
                const price = parseFloat(checkbox.dataset.price);
                const quantity = parseInt(checkbox.dataset.quantity);
                const itemTotal = price * quantity;
                selectedTotal += itemTotal;
                selectedCount++;
                selectedProductIds.push(checkbox.dataset.productId);

                console.log('Selected item:', {
                    productId: checkbox.dataset.productId,
                    price: price,
                    quantity: quantity,
                    itemTotal: itemTotal
                });
            }
        });

        // Luôn cập nhật tổng tiền dựa trên các sản phẩm được chọn
        const allSelected = selectedCount === totalItems && totalItems > 0;

        console.log('updateTotal - allSelected:', allSelected, 'selectedCount:', selectedCount, 'totalItems:', totalItems, 'selectedTotal:', selectedTotal);

        // Cập nhật tổng tiền cho các sản phẩm được chọn
        if (cartSubtotal && cartTotal) {
            console.log('Updating total to selected amount:', formatCurrency(selectedTotal));
            cartSubtotal.textContent = formatCurrency(selectedTotal);
            cartTotal.textContent = formatCurrency(selectedTotal);

            // Không thêm hiệu ứng highlight cho tổng đơn hàng
            // Chỉ cập nhật khi có thay đổi checkbox hoặc nhấn nút cập nhật
        } else {
            console.log('Cart total elements not found');
        }

        // Cập nhật số lượng sản phẩm được chọn
        if (selectedItemsCount) {
            selectedItemsCount.textContent = selectedCount;
        }

        // Cập nhật phần trăm sản phẩm được chọn
        if (selectedItemsPercentage) {
            const percentage = totalItems > 0 ? Math.round((selectedCount / totalItems) * 100) : 0;
            selectedItemsPercentage.textContent = percentage;
        }

        // Cập nhật badge số lượng sản phẩm được chọn
        if (selectedItemsBadge) {
            selectedItemsBadge.textContent = selectedCount;
        }

        // Cập nhật URL nút thanh toán - LUÔN cập nhật bất kể trạng thái
        if (checkoutBtn) {
            let baseUrl = checkoutBtn.getAttribute('href');

            // Nếu href hiện tại là '#', sử dụng checkout.php làm base URL
            if (baseUrl === '#' || !baseUrl) {
                baseUrl = 'checkout.php';
            } else {
                baseUrl = baseUrl.split('?')[0];
            }

            console.log('Updating checkout button:', { selectedCount, selectedProductIds, baseUrl });

            if (selectedCount > 0) {
                const newHref = `${baseUrl}?selected_items=${selectedProductIds.join(',')}`;
                checkoutBtn.setAttribute('href', newHref);
                checkoutBtn.classList.remove('opacity-50', 'pointer-events-none');
                console.log('Checkout button enabled:', newHref);
            } else {
                checkoutBtn.setAttribute('href', '#');
                checkoutBtn.classList.add('opacity-50', 'pointer-events-none');
                console.log('Checkout button disabled');
            }
        }

        // Highlight các phần tử được cập nhật
        highlightUpdatedElements();
    }

    /**
     * Highlight các phần tử được cập nhật
     */
    function highlightUpdatedElements() {
        // Highlight tổng tiền
        if (cartSubtotal) {
            cartSubtotal.classList.add('highlight-change');
            setTimeout(() => cartSubtotal.classList.remove('highlight-change'), 1500);
        }

        if (cartTotal) {
            cartTotal.classList.add('highlight-change');
            setTimeout(() => cartTotal.classList.remove('highlight-change'), 1500);
        }

        // Highlight số lượng sản phẩm được chọn
        if (selectedItemsCount) {
            selectedItemsCount.classList.add('highlight-change');
            setTimeout(() => selectedItemsCount.classList.remove('highlight-change'), 1500);
        }

        // Highlight phần trăm sản phẩm được chọn
        if (selectedItemsPercentage) {
            selectedItemsPercentage.classList.add('highlight-change');
            setTimeout(() => selectedItemsPercentage.classList.remove('highlight-change'), 1500);
        }

        // Highlight badge số lượng sản phẩm được chọn
        if (selectedItemsBadge) {
            selectedItemsBadge.classList.add('badge-pulse');
            setTimeout(() => selectedItemsBadge.classList.remove('badge-pulse'), 1500);
        }
    }

    /**
     * Format số tiền
     */
    function formatCurrency(amount) {
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'đ';
    }

    /**
     * Chọn tất cả sản phẩm
     */
    function selectAll() {
        // Chọn tất cả checkbox (cả mobile và desktop)
        const allCheckboxes = document.querySelectorAll('.cart-item-checkbox');
        allCheckboxes.forEach(function(checkbox) {
            checkbox.checked = true;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
        }

        if (selectAllCheckboxMobile) {
            selectAllCheckboxMobile.checked = true;
        }

        updateTotal();
        updateMobileSelectAllButton();
        showCartInlineNotification('Đã chọn tất cả', 'Tất cả sản phẩm đã được chọn', 'success');
    }

    /**
     * Bỏ chọn tất cả sản phẩm
     */
    function deselectAll() {
        // Bỏ chọn tất cả checkbox (cả mobile và desktop)
        const allCheckboxes = document.querySelectorAll('.cart-item-checkbox');
        allCheckboxes.forEach(function(checkbox) {
            checkbox.checked = false;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }

        if (selectAllCheckboxMobile) {
            selectAllCheckboxMobile.checked = false;
        }

        updateTotal();
        updateMobileSelectAllButton();
        showCartInlineNotification('Đã bỏ chọn tất cả', 'Tất cả sản phẩm đã được bỏ chọn', 'info');
    }

    /**
     * Cập nhật trạng thái nút mobile select all và progress bar
     */
    function updateMobileSelectAllButton() {
        if (mobileSelectAllBtn) {
            // Lấy danh sách checkbox duy nhất
            const currentCheckboxes = getUniqueCheckboxes();

            const allChecked = Array.from(currentCheckboxes).every(cb => cb.checked);
            const checkedCount = Array.from(currentCheckboxes).filter(cb => cb.checked).length;
            const totalCount = currentCheckboxes.length;
            const percentage = totalCount > 0 ? Math.round((checkedCount / totalCount) * 100) : 0;

            console.log('Progress calculation:', { checkedCount, totalCount, percentage });

            // Cập nhật nút
            if (totalCount === 0) {
                // Không có sản phẩm nào
                mobileSelectAllBtn.innerHTML = '<i class="fas fa-square text-sm"></i><span>Chọn tất cả</span>';
            } else if (allChecked) {
                mobileSelectAllBtn.innerHTML = '<i class="fas fa-check-square text-sm"></i><span>Đã chọn tất cả</span>';
            } else if (checkedCount > 0) {
                mobileSelectAllBtn.innerHTML = '<i class="fas fa-minus-square text-sm"></i><span>Chọn thêm</span>';
            } else {
                mobileSelectAllBtn.innerHTML = '<i class="fas fa-square text-sm"></i><span>Chọn tất cả</span>';
            }

            // Cập nhật progress bar
            const progressBar = document.querySelector('.selected-progress-bar');
            const progressText = document.querySelector('.selected-progress-text');

            if (progressBar) {
                progressBar.style.width = percentage + '%';

                // Thay đổi màu dựa trên tiến độ
                if (percentage === 100) {
                    progressBar.style.background = 'linear-gradient(90deg, #10b981 0%, #059669 100%)';
                } else if (percentage > 0) {
                    progressBar.style.background = 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)';
                } else {
                    progressBar.style.background = 'linear-gradient(90deg, #6b7280 0%, #4b5563 100%)';
                }
            }

            if (progressText) {
                progressText.textContent = percentage + '%';
            }
        }
    }

    // Sử dụng event delegation để xử lý checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('cart-item-checkbox')) {
            // Không xử lý nếu đang cập nhật từ server
            if (isUpdatingFromServer) {
                console.log('Skipping checkbox change - server update in progress');
                return;
            }

            // Đồng bộ hóa checkbox giữa mobile và desktop
            const productId = e.target.dataset.productId;
            const checked = e.target.checked;
            syncCheckboxes(productId, checked);

            // Lấy danh sách checkbox duy nhất
            const currentCheckboxes = getUniqueCheckboxes();
            const allChecked = Array.from(currentCheckboxes).every(cb => cb.checked);

            // Cập nhật trạng thái checkbox chọn tất cả
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
            }

            if (selectAllCheckboxMobile) {
                selectAllCheckboxMobile.checked = allChecked;
            }

            // Cập nhật tổng tiền
            updateTotal();

            // Cập nhật nút mobile
            updateMobileSelectAllButton();

            // Hiển thị thông báo
            const productName = e.target.closest('.bg-white.rounded-xl').querySelector('.text-lg.font-semibold.text-gray-800').textContent.trim();
            if (e.target.checked) {
                showCartInlineNotification('Đã chọn sản phẩm', `${productName} đã được thêm vào lựa chọn`, 'success');
            } else {
                showCartInlineNotification('Đã bỏ chọn sản phẩm', `${productName} đã bị loại khỏi lựa chọn`, 'info');
            }
        }
    });

    // Xử lý sự kiện khi checkbox chọn tất cả thay đổi
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            if (this.checked) {
                selectAll();
            } else {
                deselectAll();
            }
        });
    }

    // Xử lý sự kiện khi nhấn nút chọn tất cả
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAll);
    }

    // Xử lý sự kiện khi nhấn nút bỏ chọn tất cả
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', deselectAll);
    }

    // Xử lý sự kiện khi checkbox mobile chọn tất cả thay đổi
    if (selectAllCheckboxMobile) {
        selectAllCheckboxMobile.addEventListener('change', function() {
            if (this.checked) {
                selectAll();
            } else {
                deselectAll();
            }
        });
    }

    // Xử lý sự kiện khi nhấn nút mobile select all
    if (mobileSelectAllBtn) {
        mobileSelectAllBtn.addEventListener('click', function() {
            // Toggle trạng thái dựa trên checkbox hiện tại
            const currentCheckboxes = document.querySelectorAll('.cart-item-checkbox');
            const allChecked = Array.from(currentCheckboxes).every(cb => cb.checked);

            if (allChecked) {
                deselectAll();
            } else {
                selectAll();
            }
        });
    }

    // Khởi tạo tổng tiền ban đầu - delay một chút để đảm bảo DOM đã sẵn sàng
    setTimeout(() => {
        // Kiểm tra xem tất cả sản phẩm có được chọn không
        const currentCheckboxes = getUniqueCheckboxes();
        const allChecked = Array.from(currentCheckboxes).every(cb => cb.checked);

        console.log('Initial state - all checkboxes checked:', allChecked, 'total checkboxes:', currentCheckboxes.length);
        console.log('Checkout button found:', !!checkoutBtn);

        // Lưu tổng tiền gốc từ server
        if (cartTotal && originalCartTotal === 0) {
            const originalText = cartTotal.textContent.trim();
            // Chuyển đổi từ định dạng tiền tệ về số
            originalCartTotal = parseFloat(originalText.replace(/[^\d]/g, ''));
            console.log('Original cart total saved:', originalCartTotal, 'from text:', originalText);
        }

        if (checkoutBtn) {
            console.log('Initial checkout button href:', checkoutBtn.getAttribute('href'));
            console.log('Initial checkout button classes:', checkoutBtn.className);
        }

        // Luôn chạy updateTotal để đảm bảo nút thanh toán hoạt động đúng
        if (!isUpdatingFromServer) {
            console.log('Running initial updateTotal');
            updateTotal();
        } else {
            console.log('Skipping initial updateTotal - server update in progress');
        }
        updateMobileSelectAllButton();

        // Debug: Kiểm tra lại trạng thái nút sau khi update
        if (checkoutBtn) {
            console.log('After update - checkout button href:', checkoutBtn.getAttribute('href'));
            console.log('After update - checkout button classes:', checkoutBtn.className);

            // Nếu tất cả sản phẩm được chọn mặc định nhưng nút vẫn bị disable, sửa lại
            if (allChecked && currentCheckboxes.length > 0 && checkoutBtn.getAttribute('href') === '#') {
                console.log('Fixing checkout button for all selected items');
                const productIds = Array.from(currentCheckboxes).map(cb => cb.dataset.productId);
                checkoutBtn.setAttribute('href', `checkout.php?selected_items=${productIds.join(',')}`);
                checkoutBtn.classList.remove('opacity-50', 'pointer-events-none');
                console.log('Checkout button fixed:', checkoutBtn.getAttribute('href'));
            }
        }
    }, 100);

    // Thêm event listener cho nút thanh toán để debug
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function(e) {
            console.log('Checkout button clicked!');
            console.log('Button href:', this.getAttribute('href'));
            console.log('Button classes:', this.className);

            // Nếu href là '#', ngăn chặn navigation và hiển thị thông báo
            if (this.getAttribute('href') === '#') {
                e.preventDefault();
                console.log('Checkout blocked - no items selected');

                // Hiển thị thông báo cho người dùng
                if (typeof showCartInlineNotification === 'function') {
                    showCartInlineNotification('Chưa chọn sản phẩm', 'Vui lòng chọn ít nhất một sản phẩm để thanh toán', 'warning');
                } else {
                    alert('Vui lòng chọn ít nhất một sản phẩm để thanh toán');
                }
                return false;
            }

            console.log('Proceeding to checkout...');
        });
    }

    // Lắng nghe sự kiện cập nhật giỏ hàng (khi xóa sản phẩm)
    document.addEventListener('cart:updated', function(event) {
        console.log('Cart updated event received in selection handler:', event.detail);

        // Đánh dấu đang cập nhật từ server
        isUpdatingFromServer = true;

        // Delay một chút để đảm bảo DOM đã được cập nhật
        setTimeout(() => {
            updateMobileSelectAllButton();
            // Không gọi updateTotal() ở đây để tránh ghi đè tổng tiền từ server

            // Kéo dài thời gian block để đảm bảo không can thiệp
            setTimeout(() => {
                isUpdatingFromServer = false;
                console.log('Cart updated event processing completed');
            }, 1000);
        }, 500);
    });

    // Lắng nghe sự kiện DOM thay đổi để cập nhật khi có sản phẩm bị xóa
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // Kiểm tra xem có cart item nào bị xóa không
                mutation.removedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE &&
                        (node.classList.contains('bg-white') && node.classList.contains('rounded-xl') &&
                         node.querySelector('.cart-item-checkbox'))) {
                        shouldUpdate = true;
                    }
                });
            }
        });

        if (shouldUpdate && !isUpdatingFromServer) {
            console.log('Cart item removed, updating progress bar');
            setTimeout(() => {
                updateMobileSelectAllButton();

                // Luôn cập nhật total để đảm bảo nút thanh toán hoạt động đúng
                console.log('MutationObserver: Running updateTotal');
                updateTotal();
            }, 50);
        }
    });

    // Quan sát thay đổi trong container chứa các cart items
    const cartContainer = document.querySelector('.space-y-4');
    if (cartContainer) {
        observer.observe(cartContainer, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Hiển thị thông báo trong giỏ hàng
     * Sử dụng hàm toàn cục nếu có, nếu không tạo thông báo mới
     */
    function showCartInlineNotification(message, detail, type = 'success') {
        // Kiểm tra xem hàm có tồn tại trong window không
        if (typeof window.showCartInlineNotification === 'function') {
            // Sử dụng hàm toàn cục nếu có
            window.showCartInlineNotification(message, detail, type);
            return;
        }

        // Lấy phần tử thông báo
        const notification = document.getElementById('cart-inline-notification');
        const messageElement = document.getElementById('cart-notification-message');
        const detailElement = document.getElementById('cart-notification-detail');
        const iconElement = document.getElementById('cart-notification-icon');

        if (!notification || !messageElement || !detailElement || !iconElement) {
            console.error('Không tìm thấy phần tử thông báo');
            return;
        }

        // Xóa class hiding nếu có
        notification.classList.remove('hiding');

        // Cập nhật nội dung
        messageElement.textContent = message;
        detailElement.textContent = detail;

        // Cập nhật màu sắc và icon dựa trên loại thông báo
        if (type === 'success') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-primary';
            iconElement.innerHTML = '<i class="fas fa-check"></i>';
        } else if (type === 'error') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-red-500';
            iconElement.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
        } else if (type === 'warning') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-yellow-500';
            iconElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
        } else if (type === 'info') {
            iconElement.className = 'w-10 h-10 rounded-full flex items-center justify-center text-white bg-blue-500';
            iconElement.innerHTML = '<i class="fas fa-info-circle"></i>';
        }

        // Hiển thị thông báo
        notification.classList.remove('hidden');

        // Tự động ẩn sau 5 giây
        clearTimeout(window.cartNotificationTimeout);
        window.cartNotificationTimeout = setTimeout(hideCartNotification, 5000);
    }

    /**
     * Ẩn thông báo trong giỏ hàng
     */
    function hideCartNotification() {
        // Kiểm tra xem hàm có tồn tại trong window không
        if (typeof window.hideCartNotification === 'function') {
            // Sử dụng hàm toàn cục nếu có
            window.hideCartNotification();
            return;
        }

        const notification = document.getElementById('cart-inline-notification');
        if (notification) {
            notification.classList.add('hiding');
            setTimeout(() => {
                notification.classList.add('hidden');
                notification.classList.remove('hiding');
            }, 300);
        }
    }
});
