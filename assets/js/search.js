/**
 * <PERSON><PERSON> lý tìm kiếm gợi ý theo thời gian thực
 */
document.addEventListener('DOMContentLoaded', function () {
  // Lấy các phần tử tìm kiếm
  const searchInputs = document.querySelectorAll('.search-input');
  const searchForms = document.querySelectorAll('.search-form');

  // Xử lý cho mỗi ô tìm kiếm
  searchInputs.forEach((searchInput, index) => {
    // Tạo container cho kết quả gợi ý
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className =
      'search-suggestions absolute left-0 right-0 bg-white rounded-b-md shadow-lg hidden';
    suggestionsContainer.style.top = 'calc(100% - 1px)';
    suggestionsContainer.style.maxHeight = '400px';
    suggestionsContainer.style.overflowY = 'auto';
    suggestionsContainer.style.zIndex = '9999'; // Tăng z-index lên cao nhất

    // Thêm container vào form
    const searchForm = searchForms[index];
    searchForm.style.position = 'relative';
    searchForm.appendChild(suggestionsContainer);

    // Biến để lưu timeout
    let searchTimeout;

    // Xử lý sự kiện nhập từ khóa
    searchInput.addEventListener('input', function () {
      const keyword = this.value.trim();

      // Xóa timeout cũ nếu có
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      // Nếu từ khóa quá ngắn, ẩn gợi ý
      if (keyword.length < 1) {
        suggestionsContainer.classList.add('hidden');
        return;
      }

      // Adaptive debounce timing based on keyword length
      let debounceTime;
      if (keyword.length === 1) {
        debounceTime = 600; // Longer delay for single character
      } else if (keyword.length === 2) {
        debounceTime = 400; // Medium delay for 2 characters
      } else {
        debounceTime = 200; // Fast for 3+ characters
      }

      // Đặt timeout mới để tránh gửi quá nhiều request
      searchTimeout = setTimeout(() => {
        // Hiển thị trạng thái loading ngay lập tức
        suggestionsContainer.innerHTML = `
          <div class="search-loading">
            <div class="search-loading-spinner"></div>
            <p style="margin: 0; color: #666; font-size: 0.9rem;">Đang tìm kiếm...</p>
          </div>
        `;
        suggestionsContainer.classList.remove('hidden');

        // Ghi nhận thời gian bắt đầu để đảm bảo loading tối thiểu
        const startTime = Date.now();
        const minLoadingTime = 300; // 300ms loading tối thiểu

        // Gửi request đến API
        fetch(
          `${BASE_URL}/api/search_suggestions.php?keyword=${encodeURIComponent(
            keyword
          )}`
        )
          .then((response) => response.json())
          .then((data) => {
            // Tính thời gian đã trôi qua
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            // Đợi thời gian loading tối thiểu trước khi hiển thị kết quả
            setTimeout(() => {
              // Xóa nội dung cũ
              suggestionsContainer.innerHTML = '';

              // Nếu không có kết quả
              if (data.suggestions.length === 0) {
                suggestionsContainer.innerHTML = `
                  <div class="search-no-results" style="padding: 2rem; text-align: center; color: #666;">
                    <i class="fas fa-search" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #999;"></i>
                    <p style="margin: 0; font-size: 0.9rem;">Không tìm thấy sản phẩm nào phù hợp với từ khóa "<strong>${keyword}</strong>"</p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #999;">Hãy thử tìm kiếm với từ khóa khác</p>
                  </div>
                `;
                suggestionsContainer.classList.remove('hidden');
                return;
              }

              // Hiển thị kết quả
              data.suggestions.forEach((product) => {
                const productElement = document.createElement('a');
                productElement.href = product.url;
                productElement.className =
                  'flex items-center p-3 hover:bg-gray-100 border-b border-gray-100';

                productElement.innerHTML = `
                                  <div class="search-item-image">
                                      <div class="skeleton-loading skeleton-wave absolute inset-0 rounded z-0"></div>
                                      <img
                                          src="${product.image}"
                                          alt="${product.name}"
                                          style="width: 100%; height: 100%; object-fit: cover; border-radius: 0.5rem; position: relative; z-index: 2;"
                                          onerror="this.style.display='none';"
                                          onload="this.previousElementSibling.style.display='none';"
                                      >
                                  </div>
                                  <div class="search-item-info">
                                      <div class="search-item-name">${product.name}</div>
                                      <div class="search-item-meta">
                                          <div class="search-item-rating">
                                              <i class="fas fa-star"></i>
                                              <span>${product.rating}</span>
                                          </div>
                                          <div class="search-item-sales">
                                              <span>${product.sales} đã bán</span>
                                          </div>
                                      </div>
                                      <div class="search-item-category">${product.category}</div>
                                  </div>
                                  <div class="search-item-price">${product.price}</div>
                              `;

                suggestionsContainer.appendChild(productElement);
              });

              // Thêm nút xem tất cả kết quả
              const viewAllElement = document.createElement('a');
              viewAllElement.href = `${BASE_URL}/products.php?keyword=${encodeURIComponent(
                keyword
              )}`;
              viewAllElement.className = 'search-view-all';
              viewAllElement.innerHTML =
                '<i class="fas fa-search"></i> Xem tất cả kết quả';
              suggestionsContainer.appendChild(viewAllElement);

              // Hiển thị container
              suggestionsContainer.classList.remove('hidden');
            }, remainingTime);
          })
          .catch((error) => {
            console.error('Lỗi khi tìm kiếm:', error);

            // Tính thời gian đã trôi qua cho trường hợp lỗi
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            setTimeout(() => {
              suggestionsContainer.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #666;">
                  <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #f59e0b;"></i>
                  <p style="margin: 0; font-size: 0.9rem;">Có lỗi xảy ra khi tìm kiếm</p>
                  <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #999;">Vui lòng thử lại sau</p>
                </div>
              `;
              suggestionsContainer.classList.remove('hidden');
            }, remainingTime);
          });
      }, debounceTime); // Adaptive timing based on keyword length
    });

    // Ẩn gợi ý khi click ra ngoài
    document.addEventListener('click', function (event) {
      if (!searchForm.contains(event.target)) {
        suggestionsContainer.classList.add('hidden');
      }
    });

    // Hiển thị lại gợi ý khi focus vào ô tìm kiếm
    searchInput.addEventListener('focus', function () {
      if (this.value.trim().length >= 2) {
        suggestionsContainer.classList.remove('hidden');
      }
    });
  });
});
