/**
 * <PERSON><PERSON> lý trang chi tiết sản phẩm
 */
document.addEventListener('DOMContentLoaded', function () {
  // C<PERSON>c biến cho gallery
  const thumbnails = document.querySelectorAll('.product-thumbnail');
  const viewMoreThumbnail = document.querySelector('.view-more-thumbnail');
  const mainSliderTrack = document.querySelector('.main-slider-track');
  const mainSlides = document.querySelectorAll('.main-slide');
  const galleryPrevBtn = document.querySelector('.gallery-prev');
  const galleryNextBtn = document.querySelector('.gallery-next');

  // Các biến cho fullscreen gallery
  const fullscreenGallery = document.getElementById('fullscreen-gallery');
  const closeGalleryBtn = document.getElementById('close-gallery');
  const gallerySliderTrack = document.querySelector('.gallery-slider-track');
  const gallerySlides = document.querySelectorAll('.gallery-slide');
  const fullscreenPrevBtn = document.querySelector('.gallery-fullscreen-prev');
  const fullscreenNextBtn = document.querySelector('.gallery-fullscreen-next');
  const currentSlideEl = document.getElementById('current-slide');

  // Biến trạng thái
  let currentIndex = 0;
  let touchStartX = 0;
  let touchEndX = 0;
  let isDragging = false;
  let startPos = 0;
  let currentTranslate = 0;
  let prevTranslate = 0;

  // Hàm chuyển đổi slide chính
  function changeMainSlide(index, animate = true) {
    if (!mainSliderTrack || index < 0 || index >= mainSlides.length) return;

    // Cập nhật index hiện tại
    currentIndex = index;

    // Tính toán vị trí transform
    const slideWidth = mainSlides[0].offsetWidth;
    const translateX = -index * slideWidth;

    // Áp dụng transform với hoặc không có animation
    if (animate) {
      mainSliderTrack.style.transition =
        'transform 0.4s cubic-bezier(0.25, 1, 0.5, 1)';
    } else {
      mainSliderTrack.style.transition = 'none';
    }

    mainSliderTrack.style.transform = `translateX(${translateX}px)`;

    // Cập nhật trạng thái active cho thumbnails
    thumbnails.forEach((thumb, i) => {
      const thumbImg = thumb.querySelector('img');
      if (i === index) {
        thumbImg.classList.remove(
          'border-transparent',
          'hover:border-gray-300'
        );
        thumbImg.classList.add('border-blue-500');
      } else {
        thumbImg.classList.remove('border-blue-500');
        thumbImg.classList.add('border-transparent', 'hover:border-gray-300');
      }
    });

    // Khôi phục transition sau khi đã áp dụng transform
    if (!animate) {
      setTimeout(() => {
        mainSliderTrack.style.transition =
          'transform 0.4s cubic-bezier(0.25, 1, 0.5, 1)';
      }, 50);
    }
  }

  // Hàm chuyển đổi slide trong fullscreen gallery
  function changeFullscreenSlide(index) {
    if (!gallerySliderTrack || index < 0 || index >= gallerySlides.length)
      return;

    // Tính toán vị trí transform
    const slideWidth = gallerySlides[0].offsetWidth;
    const translateX = -index * slideWidth;

    // Áp dụng transform
    gallerySliderTrack.style.transform = `translateX(${translateX}px)`;

    // Cập nhật counter
    if (currentSlideEl) {
      currentSlideEl.textContent = index + 1;
    }
  }

  // Xử lý sự kiện click vào thumbnail
  if (thumbnails.length > 0 && mainSliderTrack) {
    thumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener('click', function () {
        // Thêm hiệu ứng phản hồi trực quan
        const thumbImg = this.querySelector('img');
        if (thumbImg) {
          // Tạo hiệu ứng nhấp nháy
          thumbImg.style.opacity = '0.7';
          setTimeout(() => {
            thumbImg.style.opacity = '1';
          }, 100);
        }

        // Thay đổi slide sau một chút delay để hiệu ứng được hiển thị
        setTimeout(() => {
          changeMainSlide(index);
        }, 50);
      });
    });
  }

  // Xử lý nút điều hướng gallery chính
  if (galleryPrevBtn && galleryNextBtn && mainSlides.length > 1) {
    galleryPrevBtn.addEventListener('click', function () {
      const newIndex =
        (currentIndex - 1 + mainSlides.length) % mainSlides.length;
      changeMainSlide(newIndex);
    });

    galleryNextBtn.addEventListener('click', function () {
      const newIndex = (currentIndex + 1) % mainSlides.length;
      changeMainSlide(newIndex);
    });
  }

  // Xử lý nút "Xem thêm"
  if (viewMoreThumbnail && fullscreenGallery) {
    viewMoreThumbnail.addEventListener('click', function () {
      // Thêm class để kích hoạt hiệu ứng gợn sóng
      viewMoreThumbnail.classList.add('clicked');

      // Thêm hiệu ứng phản hồi trực quan
      setTimeout(() => {
        // Mở gallery sau một chút delay để hiệu ứng được hiển thị
        openFullscreenGallery(currentIndex);

        // Xóa class sau khi hoàn thành
        setTimeout(() => {
          viewMoreThumbnail.classList.remove('clicked');
        }, 500);
      }, 150);
    });
  }

  // Xử lý click vào hình ảnh chính để mở gallery toàn màn hình
  const mainImageContainer = document.querySelector(
    '.product-main-image-container'
  );
  if (mainImageContainer && fullscreenGallery) {
    mainImageContainer.addEventListener('click', function (e) {
      // Chỉ mở gallery khi click vào hình ảnh, không phải nút điều hướng
      if (!e.target.closest('.gallery-nav-btn')) {
        // Thêm hiệu ứng phản hồi trực quan
        const clickEffect = document.createElement('div');
        clickEffect.className = 'click-effect';
        clickEffect.style.position = 'absolute';
        clickEffect.style.top =
          e.clientY - mainImageContainer.getBoundingClientRect().top + 'px';
        clickEffect.style.left =
          e.clientX - mainImageContainer.getBoundingClientRect().left + 'px';
        clickEffect.style.width = '0';
        clickEffect.style.height = '0';
        clickEffect.style.borderRadius = '50%';
        clickEffect.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
        clickEffect.style.transform = 'translate(-50%, -50%)';
        clickEffect.style.transition = 'all 0.3s ease-out';

        mainImageContainer.appendChild(clickEffect);

        // Hiệu ứng phóng to
        setTimeout(() => {
          clickEffect.style.width = '100px';
          clickEffect.style.height = '100px';
          clickEffect.style.opacity = '0';
        }, 10);

        // Xóa hiệu ứng sau khi hoàn thành
        setTimeout(() => {
          mainImageContainer.removeChild(clickEffect);
          // Mở gallery
          openFullscreenGallery(currentIndex);
        }, 300);
      }
    });
  }

  // Mở gallery toàn màn hình
  function openFullscreenGallery(startIndex = 0) {
    if (!fullscreenGallery) return;

    // Lưu vị trí cuộn hiện tại
    const scrollY = window.scrollY;

    // Hiển thị gallery
    fullscreenGallery.classList.add('active');

    // Ngăn cuộn trang và cố định vị trí
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    // Chuyển đến slide hiện tại
    changeFullscreenSlide(startIndex);
  }

  // Đóng gallery toàn màn hình
  if (closeGalleryBtn) {
    closeGalleryBtn.addEventListener('click', function () {
      // Khôi phục cuộn trang
      const scrollY = parseInt(document.body.style.top || '0') * -1;

      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';

      // Cuộn lại đến vị trí trước đó
      window.scrollTo(0, scrollY);

      // Ẩn gallery
      fullscreenGallery.classList.remove('active');
    });
  }

  // Xử lý nút điều hướng trong gallery toàn màn hình
  if (fullscreenPrevBtn && fullscreenNextBtn && gallerySlides.length > 1) {
    fullscreenPrevBtn.addEventListener('click', function () {
      const currentIndex = parseInt(currentSlideEl.textContent) - 1;
      const newIndex =
        (currentIndex - 1 + gallerySlides.length) % gallerySlides.length;
      changeFullscreenSlide(newIndex);
    });

    fullscreenNextBtn.addEventListener('click', function () {
      const currentIndex = parseInt(currentSlideEl.textContent) - 1;
      const newIndex = (currentIndex + 1) % gallerySlides.length;
      changeFullscreenSlide(newIndex);
    });
  }

  // Xử lý vuốt (swipe) trên thiết bị di động cho hình ảnh chính

  if (mainImageContainer && mainSlides.length > 1) {
    // Touch events for mobile
    mainImageContainer.addEventListener('touchstart', touchStart);
    mainImageContainer.addEventListener('touchmove', touchMove);
    mainImageContainer.addEventListener('touchend', touchEnd);

    function touchStart(e) {
      touchStartX = e.touches[0].clientX;
    }

    function touchMove(e) {
      touchEndX = e.touches[0].clientX;
    }

    function touchEnd() {
      if (touchStartX - touchEndX > 50) {
        // Swipe left - next image
        const newIndex = (currentIndex + 1) % mainSlides.length;
        changeMainSlide(newIndex);
      } else if (touchEndX - touchStartX > 50) {
        // Swipe right - previous image
        const newIndex =
          (currentIndex - 1 + mainSlides.length) % mainSlides.length;
        changeMainSlide(newIndex);
      }
    }
  }

  // Xử lý vuốt (swipe) trong gallery toàn màn hình
  if (fullscreenGallery && gallerySlides.length > 1) {
    fullscreenGallery.addEventListener('touchstart', fullscreenTouchStart);
    fullscreenGallery.addEventListener('touchmove', fullscreenTouchMove);
    fullscreenGallery.addEventListener('touchend', fullscreenTouchEnd);

    let fullscreenTouchStartX = 0;
    let fullscreenTouchEndX = 0;

    function fullscreenTouchStart(e) {
      fullscreenTouchStartX = e.touches[0].clientX;
    }

    function fullscreenTouchMove(e) {
      fullscreenTouchEndX = e.touches[0].clientX;
    }

    function fullscreenTouchEnd() {
      const currentIndex = parseInt(currentSlideEl.textContent) - 1;

      if (fullscreenTouchStartX - fullscreenTouchEndX > 50) {
        // Swipe left - next image
        const newIndex = (currentIndex + 1) % gallerySlides.length;
        changeFullscreenSlide(newIndex);
      } else if (fullscreenTouchEndX - fullscreenTouchStartX > 50) {
        // Swipe right - previous image
        const newIndex =
          (currentIndex - 1 + gallerySlides.length) % gallerySlides.length;
        changeFullscreenSlide(newIndex);
      }
    }
  }

  // Xử lý phím mũi tên trong gallery toàn màn hình
  document.addEventListener('keydown', function (e) {
    if (!fullscreenGallery || !fullscreenGallery.classList.contains('active'))
      return;

    const currentIndex = parseInt(currentSlideEl.textContent) - 1;

    if (e.key === 'ArrowLeft') {
      // Mũi tên trái - previous image
      const newIndex =
        (currentIndex - 1 + gallerySlides.length) % gallerySlides.length;
      changeFullscreenSlide(newIndex);
    } else if (e.key === 'ArrowRight') {
      // Mũi tên phải - next image
      const newIndex = (currentIndex + 1) % gallerySlides.length;
      changeFullscreenSlide(newIndex);
    } else if (e.key === 'Escape') {
      // ESC - đóng gallery
      if (closeGalleryBtn) {
        // Sử dụng sự kiện click để đảm bảo tất cả các xử lý đóng gallery được thực hiện
        closeGalleryBtn.click();
      }
    }
  });

  // Khởi tạo ban đầu
  if (mainSliderTrack && mainSlides.length > 0) {
    // Đảm bảo slider track có chiều rộng đúng
    mainSliderTrack.style.width = `${mainSlides.length * 100}%`;

    // Đặt chiều rộng cho mỗi slide
    mainSlides.forEach((slide) => {
      slide.style.width = `${100 / mainSlides.length}%`;
    });

    // Chuyển đến slide đầu tiên và đảm bảo thumbnail đầu tiên được chọn
    changeMainSlide(0, false);
  }

  // Khởi tạo gallery toàn màn hình
  if (gallerySliderTrack && gallerySlides.length > 0) {
    // Đảm bảo slider track có chiều rộng đúng
    gallerySliderTrack.style.width = `${gallerySlides.length * 100}%`;

    // Đặt chiều rộng cho mỗi slide
    gallerySlides.forEach((slide) => {
      slide.style.width = `${100 / gallerySlides.length}%`;
    });
  }

  // Xử lý chọn kích thước và cập nhật giá
  const sizeOptionSelect = document.getElementById('size_option');
  if (sizeOptionSelect) {
    sizeOptionSelect.addEventListener('change', function () {
      const selectedOption = this.options[this.selectedIndex];
      const price = selectedOption.getAttribute('data-price');

      // Cập nhật hiển thị giá nếu có phần tử hiển thị giá
      const priceDisplay = document.querySelector('.product-price');
      if (priceDisplay && price) {
        // Format giá tiền
        const formattedPrice = price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'đ';

        priceDisplay.textContent = formattedPrice;
      }
    });
  }

  // Xử lý nút tăng/giảm số lượng
  const quantityInput = document.getElementById('quantity');
  const decreaseBtn = document.querySelector('.quantity-decrease');
  const increaseBtn = document.querySelector('.quantity-increase');

  if (quantityInput && decreaseBtn && increaseBtn) {
    decreaseBtn.addEventListener('click', function () {
      const currentValue = parseInt(quantityInput.value);
      if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
      }
    });

    increaseBtn.addEventListener('click', function () {
      const currentValue = parseInt(quantityInput.value);
      const maxValue = parseInt(quantityInput.getAttribute('max'));
      if (currentValue < maxValue) {
        quantityInput.value = currentValue + 1;
      }
    });
  }

  // Xử lý tabs trong phần chi tiết sản phẩm
  const productTabBtns = document.querySelectorAll('.product-tab-btn');
  const productTabContents = document.querySelectorAll('.product-tab-content');

  if (productTabBtns.length > 0 && productTabContents.length > 0) {
    productTabBtns.forEach((btn) => {
      btn.addEventListener('click', function () {
        // Xóa trạng thái active của tất cả các tab
        productTabBtns.forEach((tabBtn) => {
          tabBtn.classList.remove('active', 'text-blue-500', 'border-blue-500');
          tabBtn.classList.add('text-gray-500', 'border-transparent');
        });

        // Thêm trạng thái active cho tab được chọn
        this.classList.remove('text-gray-500', 'border-transparent');
        this.classList.add('active', 'text-blue-500', 'border-blue-500');

        // Ẩn tất cả nội dung tab
        productTabContents.forEach((content) => {
          content.classList.add('hidden');
        });

        // Hiển thị nội dung tab tương ứng
        const tabId = this.getAttribute('data-tab');
        const tabContent = document.getElementById('tab-' + tabId);
        if (tabContent) {
          tabContent.classList.remove('hidden');

          // Thêm hiệu ứng fade-in
          tabContent.style.opacity = '0';
          tabContent.style.transition = 'opacity 0.3s ease';
          setTimeout(() => {
            tabContent.style.opacity = '1';
          }, 10);
        }
      });
    });
  }

  // Xử lý tabs trong phần đánh giá
  const reviewTabBtns = document.querySelectorAll(
    '#tab-ratings, #tab-comments'
  );

  if (reviewTabBtns.length > 0) {
    reviewTabBtns.forEach((btn) => {
      btn.addEventListener('click', function () {
        // Xóa trạng thái active của tất cả các tab
        reviewTabBtns.forEach((tabBtn) => {
          tabBtn.classList.remove('text-blue-500', 'border-blue-500');
          tabBtn.classList.add('text-gray-500', 'border-transparent');
        });

        // Thêm trạng thái active cho tab được chọn
        this.classList.remove('text-gray-500', 'border-transparent');
        this.classList.add('text-blue-500', 'border-blue-500');

        // Xác định tab hiện tại
        const isCommentsTab = this.id === 'tab-comments';

        // Cập nhật URL với tham số tab
        const url = new URL(window.location.href);
        url.searchParams.set('tab', isCommentsTab ? 'comments' : 'ratings');
        window.history.replaceState({}, '', url);

        // Tải lại trang để hiển thị tab mới
        // window.location.href = url.toString();

        // Hoặc sử dụng AJAX để tải nội dung tab mới mà không cần tải lại trang
        // Đây là phần có thể phát triển thêm trong tương lai
      });
    });
  }
});
