/**
 * Xử lý chọn địa chỉ Việt Nam từ API
 * Sử dụng API: https://provinces.open-api.vn/api/
 */
document.addEventListener('DOMContentLoaded', function() {
    // Các phần tử DOM
    const provinceSelect = document.getElementById('province');
    const districtSelect = document.getElementById('district');
    const wardSelect = document.getElementById('ward');
    const addressDetailInput = document.getElementById('address_detail');
    const addressPreview = document.getElementById('address_preview');
    const addressInput = document.getElementById('address');

    // Các URL API
    const API_URL = 'https://provinces.open-api.vn/api/';
    const PROVINCE_API = API_URL + 'p/';
    const DISTRICT_API = API_URL + 'd/';
    const WARD_API = API_URL + 'w/';

    // Biến lưu trữ dữ liệu
    let provinces = [];
    let districts = [];
    let wards = [];
    let selectedProvince = '';
    let selectedDistrict = '';
    let selectedWard = '';

    // Khởi tạo
    init();

    /**
     * Khởi tạo dữ liệu
     */
    function init() {
        // Vô hiệu hóa các select chưa có dữ liệu
        districtSelect.disabled = true;
        wardSelect.disabled = true;

        // Lấy danh sách tỉnh/thành phố
        fetchProvinces();

        // Thêm sự kiện cho các select
        provinceSelect.addEventListener('change', onProvinceChange);
        districtSelect.addEventListener('change', onDistrictChange);
        wardSelect.addEventListener('change', onWardChange);
        
        // Thêm sự kiện cho input địa chỉ chi tiết
        if (addressDetailInput) {
            addressDetailInput.addEventListener('input', updateAddressPreview);
        }
    }

    /**
     * Lấy danh sách tỉnh/thành phố
     */
    function fetchProvinces() {
        fetch(API_URL)
            .then(response => response.json())
            .then(data => {
                provinces = data;
                renderProvinces();
                
                // Nếu đã có dữ liệu province_code, chọn tỉnh/thành phố tương ứng
                const savedProvinceCode = provinceSelect.getAttribute('data-selected');
                if (savedProvinceCode) {
                    provinceSelect.value = savedProvinceCode;
                    onProvinceChange();
                }
            })
            .catch(error => {
                console.error('Lỗi khi lấy danh sách tỉnh/thành phố:', error);
            });
    }

    /**
     * Hiển thị danh sách tỉnh/thành phố
     */
    function renderProvinces() {
        // Xóa tất cả các option hiện tại
        provinceSelect.innerHTML = '<option value="">-- Chọn Tỉnh/Thành phố --</option>';

        // Thêm các option mới
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.code;
            option.textContent = province.name;
            provinceSelect.appendChild(option);
        });
    }

    /**
     * Xử lý khi chọn tỉnh/thành phố
     */
    function onProvinceChange() {
        const provinceCode = provinceSelect.value;
        
        // Nếu không chọn tỉnh/thành phố
        if (!provinceCode) {
            districtSelect.innerHTML = '<option value="">-- Chọn Quận/Huyện --</option>';
            wardSelect.innerHTML = '<option value="">-- Chọn Phường/Xã --</option>';
            districtSelect.disabled = true;
            wardSelect.disabled = true;
            selectedProvince = '';
            updateAddressPreview();
            return;
        }

        // Lưu tên tỉnh/thành phố đã chọn
        selectedProvince = provinceSelect.options[provinceSelect.selectedIndex].text;
        
        // Lấy danh sách quận/huyện
        fetch(PROVINCE_API + provinceCode + '?depth=2')
            .then(response => response.json())
            .then(data => {
                districts = data.districts;
                renderDistricts();
                
                // Vô hiệu hóa phường/xã cho đến khi chọn quận/huyện
                wardSelect.innerHTML = '<option value="">-- Chọn Phường/Xã --</option>';
                wardSelect.disabled = true;
                
                // Nếu đã có dữ liệu district_code, chọn quận/huyện tương ứng
                const savedDistrictCode = districtSelect.getAttribute('data-selected');
                if (savedDistrictCode) {
                    districtSelect.value = savedDistrictCode;
                    onDistrictChange();
                }
                
                updateAddressPreview();
            })
            .catch(error => {
                console.error('Lỗi khi lấy danh sách quận/huyện:', error);
            });
    }

    /**
     * Hiển thị danh sách quận/huyện
     */
    function renderDistricts() {
        // Xóa tất cả các option hiện tại
        districtSelect.innerHTML = '<option value="">-- Chọn Quận/Huyện --</option>';

        // Thêm các option mới
        districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.code;
            option.textContent = district.name;
            districtSelect.appendChild(option);
        });

        // Kích hoạt select quận/huyện
        districtSelect.disabled = false;
    }

    /**
     * Xử lý khi chọn quận/huyện
     */
    function onDistrictChange() {
        const districtCode = districtSelect.value;
        
        // Nếu không chọn quận/huyện
        if (!districtCode) {
            wardSelect.innerHTML = '<option value="">-- Chọn Phường/Xã --</option>';
            wardSelect.disabled = true;
            selectedDistrict = '';
            updateAddressPreview();
            return;
        }

        // Lưu tên quận/huyện đã chọn
        selectedDistrict = districtSelect.options[districtSelect.selectedIndex].text;
        
        // Lấy danh sách phường/xã
        fetch(DISTRICT_API + districtCode + '?depth=2')
            .then(response => response.json())
            .then(data => {
                wards = data.wards;
                renderWards();
                
                // Nếu đã có dữ liệu ward_code, chọn phường/xã tương ứng
                const savedWardCode = wardSelect.getAttribute('data-selected');
                if (savedWardCode) {
                    wardSelect.value = savedWardCode;
                    onWardChange();
                }
                
                updateAddressPreview();
            })
            .catch(error => {
                console.error('Lỗi khi lấy danh sách phường/xã:', error);
            });
    }

    /**
     * Hiển thị danh sách phường/xã
     */
    function renderWards() {
        // Xóa tất cả các option hiện tại
        wardSelect.innerHTML = '<option value="">-- Chọn Phường/Xã --</option>';

        // Thêm các option mới
        wards.forEach(ward => {
            const option = document.createElement('option');
            option.value = ward.code;
            option.textContent = ward.name;
            wardSelect.appendChild(option);
        });

        // Kích hoạt select phường/xã
        wardSelect.disabled = false;
    }

    /**
     * Xử lý khi chọn phường/xã
     */
    function onWardChange() {
        const wardCode = wardSelect.value;
        
        // Nếu không chọn phường/xã
        if (!wardCode) {
            selectedWard = '';
            updateAddressPreview();
            return;
        }

        // Lưu tên phường/xã đã chọn
        selectedWard = wardSelect.options[wardSelect.selectedIndex].text;
        updateAddressPreview();
    }

    /**
     * Cập nhật xem trước địa chỉ
     */
    function updateAddressPreview() {
        let addressParts = [];
        let addressDetail = addressDetailInput ? addressDetailInput.value.trim() : '';
        
        // Thêm địa chỉ chi tiết
        if (addressDetail) {
            addressParts.push(addressDetail);
        }
        
        // Thêm phường/xã
        if (selectedWard) {
            addressParts.push(selectedWard);
        }
        
        // Thêm quận/huyện
        if (selectedDistrict) {
            addressParts.push(selectedDistrict);
        }
        
        // Thêm tỉnh/thành phố
        if (selectedProvince) {
            addressParts.push(selectedProvince);
        }
        
        // Tạo chuỗi địa chỉ đầy đủ
        const fullAddress = addressParts.join(', ');
        
        // Cập nhật xem trước
        if (addressPreview) {
            if (fullAddress) {
                addressPreview.textContent = fullAddress;
                addressPreview.parentElement.classList.remove('hidden');
            } else {
                addressPreview.parentElement.classList.add('hidden');
            }
        }
        
        // Cập nhật input ẩn
        if (addressInput) {
            addressInput.value = fullAddress;
        }
    }
});
