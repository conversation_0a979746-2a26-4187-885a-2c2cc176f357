/**
 * Mega Menu JavaScript for Nội Thất Bàng Vũ
 * Handles interactions with the mega menu
 *
 * Tính năng:
 * - Điều chỉnh vị trí và kích thước mega menu theo không gian còn lại
 * - Thích ứng theo trạng thái sticky của header
 * - Hỗ trợ thanh cuộn cho mega menu
 */
document.addEventListener('DOMContentLoaded', function () {
  // Xử lý chuyển đổi tab trong mega menu
  const megaMenuCategories = document.querySelectorAll('.mega-menu-category');
  const megaMenuContents = document.querySelectorAll('.mega-menu-content');

  // Mặc định hiển thị tab đầu tiên
  if (megaMenuCategories.length > 0 && megaMenuContents.length > 0) {
    megaMenuCategories[0].classList.add('active');
    megaMenuCategories[0].setAttribute('aria-selected', 'true');
    megaMenuContents[0].classList.add('active');
    megaMenuContents[0].removeAttribute('hidden');
  }

  // Variable to store animation timeout
  let animationTimeout = null;

  // Xử lý sự kiện hover vào danh mục
  megaMenuCategories.forEach(function (category, index) {
    category.addEventListener('mouseenter', function () {
      // Clear any existing animation timeout
      if (animationTimeout) {
        clearTimeout(animationTimeout);
        animationTimeout = null;
      }

      // Immediately hide overflow on all contents to prevent scrollbar
      megaMenuContents.forEach(function (content) {
        content.style.overflow = 'hidden';
        content.classList.remove('active', 'animation-complete');
        content.setAttribute('hidden', '');
      });

      // Xóa active class từ tất cả các danh mục
      megaMenuCategories.forEach(function (cat) {
        cat.classList.remove('active');
        cat.setAttribute('aria-selected', 'false');
        cat.setAttribute('tabindex', '-1');
      });

      // Thêm active class cho danh mục và nội dung tương ứng
      this.classList.add('active');
      this.setAttribute('aria-selected', 'true');
      this.setAttribute('tabindex', '0');

      if (megaMenuContents[index]) {
        megaMenuContents[index].classList.add('active');
        megaMenuContents[index].removeAttribute('hidden');

        // Set new animation timeout
        animationTimeout = setTimeout(function() {
          if (megaMenuContents[index] && megaMenuContents[index].classList.contains('active')) {
            megaMenuContents[index].classList.add('animation-complete');
            megaMenuContents[index].style.overflow = ''; // Restore default overflow
          }
        }, 800); // 0.8s = longest animation duration
      }
    });

    // Thêm sự kiện click để chuyển hướng đến trang danh mục
    category.addEventListener('click', function () {
      // Lấy ID danh mục từ thuộc tính data-category-id
      const categoryId = this.getAttribute('data-category-id');
      if (categoryId) {
        // Lấy slug từ nội dung tương ứng
        const categoryContent = document.getElementById(
          'category-content-' + categoryId
        );
        if (categoryContent) {
          const viewAllLink = categoryContent.querySelector(
            '.mega-menu-view-all'
          );
          if (viewAllLink && viewAllLink.href) {
            // Chuyển hướng đến trang danh mục
            window.location.href = viewAllLink.href;
          } else {
            // Fallback nếu không tìm thấy link "Xem tất cả"
            window.location.href = BASE_URL + '/category.php?id=' + categoryId;
          }
        }
      }
    });
  });

  // Xử lý hover vào mega menu
  const navItems = document.querySelectorAll('.nav-item');
  let hoverTimeout;

  // Biến để lưu trữ nav-item có mega menu
  let megaMenuNavItem = null;

  // Tìm nav-item có mega menu
  navItems.forEach(function (navItem) {
    if (navItem.querySelector('.mega-menu')) {
      megaMenuNavItem = navItem;
    }
  });

  navItems.forEach(function (item) {
    const megaMenu = item.querySelector('.mega-menu');
    if (megaMenu) {
      // Khi hover vào nav item
      item.addEventListener('mouseenter', function () {
        clearTimeout(hoverTimeout);
        megaMenu.style.display = 'flex';

        // Cập nhật thuộc tính ARIA
        const navLink = item.querySelector('.nav-link');
        if (navLink) {
          navLink.setAttribute('aria-expanded', 'true');
        }

        setTimeout(function () {
          megaMenu.style.opacity = '1';
          megaMenu.style.visibility = 'visible';
          megaMenu.style.transform = 'translateY(0)';
          megaMenu.setAttribute('aria-hidden', 'false');
        }, 10);
      });

      // Khi rời khỏi nav item
      item.addEventListener('mouseleave', function () {
        hoverTimeout = setTimeout(function () {
          megaMenu.style.opacity = '0';
          megaMenu.style.visibility = 'hidden';
          megaMenu.style.transform = 'translateY(10px)';
          megaMenu.setAttribute('aria-hidden', 'true');

          // Cập nhật thuộc tính ARIA
          const navLink = item.querySelector('.nav-link');
          if (navLink) {
            navLink.setAttribute('aria-expanded', 'false');
          }
        }, 200);
      });

      // Khi hover vào mega menu
      megaMenu.addEventListener('mouseenter', function () {
        clearTimeout(hoverTimeout);

        // Thêm class hover-effect vào nav-item cha (mục Sản phẩm)
        if (megaMenuNavItem) {
          megaMenuNavItem.classList.add('hover-effect');
        }
      });

      // Khi rời khỏi mega menu
      megaMenu.addEventListener('mouseleave', function () {
        hoverTimeout = setTimeout(function () {
          // Xóa class hover-effect khỏi nav-item cha (mục Sản phẩm)
          if (megaMenuNavItem) {
            megaMenuNavItem.classList.remove('hover-effect');
          }
          megaMenu.style.opacity = '0';
          megaMenu.style.visibility = 'hidden';
          megaMenu.style.transform = 'translateY(10px)';
        }, 200);
      });
    }
  });

  /**
   * Tối ưu mega menu
   * - Điều chỉnh vị trí và kích thước theo không gian còn lại
   * - Thích ứng theo trạng thái sticky của header
   */
  function optimizeMegaMenu() {
    // Tìm header và các phần tử liên quan
    const header =
      document.querySelector('.premium-header') ||
      document.querySelector('.elegant-header') ||
      document.querySelector('.modern-header') ||
      document.querySelector('.luxury-header') ||
      document.querySelector('.three-tier-header') ||
      document.querySelector('header');

    if (!header) return;

    const navItems = document.querySelectorAll('.nav-item');

    function adjustMegaMenu() {
      const headerHeight = header.offsetHeight;
      const windowHeight = window.innerHeight;
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const isSticky = scrollTop > 10; // Header đã sticky

      navItems.forEach(function (item) {
        const megaMenu = item.querySelector('.mega-menu');
        if (megaMenu) {
          const itemRect = item.getBoundingClientRect();
          const spaceBelow = windowHeight - itemRect.bottom;

          // Điều chỉnh theo không gian và trạng thái sticky
          if (isSticky) {
            // Khi header đã sticky, giới hạn chiều cao mega menu
            const maxHeight = windowHeight - headerHeight - 20; // Trừ đi margin
            megaMenu.style.maxHeight = `${maxHeight}px`;
            megaMenu.style.overflowY = 'auto';

            // Điều chỉnh vị trí hiển thị để không bị cắt
            if (maxHeight < 400) {
              // Nếu không gian quá nhỏ
              // Đảm bảo mega menu không bị cắt bởi cạnh dưới của màn hình
              megaMenu.style.top = 'auto';
              megaMenu.style.bottom = '0';
            } else {
              megaMenu.style.top = '100%';
              megaMenu.style.bottom = 'auto';
            }
          } else {
            // Khi header ở vị trí bình thường
            const maxHeight = spaceBelow - 20; // Trừ đi margin
            megaMenu.style.maxHeight = `${maxHeight}px`;
            megaMenu.style.overflowY = 'auto';

            // Điều chỉnh vị trí hiển thị
            if (maxHeight < 400) {
              // Nếu không gian quá nhỏ
              megaMenu.style.top = 'auto';
              megaMenu.style.bottom = '0';
            } else {
              megaMenu.style.top = '100%';
              megaMenu.style.bottom = 'auto';
            }
          }

          // Điều chỉnh chiều cao của các phần con
          const megaMenuCategories = megaMenu.querySelector(
            '.mega-menu-categories'
          );
          const megaMenuContent = megaMenu.querySelector(
            '.mega-menu-content.active'
          );

          if (megaMenuCategories && megaMenuContent) {
            // Đảm bảo các phần con có thể cuộn độc lập
            megaMenuCategories.style.maxHeight = megaMenu.style.maxHeight;
            megaMenuContent.style.maxHeight = megaMenu.style.maxHeight;
          }
        }
      });
    }

    // Gọi hàm khi hover vào menu
    navItems.forEach(function (item) {
      const megaMenu = item.querySelector('.mega-menu');
      if (megaMenu) {
        item.addEventListener('mouseenter', function () {
          // Gọi hàm điều chỉnh ngay khi hover
          adjustMegaMenu();

          // Gọi lại sau một khoảng thời gian ngắn để đảm bảo chính xác
          setTimeout(adjustMegaMenu, 50);
        });

        // Thêm sự kiện khi mega menu hiển thị
        megaMenu.addEventListener('transitionend', function (e) {
          if (e.propertyName === 'opacity' && megaMenu.style.opacity === '1') {
            adjustMegaMenu();
          }
        });
      }
    });

    // Gọi hàm khi cuộn trang hoặc thay đổi kích thước
    let scrollTimer;
    window.addEventListener('scroll', function () {
      // Sử dụng debounce để tránh gọi quá nhiều lần
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(adjustMegaMenu, 100);
    });

    let resizeTimer;
    window.addEventListener('resize', function () {
      // Sử dụng debounce để tránh gọi quá nhiều lần
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(adjustMegaMenu, 100);
    });

    // Gọi hàm khi trang tải xong
    window.addEventListener('load', adjustMegaMenu);
  }

  // Khởi chạy hàm tối ưu mega menu
  optimizeMegaMenu();

  // Thêm hỗ trợ keyboard navigation cho mega menu
  function setupKeyboardNavigation() {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(function(item) {
      const navLink = item.querySelector('.nav-link');
      const megaMenu = item.querySelector('.mega-menu');

      if (navLink && megaMenu) {
        // Xử lý khi nhấn Enter hoặc Space trên nav link
        navLink.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();

            // Toggle mega menu
            if (navLink.getAttribute('aria-expanded') === 'true') {
              // Đóng mega menu
              navLink.setAttribute('aria-expanded', 'false');
              megaMenu.style.opacity = '0';
              megaMenu.style.visibility = 'hidden';
              megaMenu.style.transform = 'translateY(10px)';
              megaMenu.setAttribute('aria-hidden', 'true');
            } else {
              // Mở mega menu
              navLink.setAttribute('aria-expanded', 'true');
              megaMenu.style.display = 'flex';
              megaMenu.style.opacity = '1';
              megaMenu.style.visibility = 'visible';
              megaMenu.style.transform = 'translateY(0)';
              megaMenu.setAttribute('aria-hidden', 'false');

              // Focus vào tab đầu tiên
              const firstTab = megaMenu.querySelector('.mega-menu-category[tabindex="0"]');
              if (firstTab) {
                firstTab.focus();
              }
            }
          }
        });

        // Xử lý keyboard navigation trong mega menu tabs
        const tabs = megaMenu.querySelectorAll('.mega-menu-category');

        tabs.forEach(function(tab, index) {
          tab.addEventListener('keydown', function(e) {
            // Xử lý phím mũi tên
            if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
              e.preventDefault();
              // Focus vào tab tiếp theo
              const nextTab = tabs[(index + 1) % tabs.length];
              if (nextTab) {
                nextTab.focus();
                // Kích hoạt tab
                nextTab.dispatchEvent(new Event('mouseenter'));
              }
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
              e.preventDefault();
              // Focus vào tab trước đó
              const prevIndex = (index - 1 + tabs.length) % tabs.length;
              const prevTab = tabs[prevIndex];
              if (prevTab) {
                prevTab.focus();
                // Kích hoạt tab
                prevTab.dispatchEvent(new Event('mouseenter'));
              }
            } else if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              // Kích hoạt tab
              tab.click();
            } else if (e.key === 'Escape') {
              e.preventDefault();
              // Đóng mega menu và focus lại vào nav link
              navLink.setAttribute('aria-expanded', 'false');
              megaMenu.style.opacity = '0';
              megaMenu.style.visibility = 'hidden';
              megaMenu.style.transform = 'translateY(10px)';
              megaMenu.setAttribute('aria-hidden', 'true');
              navLink.focus();
            }
          });
        });
      }
    });
  }

  // Khởi chạy hàm hỗ trợ keyboard navigation
  setupKeyboardNavigation();
});

// Hiệu ứng cho phần danh mục sản phẩm trên trang chủ
document.addEventListener('DOMContentLoaded', function () {
  // Lấy tất cả các thẻ danh mục
  const categoryCards = document.querySelectorAll('.category-card');

  if (categoryCards.length > 0) {
    // Thêm hiệu ứng staggered animation cho các thẻ
    categoryCards.forEach((card, index) => {
      // Thêm transition delay tăng dần cho hiệu ứng xuất hiện
      card.style.opacity = '0';
      card.style.transform = 'translateY(30px)';
      card.style.transition =
        'opacity 0.6s ease, transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1)';
      card.style.transitionDelay = `${index * 0.08}s`;

      // Hiệu ứng xuất hiện mượt mà khi trang tải xong
      setTimeout(() => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
      }, 100);

      // Thêm hiệu ứng hover mượt mà
      const productCount = card.querySelector('.px-2\\.5');
      const cardTitle = card.querySelector('h3');
      const cardDesc = card.querySelector('p');
      const cardBtn = card.querySelector('.category-btn');
      const cardBtnIcon = card.querySelector('.category-btn-icon');

      card.addEventListener('mouseenter', function () {
        if (cardTitle) cardTitle.style.transform = 'translateY(-3px)';
        if (cardDesc) cardDesc.style.transform = 'translateY(-2px)';
        if (cardBtn) cardBtn.style.transform = 'translateY(-2px)';

        // Thêm hover class để kích hoạt các hiệu ứng CSS
        this.classList.add('card-hover');
      });

      card.addEventListener('mouseleave', function () {
        if (cardTitle) cardTitle.style.transform = 'translateY(0)';
        if (cardDesc) cardDesc.style.transform = 'translateY(0)';
        if (cardBtn) cardBtn.style.transform = 'translateY(0)';

        // Xoá hover class
        this.classList.remove('card-hover');
      });
    });

    // Xử lý hiệu ứng xuất hiện khi scroll đến các thẻ
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const cardObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');

          // Chỉ kích hoạt một lần hiệu ứng cho mỗi card
          cardObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Đăng ký theo dõi các thẻ category
    categoryCards.forEach((card) => {
      cardObserver.observe(card);
    });

    // Thêm hiệu ứng hover 3D nhẹ cho các thẻ - sử dụng vanilla JS cho hiệu suất tốt hơn
    categoryCards.forEach((card) => {
      card.addEventListener('mousemove', function (e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left; // vị trí x của chuột trong thẻ
        const y = e.clientY - rect.top; // vị trí y của chuột trong thẻ

        // Tính toán độ nghiêng dựa trên vị trí chuột
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        // Giới hạn độ nghiêng nhỏ để tạo hiệu ứng tinh tế
        const tiltX = (y - centerY) / 20;
        const tiltY = (centerX - x) / 20;

        // Áp dụng hiệu ứng transform nhẹ nhàng
        this.style.transform = `translateY(-8px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;

        // Hiệu ứng đổ bóng theo hướng chuột
        const shadowX = (x - centerX) / 40;
        const shadowY = (y - centerY) / 40;
        this.style.boxShadow = `${shadowX}px ${shadowY}px 30px rgba(0, 0, 0, 0.1)`;
      });

      // Reset transform khi rời chuột
      card.addEventListener('mouseleave', function () {
        this.style.transform = 'translateY(0) rotateX(0) rotateY(0)';
        this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.05)';
        // Cho phép transition mượt mà khi rời chuột
        this.style.transition = 'transform 0.5s ease, box-shadow 0.5s ease';
      });

      // Tắt transition khi di chuyển chuột để hiệu ứng mượt mà hơn
      card.addEventListener('mouseenter', function () {
        this.style.transition =
          'transform 0.2s ease-out, box-shadow 0.2s ease-out';
      });
    });
  }
});
