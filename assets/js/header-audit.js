/**
 * Header Performance Audit Tool
 * Phân tích và báo cáo tình trạng hiện tại của header
 */

class HeaderAudit {
    constructor() {
        this.eventListeners = [];
        this.performanceMetrics = {};
        this.domElements = {};
        this.conflicts = [];
    }

    /**
     * Bắt đầu audit header
     */
    startAudit() {
        console.log('🔍 Bắt đầu audit header performance...');
        
        this.auditEventListeners();
        this.auditDOMElements();
        this.auditCSSAnimations();
        this.auditPerformanceMetrics();
        this.detectConflicts();
        
        this.generateReport();
    }

    /**
     * Audit tất cả event listeners liên quan đến scroll
     */
    auditEventListeners() {
        console.log('📊 Đang audit event listeners...');
        
        // Lưu trữ original addEventListener
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const self = this;
        
        // Hook addEventListener để track
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type === 'scroll' || type === 'resize') {
                self.eventListeners.push({
                    type: type,
                    target: this,
                    listener: listener,
                    options: options,
                    stack: new Error().stack
                });
            }
            return originalAddEventListener.call(this, type, listener, options);
        };

        // Kiểm tra các file JavaScript đã load
        const scripts = document.querySelectorAll('script[src*="header"], script[src*="scroll"]');
        scripts.forEach(script => {
            console.log(`📄 Detected header-related script: ${script.src}`);
        });
    }

    /**
     * Audit DOM elements của header
     */
    auditDOMElements() {
        console.log('🏗️ Đang audit DOM elements...');
        
        this.domElements = {
            premiumHeader: document.querySelector('.premium-header'),
            topBar: document.querySelector('.top-bar'),
            midHeader: document.querySelector('.mid-header'),
            bottomHeader: document.querySelector('.bottom-header'),
            mobileHeader: document.querySelector('.mobile-header'),
            threetierHeader: document.querySelector('.three-tier-header')
        };

        // Đếm số lượng header elements
        let headerCount = 0;
        Object.values(this.domElements).forEach(el => {
            if (el) headerCount++;
        });

        console.log(`🔢 Tìm thấy ${headerCount} header elements`);
    }

    /**
     * Audit CSS animations
     */
    auditCSSAnimations() {
        console.log('🎨 Đang audit CSS animations...');
        
        const animatedElements = document.querySelectorAll('[class*="animate"], [style*="animation"]');
        console.log(`🎭 Tìm thấy ${animatedElements.length} elements có animation`);

        // Kiểm tra CSS keyframes
        const stylesheets = document.styleSheets;
        let keyframeCount = 0;
        
        try {
            for (let sheet of stylesheets) {
                for (let rule of sheet.cssRules || sheet.rules || []) {
                    if (rule.type === CSSRule.KEYFRAMES_RULE) {
                        keyframeCount++;
                        console.log(`🎯 Keyframe: ${rule.name}`);
                    }
                }
            }
        } catch (e) {
            console.log('⚠️ Không thể access một số stylesheets (CORS)');
        }

        console.log(`📊 Tổng cộng ${keyframeCount} keyframe animations`);
    }

    /**
     * Đo performance metrics
     */
    auditPerformanceMetrics() {
        console.log('⚡ Đang đo performance metrics...');
        
        // Đo FPS
        this.measureFPS();
        
        // Đo scroll event frequency
        this.measureScrollFrequency();
        
        // Đo layout shifts
        this.measureLayoutShifts();
    }

    /**
     * Đo FPS
     */
    measureFPS() {
        let frames = 0;
        let lastTime = performance.now();
        
        const measureFrame = (currentTime) => {
            frames++;
            if (currentTime - lastTime >= 1000) {
                this.performanceMetrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
                console.log(`📈 Current FPS: ${this.performanceMetrics.fps}`);
                frames = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(measureFrame);
        };
        
        requestAnimationFrame(measureFrame);
    }

    /**
     * Đo scroll event frequency
     */
    measureScrollFrequency() {
        let scrollEvents = 0;
        let startTime = performance.now();
        
        const scrollHandler = () => {
            scrollEvents++;
        };
        
        window.addEventListener('scroll', scrollHandler, { passive: true });
        
        setTimeout(() => {
            const duration = performance.now() - startTime;
            this.performanceMetrics.scrollFrequency = Math.round((scrollEvents * 1000) / duration);
            console.log(`📊 Scroll events per second: ${this.performanceMetrics.scrollFrequency}`);
            window.removeEventListener('scroll', scrollHandler);
        }, 5000);
    }

    /**
     * Đo layout shifts
     */
    measureLayoutShifts() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.value > 0.1) {
                        console.warn(`⚠️ Layout shift detected: ${entry.value}`);
                        this.performanceMetrics.layoutShifts = (this.performanceMetrics.layoutShifts || 0) + 1;
                    }
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    /**
     * Detect conflicts
     */
    detectConflicts() {
        console.log('🔍 Đang detect conflicts...');
        
        // Kiểm tra multiple headers
        const headerElements = Object.values(this.domElements).filter(el => el);
        if (headerElements.length > 1) {
            this.conflicts.push({
                type: 'Multiple Headers',
                description: `Tìm thấy ${headerElements.length} header elements`,
                severity: 'high'
            });
        }

        // Kiểm tra duplicate event listeners
        const scrollListeners = this.eventListeners.filter(l => l.type === 'scroll');
        if (scrollListeners.length > 1) {
            this.conflicts.push({
                type: 'Duplicate Scroll Listeners',
                description: `Tìm thấy ${scrollListeners.length} scroll event listeners`,
                severity: 'high'
            });
        }

        // Kiểm tra CSS conflicts
        const headerCSS = document.querySelectorAll('link[href*="header"]');
        if (headerCSS.length > 3) {
            this.conflicts.push({
                type: 'Multiple CSS Files',
                description: `Tìm thấy ${headerCSS.length} header CSS files`,
                severity: 'medium'
            });
        }
    }

    /**
     * Generate comprehensive report
     */
    generateReport() {
        console.log('\n🎯 ===== HEADER AUDIT REPORT =====');
        
        console.log('\n📊 PERFORMANCE METRICS:');
        console.log(`FPS: ${this.performanceMetrics.fps || 'Measuring...'}`);
        console.log(`Scroll Frequency: ${this.performanceMetrics.scrollFrequency || 'Measuring...'} events/sec`);
        console.log(`Layout Shifts: ${this.performanceMetrics.layoutShifts || 0}`);
        
        console.log('\n🏗️ DOM ELEMENTS:');
        Object.entries(this.domElements).forEach(([key, element]) => {
            console.log(`${key}: ${element ? '✅ Found' : '❌ Not found'}`);
        });
        
        console.log('\n📄 EVENT LISTENERS:');
        console.log(`Total scroll listeners: ${this.eventListeners.filter(l => l.type === 'scroll').length}`);
        console.log(`Total resize listeners: ${this.eventListeners.filter(l => l.type === 'resize').length}`);
        
        console.log('\n⚠️ CONFLICTS DETECTED:');
        if (this.conflicts.length === 0) {
            console.log('✅ No major conflicts detected');
        } else {
            this.conflicts.forEach(conflict => {
                const icon = conflict.severity === 'high' ? '🚨' : '⚠️';
                console.log(`${icon} ${conflict.type}: ${conflict.description}`);
            });
        }
        
        console.log('\n💡 RECOMMENDATIONS:');
        this.generateRecommendations();
        
        console.log('\n===== END REPORT =====\n');
    }

    /**
     * Generate recommendations
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (this.conflicts.some(c => c.type === 'Multiple Headers')) {
            recommendations.push('🔧 Consolidate multiple header elements into single structure');
        }
        
        if (this.conflicts.some(c => c.type === 'Duplicate Scroll Listeners')) {
            recommendations.push('🔧 Remove duplicate scroll event listeners');
        }
        
        if (this.performanceMetrics.fps && this.performanceMetrics.fps < 50) {
            recommendations.push('🔧 Optimize animations for better FPS');
        }
        
        if (this.performanceMetrics.scrollFrequency > 100) {
            recommendations.push('🔧 Implement better throttling for scroll events');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('✅ Header performance looks good!');
        }
        
        recommendations.forEach(rec => console.log(rec));
    }
}

// Auto-start audit when loaded
document.addEventListener('DOMContentLoaded', () => {
    // Delay để đảm bảo tất cả scripts đã load
    setTimeout(() => {
        window.headerAudit = new HeaderAudit();
        window.headerAudit.startAudit();
    }, 2000);
});

// Export for manual usage
window.HeaderAudit = HeaderAudit;
