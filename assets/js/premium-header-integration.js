/**
 * Premium Header Integration
 * Combines all optimizations and provides testing utilities
 * Created: 2025-07-27
 * 
 * Features:
 * - Integration of all optimization modules
 * - Performance monitoring
 * - Testing utilities
 * - Fallback mechanisms
 */

document.addEventListener('DOMContentLoaded', function() {
  // ===== CONFIGURATION =====
  const INTEGRATION_CONFIG = {
    // Performance monitoring
    ENABLE_PERFORMANCE_MONITORING: true,
    PERFORMANCE_LOG_INTERVAL: 5000, // 5 seconds
    
    // Testing mode
    ENABLE_TESTING_MODE: false,
    TESTING_LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
    
    // Fallback options
    ENABLE_FALLBACK_MODE: true,
    FALLBACK_TIMEOUT: 1000, // 1 second
    
    // Device-specific optimizations
    MOBILE_OPTIMIZATIONS: true,
    TABLET_OPTIMIZATIONS: true,
    DESKTOP_OPTIMIZATIONS: true
  };

  // ===== DEVICE DETECTION =====
  const DeviceDetector = {
    isMobile: () => window.innerWidth <= 768,
    isTablet: () => window.innerWidth > 768 && window.innerWidth <= 1024,
    isDesktop: () => window.innerWidth > 1024,
    
    // Performance capabilities
    supportsHardwareAcceleration: () => {
      const testEl = document.createElement('div');
      testEl.style.transform = 'translateZ(0)';
      return testEl.style.transform !== '';
    },
    
    supportsBackdropFilter: () => {
      const testEl = document.createElement('div');
      testEl.style.backdropFilter = 'blur(1px)';
      return testEl.style.backdropFilter !== '';
    },
    
    // Performance level detection
    getPerformanceLevel: () => {
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      const memory = navigator.deviceMemory || 4; // Default to 4GB
      const cores = navigator.hardwareConcurrency || 4; // Default to 4 cores
      
      // Calculate performance score
      let score = 0;
      score += cores * 25; // CPU cores
      score += memory * 10; // RAM
      
      if (connection) {
        const effectiveType = connection.effectiveType;
        if (effectiveType === '4g') score += 30;
        else if (effectiveType === '3g') score += 20;
        else if (effectiveType === '2g') score += 10;
      }
      
      if (score >= 150) return 'high';
      if (score >= 100) return 'medium';
      return 'low';
    }
  };

  // ===== PERFORMANCE MONITOR =====
  const PerformanceMonitor = {
    metrics: {
      frameRate: 0,
      scrollEvents: 0,
      animationFrames: 0,
      memoryUsage: 0,
      startTime: performance.now()
    },
    
    init() {
      if (!INTEGRATION_CONFIG.ENABLE_PERFORMANCE_MONITORING) return;
      
      this.startMonitoring();
      this.setupLogging();
    },
    
    startMonitoring() {
      let lastTime = performance.now();
      let frameCount = 0;
      
      const measureFrameRate = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          this.metrics.frameRate = frameCount;
          frameCount = 0;
          lastTime = currentTime;
        }
        
        this.metrics.animationFrames++;
        requestAnimationFrame(measureFrameRate);
      };
      
      requestAnimationFrame(measureFrameRate);
    },
    
    setupLogging() {
      setInterval(() => {
        this.logMetrics();
      }, INTEGRATION_CONFIG.PERFORMANCE_LOG_INTERVAL);
    },
    
    logMetrics() {
      if (INTEGRATION_CONFIG.ENABLE_TESTING_MODE) {
        console.group('🚀 Premium Header Performance Metrics');
        console.log('📊 Frame Rate:', this.metrics.frameRate, 'fps');
        console.log('📜 Scroll Events:', this.metrics.scrollEvents);
        console.log('🎬 Animation Frames:', this.metrics.animationFrames);
        
        if (performance.memory) {
          console.log('💾 Memory Usage:', {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
          });
        }
        
        console.groupEnd();
      }
    },
    
    recordScrollEvent() {
      this.metrics.scrollEvents++;
    }
  };

  // ===== ADAPTIVE CONFIGURATION =====
  const AdaptiveConfig = {
    getOptimalConfig() {
      const device = DeviceDetector;
      const performanceLevel = device.getPerformanceLevel();
      
      let config = {
        // Base configuration
        scrollThreshold: 10,
        scrollHysteresis: 5,
        compactThreshold: 200,
        throttleDelay: 16,
        transitionDuration: 400,
        enableHardwareAcceleration: true,
        enableLogoTransition: true,
        enableScaleEffect: true
      };
      
      // Device-specific optimizations
      if (device.isMobile()) {
        config = {
          ...config,
          throttleDelay: 20, // Slightly slower for mobile
          transitionDuration: 300, // Faster transitions
          enableScaleEffect: performanceLevel === 'high'
        };
      } else if (device.isTablet()) {
        config = {
          ...config,
          throttleDelay: 18,
          transitionDuration: 350
        };
      }
      
      // Performance-based optimizations
      if (performanceLevel === 'low') {
        config = {
          ...config,
          throttleDelay: 32, // 30fps
          transitionDuration: 200,
          enableHardwareAcceleration: device.supportsHardwareAcceleration(),
          enableScaleEffect: false
        };
      } else if (performanceLevel === 'high') {
        config = {
          ...config,
          throttleDelay: 8, // 120fps
          transitionDuration: 500,
          enableScaleEffect: true
        };
      }
      
      return config;
    }
  };

  // ===== TESTING UTILITIES =====
  const TestingUtils = {
    init() {
      if (!INTEGRATION_CONFIG.ENABLE_TESTING_MODE) return;
      
      this.setupTestingInterface();
      this.runInitialTests();
    },
    
    setupTestingInterface() {
      // Create testing panel
      const testPanel = document.createElement('div');
      testPanel.id = 'header-test-panel';
      testPanel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        max-width: 300px;
      `;
      
      testPanel.innerHTML = `
        <h4>🧪 Header Testing Panel</h4>
        <button onclick="window.HeaderTesting.testScrollPerformance()">Test Scroll Performance</button>
        <button onclick="window.HeaderTesting.testTransitions()">Test Transitions</button>
        <button onclick="window.HeaderTesting.testResponsiveness()">Test Responsiveness</button>
        <div id="test-results"></div>
      `;
      
      document.body.appendChild(testPanel);
      
      // Expose testing methods globally
      window.HeaderTesting = this;
    },
    
    runInitialTests() {
      console.group('🧪 Premium Header Initial Tests');
      
      // Test element availability
      const elements = {
        header: document.querySelector('.premium-header'),
        topBar: document.querySelector('.top-bar'),
        logoImage: document.querySelector('.premium-logo-image img')
      };
      
      Object.entries(elements).forEach(([name, element]) => {
        console.log(`${element ? '✅' : '❌'} ${name}:`, element ? 'Found' : 'Not found');
      });
      
      // Test CSS support
      const cssSupport = {
        transforms: DeviceDetector.supportsHardwareAcceleration(),
        backdropFilter: DeviceDetector.supportsBackdropFilter(),
        customProperties: CSS.supports('color', 'var(--test)')
      };
      
      Object.entries(cssSupport).forEach(([feature, supported]) => {
        console.log(`${supported ? '✅' : '❌'} ${feature}:`, supported ? 'Supported' : 'Not supported');
      });
      
      console.groupEnd();
    },
    
    testScrollPerformance() {
      console.log('🚀 Testing scroll performance...');
      
      const startTime = performance.now();
      let frameCount = 0;
      
      const testScroll = () => {
        window.scrollTo(0, Math.sin(frameCount * 0.1) * 500 + 500);
        frameCount++;
        
        if (frameCount < 120) { // Test for 2 seconds at 60fps
          requestAnimationFrame(testScroll);
        } else {
          const endTime = performance.now();
          const duration = endTime - startTime;
          const fps = (frameCount / duration) * 1000;
          
          console.log(`📊 Scroll test completed: ${fps.toFixed(2)} fps`);
          window.scrollTo(0, 0);
        }
      };
      
      requestAnimationFrame(testScroll);
    },
    
    testTransitions() {
      console.log('🎬 Testing transitions...');
      
      const header = document.querySelector('.premium-header');
      if (!header) return;
      
      // Test scrolled state transition
      header.classList.add('scrolled');
      
      setTimeout(() => {
        header.classList.remove('scrolled');
        console.log('✅ Transition test completed');
      }, 1000);
    },
    
    testResponsiveness() {
      console.log('📱 Testing responsiveness...');
      
      const viewports = [
        { width: 375, height: 667, name: 'Mobile' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 1920, height: 1080, name: 'Desktop' }
      ];
      
      viewports.forEach(viewport => {
        // Simulate viewport change
        const mediaQuery = `(max-width: ${viewport.width}px)`;
        const matches = window.matchMedia(mediaQuery).matches;
        console.log(`${matches ? '✅' : '❌'} ${viewport.name} (${viewport.width}x${viewport.height})`);
      });
    }
  };

  // ===== FALLBACK SYSTEM =====
  const FallbackSystem = {
    init() {
      if (!INTEGRATION_CONFIG.ENABLE_FALLBACK_MODE) return;
      
      this.setupFallbacks();
      this.monitorErrors();
    },
    
    setupFallbacks() {
      // Fallback for missing elements
      if (!document.querySelector('.premium-header')) {
        console.warn('⚠️ Premium header not found, creating fallback');
        this.createFallbackHeader();
      }
      
      // Fallback for unsupported features
      if (!DeviceDetector.supportsHardwareAcceleration()) {
        console.warn('⚠️ Hardware acceleration not supported, using fallback animations');
        this.disableHardwareAcceleration();
      }
    },
    
    createFallbackHeader() {
      // Create minimal fallback header
      const fallbackHeader = document.createElement('div');
      fallbackHeader.className = 'premium-header fallback-header';
      fallbackHeader.style.cssText = `
        position: sticky;
        top: 0;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        padding: 10px;
        text-align: center;
      `;
      fallbackHeader.innerHTML = '<h1>Nội Thất Bàng Vũ</h1>';
      
      document.body.insertBefore(fallbackHeader, document.body.firstChild);
    },
    
    disableHardwareAcceleration() {
      const style = document.createElement('style');
      style.textContent = `
        .premium-header * {
          transform: none !important;
          will-change: auto !important;
        }
      `;
      document.head.appendChild(style);
    },
    
    monitorErrors() {
      window.addEventListener('error', (event) => {
        if (event.filename && event.filename.includes('premium-header')) {
          console.error('🚨 Premium header error detected:', event.error);
          this.handleError(event.error);
        }
      });
    },
    
    handleError(error) {
      console.warn('🔧 Attempting to recover from error...');
      
      // Try to reinitialize with safe defaults
      setTimeout(() => {
        this.initSafeMode();
      }, 100);
    },
    
    initSafeMode() {
      console.log('🛡️ Initializing safe mode...');
      
      // Remove problematic classes
      const header = document.querySelector('.premium-header');
      if (header) {
        header.classList.remove('smooth-transition', 'scrolled', 'compact');
        
        // Apply safe styles
        header.style.transition = 'none';
        header.style.transform = 'none';
      }
    }
  };

  // ===== MAIN INITIALIZATION =====
  function initPremiumHeaderOptimizations() {
    console.log('🚀 Initializing Premium Header Optimizations...');
    
    try {
      // Initialize monitoring
      PerformanceMonitor.init();
      
      // Initialize testing utilities
      TestingUtils.init();
      
      // Initialize fallback system
      FallbackSystem.init();
      
      // Get optimal configuration
      const config = AdaptiveConfig.getOptimalConfig();
      console.log('⚙️ Using configuration:', config);
      
      // Initialize scroll optimizer with adaptive config
      if (window.ScrollOptimizer) {
        window.headerScrollOptimizer = new ScrollOptimizer({
          ...config,
          onScrollStart: () => PerformanceMonitor.recordScrollEvent(),
          onStateChange: (state, value) => {
            if (INTEGRATION_CONFIG.ENABLE_TESTING_MODE) {
              console.log(`🔄 State change: ${state} = ${value}`);
            }
          }
        });
      }
      
      console.log('✅ Premium Header Optimizations initialized successfully');
      
    } catch (error) {
      console.error('❌ Failed to initialize Premium Header Optimizations:', error);
      FallbackSystem.handleError(error);
    }
  }

  // Start initialization
  initPremiumHeaderOptimizations();
});
