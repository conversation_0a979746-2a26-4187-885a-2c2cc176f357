/**
 * Cart Realtime JavaScript
 * Xử lý các tương tác với giỏ hàng không cần tải lại trang
 */

// <PERSON><PERSON> báo các biến toàn cục để tránh xử lý trùng lặp
window.isAddingToCart = false;
window.isRemovingFromCart = false;
window.cartLastUpdated = localStorage.getItem('cartLastUpdated') || Date.now();

// Debounce function để tránh cập nhật badge quá nhiều lần
window.debounceUpdateBadge = null;

// Khởi tạo giá trị ban đầu cho cartCount trong localStorage
document.addEventListener('DOMContentLoaded', function () {
  console.log('Cart realtime: Initializing...');

  // Gọi API để lấy số lượng giỏ hàng hiện tại
  fetch(`${BASE_URL}/ajax/get_cart_count.php`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // <PERSON><PERSON><PERSON> số lượng giỏ hàng vào localStorage
        localStorage.setItem('cartCount', data.count.toString());
        console.log('Cart count initialized:', data.count);

        // Cập nhật UI
        updateCartCount();
      }
    })
    .catch((error) => console.error('Error initializing cart count:', error));

  // Đảm bảo rằng badge giỏ hàng được hiển thị đúng cách
  setTimeout(() => {
    updateCartCount();
  }, 500);
});

// Khởi tạo giỏ hàng khi trang được tải
document.addEventListener('DOMContentLoaded', function () {
  // Khởi tạo cart badges từ localStorage ngay lập tức
  setTimeout(() => {
    initializeCartBadgesFromStorage();
  }, 100);

  // Kiểm tra xem giỏ hàng có cần cập nhật không
  checkCartUpdates();

  // Thiết lập interval để kiểm tra cập nhật giỏ hàng mỗi 5 giây
  setInterval(checkCartUpdates, 5000);

  // Lắng nghe thay đổi localStorage để đồng bộ giữa các tab
  window.addEventListener('storage', function(e) {
    if (e.key === 'cartCount') {
      const newCount = parseInt(e.newValue || '0');
      console.log('localStorage cartCount changed, updating badges:', newCount);
      updateAllCartBadgesImmediate(newCount);
    }
  });

  // Đảm bảo chỉ đăng ký event listener một lần
  if (window.cartRealtimeInitialized) {
    return;
  }
  window.cartRealtimeInitialized = true;

  /**
   * Thêm sản phẩm vào giỏ hàng
   */
  document.querySelectorAll('.add-to-cart-btn, .quick-add-to-cart-btn').forEach((button) => {
    // Kiểm tra xem nút đã được đăng ký sự kiện chưa để tránh trùng lặp
    if (button.dataset.cartRealtimeRegistered) {
      return;
    }

    button.addEventListener('click', function (e) {
      e.preventDefault();

      // Kiểm tra xem đang trong quá trình thêm vào giỏ hàng không
      if (window.isAddingToCart) {
        console.log('Already adding to cart, ignoring click');
        return;
      }

      // Đánh dấu đang trong quá trình thêm vào giỏ hàng
      window.isAddingToCart = true;

      // Lấy product ID từ data-product-id hoặc dataset.productId
      const productId = this.getAttribute('data-product-id') || this.dataset.productId;

      // Đối với nút quick-add, luôn dùng quantity = 1
      // Đối với nút add-to-cart trong trang sản phẩm, lấy từ input quantity
      let quantity = 1;
      if (this.classList.contains('add-to-cart-btn')) {
        const quantityInput = document.getElementById('quantity');
        quantity = quantityInput ? parseInt(quantityInput.value) : 1;
      }

      // Thêm hiệu ứng visual ngay lập tức
      this.classList.add('clicked', 'button-clicked');

      // Hiển thị hiệu ứng loading trên nút
      this.classList.add('loading');
      const originalText = this.innerHTML;

      // Hiệu ứng loading khác nhau cho từng loại nút
      if (this.classList.contains('quick-add-to-cart-btn')) {
        this.classList.add('adding');
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      } else {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
      }
      this.disabled = true;

      // Gọi flyToCart để tạo hiệu ứng visual
      if (typeof window.flyToCart === 'function') {
        window.flyToCart(this);
      }

      // Lưu số lượng giỏ hàng hiện tại
      const currentCount = parseInt(localStorage.getItem('cartCount') || '0');

      fetch(`${BASE_URL}/ajax/add_to_cart.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}&quantity=${quantity}`,
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // Hiển thị thông báo thành công
            showNotification(data.message, 'success');

            // Lấy số lượng giỏ hàng mới từ server
            const newCount = parseInt(data.count || currentCount + quantity);

            console.log('Added to cart:', {
              productId,
              quantity,
              oldCount: currentCount,
              newCount,
            });

            // Cập nhật cart badge ngay lập tức với dữ liệu từ server
            // Lưu số lượng giỏ hàng mới vào localStorage
            localStorage.setItem('cartCount', newCount.toString());

            // Cập nhật thời gian cập nhật cuối cùng
            if (data.timestamp) {
              localStorage.setItem('cartLastUpdated', data.timestamp);
            } else {
              localStorage.setItem('cartLastUpdated', Date.now().toString());
            }

            // Cập nhật tất cả cart badges ngay lập tức với debouncing
            console.log('cart-realtime.js: Updating badges immediately with count:', newCount);

            // Clear previous debounce timer
            if (window.debounceUpdateBadge) {
              clearTimeout(window.debounceUpdateBadge);
            }

            // Set new debounce timer
            window.debounceUpdateBadge = setTimeout(() => {
              updateAllCartBadgesImmediate(newCount);
            }, 50); // 50ms debounce

            // Hiệu ứng thêm vào giỏ hàng
            animateAddToCart(this);

            // Gọi hàm handleAddToCartSuccess từ cart-sync.js nếu có (nhưng không để nó override badge count)
            if (typeof window.handleAddToCartSuccess === 'function') {
              // Kiểm tra để tránh gọi trùng lặp trong thời gian ngắn
              const now = Date.now();
              const lastCall = window.lastHandleAddToCartSuccessCall || 0;

              if (now - lastCall > 500) { // Chỉ gọi nếu đã qua 500ms từ lần gọi cuối
                window.lastHandleAddToCartSuccessCall = now;
                // Gọi hàm xử lý thành công từ cart-sync.js với dữ liệu đầy đủ
                window.handleAddToCartSuccess(newCount, data);
                console.log('cart-realtime.js: Called handleAddToCartSuccess with count:', newCount);
              } else {
                console.log('cart-realtime.js: Skipping handleAddToCartSuccess to avoid duplication');
              }
            }

            // Khôi phục nút với hiệu ứng thành công
            this.classList.remove('loading', 'clicked', 'button-clicked', 'adding');
            this.disabled = false;

            if (this.classList.contains('quick-add-to-cart-btn')) {
              // Hiệu ứng thành công cho quick-add button
              this.innerHTML = '<i class="fas fa-check"></i>';
              setTimeout(() => {
                this.innerHTML = originalText;
              }, 1000);
            } else {
              // Khôi phục nút add-to-cart thông thường
              this.innerHTML = originalText;
            }
          } else {
            // Hiển thị thông báo lỗi
            showNotification(data.message, 'error');

            // Khôi phục nút khi có lỗi
            this.classList.remove('loading', 'clicked', 'button-clicked', 'adding');
            this.innerHTML = originalText;
            this.disabled = false;
          }

          // Đánh dấu đã hoàn thành quá trình thêm vào giỏ hàng
          setTimeout(() => {
            window.isAddingToCart = false;
          }, 1000);
        })
        .catch((error) => {
          // Khôi phục nút
          this.classList.remove('loading', 'clicked', 'button-clicked', 'adding');
          this.innerHTML = originalText;
          this.disabled = false;

          // Hiển thị thông báo lỗi
          showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');
          console.error('Error adding to cart:', error);

          // Đánh dấu đã hoàn thành quá trình thêm vào giỏ hàng
          setTimeout(() => {
            window.isAddingToCart = false;
          }, 1000);
        });
    });

    // Đánh dấu nút đã được đăng ký sự kiện
    button.dataset.cartRealtimeRegistered = 'true';
  });

  /**
   * Cập nhật số lượng sản phẩm trong giỏ hàng
   */
  if (document.querySelector('.update-cart-btn')) {
    document.querySelectorAll('.update-cart-btn').forEach((button) => {
      button.addEventListener('click', function (e) {
        e.preventDefault();

        console.log('Update cart button clicked');

        const productId = this.dataset.productId;
        const quantityInput =
          this.closest('td').querySelector('.quantity-input');
        const quantity = parseInt(quantityInput.value);

        console.log('Updating product:', {
          productId,
          quantity,
          input: quantityInput
        });

        // Hiển thị hiệu ứng loading
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        this.disabled = true;

        fetch(`${BASE_URL}/ajax/update_cart.php`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: `product_id=${productId}&quantity=${quantity}`,
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // Cập nhật UI mà không cần tải lại trang
              updateCartUI(data);

              // Cập nhật giá trị hiển thị cho sản phẩm
              const itemTotal = this.closest('tr').querySelector('td:nth-child(4) .text-sm');
              if (itemTotal && data.total) {
                // Tính toán tổng tiền cho sản phẩm này
                const itemPrice = parseFloat(this.closest('tr').querySelector('td:nth-child(2) .text-sm').textContent.replace(/[^\d]/g, ''));
                const itemTotalValue = itemPrice * quantity;
                itemTotal.textContent = formatCurrency(itemTotalValue);
              }

              // Hiển thị thông báo thành công
              showNotification(data.message, 'success');
            } else {
              // Hiển thị thông báo lỗi
              showNotification(data.message, 'error');

              // Khôi phục nút
              this.innerHTML = 'Cập nhật';
              this.disabled = false;
            }
          })
          .catch((error) => {
            // Hiển thị thông báo lỗi
            showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');

            // Khôi phục nút
            this.innerHTML = 'Cập nhật';
            this.disabled = false;

            console.error('Error:', error);
          });
      });
    });
  }

  /**
   * Format số tiền
   */
  function formatCurrency(amount) {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + 'đ';
  }

  /**
   * Xóa sản phẩm khỏi giỏ hàng
   */
  if (document.querySelector('.remove-cart-btn')) {
    document.querySelectorAll('.remove-cart-btn').forEach((button) => {
      button.addEventListener('click', function (e) {
        e.preventDefault();

        // Kiểm tra xem đang trong quá trình xóa sản phẩm không
        if (window.isRemovingFromCart) {
          return;
        }

        // Đánh dấu đang trong quá trình xóa sản phẩm
        window.isRemovingFromCart = true;

        if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
          const productId = this.dataset.productId;
          const row = this.closest('tr');

          // Hiển thị hiệu ứng loading
          row.style.opacity = '0.5';
          this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
          this.disabled = true;

          fetch(`${BASE_URL}/ajax/remove_from_cart.php`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `product_id=${productId}`,
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                // Xóa hàng khỏi bảng với hiệu ứng
                row.style.height = row.offsetHeight + 'px';
                row.style.overflow = 'hidden';

                setTimeout(() => {
                  row.style.height = '0';
                  row.style.padding = '0';
                  row.style.margin = '0';

                  setTimeout(() => {
                    // Xóa hàng khỏi DOM
                    row.remove();

                    // Cập nhật UI giỏ hàng
                    updateCartUI(data);

                    // Hiển thị thông báo thành công
                    showNotification(data.message, 'success');

                    // Kiểm tra nếu giỏ hàng trống, hiển thị thông báo
                    if (data.count === 0) {
                      showEmptyCart();
                    }

                    // Đặt lại biến isRemovingFromCart
                    setTimeout(() => {
                      window.isRemovingFromCart = false;
                    }, 500);
                  }, 300);
                }, 10);
              } else {
                // Hiển thị thông báo lỗi
                showNotification(data.message, 'error');

                // Khôi phục hàng
                row.style.opacity = '1';
                this.innerHTML = '<i class="fas fa-trash-alt"></i>';
                this.disabled = false;

                // Đặt lại biến isRemovingFromCart
                window.isRemovingFromCart = false;
              }
            })
            .catch((error) => {
              // Hiển thị thông báo lỗi
              showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');

              // Khôi phục hàng
              row.style.opacity = '1';
              this.innerHTML = '<i class="fas fa-trash-alt"></i>';
              this.disabled = false;

              // Đặt lại biến isRemovingFromCart
              window.isRemovingFromCart = false;

              console.error('Error:', error);
            });
        } else {
          // Nếu người dùng hủy xác nhận, đặt lại biến isRemovingFromCart
          window.isRemovingFromCart = false;
        }
      });
    });
  }

  /**
   * Điều khiển số lượng sản phẩm
   */
  if (document.querySelector('.quantity-control')) {
    document.querySelectorAll('.quantity-control').forEach((button) => {
      button.addEventListener('click', function () {
        console.log('Quantity control button clicked:', this.classList.contains('quantity-decrease') ? 'decrease' : 'increase');

        const input = this.parentElement.querySelector('.quantity-input');
        const currentValue = parseInt(input.value);
        const productId = input.dataset.productId;
        let newValue = currentValue;

        if (this.classList.contains('quantity-decrease')) {
          if (currentValue > 1) {
            newValue = currentValue - 1;
            input.value = newValue;
          }
        } else {
          newValue = currentValue + 1;
          input.value = newValue;
        }

        console.log('Quantity changed:', {
          productId,
          oldValue: currentValue,
          newValue: newValue
        });

        // Tự động cập nhật giỏ hàng khi thay đổi số lượng
        if (newValue !== currentValue) {
          // Tìm nút cập nhật - thử nhiều cách khác nhau để đảm bảo tìm được nút
          let updateButton = null;

          // Cách 1: Tìm trong cùng ô td
          updateButton = this.closest('td').querySelector('.update-cart-btn');

          // Cách 2: Tìm trong cùng hàng tr
          if (!updateButton) {
            updateButton = this.closest('tr').querySelector('.update-cart-btn');
          }

          // Cách 3: Tìm theo data-product-id
          if (!updateButton) {
            const allUpdateButtons = document.querySelectorAll('.update-cart-btn');
            for (const btn of allUpdateButtons) {
              if (btn.dataset.productId === productId) {
                updateButton = btn;
                break;
              }
            }
          }

          // Nếu không tìm thấy nút cập nhật, tự gửi yêu cầu cập nhật
          if (!updateButton) {
            console.log('Update button not found, sending update request directly');

            // Gửi yêu cầu cập nhật trực tiếp
            fetch(`${BASE_URL}/ajax/update_cart.php`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: `product_id=${productId}&quantity=${newValue}`,
            })
              .then((response) => response.json())
              .then((data) => {
                if (data.success) {
                  // Cập nhật UI mà không cần tải lại trang
                  updateCartUI(data);

                  // Cập nhật giá trị hiển thị cho sản phẩm
                  const row = input.closest('tr');
                  const itemTotal = row.querySelector('td:nth-child(4) .text-sm');
                  if (itemTotal) {
                    // Tính toán tổng tiền cho sản phẩm này
                    const itemPrice = parseFloat(row.querySelector('td:nth-child(2) .text-sm').textContent.replace(/[^\d]/g, ''));
                    const itemTotalValue = itemPrice * newValue;
                    itemTotal.textContent = formatCurrency(itemTotalValue);
                  }

                  // Hiển thị thông báo thành công
                  showNotification(data.message, 'success');
                } else {
                  // Hiển thị thông báo lỗi
                  showNotification(data.message, 'error');

                  // Khôi phục giá trị cũ
                  input.value = currentValue;
                }
              })
              .catch((error) => {
                // Hiển thị thông báo lỗi
                showNotification(
                  'Có lỗi xảy ra. Vui lòng thử lại sau.',
                  'error'
                );

                // Khôi phục giá trị cũ
                input.value = currentValue;

                console.error('Error:', error);
              });
          } else {
            console.log('Found update button:', updateButton);

            // Hiển thị hiệu ứng loading
            updateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            updateButton.disabled = true;

            // Gửi yêu cầu cập nhật
            fetch(`${BASE_URL}/ajax/update_cart.php`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: `product_id=${productId}&quantity=${newValue}`,
            })
              .then((response) => response.json())
              .then((data) => {
                if (data.success) {
                  // Cập nhật UI mà không cần tải lại trang
                  updateCartUI(data);

                  // Cập nhật giá trị hiển thị cho sản phẩm
                  const row = updateButton.closest('tr');
                  const itemTotal = row.querySelector('td:nth-child(4) .text-sm');
                  if (itemTotal) {
                    // Tính toán tổng tiền cho sản phẩm này
                    const itemPrice = parseFloat(row.querySelector('td:nth-child(2) .text-sm').textContent.replace(/[^\d]/g, ''));
                    const itemTotalValue = itemPrice * newValue;
                    itemTotal.textContent = formatCurrency(itemTotalValue);
                  }

                  // Hiển thị thông báo thành công
                  showNotification(data.message, 'success');
                } else {
                  // Hiển thị thông báo lỗi
                  showNotification(data.message, 'error');

                  // Khôi phục giá trị cũ
                  input.value = currentValue;

                  // Khôi phục nút
                  updateButton.innerHTML = 'Cập nhật';
                  updateButton.disabled = false;
                }
              })
              .catch((error) => {
                // Hiển thị thông báo lỗi
                showNotification(
                  'Có lỗi xảy ra. Vui lòng thử lại sau.',
                  'error'
                );

                // Khôi phục giá trị cũ
                input.value = currentValue;

                // Khôi phục nút
                updateButton.innerHTML = 'Cập nhật';
                updateButton.disabled = false;

                console.error('Error:', error);
              });
          }
        }
      });
    });

    // Thêm xử lý sự kiện cho input số lượng
    document.querySelectorAll('.quantity-input').forEach((input) => {
      // Lưu giá trị ban đầu khi focus vào input
      input.addEventListener('focus', function() {
        this.dataset.originalValue = this.value;
      });

      // Xử lý khi người dùng nhấn Enter
      input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();

          // Tìm nút cập nhật - thử nhiều cách khác nhau để đảm bảo tìm được nút
          let updateButton = null;
          const productId = this.dataset.productId;

          // Cách 1: Tìm trong cùng ô td
          updateButton = this.closest('td').querySelector('.update-cart-btn');

          // Cách 2: Tìm trong cùng hàng tr
          if (!updateButton) {
            updateButton = this.closest('tr').querySelector('.update-cart-btn');
          }

          // Cách 3: Tìm theo data-product-id
          if (!updateButton) {
            const allUpdateButtons = document.querySelectorAll('.update-cart-btn');
            for (const btn of allUpdateButtons) {
              if (btn.dataset.productId === productId) {
                updateButton = btn;
                break;
              }
            }
          }

          if (updateButton) {
            updateButton.click();
          } else {
            // Nếu không tìm thấy nút cập nhật, tự gửi yêu cầu cập nhật
            const newValue = parseInt(this.value);
            const currentValue = parseInt(this.dataset.originalValue || 0);

            if (!isNaN(newValue) && newValue > 0 && newValue !== currentValue) {
              // Gửi yêu cầu cập nhật trực tiếp
              fetch(`${BASE_URL}/ajax/update_cart.php`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}&quantity=${newValue}`,
              })
                .then((response) => response.json())
                .then((data) => {
                  if (data.success) {
                    // Cập nhật UI mà không cần tải lại trang
                    updateCartUI(data);

                    // Cập nhật giá trị hiển thị cho sản phẩm
                    const row = this.closest('tr');
                    const itemTotal = row.querySelector('td:nth-child(4) .text-sm');
                    if (itemTotal) {
                      // Tính toán tổng tiền cho sản phẩm này
                      const itemPrice = parseFloat(row.querySelector('td:nth-child(2) .text-sm').textContent.replace(/[^\d]/g, ''));
                      const itemTotalValue = itemPrice * newValue;
                      itemTotal.textContent = formatCurrency(itemTotalValue);
                    }

                    // Hiển thị thông báo thành công
                    showNotification(data.message, 'success');
                  } else {
                    // Hiển thị thông báo lỗi
                    showNotification(data.message, 'error');

                    // Khôi phục giá trị cũ
                    this.value = currentValue || 1;
                  }
                })
                .catch((error) => {
                  // Hiển thị thông báo lỗi
                  showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');

                  // Khôi phục giá trị cũ
                  this.value = currentValue || 1;

                  console.error('Error:', error);
                });
            }
          }
        }
      });

      // Xử lý khi input mất focus
      input.addEventListener('blur', function() {
        const newValue = parseInt(this.value);
        const originalValue = parseInt(this.dataset.originalValue || 0);

        // Đảm bảo giá trị hợp lệ
        if (isNaN(newValue) || newValue < 1) {
          this.value = originalValue || 1;
        }
      });
    });
  }

  /**
   * Khởi tạo cart badges từ localStorage khi trang load
   */
  function initializeCartBadgesFromStorage() {
    const cartCount = parseInt(localStorage.getItem('cartCount') || '0');
    console.log('Initializing cart badges from localStorage with count:', cartCount);

    if (cartCount > 0) {
      updateAllCartBadgesImmediate(cartCount);
    }
  }

  /**
   * Cập nhật tất cả cart badges ngay lập tức (không cần gọi API)
   */
  function updateAllCartBadgesImmediate(count) {
    // Kiểm tra để tránh cập nhật trùng lặp trong thời gian ngắn
    const now = Date.now();
    const lastUpdate = window.lastBadgeUpdateTime || 0;

    if (now - lastUpdate < 100) { // Nếu đã cập nhật trong vòng 100ms, bỏ qua
      console.log('updateAllCartBadgesImmediate: Skipping update to avoid duplication');
      return;
    }

    console.log('updateAllCartBadgesImmediate: Updating all cart badges with count:', count);

    // Đánh dấu thời gian cập nhật để tránh trùng lặp
    window.lastBadgeUpdateTime = now;

    // Tìm tất cả các badge giỏ hàng trên trang
    let cartBadges = document.querySelectorAll('.cart-badge');
    let mobileBadge = document.querySelector('.mobile-badge');
    let mobileNavBadge = document.querySelector('.mobile-nav-badge');
    let mobileCartBadge = document.querySelector('.mobile-cart-badge');

    if (count > 0) {
      // Cập nhật tất cả cart badges desktop
      cartBadges.forEach((badge) => {
        badge.textContent = count;
        badge.style.display = 'flex';
        badge.style.visibility = 'visible';
      });

      // Nếu không có cart badge desktop, tạo mới
      if (cartBadges.length === 0) {
        const cartContainer = document.querySelector('.cart-container');
        const cartBtn = document.querySelector('.cart-btn');
        if (cartContainer && !cartContainer.querySelector('.cart-badge')) {
          const badge = document.createElement('span');
          badge.className = 'cart-badge';
          badge.textContent = count;
          badge.style.display = 'flex';
          badge.style.visibility = 'visible';
          badge.setAttribute('aria-hidden', 'true');
          cartContainer.appendChild(badge);
          console.log('Created new desktop cart badge');
        }
      }

      // Cập nhật mobile badges
      if (mobileBadge) {
        mobileBadge.textContent = count;
        mobileBadge.style.display = 'flex';
        mobileBadge.style.visibility = 'visible';
      }

      if (mobileNavBadge) {
        mobileNavBadge.textContent = count > 99 ? '99+' : count;
        mobileNavBadge.style.display = 'flex';
        mobileNavBadge.style.visibility = 'visible';
        mobileNavBadge.setAttribute('data-count', count > 99 ? '99+' : count);
      } else {
        // Nếu không có mobile nav badge, tạo mới
        const mobileNavItem = document.querySelector('.mobile-nav-item[href*="/cart.php"]');
        if (mobileNavItem && !mobileNavItem.querySelector('.mobile-nav-badge')) {
          const badge = document.createElement('span');
          badge.className = 'mobile-nav-badge mobile-cart-badge';
          badge.textContent = count > 99 ? '99+' : count;
          badge.style.display = 'flex';
          badge.style.visibility = 'visible';
          badge.setAttribute('data-count', count > 99 ? '99+' : count);
          badge.setAttribute('aria-hidden', 'true');
          mobileNavItem.appendChild(badge);
          console.log('Created new mobile nav badge');
        }
      }

      if (mobileCartBadge && mobileCartBadge !== mobileNavBadge) {
        mobileCartBadge.textContent = count > 99 ? '99+' : count;
        mobileCartBadge.style.display = 'flex';
        mobileCartBadge.style.visibility = 'visible';
        mobileCartBadge.setAttribute('data-count', count > 99 ? '99+' : count);
      }
    } else {
      // Ẩn badge nếu giỏ hàng trống
      cartBadges.forEach((badge) => {
        badge.style.display = 'none';
        badge.style.visibility = 'hidden';
      });
      if (mobileBadge) {
        mobileBadge.style.display = 'none';
        mobileBadge.style.visibility = 'hidden';
      }
      if (mobileNavBadge) {
        mobileNavBadge.style.display = 'none';
        mobileNavBadge.style.visibility = 'hidden';
      }
      if (mobileCartBadge) {
        mobileCartBadge.style.display = 'none';
        mobileCartBadge.style.visibility = 'hidden';
      }
    }

    console.log('Cart badges updated immediately:', {
      count,
      cartBadges: document.querySelectorAll('.cart-badge').length,
      hasMobileBadge: !!document.querySelector('.mobile-badge'),
      hasMobileNavBadge: !!document.querySelector('.mobile-nav-badge'),
      hasMobileCartBadge: !!document.querySelector('.mobile-cart-badge')
    });
  }

  /**
   * Cập nhật số lượng giỏ hàng
   */
  function updateCartCount() {
    fetch(`${BASE_URL}/ajax/get_cart_count.php`)
      .then((response) => response.json())
      .then((data) => {
        // Tìm tất cả các badge giỏ hàng trên trang
        const cartBadges = document.querySelectorAll('.cart-badge');
        const mobileBadge = document.querySelector('.mobile-badge');
        const mobileNavBadge = document.querySelector('.mobile-nav-badge');

        // Ghi log để debug
        console.log('Updating cart count:', {
          count: data.count,
          cartBadges: cartBadges.length,
          hasMobileBadge: !!mobileBadge,
          hasMobileNavBadge: !!mobileNavBadge,
        });

        // Lưu số lượng giỏ hàng vào localStorage
        localStorage.setItem('cartCount', data.count.toString());

        if (data.count > 0) {
          // Cập nhật tất cả các badge trên desktop
          cartBadges.forEach((badge) => {
            badge.textContent = data.count;
            badge.style.display = 'flex';

            // Thêm hiệu ứng nhấp nháy
            badge.classList.add('badge-pulse');
            setTimeout(() => {
              badge.classList.remove('badge-pulse');
            }, 1000);
          });

          // Cập nhật badge trên mobile menu
          if (mobileBadge) {
            mobileBadge.textContent = data.count;
            mobileBadge.style.display = 'inline-block';
          }

          // Cập nhật badge trên mobile bottom navigation
          if (mobileNavBadge) {
            mobileNavBadge.textContent = data.count > 99 ? '99+' : data.count;
            mobileNavBadge.setAttribute(
              'data-count',
              data.count > 99 ? '99+' : data.count
            );
            mobileNavBadge.style.display = 'flex';
          } else {
            // Nếu badge chưa tồn tại, tạo mới
            const mobileNavItem = document.querySelector(
              '.mobile-nav-item[href*="/cart.php"]'
            );
            if (
              mobileNavItem &&
              !mobileNavItem.querySelector('.mobile-nav-badge')
            ) {
              const badge = document.createElement('span');
              badge.className = 'mobile-nav-badge';
              badge.setAttribute(
                'data-count',
                data.count > 99 ? '99+' : data.count
              );
              badge.textContent = data.count > 99 ? '99+' : data.count;
              mobileNavItem.appendChild(badge);
            }
          }

          // Kiểm tra nếu không có badge nào được tìm thấy, tạo mới
          if (cartBadges.length === 0) {
            console.log('No cart badge found, creating new one');
            const cartContainer = document.querySelector('.cart-container');
            if (cartContainer && !cartContainer.querySelector('.cart-badge')) {
              const badge = document.createElement('span');
              badge.className = 'cart-badge';
              badge.textContent = data.count;
              badge.style.display = 'flex';

              // Thêm badge vào cart-container (KHÔNG phải cart-btn)
              // để đảm bảo vị trí giống như khi tải lại trang
              cartContainer.appendChild(badge);

              console.log('Created new cart badge in cart-container');

              // Thêm hiệu ứng nhấp nháy
              badge.classList.add('badge-pulse');
              setTimeout(() => {
                badge.classList.remove('badge-pulse');
              }, 1000);
            }
          }
        } else {
          // Ẩn badge nếu giỏ hàng trống
          cartBadges.forEach((badge) => {
            badge.style.display = 'none';
          });
          if (mobileBadge) mobileBadge.style.display = 'none';
          if (mobileNavBadge) mobileNavBadge.style.display = 'none';
        }

        // Cập nhật thời gian cập nhật cuối cùng
        if (data.timestamp) {
          localStorage.setItem('cartLastUpdated', data.timestamp);
        } else {
          localStorage.setItem('cartLastUpdated', Date.now().toString());
        }

        // Ghi log để debug
        console.log('Cart count updated successfully:', data.count);

        // Đồng bộ hóa với cart-sync.js nếu có
        if (typeof window.syncCartBadges === 'function') {
          // Gọi hàm đồng bộ hóa từ cart-sync.js
          setTimeout(() => {
            window.syncCartBadges();
          }, 100);
        }
      })
      .catch((error) => console.error('Error updating cart count:', error));
  }

  // Đảm bảo hàm updateCartCount có thể được gọi từ các file JavaScript khác
  window.updateCartCount = updateCartCount;

  /**
   * Cập nhật UI giỏ hàng
   */
  function updateCartUI(data) {
    console.log('Updating cart UI with data:', data);

    // Phát sự kiện để ngăn cart-item-selection can thiệp
    window.dispatchEvent(new CustomEvent('cart-updating-from-server'));

    // Cập nhật tổng tiền ở tất cả các vị trí
    const totalElements = document.querySelectorAll('.cart-total, .cart-subtotal, .text-blue-600.font-bold');
    if (totalElements.length > 0 && data.total) {
      totalElements.forEach(element => {
        element.textContent = data.total;
      });
      console.log('Updated total price elements:', totalElements.length);
    }

    // Cập nhật số lượng giỏ hàng
    updateCartCount();

    // Cập nhật nút cập nhật
    const updateButtons = document.querySelectorAll('.update-cart-btn');
    updateButtons.forEach((button) => {
      button.innerHTML = 'Cập nhật';
      button.disabled = false;
    });

    // Cập nhật localStorage
    if (data.count !== undefined) {
      localStorage.setItem('cartCount', data.count.toString());
    }

    if (data.timestamp) {
      localStorage.setItem('cartLastUpdated', data.timestamp);
    }

    // Phát sự kiện hoàn thành cập nhật
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('cart-update-completed'));
    }, 100);
  }

  /**
   * Hiển thị giỏ hàng trống
   */
  function showEmptyCart() {
    const cartTable = document.querySelector('.cart-table');
    const cartSummary = document.querySelector('.cart-summary');

    // Đảm bảo localStorage được cập nhật khi giỏ hàng trống
    localStorage.setItem('cartCount', '0');

    // Cập nhật thời gian cập nhật cuối cùng
    localStorage.setItem('cartLastUpdated', Date.now().toString());

    // Cập nhật UI badge - tìm tất cả các badge
    const cartBadges = document.querySelectorAll('.cart-badge');
    const mobileBadge = document.querySelector('.mobile-badge');
    const mobileNavBadge = document.querySelector('.mobile-nav-badge');

    // Ẩn tất cả các badge nếu giỏ hàng trống
    cartBadges.forEach((badge) => {
      badge.style.display = 'none';

      // Xóa badge khỏi DOM để tránh vấn đề vị trí khi thêm lại
      // Chỉ xóa nếu badge nằm trong cart-btn (không đúng vị trí)
      const parent = badge.parentElement;
      if (parent && parent.classList.contains('cart-btn')) {
        console.log('Removing badge from incorrect position (inside cart-btn)');
        badge.remove();
      }
    });

    if (mobileBadge) mobileBadge.style.display = 'none';
    if (mobileNavBadge) mobileNavBadge.style.display = 'none';

    if (cartTable) {
      // Tạo thông báo giỏ hàng trống
      const emptyCartHtml = `
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <div class="text-gray-500 text-6xl mb-4">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Giỏ hàng của bạn đang trống</h2>
                    <p class="text-gray-600 mb-6">Hãy thêm sản phẩm vào giỏ hàng để tiến hành mua sắm.</p>
                    <a href="${BASE_URL}/products.php" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded inline-block transition duration-200">
                        Tiếp tục mua sắm
                    </a>
                </div>
            `;

      // Thay thế bảng giỏ hàng bằng thông báo
      cartTable.parentElement.innerHTML = emptyCartHtml;

      // Ẩn phần tổng kết giỏ hàng
      if (cartSummary) {
        cartSummary.style.display = 'none';
      }
    }

    console.log('Cart emptied, localStorage updated, badges cleaned up');
  }

  /**
   * Hiệu ứng thêm vào giỏ hàng - đã vô hiệu hóa hiệu ứng bay theo yêu cầu
   */
  function animateAddToCart(button) {
    // Kiểm tra xem đang ở giao diện desktop hay mobile
    const isMobile = window.innerWidth <= 576;

    // Tìm biểu tượng giỏ hàng
    let cart = null;

    if (isMobile) {
      // Tìm biểu tượng giỏ hàng trên mobile bottom navigation
      cart = document.querySelector(
        '.mobile-bottom-nav .mobile-nav-item[href*="/cart.php"]'
      );
    } else {
      // Tìm biểu tượng giỏ hàng trên desktop
      cart = document.querySelector('.cart-btn');
    }

    // Nếu không tìm thấy biểu tượng giỏ hàng, thử tìm kiếm lại
    if (!cart) {
      if (isMobile) {
        // Thử tìm kiếm lại trên mobile với các selector khác
        cart = document.querySelector(
          '.mobile-bottom-nav a[href*="/cart.php"]'
        );

        if (!cart) {
          // Thử tìm mobile-nav-badge (badge giỏ hàng trên mobile)
          const mobileBadge = document.querySelector('.mobile-nav-badge');
          if (mobileBadge) {
            cart = mobileBadge.parentElement;
          }
        }

        // Nếu vẫn không tìm thấy, thử tìm trong mobile-bottom-nav
        if (!cart) {
          const allMobileNavItems = document.querySelectorAll(
            '.mobile-bottom-nav .mobile-nav-item'
          );

          // Tìm item có chứa icon giỏ hàng
          for (const item of allMobileNavItems) {
            if (item.querySelector('i.fa-shopping-cart')) {
              cart = item;
              break;
            }
          }
        }
      } else {
        // Thử tìm kiếm lại trên desktop
        cart = document.querySelector('.cart-container');
        if (!cart) {
          cart = document.querySelector('a[href*="/cart.php"]');
        }
      }
    }

    // Thêm hiệu ứng nhấp nháy cho tất cả các badge giỏ hàng
    const cartBadges = document.querySelectorAll('.cart-badge');
    const mobileNavBadge = document.querySelector('.mobile-nav-badge');

    // Thêm hiệu ứng cho badge desktop
    cartBadges.forEach((badge) => {
      badge.classList.add('badge-pulse');
      setTimeout(() => {
        badge.classList.remove('badge-pulse');
      }, 1000);
    });

    // Thêm hiệu ứng cho badge mobile - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
    if (mobileNavBadge) {
      // mobileNavBadge.classList.add('badge-pulse');
      setTimeout(() => {
        // mobileNavBadge.classList.remove('badge-pulse');
      }, 1000);
    }

    // Thêm hiệu ứng cho nút giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT
    if (cart) {
      // cart.classList.add('cart-bounce');
      setTimeout(() => {
        // cart.classList.remove('cart-bounce');
      }, 700);
    }

    // Cập nhật số lượng giỏ hàng
    updateCartCount();
  }

  /**
   * Hiển thị thông báo
   */
  function showNotification(message, type) {
    // Sử dụng hàm showSimpleNotification để hiển thị thông báo đơn giản
    return showSimpleNotification(message, type);
  }

  /**
   * Hiển thị thông báo đơn giản
   */
  function showSimpleNotification(message, type = 'success', duration = 5000) {
    console.log('Hiển thị thông báo đơn giản:', message, type);

    // Xóa thông báo cũ nếu có
    const oldNotification = document.getElementById('simple-notification');
    if (oldNotification) {
      oldNotification.remove();
    }

    // Tạo thông báo mới
    const notification = document.createElement('div');
    notification.id = 'simple-notification';
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.zIndex = '9999';
    notification.style.backgroundColor = type === 'success' ? '#10B981' : '#EF4444';
    notification.style.color = 'white';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.maxWidth = '90%';
    notification.style.width = 'auto';
    notification.style.animation = 'fadeIn 0.3s ease-out forwards';

    // Tạo nội dung thông báo
    notification.innerHTML = `
      <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}" style="margin-right: 10px;"></i>
      <span style="flex: 1;">${message}</span>
      <button onclick="this.parentElement.remove();" style="background: none; border: none; color: white; cursor: pointer; margin-left: 10px;">
        <i class="fas fa-times"></i>
      </button>
    `;

    // Thêm thông báo vào body
    document.body.appendChild(notification);

    // Tự động đóng thông báo sau thời gian đã định
    setTimeout(function() {
      if (notification && notification.parentNode) {
        notification.remove();
      }
    }, duration);

    return notification;
  }

  /**
   * Kiểm tra cập nhật giỏ hàng
   * Hàm này sẽ kiểm tra xem giỏ hàng có thay đổi không và cập nhật UI nếu cần
   */
  function checkCartUpdates() {
    // Lấy thời gian cập nhật cuối cùng từ localStorage
    const lastUpdated = localStorage.getItem('cartLastUpdated') || 0;

    // Gửi yêu cầu kiểm tra cập nhật
    fetch(`${BASE_URL}/ajax/check_cart_updates.php?last_updated=${lastUpdated}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Lưu số lượng giỏ hàng hiện tại từ localStorage
          const currentCount = parseInt(
            localStorage.getItem('cartCount') || '0'
          );

          // Lấy số lượng giỏ hàng mới từ server
          const newCount = parseInt(data.count || '0');

          // Kiểm tra xem giỏ hàng có thay đổi không
          const cartChanged = newCount !== currentCount;

          // Nếu giỏ hàng có thay đổi hoặc server báo đã cập nhật
          if (cartChanged || data.updated) {
            console.log('Cart updated:', {
              currentCount,
              newCount,
              cartChanged,
              serverUpdated: data.updated,
            });

            // Cập nhật thời gian cập nhật cuối cùng
            localStorage.setItem('cartLastUpdated', data.timestamp);

            // Cập nhật UI giỏ hàng
            updateCartCount();

            // Lưu số lượng giỏ hàng mới
            localStorage.setItem('cartCount', newCount.toString());

            // Nếu số lượng giỏ hàng thay đổi, thêm hiệu ứng nhấp nháy cho badge
            if (cartChanged) {
              // Thêm hiệu ứng nhấp nháy cho badge giỏ hàng
              const cartBadge = document.querySelector('.cart-badge');
              const mobileNavBadge =
                document.querySelector('.mobile-nav-badge');

              if (cartBadge) {
                cartBadge.classList.add('badge-pulse');
                setTimeout(() => {
                  cartBadge.classList.remove('badge-pulse');
                }, 1000);
              }

              if (mobileNavBadge) {
                // mobileNavBadge.classList.add('badge-pulse');
                setTimeout(() => {
                  // mobileNavBadge.classList.remove('badge-pulse');
                }, 1000);
              }
            }

            // Nếu đang ở trang giỏ hàng, cập nhật nội dung giỏ hàng
            if (window.location.pathname.includes('/cart.php')) {
              // Tải lại trang giỏ hàng để cập nhật nội dung
              window.location.reload();
            }
          }
        }
      })
      .catch((error) => {
        console.error('Error checking cart updates:', error);
      });
  }

  // Xuất các hàm ra global scope để main.js và các file khác có thể sử dụng
  window.showNotificationRealtime = function (message, type) {
    // Sử dụng showSimpleNotification để hiển thị thông báo đơn giản
    return showSimpleNotification(message, type);
  };

  // Xuất hàm showSimpleNotification ra global scope
  window.showSimpleNotification = showSimpleNotification;
  window.updateCartCountRealtime = updateCartCount;
  window.updateAllCartBadgesImmediate = updateAllCartBadgesImmediate;
  window.initializeCartBadgesFromStorage = initializeCartBadgesFromStorage;
  window.checkCartUpdates = checkCartUpdates;
  window.animateAddToCart = animateAddToCart;
});
