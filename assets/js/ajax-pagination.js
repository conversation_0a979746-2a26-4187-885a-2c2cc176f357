/**
 * AJAX Pagination for Products Page
 * Handles pagination without page reload
 */

class AjaxPagination {
    constructor() {
        this.isLoading = false;
        this.productsContainer = null;
        this.paginationContainer = null;
        this.statsContainer = null;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Find containers
        this.productsContainer = document.getElementById('productsGrid');
        this.paginationContainer = document.querySelector('.pagination-section');
        this.statsContainer = document.getElementById('products-stats');

        if (!this.productsContainer) {
            console.warn('Products container not found');
            return;
        }

        // Bind events
        this.bindEvents();
        
        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.loadPage(e.state.page, false);
            }
        });

        // Handle keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.target.closest('.ajax-pagination-link')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.target.click();
                }
            }
        });

        // Listen for filter changes to reset pagination
        this.setupFilterIntegration();

        console.log('AJAX Pagination initialized');
    }

    bindEvents() {
        // Use event delegation for pagination links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('.ajax-pagination-link');
            if (link) {
                e.preventDefault();
                const page = parseInt(link.dataset.page);
                if (page && !this.isLoading) {
                    // Add loading state to clicked link
                    this.addLoadingStateToLink(link);
                    this.loadPage(page, true);
                }
            }
        });
    }

    async loadPage(page, updateHistory = true) {
        if (this.isLoading) return;

        // Validate page number
        if (!page || page < 1) {
            console.warn('Invalid page number:', page);
            return;
        }

        try {
            this.isLoading = true;
            this.showLoading();

            // Ghi nhận thời gian bắt đầu để đảm bảo loading tối thiểu
            const startTime = Date.now();
            const minLoadingTime = 800; // Thời gian loading tối thiểu để người dùng kịp nhận biết

            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('page', page);

            // Build AJAX URL
            const ajaxUrl = `${window.BASE_URL}/ajax-products.php?${urlParams.toString()}`;

            // Make AJAX request
            const response = await fetch(ajaxUrl, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'Unknown error occurred');
            }

            // Check if we have valid data
            if (!data.data || data.data.total_products === 0) {
                console.log('No products found for this page');
                // Still update content to show empty state
                this.updateContent(data.data);
                return;
            }

            // Update content
            this.updateContent(data.data);

            // Update URL and history
            if (updateHistory) {
                const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
                history.pushState({ page: page }, '', newUrl);
            }

            // Update page title if needed
            this.updatePageTitle(page, data.data.total_products);

            // Lưu data để sử dụng sau khi loading hoàn thành
            this.pendingScrollData = { page, totalProducts: data.data.total_products };

        } catch (error) {
            console.error('AJAX Pagination Error:', error);

            // Đảm bảo thời gian loading tối thiểu ngay cả khi có lỗi
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            if (remainingTime > 0) {
                await new Promise(resolve => setTimeout(resolve, remainingTime));
            }

            this.showError(error.message);

            // Fallback to normal page navigation if AJAX fails
            if (updateHistory) {
                console.log('Falling back to normal navigation...');
                setTimeout(() => {
                    const urlParams = new URLSearchParams(window.location.search);
                    urlParams.set('page', page);
                    window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
                }, 2000);
            }
        } finally {
            // Đảm bảo thời gian loading tối thiểu
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

            if (remainingTime > 0) {
                await new Promise(resolve => setTimeout(resolve, remainingTime));
            }

            this.isLoading = false;
            this.hideLoading();

            // Scroll to products sau khi loading hoàn thành
            if (this.pendingScrollData) {
                this.scrollToProducts();
                this.pendingScrollData = null;
            }
        }
    }

    updateContent(data) {
        // Update products grid with smooth transition
        if (this.productsContainer && data.products_html) {
            // Fade out first
            this.productsContainer.style.opacity = '0';
            this.productsContainer.style.transform = 'translateY(10px)';

            setTimeout(() => {
                this.productsContainer.innerHTML = data.products_html;

                // Fade in with staggered animation
                this.productsContainer.style.opacity = '1';
                this.productsContainer.style.transform = 'translateY(0)';

                // Trigger staggered animations for product cards
                const productCards = this.productsContainer.querySelectorAll('.product-card');
                productCards.forEach((card, index) => {
                    card.style.animationDelay = `${index * 0.05}s`;
                });
            }, 200);
        }

        // Update pagination with smooth transition
        if (this.paginationContainer && data.pagination_html) {
            const oldPagination = this.paginationContainer;
            oldPagination.style.opacity = '0';

            setTimeout(() => {
                oldPagination.outerHTML = data.pagination_html;
                this.paginationContainer = document.querySelector('.pagination-section');

                if (this.paginationContainer) {
                    this.paginationContainer.style.opacity = '0';
                    setTimeout(() => {
                        this.paginationContainer.style.opacity = '1';
                    }, 50);
                }
            }, 100);
        }

        // Update stats with animation
        if (this.statsContainer) {
            const showingSpan = this.statsContainer.querySelector('#products-showing');
            const totalSpan = this.statsContainer.querySelector('#products-total');

            if (showingSpan) {
                this.animateNumberChange(showingSpan, data.products_count);
            }
            if (totalSpan) {
                this.animateNumberChange(totalSpan, data.total_products);
            }
        }

        // Update any other elements that show product counts
        this.updateProductCounts(data);

        // Show success notification
        this.showSuccessNotification(`Đã tải ${data.products_count} sản phẩm`);
    }

    updateProductCounts(data) {
        // Update filter results header if exists
        const filterHeader = document.querySelector('.filter-results-header');
        if (filterHeader) {
            const countBadge = filterHeader.querySelector('.count-badge');
            if (countBadge) {
                countBadge.textContent = data.total_products.toLocaleString();
                countBadge.classList.add('updating');
                setTimeout(() => countBadge.classList.remove('updating'), 600);
            }
        }
    }

    showLoading() {
        // Add loading class to products container
        if (this.productsContainer) {
            this.productsContainer.classList.add('loading');
            
            // Create loading overlay
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'ajax-loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-orange-500" role="status">
                        <span class="sr-only">Đang tải...</span>
                    </div>
                    <p class="mt-3 text-gray-600">Đang tải sản phẩm...</p>
                </div>
            `;
            
            this.productsContainer.style.position = 'relative';
            this.productsContainer.appendChild(loadingOverlay);
        }

        // Disable pagination links
        document.querySelectorAll('.ajax-pagination-link').forEach(link => {
            link.style.pointerEvents = 'none';
            link.style.opacity = '0.6';
        });
    }

    hideLoading() {
        // Remove loading class
        if (this.productsContainer) {
            this.productsContainer.classList.remove('loading');

            // Remove loading overlay
            const loadingOverlay = this.productsContainer.querySelector('.ajax-loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    if (loadingOverlay.parentNode) {
                        loadingOverlay.remove();
                    }
                }, 200);
            }
        }

        // Remove loading states from pagination links
        this.removeLoadingStateFromLinks();
    }

    showError(message) {
        // Create user-friendly error message
        let userMessage = 'Không thể tải trang. Vui lòng thử lại.';

        if (message.includes('404')) {
            userMessage = 'Trang không tồn tại.';
        } else if (message.includes('500')) {
            userMessage = 'Lỗi máy chủ. Vui lòng thử lại sau.';
        } else if (message.includes('network') || message.includes('fetch')) {
            userMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.';
        }

        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ajax-error-notification bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <div>
                        <div class="font-medium">${userMessage}</div>
                        <div class="text-sm text-red-600 mt-1">Trang sẽ tự động tải lại sau 2 giây...</div>
                    </div>
                </div>
                <button class="text-red-700 hover:text-red-900 ml-4" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Insert before products container
        if (this.productsContainer && this.productsContainer.parentNode) {
            this.productsContainer.parentNode.insertBefore(errorDiv, this.productsContainer);

            // Auto remove after 8 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 8000);
        }
    }

    scrollToProducts() {
        // Smooth scroll to products section
        if (this.productsContainer) {
            const offset = 100; // Offset for fixed header
            const elementPosition = this.productsContainer.offsetTop;
            const offsetPosition = elementPosition - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    }

    updatePageTitle(page, totalProducts) {
        // Update page title to include page number
        const baseTitle = document.title.split(' - Trang ')[0];
        if (page > 1) {
            document.title = `${baseTitle} - Trang ${page}`;
        } else {
            document.title = baseTitle;
        }
    }

    animateNumberChange(element, newValue) {
        // Animate number changes with counting effect
        const currentValue = parseInt(element.textContent.replace(/,/g, '')) || 0;
        const increment = (newValue - currentValue) / 20;
        let current = currentValue;

        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= newValue) || (increment < 0 && current <= newValue)) {
                current = newValue;
                clearInterval(timer);
            }
            element.textContent = Math.round(current).toLocaleString();
        }, 50);
    }

    showSuccessNotification(message) {
        // Create and show success notification
        const notification = document.createElement('div');
        notification.className = 'ajax-success-notification';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideInFromRight 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    addLoadingStateToLink(link) {
        // Add loading state to specific pagination link
        link.classList.add('loading');
        link.style.pointerEvents = 'none';
    }

    removeLoadingStateFromLinks() {
        // Remove loading state from all pagination links
        document.querySelectorAll('.ajax-pagination-link.loading').forEach(link => {
            link.classList.remove('loading');
            link.style.pointerEvents = '';
        });
    }

    setupFilterIntegration() {
        // Listen for custom events from AJAX filter system
        document.addEventListener('ajaxFilterStart', () => {
            console.log('AJAX Pagination: Filter started, resetting pagination');
            this.currentPage = 1;
        });

        document.addEventListener('ajaxFilterComplete', () => {
            console.log('AJAX Pagination: Filter completed, updating pagination');
            // Re-bind events for new pagination elements
            setTimeout(() => {
                this.paginationContainer = document.querySelector('.pagination-section');
            }, 100);
        });

        // Listen for URL changes that might affect pagination
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = function(...args) {
            originalPushState.apply(history, args);
            // Check if URL contains page parameter
            const url = args[2];
            if (url && !url.includes('page=')) {
                // Filter changed, reset to page 1
                console.log('AJAX Pagination: URL changed without page, resetting to page 1');
            }
        };

        history.replaceState = function(...args) {
            originalReplaceState.apply(history, args);
            const url = args[2];
            if (url && !url.includes('page=')) {
                console.log('AJAX Pagination: URL replaced without page, resetting to page 1');
            }
        };
    }
}

// CSS for loading states and animations
const ajaxPaginationCSS = `
    .ajax-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
        backdrop-filter: blur(3px);
        border-radius: 0.5rem;
        animation: fadeInOverlay 0.2s ease-out;
    }

    @keyframes fadeInOverlay {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .loading-spinner {
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(249, 115, 22, 0.1);
    }

    /* Removed spinner-border - using loading dots only */

    .text-orange-500 {
        color: #f97316;
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    #productsGrid {
        transition: opacity 0.4s ease, transform 0.4s ease;
        min-height: 400px;
    }

    #productsGrid.loading {
        opacity: 0.7;
        transform: translateY(10px);
        pointer-events: none;
    }

    .ajax-error-notification {
        animation: slideInFromTop 0.3s ease-out;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
    }

    @keyframes slideInFromTop {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .count-badge.updating {
        animation: countUpdate 0.6s ease-in-out;
    }

    @keyframes countUpdate {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
    }

    /* Pagination loading states - removed spinner, using dots only */
    .ajax-pagination-link.loading {
        opacity: 0.8;
        pointer-events: none;
        position: relative;
    }

    /* Smooth page transitions */
    .products-grid .product-card {
        animation: fadeInUp 0.4s ease-out;
        animation-fill-mode: both;
    }

    .products-grid .product-card:nth-child(1) { animation-delay: 0.05s; }
    .products-grid .product-card:nth-child(2) { animation-delay: 0.1s; }
    .products-grid .product-card:nth-child(3) { animation-delay: 0.15s; }
    .products-grid .product-card:nth-child(4) { animation-delay: 0.2s; }
    .products-grid .product-card:nth-child(5) { animation-delay: 0.25s; }
    .products-grid .product-card:nth-child(6) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Success notification */
    .ajax-success-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        z-index: 1000;
        animation: slideInFromRight 0.4s ease-out;
        font-weight: 500;
    }

    @keyframes slideInFromRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .ajax-loading-overlay {
            backdrop-filter: blur(2px);
        }

        .loading-spinner {
            padding: 1.5rem;
            margin: 1rem;
        }

        /* Removed spinner-border mobile styles */

        .ajax-success-notification {
            top: 10px;
            right: 10px;
            left: 10px;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }
    }
`;

// Inject CSS
const paginationStyle = document.createElement('style');
paginationStyle.textContent = ajaxPaginationCSS;
document.head.appendChild(paginationStyle);

// Initialize when script loads
new AjaxPagination();
