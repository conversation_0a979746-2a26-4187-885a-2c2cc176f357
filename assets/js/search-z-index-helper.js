/**
 * Search Z-Index Helper JavaScript
 * Xử lý các vấn đề z-index phức tạp cho search suggestions
 */

(function() {
  'use strict';

  // Hàm kiểm tra và sửa z-index conflicts
  function fixSearchZIndexConflicts() {
    const searchContainers = document.querySelectorAll('.search-container');
    const searchSuggestions = document.querySelectorAll('.search-suggestions');
    
    searchContainers.forEach(container => {
      // Đảm bảo search-container có stacking context
      const computedStyle = getComputedStyle(container);
      
      if (computedStyle.position === 'static') {
        container.style.position = 'relative';
      }
      
      // Thêm transform để tạo stacking context nếu chưa có
      if (computedStyle.transform === 'none') {
        container.style.transform = 'translateZ(0)';
      }
      
      // Đảm bảo z-index cao
      if (parseInt(computedStyle.zIndex) < 1100 || isNaN(parseInt(computedStyle.zIndex))) {
        container.style.zIndex = '1100';
      }
    });
    
    searchSuggestions.forEach(suggestion => {
      // Đảm bảo search-suggestions có z-index cao nhất
      const computedStyle = getComputedStyle(suggestion);
      
      if (parseInt(computedStyle.zIndex) < 1200 || isNaN(parseInt(computedStyle.zIndex))) {
        suggestion.style.zIndex = '1200';
      }
      
      // Đảm bảo position absolute
      if (computedStyle.position !== 'absolute' && computedStyle.position !== 'fixed') {
        suggestion.style.position = 'absolute';
      }
      
      // Thêm isolation nếu browser hỗ trợ
      if (CSS.supports('isolation', 'isolate')) {
        suggestion.style.isolation = 'isolate';
      }
    });
  }

  // Hàm xử lý khi search suggestions được hiển thị
  function handleSearchSuggestionsShow(suggestion) {
    // Kiểm tra xem có bị che khuất không
    const rect = suggestion.getBoundingClientRect();
    const bottomHeader = document.querySelector('.bottom-header-container');
    
    if (bottomHeader) {
      const bottomHeaderRect = bottomHeader.getBoundingClientRect();
      
      // Nếu search suggestions bị che khuất
      if (rect.top < bottomHeaderRect.bottom && rect.bottom > bottomHeaderRect.top) {
        // Tăng z-index lên mức cao hơn
        suggestion.style.zIndex = '9999';
        suggestion.classList.add('force-top');
        
        // Thêm class để CSS có thể xử lý
        suggestion.setAttribute('data-overlapped', 'true');
        
        console.log('Search suggestions bị che khuất, đã áp dụng fix z-index');
      }
    }
  }

  // Hàm xử lý khi search suggestions được ẩn
  function handleSearchSuggestionsHide(suggestion) {
    // Reset về z-index bình thường
    suggestion.style.zIndex = '';
    suggestion.classList.remove('force-top');
    suggestion.removeAttribute('data-overlapped');
  }

  // Observer để theo dõi thay đổi visibility của search suggestions
  function setupSearchSuggestionsObserver() {
    const searchSuggestions = document.querySelectorAll('.search-suggestions');
    
    searchSuggestions.forEach(suggestion => {
      // Sử dụng MutationObserver để theo dõi thay đổi class
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const target = mutation.target;
            
            if (target.classList.contains('show') && !target.classList.contains('hidden')) {
              // Search suggestions được hiển thị
              setTimeout(() => handleSearchSuggestionsShow(target), 10);
            } else if (target.classList.contains('hidden') || !target.classList.contains('show')) {
              // Search suggestions được ẩn
              handleSearchSuggestionsHide(target);
            }
          }
        });
      });
      
      observer.observe(suggestion, {
        attributes: true,
        attributeFilter: ['class']
      });
    });
  }

  // Hàm debug z-index issues
  function debugZIndexIssues() {
    if (window.location.search.includes('debug-zindex')) {
      const elements = [
        '.search-container',
        '.search-suggestions', 
        '.mid-header-container',
        '.bottom-header-container',
        '.premium-header'
      ];
      
      elements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
          const style = getComputedStyle(element);
          console.log(`${selector}:`, {
            zIndex: style.zIndex,
            position: style.position,
            transform: style.transform,
            isolation: style.isolation
          });
        }
      });
    }
  }

  // Hàm khởi tạo
  function init() {
    // Chờ DOM load xong
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
      return;
    }
    
    // Fix z-index conflicts ngay lập tức
    fixSearchZIndexConflicts();
    
    // Setup observer cho search suggestions
    setupSearchSuggestionsObserver();
    
    // Debug nếu cần
    debugZIndexIssues();
    
    // Re-check z-index sau khi các script khác load xong
    setTimeout(fixSearchZIndexConflicts, 1000);
    
    console.log('Search Z-Index Helper initialized');
  }

  // Hàm cleanup
  function cleanup() {
    // Cleanup observers nếu cần
    // (MutationObserver sẽ tự cleanup khi element bị remove)
  }

  // Export functions để có thể gọi từ bên ngoài
  window.SearchZIndexHelper = {
    fix: fixSearchZIndexConflicts,
    debug: debugZIndexIssues,
    init: init,
    cleanup: cleanup
  };

  // Auto init
  init();

  // Cleanup khi unload
  window.addEventListener('beforeunload', cleanup);

})();
