/**
 * Luxury Mobile Menu JavaScript for Nội Thất Bàng Vũ
 * Xử lý các tính năng tương tác của menu sang trọng trên điện thoại
 */

document.addEventListener('DOMContentLoaded', function () {
  // C<PERSON>c phần tử DOM
  const mobileHeader = document.querySelector('.mobile-header');
  const mobileMenuToggle = document.querySelector('.mobile-header-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
  const mobileDropdownToggles = document.querySelectorAll(
    '.mobile-dropdown-toggle'
  );
  const mobileBackButtons = document.querySelectorAll('.mobile-menu-back');

  // Xử lý mở/đóng menu bằng nút toggle
  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      // Thêm hiệu ứng ripple khi nhấn
      const ripple = document.createElement('span');
      ripple.classList.add('menu-toggle-ripple');
      this.appendChild(ripple);

      // Xóa hiệu ứng ripple sau khi hoàn thành
      setTimeout(() => {
        ripple.remove();
      }, 600);

      // Toggle trạng thái active của nút
      this.classList.toggle('active');

      // Mở/đóng menu
      if (mobileMenu.classList.contains('active')) {
        // Đóng menu
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
      } else {
        // Mở menu
        // Sử dụng requestAnimationFrame để đảm bảo hiệu ứng mượt mà
        requestAnimationFrame(() => {
          // Thêm một khoảng thời gian nhỏ để đảm bảo CSS được áp dụng đúng
          setTimeout(() => {
            mobileMenu.classList.add('active');
            document.body.classList.add('overflow-hidden');
            if (mobileMenuOverlay) {
              mobileMenuOverlay.classList.add('active');
            }
          }, 10);
        });
      }
    });
  }

  // Xử lý đóng menu khi nhấp vào overlay
  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      // Xóa class active khỏi nút toggle
      if (mobileMenuToggle) {
        mobileMenuToggle.classList.remove('active');
      }

      // Đóng menu
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Xử lý dropdown menu
  if (mobileDropdownToggles) {
    mobileDropdownToggles.forEach(function (toggle) {
      toggle.addEventListener('click', function (e) {
        e.preventDefault();
        const parent = this.parentElement;
        const submenu = parent.querySelector('.mobile-submenu');

        // Đóng tất cả các submenu cùng cấp khác
        const siblings = parent.parentElement.querySelectorAll(
          '.mobile-menu-item.active, .mobile-submenu-item.active'
        );
        siblings.forEach(function (sibling) {
          if (
            sibling !== parent &&
            !parent.contains(sibling) &&
            !sibling.contains(parent)
          ) {
            sibling.classList.remove('active');
          }
        });

        // Mở/đóng submenu
        parent.classList.toggle('active');

        // Log để debug
        console.log('Toggle clicked:', parent, submenu);
      });
    });
  }

  // Xử lý nút quay lại
  if (mobileBackButtons) {
    mobileBackButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        e.preventDefault();

        // Tìm submenu cha
        const submenu = this.closest('.mobile-submenu');
        const parentItem = submenu.closest(
          '.mobile-menu-item, .mobile-submenu-item'
        );

        if (parentItem) {
          parentItem.classList.remove('active');
        }
      });
    });
  }

  // Xử lý khi resize cửa sổ
  window.addEventListener('resize', function () {
    if (window.innerWidth > 576) {
      // Đóng menu khi chuyển sang desktop
      if (mobileMenu && mobileMenu.classList.contains('active')) {
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
        if (mobileMenuToggle) {
          mobileMenuToggle.classList.remove('active');
        }
      }
    }
  });
});
