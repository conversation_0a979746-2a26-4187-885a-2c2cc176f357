/**
 * Header Performance Testing Suite
 * So sánh performance trước và sau optimization
 */

class HeaderPerformanceTest {
    constructor() {
        this.testResults = {
            before: {},
            after: {},
            comparison: {}
        };
        this.isRunning = false;
        this.testDuration = 10000; // 10 seconds
    }

    /**
     * Chạy full performance test suite
     */
    async runFullTest() {
        console.log('🧪 Bắt đầu Header Performance Test Suite...');
        
        // Test 1: Baseline (current state)
        console.log('📊 Phase 1: Testing current header performance...');
        this.testResults.before = await this.runPerformanceTest('before');
        
        // Activate unified handler
        console.log('🔄 Phase 2: Activating unified header handler...');
        await this.activateUnifiedHandler();
        
        // Test 2: Optimized state
        console.log('📊 Phase 3: Testing optimized header performance...');
        this.testResults.after = await this.runPerformanceTest('after');
        
        // Generate comparison
        console.log('📈 Phase 4: Generating performance comparison...');
        this.generateComparison();
        
        // Show results
        this.showResults();
        
        console.log('✅ Performance test completed!');
    }

    /**
     * Run individual performance test
     */
    runPerformanceTest(phase) {
        return new Promise((resolve) => {
            const metrics = {
                scrollEvents: 0,
                frameDrops: 0,
                averageFPS: 0,
                maxScrollVelocity: 0,
                layoutShifts: 0,
                memoryUsage: 0,
                cpuTime: 0
            };

            const startTime = performance.now();
            let frames = 0;
            let lastFrameTime = startTime;
            let scrollEventCount = 0;
            let maxVelocity = 0;
            let lastScrollTop = 0;
            let lastScrollTime = startTime;

            // FPS monitoring
            const measureFPS = (currentTime) => {
                frames++;
                const deltaTime = currentTime - lastFrameTime;
                
                if (deltaTime >= 1000) {
                    const fps = Math.round((frames * 1000) / deltaTime);
                    metrics.averageFPS = fps;
                    
                    if (fps < 50) {
                        metrics.frameDrops++;
                    }
                    
                    frames = 0;
                    lastFrameTime = currentTime;
                }
                
                if (currentTime - startTime < this.testDuration) {
                    requestAnimationFrame(measureFPS);
                }
            };

            // Scroll event monitoring
            const scrollHandler = () => {
                scrollEventCount++;
                const now = performance.now();
                const currentScrollTop = window.pageYOffset;
                
                // Calculate scroll velocity
                const velocity = Math.abs(currentScrollTop - lastScrollTop) / (now - lastScrollTime);
                if (velocity > maxVelocity) {
                    maxVelocity = velocity;
                }
                
                lastScrollTop = currentScrollTop;
                lastScrollTime = now;
            };

            // Layout shift monitoring
            let layoutShiftObserver;
            if ('PerformanceObserver' in window) {
                layoutShiftObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.value > 0.1) {
                            metrics.layoutShifts++;
                        }
                    }
                });
                layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
            }

            // Memory monitoring
            const measureMemory = () => {
                if (performance.memory) {
                    metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                }
            };

            // Start monitoring
            requestAnimationFrame(measureFPS);
            window.addEventListener('scroll', scrollHandler, { passive: true });
            measureMemory();

            // Auto-scroll for testing
            this.simulateScrolling();

            // Complete test after duration
            setTimeout(() => {
                window.removeEventListener('scroll', scrollHandler);
                if (layoutShiftObserver) {
                    layoutShiftObserver.disconnect();
                }

                metrics.scrollEvents = scrollEventCount;
                metrics.maxScrollVelocity = Math.round(maxVelocity);
                metrics.cpuTime = performance.now() - startTime;
                measureMemory();

                console.log(`📊 ${phase} test completed:`, metrics);
                resolve(metrics);
            }, this.testDuration);
        });
    }

    /**
     * Simulate scrolling for consistent testing
     */
    simulateScrolling() {
        let scrollPosition = 0;
        let direction = 1;
        const maxScroll = document.body.scrollHeight - window.innerHeight;
        
        const scroll = () => {
            scrollPosition += direction * 50;
            
            if (scrollPosition >= maxScroll || scrollPosition <= 0) {
                direction *= -1;
            }
            
            window.scrollTo({
                top: scrollPosition,
                behavior: 'auto'
            });
            
            if (this.isRunning) {
                setTimeout(scroll, 100);
            }
        };
        
        this.isRunning = true;
        scroll();
        
        // Stop after test duration
        setTimeout(() => {
            this.isRunning = false;
        }, this.testDuration);
    }

    /**
     * Activate unified handler
     */
    activateUnifiedHandler() {
        return new Promise((resolve) => {
            // Cleanup existing handlers
            if (window.headerCleanup) {
                window.headerCleanup.startCleanup();
            }
            
            // Initialize unified handler
            if (window.UnifiedHeaderHandler) {
                window.unifiedHeaderHandler = new UnifiedHeaderHandler({
                    enablePerformanceMonitoring: true,
                    enableVelocityTracking: true,
                    enableHardwareAcceleration: true
                });
            }
            
            // Wait for initialization
            setTimeout(resolve, 1000);
        });
    }

    /**
     * Generate performance comparison
     */
    generateComparison() {
        const { before, after } = this.testResults;
        
        this.testResults.comparison = {
            fpsImprovement: ((after.averageFPS - before.averageFPS) / before.averageFPS * 100).toFixed(1),
            frameDropReduction: ((before.frameDrops - after.frameDrops) / before.frameDrops * 100).toFixed(1),
            scrollEventReduction: ((before.scrollEvents - after.scrollEvents) / before.scrollEvents * 100).toFixed(1),
            memoryReduction: ((before.memoryUsage - after.memoryUsage) / before.memoryUsage * 100).toFixed(1),
            cpuTimeReduction: ((before.cpuTime - after.cpuTime) / before.cpuTime * 100).toFixed(1),
            layoutShiftReduction: before.layoutShifts > 0 ? 
                ((before.layoutShifts - after.layoutShifts) / before.layoutShifts * 100).toFixed(1) : '0'
        };
    }

    /**
     * Show detailed results
     */
    showResults() {
        const { before, after, comparison } = this.testResults;
        
        console.log('\n🎯 ===== HEADER PERFORMANCE TEST RESULTS =====');
        
        console.log('\n📊 BEFORE OPTIMIZATION:');
        console.log(`  FPS: ${before.averageFPS}`);
        console.log(`  Frame Drops: ${before.frameDrops}`);
        console.log(`  Scroll Events: ${before.scrollEvents}`);
        console.log(`  Layout Shifts: ${before.layoutShifts}`);
        console.log(`  Memory Usage: ${before.memoryUsage}MB`);
        console.log(`  CPU Time: ${before.cpuTime.toFixed(0)}ms`);
        
        console.log('\n📈 AFTER OPTIMIZATION:');
        console.log(`  FPS: ${after.averageFPS}`);
        console.log(`  Frame Drops: ${after.frameDrops}`);
        console.log(`  Scroll Events: ${after.scrollEvents}`);
        console.log(`  Layout Shifts: ${after.layoutShifts}`);
        console.log(`  Memory Usage: ${after.memoryUsage}MB`);
        console.log(`  CPU Time: ${after.cpuTime.toFixed(0)}ms`);
        
        console.log('\n🚀 IMPROVEMENTS:');
        console.log(`  FPS: ${comparison.fpsImprovement > 0 ? '+' : ''}${comparison.fpsImprovement}%`);
        console.log(`  Frame Drops: -${comparison.frameDropReduction}%`);
        console.log(`  Scroll Events: -${comparison.scrollEventReduction}%`);
        console.log(`  Layout Shifts: -${comparison.layoutShiftReduction}%`);
        console.log(`  Memory Usage: -${comparison.memoryReduction}%`);
        console.log(`  CPU Time: -${comparison.cpuTimeReduction}%`);
        
        console.log('\n💡 RECOMMENDATIONS:');
        this.generateRecommendations();
        
        console.log('\n===== END RESULTS =====\n');
        
        // Store results for later analysis
        localStorage.setItem('headerPerformanceTest', JSON.stringify(this.testResults));
    }

    /**
     * Generate recommendations based on results
     */
    generateRecommendations() {
        const { comparison } = this.testResults;
        
        if (parseFloat(comparison.fpsImprovement) > 10) {
            console.log('✅ Excellent FPS improvement! Consider implementing unified handler.');
        } else if (parseFloat(comparison.fpsImprovement) > 0) {
            console.log('👍 Good FPS improvement. Unified handler is beneficial.');
        } else {
            console.log('⚠️ No significant FPS improvement. Check for conflicts.');
        }
        
        if (parseFloat(comparison.scrollEventReduction) > 20) {
            console.log('✅ Great reduction in scroll events. Throttling is working well.');
        }
        
        if (parseFloat(comparison.memoryReduction) > 10) {
            console.log('✅ Significant memory savings achieved.');
        }
        
        if (parseFloat(comparison.layoutShiftReduction) > 50) {
            console.log('✅ Layout stability greatly improved.');
        }
    }

    /**
     * Quick performance check
     */
    quickCheck() {
        console.log('⚡ Running quick performance check...');
        
        let scrollEvents = 0;
        const startTime = performance.now();
        
        const handler = () => scrollEvents++;
        window.addEventListener('scroll', handler, { passive: true });
        
        // Simulate some scrolling
        window.scrollBy(0, 100);
        window.scrollBy(0, -50);
        window.scrollBy(0, 200);
        
        setTimeout(() => {
            window.removeEventListener('scroll', handler);
            const duration = performance.now() - startTime;
            const eventsPerSecond = Math.round((scrollEvents * 1000) / duration);
            
            console.log(`📊 Quick Check Results:`);
            console.log(`  Scroll events: ${scrollEvents}`);
            console.log(`  Events/second: ${eventsPerSecond}`);
            console.log(`  Status: ${eventsPerSecond < 60 ? '✅ Good' : eventsPerSecond < 120 ? '⚠️ Moderate' : '🚨 High'}`);
        }, 1000);
    }
}

// Initialize test suite
document.addEventListener('DOMContentLoaded', () => {
    window.headerPerformanceTest = new HeaderPerformanceTest();
    
    console.log('🧪 Header Performance Test ready');
    console.log('💡 Commands:');
    console.log('  - headerPerformanceTest.runFullTest() // Full test suite');
    console.log('  - headerPerformanceTest.quickCheck() // Quick check');
});

// Export
window.HeaderPerformanceTest = HeaderPerformanceTest;
