/**
 * Contact Buttons JavaScript - Nội Thất Bàng Vũ
 * Xử lý các tương tác và hiệu ứng cho các nút liên hệ
 */

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo các nút liên hệ
    initContactButtons();

    // Khởi tạo nút cuộn lên đầu trang
    initScrollToTop();
});

/**
 * Khởi tạo các nút liên hệ
 */
function initContactButtons() {
    // Lấy tất cả các nút liên hệ
    const contactButtons = document.querySelectorAll('.contact-buttons-container .contact-button');
    const phoneButton = document.querySelector('.contact-buttons-container .phone-button');

    // Thêm hiệu ứng xuất hiện lần lượt cho các nút liên hệ (bên phải)
    setTimeout(() => {
        contactButtons.forEach((button, index) => {
            setTimeout(() => {
                button.classList.add('show');
            }, index * 100); // Mỗi nút xuất hiện cách nhau 100ms
        });

        // Thêm hiệu ứng đặc biệt cho nút gọi điện sau khi hiển thị
        if (phoneButton) {
            setTimeout(() => {
                // Thêm hiệu ứng nhấp nháy đặc biệt sau khi nút hiển thị
                addPhoneSpecialEffects(phoneButton);
            }, 1000);
        }
    }, 500); // Đợi 500ms sau khi trang tải xong

    // Thêm hiệu ứng ripple khi click
    contactButtons.forEach(button => {
        button.addEventListener('click', createRippleEffect);
    });

    // Theo dõi vị trí cuộn để hiển thị/ẩn các nút khi cần
    window.addEventListener('scroll', handleScroll);
}

/**
 * Thêm hiệu ứng đặc biệt cho nút gọi điện
 */
function addPhoneSpecialEffects(phoneButton) {
    // Thêm hiệu ứng nhấp nháy đặc biệt
    const specialEffect = () => {
        // Tạo hiệu ứng nhấp nháy mạnh
        phoneButton.classList.add('phone-attention');

        // Sau 1 giây, loại bỏ hiệu ứng
        setTimeout(() => {
            phoneButton.classList.remove('phone-attention');

            // Lặp lại hiệu ứng sau một khoảng thời gian ngẫu nhiên (30-60 giây)
            const randomDelay = Math.floor(Math.random() * 30000) + 30000;
            setTimeout(specialEffect, randomDelay);
        }, 1000);
    };

    // Bắt đầu hiệu ứng sau 5 giây
    setTimeout(specialEffect, 5000);
}

/**
 * Tạo hiệu ứng ripple khi click vào nút
 */
function createRippleEffect(event) {
    const button = event.currentTarget;

    // Tạo phần tử ripple
    const ripple = document.createElement('span');
    ripple.classList.add('ripple-effect');

    // Tính toán vị trí click
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height) * 2;
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    // Thiết lập vị trí và kích thước ripple
    ripple.style.width = ripple.style.height = `${size}px`;
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;

    // Thêm ripple vào nút
    button.appendChild(ripple);

    // Xóa ripple sau khi hoàn thành hiệu ứng
    setTimeout(() => {
        ripple.remove();
    }, 800);

    // Thêm hiệu ứng âm thanh nhẹ khi click (tùy chọn)
    // playClickSound();
}

/**
 * Khởi tạo nút cuộn lên đầu trang
 */
function initScrollToTop() {
    // Lấy nút cuộn lên đầu trang
    const scrollButton = document.getElementById('scroll-to-top');

    if (!scrollButton) return;

    // Thêm sự kiện click
    scrollButton.addEventListener('click', function() {
        // Cuộn mượt lên đầu trang
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Kiểm tra vị trí cuộn ban đầu
    handleScroll();
}

/**
 * Xử lý sự kiện cuộn trang
 */
function handleScroll() {
    // Lấy nút cuộn lên đầu trang
    const scrollButton = document.getElementById('scroll-to-top');

    // Lấy container các nút liên hệ
    const contactButtonsContainer = document.querySelector('.contact-buttons-container');
    const scrollTopContainer = document.querySelector('.scroll-top-container');

    // Kiểm tra xem có phải thiết bị di động không
    const isMobile = window.innerWidth <= 768;

    // Biến theo dõi hướng cuộn
    const currentScrollY = window.scrollY;

    if (scrollButton) {
        // Hiển thị nút khi cuộn xuống đủ xa
        if (currentScrollY > 300) {
            scrollButton.classList.add('show');
        } else {
            scrollButton.classList.remove('show');
        }
    }

    // Xử lý hiệu ứng làm mờ trên thiết bị di động
    if (isMobile) {
        // Kiểm tra hướng cuộn
        const isScrollingDown = currentScrollY > (handleScroll.lastScrollY || 0);
        const scrollDelta = Math.abs(currentScrollY - (handleScroll.lastScrollY || 0));

        // Lưu vị trí cuộn hiện tại
        handleScroll.lastScrollY = currentScrollY;

        // Chỉ xử lý khi cuộn đủ xa để tránh hiệu ứng nhấp nháy
        if (scrollDelta > 5) {
            // Nếu đang cuộn xuống và đã cuộn đủ xa
            if (isScrollingDown && currentScrollY > 100) {
                // Thêm class làm mờ
                if (contactButtonsContainer) contactButtonsContainer.classList.add('fade-buttons');
                if (scrollTopContainer) scrollTopContainer.classList.add('fade-buttons');

                // Lưu trạng thái cuộn xuống
                handleScroll.isScrolledDown = true;

                // Xóa timeout hiện tại nếu có (không cần thiết nữa vì chúng ta không tự động hiển thị lại)
                if (handleScroll.fadeTimeout) {
                    clearTimeout(handleScroll.fadeTimeout);
                    handleScroll.fadeTimeout = null;
                }
            }
            // Nếu đang cuộn lên
            else if (!isScrollingDown) {
                // Chỉ xóa class làm mờ khi đang ở trạng thái đã cuộn xuống trước đó
                if (handleScroll.isScrolledDown) {
                    // Xóa class làm mờ ngay lập tức
                    if (contactButtonsContainer) contactButtonsContainer.classList.remove('fade-buttons');
                    if (scrollTopContainer) scrollTopContainer.classList.remove('fade-buttons');

                    // Đặt lại trạng thái
                    handleScroll.isScrolledDown = false;
                }

                // Xóa timeout nếu có
                if (handleScroll.fadeTimeout) {
                    clearTimeout(handleScroll.fadeTimeout);
                    handleScroll.fadeTimeout = null;
                }
            }
        }
    } else {
        // Trên desktop, luôn hiển thị bình thường
        if (contactButtonsContainer) contactButtonsContainer.classList.remove('fade-buttons');
        if (scrollTopContainer) scrollTopContainer.classList.remove('fade-buttons');

        // Đặt lại trạng thái cuộn xuống
        handleScroll.isScrolledDown = false;
    }

    // Tối ưu hiệu suất bằng cách giới hạn số lần gọi hàm
    if (!handleScroll.ticking) {
        window.requestAnimationFrame(() => {
            handleScroll.ticking = false;
        });
        handleScroll.ticking = true;
    }
}

// Khởi tạo biến theo dõi ticking và trạng thái cuộn
handleScroll.ticking = false;
handleScroll.isScrolledDown = false;

/**
 * Thêm hiệu ứng nâng cao cho các nút liên hệ
 */
function addAdvancedEffects() {
    const contactButtons = document.querySelectorAll('.contact-buttons-container .contact-button');

    // Thêm hiệu ứng hover 3D cho các icon
    contactButtons.forEach(button => {
        button.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left; // Vị trí X của chuột trong nút
            const y = e.clientY - rect.top; // Vị trí Y của chuột trong nút

            // Tính toán góc nghiêng dựa trên vị trí chuột
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const angleX = (y - centerY) / 15; // Góc nghiêng theo trục X (giảm độ nghiêng)
            const angleY = (centerX - x) / 15; // Góc nghiêng theo trục Y (giảm độ nghiêng)

            // Di chuyển icon theo hướng ngược lại một chút để tạo hiệu ứng 3D
            const icon = this.querySelector('.contact-icon');
            if (icon) {
                icon.style.transform = `translateY(-5px) translateX(${-angleY}px) translateY(${-angleX}px) scale(1.1)`;
                icon.style.filter = `drop-shadow(0 8px 15px rgba(0, 0, 0, 0.25))`;
            }
        });

        // Khôi phục khi rời chuột
        button.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.contact-icon');
            if (icon) {
                icon.style.transform = '';
                icon.style.filter = '';
            }
        });
    });
}

/**
 * Theo dõi sự kiện click trên toàn trang để tạo hiệu ứng ripple
 */
document.addEventListener('click', function(event) {
    // Kiểm tra xem phần tử được click có phải là nút liên hệ không
    if (event.target.closest('.contact-button')) {
        const button = event.target.closest('.contact-button');

        // Tạo hiệu ứng ripple
        const ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');

        // Tính toán vị trí click
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height) * 2;
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        // Thiết lập vị trí và kích thước ripple
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;

        // Thêm ripple vào nút
        button.appendChild(ripple);

        // Xóa ripple sau khi hoàn thành hiệu ứng
        setTimeout(() => {
            ripple.remove();
        }, 800);
    }
});

// Gọi hàm thêm hiệu ứng nâng cao khi trang đã tải xong
document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra xem thiết bị có hỗ trợ hover không
    const hasHoverSupport = window.matchMedia('(hover: hover)').matches;

    // Chỉ thêm hiệu ứng nâng cao trên thiết bị hỗ trợ hover (desktop)
    if (hasHoverSupport) {
        addAdvancedEffects();
    }
});
