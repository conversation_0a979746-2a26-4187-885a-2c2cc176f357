/**
 * <PERSON><PERSON><PERSON><PERSON> pháp 2: Position Fixed với JS Positioning
 * JavaScript để quản lý vị trí của search suggestions với position fixed
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        searchInputSelector: '#main-search-input',
        suggestionsSelector: '#search-suggestions',
        showClass: 'show',
        loadingClass: 'loading',
        debounceDelay: 150,
        offsetY: 8, // Khoảng cách từ input xuống suggestions
        offsetX: 0, // Khoảng cách từ trái
        minWidth: 300, // Chiều rộng tối thiểu
        maxWidth: 600, // Chiều rộng tối đa
        maxHeight: 400, // Chiều cao tối đa
        viewportPadding: 16 // Padding từ viewport
    };

    // State management
    let isInitialized = false;
    let searchInput = null;
    let suggestionsElement = null;
    let debounceTimer = null;
    let resizeObserver = null;
    let isPositioning = false;

    /**
     * Initialize the solution
     */
    function init() {
        if (isInitialized) return;

        // Find elements
        searchInput = document.querySelector(CONFIG.searchInputSelector);
        suggestionsElement = document.querySelector(CONFIG.suggestionsSelector);

        if (!searchInput || !suggestionsElement) {
            console.warn('Search Fixed Solution 2: Required elements not found');
            return;
        }

        // Setup suggestions element
        setupSuggestionsElement();

        // Setup event listeners
        setupEventListeners();

        // Setup resize observer
        setupResizeObserver();

        isInitialized = true;
        console.log('Search Fixed Solution 2: Initialized successfully');
    }

    /**
     * Setup suggestions element with fixed positioning
     */
    function setupSuggestionsElement() {
        // Add solution-specific classes
        suggestionsElement.classList.add('search-suggestions-solution2');

        // Set initial position fixed
        suggestionsElement.style.position = 'fixed';
        suggestionsElement.style.zIndex = '9999';

        // Hide initially
        suggestionsElement.style.opacity = '0';
        suggestionsElement.style.visibility = 'hidden';
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Search input events
        searchInput.addEventListener('focus', handleSearchFocus);
        searchInput.addEventListener('blur', handleSearchBlur);
        searchInput.addEventListener('input', handleSearchInput);

        // Suggestions events
        suggestionsElement.addEventListener('mouseenter', handleSuggestionsMouseEnter);
        suggestionsElement.addEventListener('mouseleave', handleSuggestionsMouseLeave);

        // Global events
        document.addEventListener('click', handleGlobalClick);
        document.addEventListener('keydown', handleKeyDown);
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('resize', handleResize, { passive: true });
    }

    /**
     * Setup resize observer to watch for input size changes
     */
    function setupResizeObserver() {
        if (typeof ResizeObserver !== 'undefined') {
            resizeObserver = new ResizeObserver(function(entries) {
                if (suggestionsElement.classList.contains(CONFIG.showClass)) {
                    debounce(updatePosition, 50);
                }
            });
            resizeObserver.observe(searchInput);
        }
    }

    /**
     * Handle search input focus
     */
    function handleSearchFocus() {
        debounce(function() {
            if (shouldShowSuggestions()) {
                showSuggestions();
            }
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle search input blur
     */
    function handleSearchBlur() {
        debounce(function() {
            if (!isMouseOverSuggestions()) {
                hideSuggestions();
            }
        }, 200);
    }

    /**
     * Handle search input
     */
    function handleSearchInput() {
        debounce(function() {
            updatePosition();
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle suggestions mouse enter
     */
    function handleSuggestionsMouseEnter() {
        clearTimeout(debounceTimer);
    }

    /**
     * Handle suggestions mouse leave
     */
    function handleSuggestionsMouseLeave() {
        if (!searchInput.matches(':focus')) {
            debounce(hideSuggestions, 200);
        }
    }

    /**
     * Handle global click
     */
    function handleGlobalClick(event) {
        if (!searchInput.contains(event.target) &&
            !suggestionsElement.contains(event.target)) {
            hideSuggestions();
        }
    }

    /**
     * Handle keyboard events
     */
    function handleKeyDown(event) {
        if (event.key === 'Escape') {
            hideSuggestions();
            searchInput.blur();
        }
    }

    /**
     * Handle scroll events
     */
    function handleScroll() {
        if (suggestionsElement.classList.contains(CONFIG.showClass)) {
            debounce(updatePosition, 50);
        }
    }

    /**
     * Handle resize events
     */
    function handleResize() {
        if (suggestionsElement.classList.contains(CONFIG.showClass)) {
            debounce(updatePosition, 100);
        }
    }

    /**
     * Show suggestions with proper positioning
     */
    function showSuggestions() {
        if (isPositioning) return;

        // Update position first
        updatePosition();

        // Show suggestions
        suggestionsElement.classList.add(CONFIG.showClass);
        suggestionsElement.classList.remove('hidden');
        suggestionsElement.style.opacity = '1';
        suggestionsElement.style.visibility = 'visible';

        // Set ARIA attributes
        suggestionsElement.setAttribute('aria-hidden', 'false');
        searchInput.setAttribute('aria-expanded', 'true');
    }

    /**
     * Hide suggestions
     */
    function hideSuggestions() {
        suggestionsElement.classList.remove(CONFIG.showClass);
        suggestionsElement.classList.add('hidden');
        suggestionsElement.style.opacity = '0';
        suggestionsElement.style.visibility = 'hidden';

        // Set ARIA attributes
        suggestionsElement.setAttribute('aria-hidden', 'true');
        searchInput.setAttribute('aria-expanded', 'false');
    }

    /**
     * Update position of suggestions
     */
    function updatePosition() {
        if (!searchInput || !suggestionsElement || isPositioning) return;

        isPositioning = true;

        try {
            const inputRect = searchInput.getBoundingClientRect();
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            // Calculate position
            const position = calculateOptimalPosition(inputRect, viewport);

            // Apply position
            applyPosition(position);

        } catch (error) {
            console.error('Error updating position:', error);
        } finally {
            isPositioning = false;
        }
    }

    /**
     * Calculate optimal position for suggestions
     */
    function calculateOptimalPosition(inputRect, viewport) {
        // Base position
        let top = inputRect.bottom + CONFIG.offsetY;
        let left = inputRect.left + CONFIG.offsetX;
        let width = Math.max(inputRect.width, CONFIG.minWidth);
        let maxHeight = CONFIG.maxHeight;

        // Constrain width
        width = Math.min(width, CONFIG.maxWidth);
        width = Math.min(width, viewport.width - CONFIG.viewportPadding * 2);

        // Adjust horizontal position if needed
        if (left + width > viewport.width - CONFIG.viewportPadding) {
            left = viewport.width - width - CONFIG.viewportPadding;
        }
        if (left < CONFIG.viewportPadding) {
            left = CONFIG.viewportPadding;
            width = Math.min(width, viewport.width - CONFIG.viewportPadding * 2);
        }

        // Adjust vertical position if needed
        const availableSpaceBelow = viewport.height - top - CONFIG.viewportPadding;
        const availableSpaceAbove = inputRect.top - CONFIG.viewportPadding;

        if (availableSpaceBelow < CONFIG.maxHeight && availableSpaceAbove > availableSpaceBelow) {
            // Show above input
            top = inputRect.top - CONFIG.offsetY;
            maxHeight = Math.min(CONFIG.maxHeight, availableSpaceAbove);
            top = top - maxHeight;
        } else {
            // Show below input
            maxHeight = Math.min(CONFIG.maxHeight, availableSpaceBelow);
        }

        return {
            top: Math.max(CONFIG.viewportPadding, top),
            left: left,
            width: width,
            maxHeight: maxHeight
        };
    }

    /**
     * Apply calculated position to suggestions element
     */
    function applyPosition(position) {
        suggestionsElement.style.top = position.top + 'px';
        suggestionsElement.style.left = position.left + 'px';
        suggestionsElement.style.width = position.width + 'px';
        suggestionsElement.style.maxHeight = position.maxHeight + 'px';
    }

    /**
     * Check if suggestions should be shown
     */
    function shouldShowSuggestions() {
        return searchInput.value.trim().length > 0 || searchInput.matches(':focus');
    }

    /**
     * Check if mouse is over suggestions
     */
    function isMouseOverSuggestions() {
        return suggestionsElement.matches(':hover');
    }

    /**
     * Debounce function
     */
    function debounce(func, delay) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(func, delay);
    }

    /**
     * Cleanup function
     */
    function cleanup() {
        if (resizeObserver) {
            resizeObserver.disconnect();
        }
        clearTimeout(debounceTimer);
    }

    /**
     * Public API
     */
    window.SearchFixedSolution2 = {
        init: init,
        showSuggestions: showSuggestions,
        hideSuggestions: hideSuggestions,
        updatePosition: updatePosition,
        cleanup: cleanup
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);

})();