/**
 * Blog Table of Contents (TOC) - Nội Thất Bàng Vũ
 * 
 * Script tạo mục lục tự động cho bài viết blog dựa trên các thẻ heading (h2, h3, h4)
 */

document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra xem có phần tử mục lục không
    const tocContainer = document.getElementById('blog-toc-container');
    if (!tocContainer) return;

    // Kiểm tra xem có nội dung bài viết không
    const blogContent = document.querySelector('.blog-post-body');
    if (!blogContent) return;

    // Lấy tiêu đề mục lục
    const tocTitle = tocContainer.getAttribute('data-toc-title') || 'Mục lục';

    // Tìm tất cả các thẻ heading trong nội dung bài viết
    const headings = blogContent.querySelectorAll('h2, h3, h4');
    
    // <PERSON><PERSON><PERSON> không có heading nào, ẩn mục lục
    if (headings.length === 0) {
        tocContainer.style.display = 'none';
        return;
    }

    // Tạo cấu trúc mục lục
    const tocList = document.createElement('ul');
    tocList.className = 'blog-toc-list';

    // Biến để theo dõi cấp độ heading trước đó
    let previousLevel = 2;
    let currentList = tocList;
    let listStack = [tocList];

    // Duyệt qua từng heading và thêm vào mục lục
    headings.forEach((heading, index) => {
        // Lấy cấp độ heading (2 cho h2, 3 cho h3, 4 cho h4)
        const level = parseInt(heading.tagName.charAt(1));
        
        // Tạo ID cho heading nếu chưa có
        if (!heading.id) {
            heading.id = 'toc-heading-' + index;
        }
        
        // Tạo mục lục dựa trên cấp độ heading
        if (level > previousLevel) {
            // Nếu cấp độ lớn hơn, tạo danh sách con
            const nestedList = document.createElement('ul');
            nestedList.className = 'blog-toc-sublist';
            listStack[listStack.length - 1].lastChild.appendChild(nestedList);
            listStack.push(nestedList);
            currentList = nestedList;
        } else if (level < previousLevel) {
            // Nếu cấp độ nhỏ hơn, quay lại danh sách cha
            const stepsBack = previousLevel - level;
            for (let i = 0; i < stepsBack; i++) {
                if (listStack.length > 1) {
                    listStack.pop();
                }
            }
            currentList = listStack[listStack.length - 1];
        }
        
        // Tạo mục lục
        const listItem = document.createElement('li');
        listItem.className = 'blog-toc-item blog-toc-level-' + level;
        
        const link = document.createElement('a');
        link.href = '#' + heading.id;
        link.textContent = heading.textContent;
        link.className = 'blog-toc-link';
        
        // Thêm sự kiện click để cuộn mượt đến heading
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // Cuộn đến heading với hiệu ứng mượt
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Trừ đi 80px để tránh bị che bởi header
                    behavior: 'smooth'
                });
                
                // Cập nhật URL với hash mà không reload trang
                history.pushState(null, null, '#' + targetId);
            }
        });
        
        listItem.appendChild(link);
        currentList.appendChild(listItem);
        
        // Cập nhật cấp độ heading trước đó
        previousLevel = level;
    });

    // Tạo tiêu đề mục lục
    const tocTitleElement = document.createElement('h3');
    tocTitleElement.className = 'blog-toc-title';
    tocTitleElement.textContent = tocTitle;

    // Tạo nút đóng/mở mục lục trên mobile
    const tocToggle = document.createElement('button');
    tocToggle.className = 'blog-toc-toggle';
    tocToggle.innerHTML = '<i class="fas fa-bars"></i>';
    tocToggle.setAttribute('aria-label', 'Đóng/mở mục lục');
    
    tocToggle.addEventListener('click', function() {
        tocList.classList.toggle('active');
        this.classList.toggle('active');
    });

    // Thêm các phần tử vào container
    tocContainer.appendChild(tocTitleElement);
    tocContainer.appendChild(tocToggle);
    tocContainer.appendChild(tocList);
    
    // Hiển thị mục lục
    tocContainer.classList.add('active');

    // Thêm thanh tiến độ đọc
    addReadingProgressBar();
    
    // Highlight mục lục khi cuộn
    highlightTocOnScroll();
});

/**
 * Thêm thanh tiến độ đọc
 */
function addReadingProgressBar() {
    // Tạo thanh tiến độ
    const progressBar = document.createElement('div');
    progressBar.className = 'blog-reading-progress';
    document.body.appendChild(progressBar);
    
    // Cập nhật tiến độ khi cuộn
    window.addEventListener('scroll', function() {
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        
        // Tính toán phần trăm đã đọc
        const scrollPercent = (scrollTop / (documentHeight - windowHeight)) * 100;
        
        // Cập nhật chiều rộng thanh tiến độ
        progressBar.style.width = scrollPercent + '%';
    });
}

/**
 * Highlight mục lục khi cuộn
 */
function highlightTocOnScroll() {
    const tocLinks = document.querySelectorAll('.blog-toc-link');
    if (tocLinks.length === 0) return;
    
    // Lấy tất cả các heading
    const headings = Array.from(document.querySelectorAll('.blog-post-body h2, .blog-post-body h3, .blog-post-body h4'));
    
    // Thêm sự kiện cuộn
    window.addEventListener('scroll', function() {
        // Vị trí cuộn hiện tại + offset
        const scrollPosition = window.scrollY + 100;
        
        // Tìm heading hiện tại
        let currentHeading = null;
        
        for (let i = 0; i < headings.length; i++) {
            if (headings[i].offsetTop <= scrollPosition) {
                currentHeading = headings[i];
            } else {
                break;
            }
        }
        
        // Xóa active class từ tất cả các liên kết
        tocLinks.forEach(link => {
            link.classList.remove('active');
        });
        
        // Thêm active class cho liên kết hiện tại
        if (currentHeading) {
            const currentLink = document.querySelector(`.blog-toc-link[href="#${currentHeading.id}"]`);
            if (currentLink) {
                currentLink.classList.add('active');
            }
        }
    });
}
