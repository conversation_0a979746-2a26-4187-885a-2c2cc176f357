/**
 * G<PERSON><PERSON>i pháp 4: Portal Pattern
 * JavaScript để quản lý search suggestions với portal pattern
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        searchInputSelector: '#main-search-input',
        portalContainerId: 'search-portal-container',
        suggestionsId: 'search-suggestions-portal',
        showClass: 'show',
        loadingClass: 'loading',
        backdropClass: 'with-backdrop',
        debounceDelay: 150,
        offsetY: 8,
        offsetX: 0,
        minWidth: 300,
        maxWidth: 600,
        maxHeight: 400,
        viewportPadding: 16,
        animationType: 'default' // 'default', 'fade-in', 'slide-up'
    };

    // State management
    let isInitialized = false;
    let searchInput = null;
    let portalContainer = null;
    let suggestionsElement = null;
    let debounceTimer = null;
    let resizeObserver = null;
    let isPositioning = false;
    let currentSuggestions = [];

    /**
     * Initialize the portal solution
     */
    function init() {
        if (isInitialized) return;

        // Find search input
        searchInput = document.querySelector(CONFIG.searchInputSelector);
        if (!searchInput) {
            console.warn('Search Portal Solution 4: Search input not found');
            return;
        }

        // Create portal container
        createPortalContainer();

        // Setup event listeners
        setupEventListeners();

        // Setup resize observer
        setupResizeObserver();

        isInitialized = true;
        console.log('Search Portal Solution 4: Initialized successfully');
    }

    /**
     * Create portal container and append to body
     */
    function createPortalContainer() {
        // Remove existing portal if any
        const existingPortal = document.getElementById(CONFIG.portalContainerId);
        if (existingPortal) {
            existingPortal.remove();
        }

        // Create new portal container
        portalContainer = document.createElement('div');
        portalContainer.id = CONFIG.portalContainerId;
        portalContainer.className = 'search-portal-container';
        portalContainer.style.display = 'none';

        // Add to body
        document.body.appendChild(portalContainer);

        console.log('Portal container created and added to body');
    }

    /**
     * Create suggestions element inside portal
     */
    function createSuggestionsElement() {
        if (suggestionsElement) {
            suggestionsElement.remove();
        }

        suggestionsElement = document.createElement('div');
        suggestionsElement.id = CONFIG.suggestionsId;
        suggestionsElement.className = 'search-suggestions-portal';
        suggestionsElement.setAttribute('role', 'listbox');
        suggestionsElement.setAttribute('aria-hidden', 'true');

        // Add animation class if specified
        if (CONFIG.animationType !== 'default') {
            suggestionsElement.classList.add(CONFIG.animationType);
        }

        portalContainer.appendChild(suggestionsElement);

        return suggestionsElement;
    }

    /**
     * Setup event listeners
     */
    function setupEventListeners() {
        // Search input events
        searchInput.addEventListener('focus', handleSearchFocus);
        searchInput.addEventListener('blur', handleSearchBlur);
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('keydown', handleSearchKeyDown);

        // Global events
        document.addEventListener('click', handleGlobalClick);
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('resize', handleResize, { passive: true });

        // Portal container events
        portalContainer.addEventListener('click', handlePortalClick);
    }

    /**
     * Setup resize observer
     */
    function setupResizeObserver() {
        if (typeof ResizeObserver !== 'undefined') {
            resizeObserver = new ResizeObserver(function(entries) {
                if (suggestionsElement && suggestionsElement.classList.contains(CONFIG.showClass)) {
                    debounce(updatePosition, 50);
                }
            });
            resizeObserver.observe(searchInput);
        }
    }

    /**
     * Handle search input focus
     */
    function handleSearchFocus() {
        debounce(function() {
            if (shouldShowSuggestions()) {
                showSuggestions();
            }
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle search input blur
     */
    function handleSearchBlur() {
        debounce(function() {
            if (!isMouseOverPortal()) {
                hideSuggestions();
            }
        }, 200);
    }

    /**
     * Handle search input
     */
    function handleSearchInput() {
        debounce(function() {
            if (shouldShowSuggestions()) {
                updateSuggestions();
            } else {
                hideSuggestions();
            }
        }, CONFIG.debounceDelay);
    }

    /**
     * Handle search input keydown
     */
    function handleSearchKeyDown(event) {
        if (!suggestionsElement || !suggestionsElement.classList.contains(CONFIG.showClass)) {
            return;
        }

        switch (event.key) {
            case 'Escape':
                hideSuggestions();
                searchInput.blur();
                event.preventDefault();
                break;
            case 'ArrowDown':
                navigateSuggestions('down');
                event.preventDefault();
                break;
            case 'ArrowUp':
                navigateSuggestions('up');
                event.preventDefault();
                break;
            case 'Enter':
                selectCurrentSuggestion();
                event.preventDefault();
                break;
        }
    }

    /**
     * Handle global click
     */
    function handleGlobalClick(event) {
        if (!searchInput.contains(event.target) &&
            !portalContainer.contains(event.target)) {
            hideSuggestions();
        }
    }

    /**
     * Handle portal click
     */
    function handlePortalClick(event) {
        // If clicking on backdrop (not suggestions), hide suggestions
        if (event.target === portalContainer) {
            hideSuggestions();
        }
    }

    /**
     * Handle scroll events
     */
    function handleScroll() {
        if (suggestionsElement && suggestionsElement.classList.contains(CONFIG.showClass)) {
            debounce(updatePosition, 50);
        }
    }

    /**
     * Handle resize events
     */
    function handleResize() {
        if (suggestionsElement && suggestionsElement.classList.contains(CONFIG.showClass)) {
            debounce(updatePosition, 100);
        }
    }

    /**
     * Show suggestions in portal
     */
    function showSuggestions() {
        if (!portalContainer) return;

        // Create suggestions element if not exists
        if (!suggestionsElement) {
            createSuggestionsElement();
        }

        // Show portal container
        portalContainer.style.display = 'block';

        // Update position
        updatePosition();

        // Load suggestions
        loadSuggestions();

        // Show suggestions with animation
        setTimeout(() => {
            if (suggestionsElement) {
                suggestionsElement.classList.add(CONFIG.showClass);
                suggestionsElement.setAttribute('aria-hidden', 'false');
                searchInput.setAttribute('aria-expanded', 'true');
            }
        }, 10);
    }

    /**
     * Hide suggestions
     */
    function hideSuggestions() {
        if (!suggestionsElement || !portalContainer) return;

        suggestionsElement.classList.remove(CONFIG.showClass);
        suggestionsElement.setAttribute('aria-hidden', 'true');
        searchInput.setAttribute('aria-expanded', 'false');

        // Hide portal container after animation
        setTimeout(() => {
            if (portalContainer) {
                portalContainer.style.display = 'none';
            }
        }, 250);
    }

    /**
     * Update position of suggestions in portal
     */
    function updatePosition() {
        if (!searchInput || !suggestionsElement || isPositioning) return;

        isPositioning = true;

        try {
            const inputRect = searchInput.getBoundingClientRect();
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            // Calculate optimal position
            const position = calculateOptimalPosition(inputRect, viewport);

            // Apply position
            applyPosition(position);

        } catch (error) {
            console.error('Error updating portal position:', error);
        } finally {
            isPositioning = false;
        }
    }

    /**
     * Calculate optimal position for suggestions
     */
    function calculateOptimalPosition(inputRect, viewport) {
        let top = inputRect.bottom + CONFIG.offsetY;
        let left = inputRect.left + CONFIG.offsetX;
        let width = Math.max(inputRect.width, CONFIG.minWidth);
        let maxHeight = CONFIG.maxHeight;

        // Constrain width
        width = Math.min(width, CONFIG.maxWidth);
        width = Math.min(width, viewport.width - CONFIG.viewportPadding * 2);

        // Adjust horizontal position
        if (left + width > viewport.width - CONFIG.viewportPadding) {
            left = viewport.width - width - CONFIG.viewportPadding;
        }
        if (left < CONFIG.viewportPadding) {
            left = CONFIG.viewportPadding;
            width = Math.min(width, viewport.width - CONFIG.viewportPadding * 2);
        }

        // Adjust vertical position
        const availableSpaceBelow = viewport.height - top - CONFIG.viewportPadding;
        const availableSpaceAbove = inputRect.top - CONFIG.viewportPadding;

        if (availableSpaceBelow < CONFIG.maxHeight && availableSpaceAbove > availableSpaceBelow) {
            // Show above input
            top = inputRect.top - CONFIG.offsetY;
            maxHeight = Math.min(CONFIG.maxHeight, availableSpaceAbove);
            top = top - maxHeight;
        } else {
            // Show below input
            maxHeight = Math.min(CONFIG.maxHeight, availableSpaceBelow);
        }

        return {
            top: Math.max(CONFIG.viewportPadding, top),
            left: left,
            width: width,
            maxHeight: maxHeight
        };
    }

    /**
     * Apply position to suggestions element
     */
    function applyPosition(position) {
        suggestionsElement.style.top = position.top + 'px';
        suggestionsElement.style.left = position.left + 'px';
        suggestionsElement.style.width = position.width + 'px';
        suggestionsElement.style.maxHeight = position.maxHeight + 'px';
    }

    /**
     * Load and render suggestions
     */
    function loadSuggestions() {
        if (!suggestionsElement) return;

        const query = searchInput.value.trim();

        // Show loading state
        showLoadingState();

        // Simulate API call (replace with actual search logic)
        setTimeout(() => {
            const mockSuggestions = generateMockSuggestions(query);
            renderSuggestions(mockSuggestions);
        }, 300);
    }

    /**
     * Update suggestions (called on input change)
     */
    function updateSuggestions() {
        if (suggestionsElement && suggestionsElement.classList.contains(CONFIG.showClass)) {
            loadSuggestions();
        } else {
            showSuggestions();
        }
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        if (!suggestionsElement) return;

        suggestionsElement.classList.add(CONFIG.loadingClass);
        suggestionsElement.innerHTML = `
            <div class="search-loading-item-portal">
                <div class="search-loading-skeleton-portal search-loading-icon-portal"></div>
                <div class="search-loading-skeleton-portal search-loading-text-portal"></div>
            </div>
            <div class="search-loading-item-portal">
                <div class="search-loading-skeleton-portal search-loading-icon-portal"></div>
                <div class="search-loading-skeleton-portal search-loading-text-portal"></div>
            </div>
            <div class="search-loading-item-portal">
                <div class="search-loading-skeleton-portal search-loading-icon-portal"></div>
                <div class="search-loading-skeleton-portal search-loading-text-portal"></div>
            </div>
        `;
    }

    /**
     * Render suggestions
     */
    function renderSuggestions(suggestions) {
        if (!suggestionsElement) return;

        suggestionsElement.classList.remove(CONFIG.loadingClass);
        currentSuggestions = suggestions;

        if (suggestions.length === 0) {
            suggestionsElement.innerHTML = `
                <div class="search-suggestion-item-portal">
                    <div class="search-suggestion-icon-portal">🔍</div>
                    <div class="search-suggestion-text-portal">Không tìm thấy kết quả</div>
                </div>
            `;
            return;
        }

        const html = suggestions.map((suggestion, index) => `
            <div class="search-suggestion-item-portal"
                 role="option"
                 data-index="${index}"
                 tabindex="-1">
                <div class="search-suggestion-icon-portal">${suggestion.icon}</div>
                <div class="search-suggestion-text-portal">${suggestion.text}</div>
                <div class="search-suggestion-category-portal">${suggestion.category}</div>
            </div>
        `).join('');

        suggestionsElement.innerHTML = html;

        // Add click listeners
        const items = suggestionsElement.querySelectorAll('.search-suggestion-item-portal');
        items.forEach((item, index) => {
            item.addEventListener('click', () => selectSuggestion(index));
            item.addEventListener('mouseenter', () => highlightSuggestion(index));
        });
    }

    /**
     * Generate mock suggestions (replace with actual search logic)
     */
    function generateMockSuggestions(query) {
        if (!query) return [];

        const mockData = [
            { icon: '🛋️', text: 'Sofa da thật cao cấp', category: 'Ghế sofa' },
            { icon: '🪑', text: 'Ghế ăn gỗ sồi', category: 'Ghế ăn' },
            { icon: '🛏️', text: 'Giường ngủ hiện đại', category: 'Giường ngủ' },
            { icon: '📚', text: 'Tủ sách gỗ tự nhiên', category: 'Tủ kệ' },
            { icon: '🍽️', text: 'Bàn ăn gia đình', category: 'Bàn ăn' }
        ];

        return mockData.filter(item =>
            item.text.toLowerCase().includes(query.toLowerCase())
        );
    }

    /**
     * Navigate suggestions with keyboard
     */
    function navigateSuggestions(direction) {
        const items = suggestionsElement.querySelectorAll('.search-suggestion-item-portal');
        if (items.length === 0) return;

        const currentIndex = Array.from(items).findIndex(item =>
            item.getAttribute('aria-selected') === 'true'
        );

        let newIndex;
        if (direction === 'down') {
            newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        } else {
            newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        }

        highlightSuggestion(newIndex);
    }

    /**
     * Highlight suggestion
     */
    function highlightSuggestion(index) {
        const items = suggestionsElement.querySelectorAll('.search-suggestion-item-portal');
        items.forEach((item, i) => {
            if (i === index) {
                item.setAttribute('aria-selected', 'true');
                item.focus();
            } else {
                item.setAttribute('aria-selected', 'false');
            }
        });
    }

    /**
     * Select current highlighted suggestion
     */
    function selectCurrentSuggestion() {
        const selectedItem = suggestionsElement.querySelector('[aria-selected="true"]');
        if (selectedItem) {
            const index = parseInt(selectedItem.getAttribute('data-index'));
            selectSuggestion(index);
        }
    }

    /**
     * Select suggestion by index
     */
    function selectSuggestion(index) {
        if (currentSuggestions[index]) {
            const suggestion = currentSuggestions[index];
            searchInput.value = suggestion.text;
            hideSuggestions();

            // Trigger custom event
            const event = new CustomEvent('suggestionSelected', {
                detail: { suggestion, index }
            });
            searchInput.dispatchEvent(event);
        }
    }

    /**
     * Check if suggestions should be shown
     */
    function shouldShowSuggestions() {
        return searchInput.value.trim().length > 0;
    }

    /**
     * Check if mouse is over portal
     */
    function isMouseOverPortal() {
        return portalContainer && portalContainer.matches(':hover');
    }

    /**
     * Debounce function
     */
    function debounce(func, delay) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(func, delay);
    }

    /**
     * Cleanup function
     */
    function cleanup() {
        if (resizeObserver) {
            resizeObserver.disconnect();
        }
        if (portalContainer) {
            portalContainer.remove();
        }
        clearTimeout(debounceTimer);
    }

    /**
     * Public API
     */
    window.SearchPortalSolution4 = {
        init: init,
        showSuggestions: showSuggestions,
        hideSuggestions: hideSuggestions,
        updatePosition: updatePosition,
        cleanup: cleanup,
        setAnimationType: function(type) {
            CONFIG.animationType = type;
        },
        enableBackdrop: function() {
            if (portalContainer) {
                portalContainer.classList.add(CONFIG.backdropClass);
            }
        },
        disableBackdrop: function() {
            if (portalContainer) {
                portalContainer.classList.remove(CONFIG.backdropClass);
            }
        }
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);

})();