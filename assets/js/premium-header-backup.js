/**
 * BACKUP FILE - Premium Header JavaScript for Nội Thất Bàng Vũ
 * Created: 2025-07-27
 * Original file before smooth animation improvements
 * This is a backup copy of the original premium-header.js
 */

/**
 * Premium Header JavaScript for Nội Thất Bàng Vũ
 * Handles interactive features of the premium header
 */

document.addEventListener('DOMContentLoaded', function () {
  // Tablet Navigation Touch Scroll
  const navMenu = document.querySelector('.nav-menu');
  if (navMenu) {
    let isDown = false;
    let startX;
    let scrollLeft;

    // Mouse events
    navMenu.addEventListener('mousedown', (e) => {
      isDown = true;
      startX = e.pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
      navMenu.style.cursor = 'grabbing';
    });

    navMenu.addEventListener('mouseleave', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mouseup', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });

    // Touch events for mobile/tablet
    navMenu.addEventListener('touchstart', (e) => {
      startX = e.touches[0].pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
    });

    navMenu.addEventListener('touchmove', (e) => {
      const x = e.touches[0].pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });
  }

  // Mobile menu toggle
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileMenu = document.querySelector('.mobile-menu');
  const mobileMenuClose = document.querySelector('.mobile-menu-close');
  const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

  if (mobileMenuToggle && mobileMenu) {
    mobileMenuToggle.addEventListener('click', function () {
      mobileMenu.classList.add('active');
      document.body.classList.add('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.add('active');
      }
    });
  }

  if (mobileMenuClose && mobileMenu) {
    mobileMenuClose.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      if (mobileMenuOverlay) {
        mobileMenuOverlay.classList.remove('active');
      }
    });
  }

  if (mobileMenuOverlay) {
    mobileMenuOverlay.addEventListener('click', function () {
      mobileMenu.classList.remove('active');
      document.body.classList.remove('overflow-hidden');
      this.classList.remove('active');
    });
  }

  // Mobile dropdown toggles
  const mobileDropdownToggles = document.querySelectorAll(
    '.mobile-dropdown-toggle'
  );

  mobileDropdownToggles.forEach(function (toggle) {
    toggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.parentElement;
      const submenu = parent.querySelector('.mobile-submenu');

      if (parent.classList.contains('active')) {
        parent.classList.remove('active');
        submenu.style.maxHeight = '0px';
        this.querySelector('i').classList.remove('fa-chevron-up');
        this.querySelector('i').classList.add('fa-chevron-down');
      } else {
        parent.classList.add('active');
        submenu.style.maxHeight = submenu.scrollHeight + 'px';
        this.querySelector('i').classList.remove('fa-chevron-down');
        this.querySelector('i').classList.add('fa-chevron-up');
      }
    });
  });

  // Sticky header behavior
  const header = document.querySelector('.premium-header');
  const topBar = document.querySelector('.top-bar');
  const logoImage = document.querySelector('.premium-logo-image img');
  const originalLogoSrc = logoImage ? logoImage.src : '';
  const darkLogoSrc = BASE_URL + '/assets/images/logo/logo-chu-trang.svg';
  let lastScrollTop = 0;
  let scrollTimer;
  let ticking = false;
  let lastScrollUpdate = 0; // Thời điểm cập nhật cuối cùng
  const scrollThreshold = 10; // Ngưỡng để thêm/xóa class scrolled
  const throttleDelay = 10; // Thời gian tối thiểu giữa các lần cập nhật (ms)

  if (header) {
    // Đảm bảo header có z-index cao
    header.style.zIndex = '1020';

    // Thêm class để kích hoạt hiệu ứng transition mượt mà
    header.classList.add('smooth-transition');

    // Sử dụng requestAnimationFrame để tối ưu hiệu suất
    window.addEventListener('scroll', function () {
      const now = Date.now();

      // Throttle: chỉ xử lý nếu đã qua đủ thời gian từ lần cập nhật trước
      if (now - lastScrollUpdate > throttleDelay) {
        lastScrollUpdate = now;

        if (!ticking) {
          window.requestAnimationFrame(function () {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollProgress = Math.min(1, scrollTop / (scrollThreshold * 2));

            // Thay vì thêm/xóa class ngay lập tức, áp dụng style trực tiếp để có hiệu ứng mượt mà
            if (scrollTop > 0) {
              // Áp dụng hiệu ứng mờ dần khi cuộn xuống
              const opacity = Math.min(scrollProgress * 2, 1);
              const scale = 0.98 + (0.02 * opacity);
              const translateY = Math.min(scrollProgress * 2, 1) * -2;
              // Thêm class scrolled khi vượt qua ngưỡng
              if (scrollTop > scrollThreshold) {
                if (!header.classList.contains('scrolled')) {
                  header.classList.add('scrolled');
                  if (logoImage) logoImage.src = darkLogoSrc;
                }
              } else {
                if (header.classList.contains('scrolled')) {
                  header.classList.remove('scrolled');
                  if (logoImage) logoImage.src = originalLogoSrc;
                }
              }

              // Áp dụng hiệu ứng transform mượt mà - Tạm thời bỏ qua để đảm bảo sticky header hoạt động
              header.style.setProperty('--header-opacity', opacity);
              // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
              // header.style.setProperty('--header-scale', scale);
              // header.style.setProperty('--header-translate-y', `${translateY}px`);
            } else {
              // Reset về trạng thái ban đầu khi ở đầu trang
              header.classList.remove('scrolled');
              if (logoImage) logoImage.src = originalLogoSrc;
              header.style.setProperty('--header-opacity', 0);
              // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
              // header.style.setProperty('--header-scale', 1);
              // header.style.setProperty('--header-translate-y', '0px');
            }

            // Add compact mode when scrolling down more
            if (scrollTop > 200) {
              header.classList.add('compact');
            } else {
              header.classList.remove('compact');
            }

            // Hide/show top bar based on scroll direction with smoother transition
            if (topBar) {
              if (scrollTop > lastScrollTop && scrollTop > topBar.offsetHeight) {
                // Scrolling down - hide top bar
                topBar.style.transform = 'translateY(-100%)';
              } else {
                // Scrolling up - show top bar
                topBar.style.transform = 'translateY(0)';
              }
            }

            // Add a subtle animation when user stops scrolling
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(function () {
              header.classList.add('scroll-pause');

              // Remove the class after animation completes
              setTimeout(function () {
                header.classList.remove('scroll-pause');
              }, 300);
            }, 150);

            lastScrollTop = scrollTop;
            ticking = false;
          });

          ticking = true;
        }
      }
    }, { passive: true }); // Thêm passive: true để cải thiện hiệu suất

    // Kiểm tra trạng thái ban đầu khi trang tải
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (initialScrollTop > scrollThreshold) {
      header.classList.add('scrolled');
      if (logoImage) logoImage.src = darkLogoSrc;

      // Thiết lập các biến CSS cho hiệu ứng mượt mà
      const scrollProgress = Math.min(1, initialScrollTop / (scrollThreshold * 2));
      const opacity = Math.min(scrollProgress * 2, 1);
      const scale = 0.98 + (0.02 * opacity);
      const translateY = Math.min(scrollProgress * 2, 1) * -2;

      header.style.setProperty('--header-opacity', opacity);
      // Không áp dụng scale và translateY để tránh ảnh hưởng đến sticky header
      // header.style.setProperty('--header-scale', scale);
      // header.style.setProperty('--header-translate-y', `${translateY}px`);
    }
  }
});

// END OF BACKUP FILE
