/**
 * Premium Header Optimized JavaScript for Nội Thất Bàng Vũ
 * Unified smooth scroll animations and interactions
 * Created: 2025-07-27
 *
 * Features:
 * - Unified scroll handling with single requestAnimation<PERSON>rame loop
 * - Smooth transitions with consistent timing
 * - Hardware acceleration optimizations
 * - Proper debouncing and hysteresis
 */

document.addEventListener('DOMContentLoaded', function () {
  // ===== CONFIGURATION =====
  const CONFIG = {
    // Scroll thresholds
    SCROLL_THRESHOLD: 10,
    SCROLL_HYSTERESIS: 5,
    COMPACT_THRESHOLD: 200,

    // Timing
    THROTTLE_DELAY: 16, // ~60fps
    SCROLL_STOP_DELAY: 150,
    TRANSITION_DURATION: 400, // ms

    // Easing
    EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // easeOutQuad

    // Animation
    ENABLE_HARDWARE_ACCELERATION: true,
    ENABLE_LOGO_TRANSITION: true,
    E<PERSON><PERSON>E_SCALE_EFFECT: true
  };

  // ===== ELEMENT REFERENCES =====
  const elements = {
    header: document.querySelector('.premium-header'),
    topBar: document.querySelector('.top-bar'),
    midHeader: document.querySelector('.mid-header-container'),
    bottomHeader: document.querySelector('.bottom-header-container'),
    logoImage: document.querySelector('.premium-logo-image img'),
    searchInput: document.querySelector('.search-input'),
    searchForm: document.querySelector('.search-form'),
    navMenu: document.querySelector('.nav-menu')
  };

  // ===== STATE MANAGEMENT =====
  const state = {
    lastScrollTop: 0,
    isScrollingDown: false,
    isScrolled: false,
    isCompact: false,
    ticking: false,
    lastScrollUpdate: 0,
    scrollTimer: null,

    // Logo sources
    originalLogoSrc: elements.logoImage ? elements.logoImage.src : '',
    darkLogoSrc: BASE_URL + '/assets/images/logo/logo-chu-trang.svg'
  };

  // ===== INITIALIZATION =====
  function init() {
    if (!elements.header) return;

    // Setup header for smooth transitions
    setupHeader();

    // Initialize scroll handling
    initScrollHandling();

    // Initialize other features
    initSearchEffects();
    initTabletNavigation();
    initMobileMenu();
    initTabletDropdowns();

    // Check initial scroll position
    checkInitialScrollPosition();
  }

  // ===== HEADER SETUP =====
  function setupHeader() {
    const { header } = elements;

    // Ensure high z-index
    header.style.zIndex = '1020';

    // Add smooth transition class
    header.classList.add('smooth-transition');

    // Setup CSS custom properties for smooth animations
    if (CONFIG.ENABLE_HARDWARE_ACCELERATION) {
      header.style.willChange = 'transform, background-color, box-shadow';
      header.style.transform = 'translateZ(0)'; // Force hardware acceleration
    }
  }

  // ===== SCROLL HANDLING =====
  function initScrollHandling() {
    window.addEventListener('scroll', handleScroll, { passive: true });
  }

  function handleScroll() {
    const now = performance.now();

    // Throttle scroll events
    if (now - state.lastScrollUpdate < CONFIG.THROTTLE_DELAY) {
      return;
    }

    state.lastScrollUpdate = now;

    if (!state.ticking) {
      requestAnimationFrame(updateScrollEffects);
      state.ticking = true;
    }

    // Handle scroll stop detection
    clearTimeout(state.scrollTimer);
    state.scrollTimer = setTimeout(handleScrollStop, CONFIG.SCROLL_STOP_DELAY);
  }

  function updateScrollEffects() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollDelta = scrollTop - state.lastScrollTop;

    // Update scroll direction
    state.isScrollingDown = scrollDelta > 0;

    // Apply scroll effects
    updateHeaderState(scrollTop);
    updateTopBarVisibility(scrollTop);
    updateCompactMode(scrollTop);

    // Update state
    state.lastScrollTop = scrollTop;
    state.ticking = false;
  }

  function updateHeaderState(scrollTop) {
    const { header, logoImage } = elements;
    const { SCROLL_THRESHOLD, SCROLL_HYSTERESIS } = CONFIG;

    const shouldBeScrolled = scrollTop > SCROLL_THRESHOLD;
    const shouldBeUnscrolled = scrollTop < (SCROLL_THRESHOLD - SCROLL_HYSTERESIS);

    // Handle scrolled state with hysteresis
    if (shouldBeScrolled && !state.isScrolled) {
      state.isScrolled = true;
      header.classList.add('scrolled');

      // Smooth logo transition
      if (CONFIG.ENABLE_LOGO_TRANSITION && logoImage) {
        transitionLogo(state.darkLogoSrc);
      }

    } else if (shouldBeUnscrolled && state.isScrolled) {
      state.isScrolled = false;
      header.classList.remove('scrolled');

      // Smooth logo transition back
      if (CONFIG.ENABLE_LOGO_TRANSITION && logoImage) {
        transitionLogo(state.originalLogoSrc);
      }
    }

    // Apply scale effect if enabled
    if (CONFIG.ENABLE_SCALE_EFFECT && scrollTop > 0) {
      const scrollProgress = Math.min(1, scrollTop / (SCROLL_THRESHOLD * 2));
      const scale = 0.98 + (0.02 * (1 - scrollProgress * 0.5));
      header.style.setProperty('--header-scale', scale.toFixed(4));
    } else {
      header.style.setProperty('--header-scale', '1');
    }
  }

  function updateTopBarVisibility(scrollTop) {
    const { topBar } = elements;
    if (!topBar) return;

    const shouldHide = state.isScrollingDown && scrollTop > topBar.offsetHeight;

    if (shouldHide) {
      topBar.style.transform = 'translateY(-100%)';
    } else {
      topBar.style.transform = 'translateY(0)';
    }
  }

  function updateCompactMode(scrollTop) {
    const { header } = elements;
    const shouldBeCompact = scrollTop > CONFIG.COMPACT_THRESHOLD;

    if (shouldBeCompact && !state.isCompact) {
      state.isCompact = true;
      header.classList.add('compact');
    } else if (!shouldBeCompact && state.isCompact) {
      state.isCompact = false;
      header.classList.remove('compact');
    }
  }

  function handleScrollStop() {
    const { header } = elements;

    // Add scroll pause effect
    header.classList.add('scroll-pause');

    // Remove after animation completes
    setTimeout(() => {
      header.classList.remove('scroll-pause');
    }, CONFIG.TRANSITION_DURATION);
  }

  // ===== LOGO TRANSITION =====
  function transitionLogo(newSrc) {
    const { logoImage } = elements;
    if (!logoImage || logoImage.src === newSrc) return;

    // Create smooth transition effect
    logoImage.style.transition = `opacity ${CONFIG.TRANSITION_DURATION * 0.5}ms ${CONFIG.EASING}`;
    logoImage.style.opacity = '0';

    setTimeout(() => {
      logoImage.src = newSrc;
      logoImage.style.opacity = '1';

      // Clean up transition
      setTimeout(() => {
        logoImage.style.transition = '';
      }, CONFIG.TRANSITION_DURATION * 0.5);
    }, CONFIG.TRANSITION_DURATION * 0.25);
  }

  // ===== SEARCH EFFECTS =====
  function initSearchEffects() {
    const { searchInput, searchForm } = elements;
    if (!searchInput || !searchForm) return;

    searchInput.addEventListener('focus', function () {
      searchForm.classList.add('focused');

      // Add ripple effect
      const ripple = document.createElement('span');
      ripple.classList.add('search-ripple');
      searchForm.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });

    searchInput.addEventListener('blur', function () {
      searchForm.classList.remove('focused');
    });
  }

  // ===== TABLET NAVIGATION =====
  function initTabletNavigation() {
    const { navMenu } = elements;
    if (!navMenu) return;

    let isDown = false;
    let startX;
    let scrollLeft;

    // Mouse events
    navMenu.addEventListener('mousedown', (e) => {
      isDown = true;
      startX = e.pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
      navMenu.style.cursor = 'grabbing';
    });

    navMenu.addEventListener('mouseleave', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mouseup', () => {
      isDown = false;
      navMenu.style.cursor = 'grab';
    });

    navMenu.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });

    // Touch events
    navMenu.addEventListener('touchstart', (e) => {
      startX = e.touches[0].pageX - navMenu.offsetLeft;
      scrollLeft = navMenu.scrollLeft;
    });

    navMenu.addEventListener('touchmove', (e) => {
      const x = e.touches[0].pageX - navMenu.offsetLeft;
      const walk = (x - startX) * 2;
      navMenu.scrollLeft = scrollLeft - walk;
    });
  }

  // ===== MOBILE MENU =====
  function initMobileMenu() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');

    if (mobileMenuToggle && mobileMenu) {
      mobileMenuToggle.addEventListener('click', function () {
        mobileMenu.classList.add('active');
        document.body.classList.add('overflow-hidden');
        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.add('active');
        }
      });
    }

    if (mobileMenuClose && mobileMenu) {
      mobileMenuClose.addEventListener('click', function () {
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
        if (mobileMenuOverlay) {
          mobileMenuOverlay.classList.remove('active');
        }
      });
    }

    if (mobileMenuOverlay) {
      mobileMenuOverlay.addEventListener('click', function () {
        mobileMenu.classList.remove('active');
        document.body.classList.remove('overflow-hidden');
        this.classList.remove('active');
      });
    }

    // Mobile dropdown toggles
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    mobileDropdownToggles.forEach(function (toggle) {
      toggle.addEventListener('click', function (e) {
        e.preventDefault();
        const parent = this.parentElement;
        const submenu = parent.querySelector('.mobile-submenu');

        if (parent.classList.contains('active')) {
          parent.classList.remove('active');
          submenu.style.maxHeight = '0px';
          this.querySelector('i').classList.remove('fa-chevron-up');
          this.querySelector('i').classList.add('fa-chevron-down');
        } else {
          parent.classList.add('active');
          submenu.style.maxHeight = submenu.scrollHeight + 'px';
          this.querySelector('i').classList.remove('fa-chevron-down');
          this.querySelector('i').classList.add('fa-chevron-up');
        }
      });
    });
  }

  // ===== TABLET DROPDOWNS =====
  function initTabletDropdowns() {
    const userDropdownToggle = document.querySelector('.user-dropdown .action-btn');
    const userDropdownMenu = document.querySelector('.user-dropdown-menu');
    const cartBtn = document.querySelector('.cart-btn');
    const miniCart = document.querySelector('.mini-cart');

    function isTablet() {
      return window.innerWidth >= 768 && window.innerWidth <= 1024;
    }

    // User dropdown
    if (userDropdownToggle && userDropdownMenu) {
      userDropdownToggle.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation();

          const isActive = userDropdownMenu.classList.contains('active');
          const userDropdown = userDropdownToggle.closest('.user-dropdown');

          // Close all dropdowns
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle current dropdown
          if (!isActive) {
            userDropdownMenu.classList.add('active');
            userDropdown.classList.add('has-active');
            userDropdownToggle.setAttribute('aria-expanded', 'true');
          }
        }
      });
    }

    // Cart dropdown
    if (cartBtn && miniCart) {
      cartBtn.addEventListener('click', function (e) {
        if (isTablet()) {
          e.preventDefault();
          e.stopPropagation();

          const isActive = miniCart.classList.contains('active');
          const cartContainer = cartBtn.closest('.cart-container');

          // Close all dropdowns
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });

          // Toggle current dropdown
          if (!isActive) {
            miniCart.classList.add('active');
            cartContainer.classList.add('has-active');
          }
        }
      });
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function (e) {
      if (isTablet()) {
        const isClickInsideDropdown = e.target.closest('.user-dropdown, .cart-container');

        if (!isClickInsideDropdown) {
          document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
            container.classList.remove('has-active');
          });
          document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
            dropdown.classList.remove('active');
          });
          document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
          });
        }
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (!isTablet()) {
        document.querySelectorAll('.user-dropdown, .cart-container').forEach(container => {
          container.classList.remove('has-active');
        });
        document.querySelectorAll('.user-dropdown-menu, .mini-cart').forEach(dropdown => {
          dropdown.classList.remove('active');
        });
        document.querySelectorAll('.user-dropdown .action-btn').forEach(btn => {
          btn.setAttribute('aria-expanded', 'false');
        });
      }
    });
  }

  // ===== INITIAL SCROLL POSITION =====
  function checkInitialScrollPosition() {
    const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (initialScrollTop > CONFIG.SCROLL_THRESHOLD) {
      state.isScrolled = true;
      elements.header.classList.add('scrolled');

      if (CONFIG.ENABLE_LOGO_TRANSITION && elements.logoImage) {
        elements.logoImage.src = state.darkLogoSrc;
      }

      if (CONFIG.ENABLE_SCALE_EFFECT) {
        const scrollProgress = Math.min(1, initialScrollTop / (CONFIG.SCROLL_THRESHOLD * 2));
        const scale = 0.98 + (0.02 * (1 - scrollProgress * 0.5));
        elements.header.style.setProperty('--header-scale', scale.toFixed(4));
      }
    }

    if (initialScrollTop > CONFIG.COMPACT_THRESHOLD) {
      state.isCompact = true;
      elements.header.classList.add('compact');
    }
  }

  // ===== START INITIALIZATION =====
  init();
});
  }
