// JavaScript cho hệ thống đánh giá và bình luận

/**
 * Hàm hiển thị tab đánh giá có sao
 */
function showRatingsTab() {
  const tabRatings = document.getElementById('tab-ratings');
  const tabComments = document.getElementById('tab-comments');
  const tabContentRatings = document.getElementById('tab-content-ratings');
  const tabContentComments = document.getElementById('tab-content-comments');

  if (!tabRatings || !tabComments || !tabContentRatings || !tabContentComments)
    return;

  tabRatings.classList.add('border-blue-500', 'text-blue-500');
  tabRatings.classList.remove('border-transparent', 'text-gray-500');
  tabComments.classList.remove('border-blue-500', 'text-blue-500');
  tabComments.classList.add('border-transparent', 'text-gray-500');

  tabContentRatings.classList.remove('hidden');
  tabContentRatings.classList.add('block');
  tabContentComments.classList.remove('block');
  tabContentComments.classList.add('hidden');

  // Hiển thị bộ lọc đánh giá
  const reviewFilter = document.querySelector('.review-filter');
  if (reviewFilter) {
    reviewFilter.classList.remove('hidden');
  }
}

/**
 * Hàm hiển thị tab bình luận
 */
function showCommentsTab() {
  const tabRatings = document.getElementById('tab-ratings');
  const tabComments = document.getElementById('tab-comments');
  const tabContentRatings = document.getElementById('tab-content-ratings');
  const tabContentComments = document.getElementById('tab-content-comments');

  if (!tabRatings || !tabComments || !tabContentRatings || !tabContentComments)
    return;

  tabComments.classList.add('border-blue-500', 'text-blue-500');
  tabComments.classList.remove('border-transparent', 'text-gray-500');
  tabRatings.classList.remove('border-blue-500', 'text-blue-500');
  tabRatings.classList.add('border-transparent', 'text-gray-500');

  tabContentComments.classList.remove('hidden');
  tabContentComments.classList.add('block');
  tabContentRatings.classList.remove('block');
  tabContentRatings.classList.add('hidden');

  // Ẩn bộ lọc đánh giá
  const reviewFilter = document.querySelector('.review-filter');
  if (reviewFilter) {
    reviewFilter.classList.add('hidden');
  }
}

document.addEventListener('DOMContentLoaded', function () {
  // Lưu trạng thái nút submit ban đầu
  document.querySelectorAll('button[type="submit"]').forEach((button) => {
    button.dataset.originalText = button.innerHTML;
  });

  // Xử lý tab đánh giá và bình luận
  const tabRatings = document.getElementById('tab-ratings');
  const tabComments = document.getElementById('tab-comments');

  // Lấy tham số tab từ URL nếu có
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');

  if (tabRatings && tabComments) {
    // Hiển thị tab theo tham số URL
    if (tabParam === 'comments') {
      showCommentsTab();
    } else {
      showRatingsTab();
    }

    tabRatings.addEventListener('click', function (e) {
      e.preventDefault();

      // Cập nhật URL
      const url = new URL(window.location.href);
      url.searchParams.delete('tab');

      // Giữ nguyên tham số comments_page nếu có
      const commentsPage = url.searchParams.get('comments_page');

      // Xóa tham số page cũ nếu có
      url.searchParams.delete('page');

      // Đặt lại ratings_page về 1 nếu không có
      if (!url.searchParams.has('ratings_page')) {
        url.searchParams.set('ratings_page', '1');
      }

      // Tải lại dữ liệu từ server
      showPageLoading();

      fetch(url.toString())
        .then((response) => response.text())
        .then((html) => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');

          // Cập nhật nội dung
          updateReviewsContent(doc);

          // Cập nhật URL mà không tải lại trang
          window.history.pushState({}, '', url);

          // Hiển thị tab đánh giá có sao
          showRatingsTab();

          // Ẩn loading
          hidePageLoading();
        })
        .catch((error) => {
          console.error('Error:', error);
          // Nếu có lỗi, tải lại trang
          window.location.href = url.toString();
        });
    });

    tabComments.addEventListener('click', function (e) {
      e.preventDefault();

      // Cập nhật URL
      const url = new URL(window.location.href);
      url.searchParams.set('tab', 'comments');

      // Giữ nguyên tham số ratings_page nếu có
      const ratingsPage = url.searchParams.get('ratings_page');

      // Xóa tham số page cũ nếu có
      url.searchParams.delete('page');

      // Đặt lại comments_page về 1 nếu không có
      if (!url.searchParams.has('comments_page')) {
        url.searchParams.set('comments_page', '1');
      }

      // Tải lại dữ liệu từ server
      showPageLoading();

      fetch(url.toString())
        .then((response) => response.text())
        .then((html) => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');

          // Cập nhật nội dung
          updateReviewsContent(doc);

          // Cập nhật URL mà không tải lại trang
          window.history.pushState({}, '', url);

          // Hiển thị tab bình luận
          showCommentsTab();

          // Ẩn loading
          hidePageLoading();
        })
        .catch((error) => {
          console.error('Error:', error);
          // Nếu có lỗi, tải lại trang
          window.location.href = url.toString();
        });
    });
  }

  // Khởi tạo các event listeners cho đánh giá
  initReviewEventListeners();

  // Khởi tạo bộ lọc đánh giá
  initReviewFilters();

  // Khởi tạo nút báo cáo
  initReportButtons();

  // Khởi tạo hiển thị thời gian tương đối
  initRelativeTime();

  // Xử lý hiệu ứng khi chọn số sao
  initRatingGuide();
});

/**
 * Gửi đánh giá
 */
function submitReview(form) {
  // Kiểm tra xem form có đủ thông tin không
  const reviewContent = form.querySelector('textarea[name="review_content"]');
  if (!reviewContent || !reviewContent.value.trim()) {
    showMessage('error', 'Vui lòng nhập nội dung đánh giá');
    return;
  }

  // Kiểm tra xem có đang gửi đánh giá không
  if (form.dataset.submitting === 'true') {
    console.log('Form is already being submitted');
    return;
  }

  // Đánh dấu form đang được gửi
  form.dataset.submitting = 'true';

  // Hiển thị loading
  showLoading(form);

  // Tạo FormData
  const formData = new FormData(form);

  // Gửi request
  fetch(`${BASE_URL}/api/review_submit.php`, {
    method: 'POST',
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      // Ẩn loading
      hideLoading(form);

      // Đánh dấu form đã được gửi xong
      form.dataset.submitting = 'false';

      if (data.success) {
        // Hiển thị thông báo thành công
        showMessage('success', data.message);

        // Reset form
        form.reset();

        // Xóa các radio buttons đã chọn
        const ratingInputs = form.querySelectorAll('input[type="radio"]');
        ratingInputs.forEach((input) => {
          input.checked = false;
        });

        // Reset hiển thị đánh giá
        const ratingLabel = form.querySelector('.rating-label');
        if (ratingLabel) {
          ratingLabel.textContent = 'Chưa đánh giá';
          ratingLabel.style.backgroundColor = '#f8fafc';
          ratingLabel.style.color = '#64748b';
          ratingLabel.style.borderLeft = 'none';
        }

        // Tải lại đánh giá
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);
      }
    })
    .catch((error) => {
      // Ẩn loading
      hideLoading(form);

      // Đánh dấu form đã được gửi xong
      form.dataset.submitting = 'false';

      // Hiển thị thông báo lỗi
      showMessage(
        'error',
        'Đã xảy ra lỗi khi gửi đánh giá. Vui lòng thử lại sau.'
      );
      console.error('Error:', error);
    });
}

/**
 * Gửi phản hồi
 */
function submitReply(form) {
  // Hiển thị loading
  showLoading(form);

  // Tạo FormData
  const formData = new FormData(form);

  // Gửi request
  fetch(`${BASE_URL}/api/reply_submit.php`, {
    method: 'POST',
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      // Ẩn loading
      hideLoading(form);

      if (data.success) {
        // Hiển thị thông báo thành công
        showMessage('success', data.message);

        // Reset form
        form.reset();

        // Ẩn form
        form.closest('.reply-form').classList.add('hidden');

        // Tải lại đánh giá
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);
      }
    })
    .catch((error) => {
      // Ẩn loading
      hideLoading(form);

      // Hiển thị thông báo lỗi
      showMessage(
        'error',
        'Đã xảy ra lỗi khi gửi phản hồi. Vui lòng thử lại sau.'
      );
      console.error('Error:', error);
    });
}

/**
 * Đánh dấu đánh giá là hữu ích
 */
function markHelpful(button) {
  const reviewId = button.dataset.reviewId;
  const csrfToken = document.querySelector('input[name="csrf_token"]').value;

  // Gửi request
  fetch(`${BASE_URL}/api/mark_helpful.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `review_id=${reviewId}&csrf_token=${csrfToken}`,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Cập nhật UI
        const countElement = button.querySelector('.helpful-count');
        const currentCount = parseInt(countElement.textContent);

        if (data.action === 'added') {
          countElement.textContent = currentCount + 1;
          button.classList.add('active');
        } else {
          countElement.textContent = currentCount - 1;
          button.classList.remove('active');
        }
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);
      }
    })
    .catch((error) => {
      // Hiển thị thông báo lỗi
      showMessage('error', 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
      console.error('Error:', error);
    });
}

/**
 * Hiển thị/ẩn form trả lời
 */
function toggleReplyForm(button) {
  const reviewItem = button.closest('.review-item');
  const replyForm = reviewItem.querySelector('.reply-form');

  // Ẩn tất cả các form trả lời khác
  document.querySelectorAll('.reply-form').forEach((form) => {
    if (form !== replyForm) {
      form.classList.add('hidden');
    }
  });

  // Hiển thị/ẩn form trả lời hiện tại
  replyForm.classList.toggle('hidden');

  // Focus vào textarea
  if (!replyForm.classList.contains('hidden')) {
    replyForm.querySelector('textarea').focus();
  }
}

/**
 * Sắp xếp đánh giá
 */
function sortReviews(sortBy) {
  const productId = document.querySelector('input[name="product_id"]').value;
  const url = new URL(window.location.href);

  // Thêm tham số sort vào URL
  url.searchParams.set('sort', sortBy);

  // Hiển thị loading
  showPageLoading();

  // Tải đánh giá bằng AJAX
  fetch(url.toString())
    .then((response) => response.text())
    .then((html) => {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Cập nhật nội dung đánh giá
      updateReviewsContent(doc);

      // Cập nhật URL mà không tải lại trang
      window.history.pushState({}, '', url);

      // Ẩn loading
      hidePageLoading();
    })
    .catch((error) => {
      console.error('Error:', error);
      // Nếu có lỗi, tải lại trang
      window.location.href = url.toString();
    });
}

/**
 * Tải trang đánh giá
 */
function loadReviewPage(page) {
  const url = new URL(window.location.href);
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');

  // Xác định tham số phân trang dựa vào tab hiện tại
  if (tabParam === 'comments') {
    // Tab bình luận - sử dụng tham số comments_page
    url.searchParams.set('comments_page', page);
    // Giữ nguyên ratings_page nếu có
    const ratingsPage = urlParams.get('ratings_page');
    if (ratingsPage) {
      url.searchParams.set('ratings_page', ratingsPage);
    }
  } else {
    // Tab đánh giá có sao - sử dụng tham số ratings_page
    url.searchParams.set('ratings_page', page);
    // Giữ nguyên comments_page nếu có
    const commentsPage = urlParams.get('comments_page');
    if (commentsPage) {
      url.searchParams.set('comments_page', commentsPage);
    }
  }

  // Xóa tham số page cũ nếu có
  url.searchParams.delete('page');

  // Hiển thị loading
  showPageLoading();

  // Tải đánh giá bằng AJAX
  fetch(url.toString())
    .then((response) => response.text())
    .then((html) => {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Cập nhật nội dung đánh giá
      updateReviewsContent(doc);

      // Cập nhật URL mà không tải lại trang
      window.history.pushState({}, '', url);

      // Ẩn loading
      hidePageLoading();

      // Cuộn đến phần đánh giá
      const scrollTarget =
        tabParam === 'comments'
          ? document.getElementById('tab-content-comments')
          : document.querySelector('.review-filter');

      if (scrollTarget) {
        scrollTarget.scrollIntoView({ behavior: 'smooth' });
      }
    })
    .catch((error) => {
      console.error('Error:', error);
      // Nếu có lỗi, tải lại trang
      window.location.href = url.toString();
    });
}

/**
 * Cập nhật nội dung đánh giá
 */
function updateReviewsContent(doc) {
  // Lấy tham số URL hiện tại
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');

  // Cập nhật cả hai tab để đảm bảo dữ liệu luôn được cập nhật
  // Cập nhật tab đánh giá có sao
  const ratingsContent = doc.getElementById('tab-content-ratings');
  if (ratingsContent) {
    document.getElementById('tab-content-ratings').innerHTML =
      ratingsContent.innerHTML;
  }

  // Cập nhật tab bình luận
  const commentsContent = doc.getElementById('tab-content-comments');
  if (commentsContent) {
    document.getElementById('tab-content-comments').innerHTML =
      commentsContent.innerHTML;
  }

  // Cập nhật dropdown sắp xếp
  const sortSelect = doc.getElementById('sort-reviews');
  if (sortSelect && document.getElementById('sort-reviews')) {
    document.getElementById('sort-reviews').value = sortSelect.value;
  }

  // Cập nhật phân trang cho cả hai tab
  // Cập nhật phân trang cho tab bình luận
  const commentsPagination = doc.querySelector('#comments-pagination');
  if (commentsPagination) {
    const currentCommentsPagination = document.querySelector(
      '#comments-pagination'
    );
    if (currentCommentsPagination) {
      currentCommentsPagination.innerHTML = commentsPagination.innerHTML;
    }
  }

  // Cập nhật phân trang cho tab đánh giá có sao
  const ratingsPagination = doc.querySelector('#ratings-pagination');
  if (ratingsPagination) {
    const currentRatingsPagination = document.querySelector(
      '#ratings-pagination'
    );
    if (currentRatingsPagination) {
      currentRatingsPagination.innerHTML = ratingsPagination.innerHTML;
    }
  }

  // Không tự động chuyển tab ở đây, để cho các hàm gọi quyết định
  // Chỉ khởi tạo lại các event listeners
  initReviewEventListeners();

  // Khởi tạo lại bộ lọc đánh giá
  initReviewFilters();
}

/**
 * Hiển thị loading cho trang
 */
function showPageLoading() {
  // Tạo overlay loading nếu chưa tồn tại
  let loadingOverlay = document.getElementById('page-loading-overlay');
  if (!loadingOverlay) {
    loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'page-loading-overlay';
    loadingOverlay.className =
      'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingOverlay.innerHTML =
      '<div class="bg-white p-4 rounded-lg shadow-lg"><i class="fas fa-spinner fa-spin text-blue-500 text-3xl"></i><p class="mt-2 text-gray-700">Đang tải...</p></div>';
    document.body.appendChild(loadingOverlay);
  } else {
    loadingOverlay.classList.remove('hidden');
  }
}

/**
 * Ẩn loading cho trang
 */
function hidePageLoading() {
  const loadingOverlay = document.getElementById('page-loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.classList.add('hidden');
  }
}

/**
 * Hiển thị loading
 */
function showLoading(element) {
  const submitButton = element.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.disabled = true;
    submitButton.innerHTML =
      '<i class="fas fa-spinner fa-spin mr-2"></i> Đang xử lý...';
  }
}

/**
 * Ẩn loading
 */
function hideLoading(element) {
  const submitButton = element.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.disabled = false;
    submitButton.innerHTML = submitButton.dataset.originalText || 'Gửi';
  }
}

/**
 * Hiển thị thông báo
 */
function showMessage(type, message) {
  // Tạo element thông báo
  const messageElement = document.createElement('div');
  messageElement.className = `fixed top-4 right-4 p-4 rounded shadow-lg z-50 ${
    type === 'success' ? 'bg-green-500' : 'bg-red-500'
  } text-white`;
  messageElement.innerHTML = message;

  // Thêm vào body
  document.body.appendChild(messageElement);

  // Tự động ẩn sau 3 giây
  setTimeout(() => {
    messageElement.classList.add('opacity-0');
    setTimeout(() => {
      document.body.removeChild(messageElement);
    }, 300);
  }, 3000);
}

/**
 * Mở media viewer
 */
function openMediaViewer(mediaItem) {
  // Tạo media viewer nếu chưa tồn tại
  let mediaViewer = document.getElementById('media-viewer');
  if (!mediaViewer) {
    mediaViewer = document.createElement('div');
    mediaViewer.id = 'media-viewer';
    mediaViewer.className = 'media-viewer';
    mediaViewer.innerHTML = `
            <div class="media-viewer-close"><i class="fas fa-times"></i></div>
            <div class="media-viewer-prev"><i class="fas fa-chevron-left"></i></div>
            <div class="media-viewer-next"><i class="fas fa-chevron-right"></i></div>
            <div class="media-viewer-content"></div>
            <div class="media-viewer-counter"><span id="current-slide">1</span> / <span id="total-slides">1</span></div>
        `;
    document.body.appendChild(mediaViewer);

    // Thêm event listeners
    mediaViewer
      .querySelector('.media-viewer-close')
      .addEventListener('click', closeMediaViewer);
    mediaViewer
      .querySelector('.media-viewer-prev')
      .addEventListener('click', () => navigateMedia(-1));
    mediaViewer
      .querySelector('.media-viewer-next')
      .addEventListener('click', () => navigateMedia(1));

    // Thêm event listener cho phím mũi tên
    document.addEventListener('keydown', handleMediaViewerKeydown);
  }

  // Hiển thị media viewer
  mediaViewer.classList.add('active');

  // Lấy tất cả media items trong cùng một nhóm
  const mediaItems = Array.from(mediaItem.parentElement.children);
  const currentIndex = mediaItems.indexOf(mediaItem);

  // Lưu thông tin vào media viewer
  mediaViewer.dataset.currentIndex = currentIndex;
  mediaViewer.dataset.totalItems = mediaItems.length;

  // Cập nhật counter
  document.getElementById('current-slide').textContent = currentIndex + 1;
  document.getElementById('total-slides').textContent = mediaItems.length;

  // Hiển thị media hiện tại
  showMedia(mediaItem, mediaViewer.querySelector('.media-viewer-content'));
}

/**
 * Xử lý phím mũi tên trong media viewer
 */
function handleMediaViewerKeydown(e) {
  const mediaViewer = document.getElementById('media-viewer');
  if (!mediaViewer || !mediaViewer.classList.contains('active')) return;

  if (e.key === 'ArrowLeft') {
    navigateMedia(-1);
    e.preventDefault();
  } else if (e.key === 'ArrowRight') {
    navigateMedia(1);
    e.preventDefault();
  } else if (e.key === 'Escape') {
    closeMediaViewer();
    e.preventDefault();
  }
}

/**
 * Đóng media viewer
 */
function closeMediaViewer() {
  const mediaViewer = document.getElementById('media-viewer');
  if (mediaViewer) {
    mediaViewer.classList.remove('active');
  }
}

/**
 * Điều hướng giữa các media
 */
function navigateMedia(direction) {
  const mediaViewer = document.getElementById('media-viewer');
  if (!mediaViewer) return;

  const currentIndex = parseInt(mediaViewer.dataset.currentIndex);
  const totalItems = parseInt(mediaViewer.dataset.totalItems);

  // Tính toán index mới
  let newIndex = currentIndex + direction;
  if (newIndex < 0) newIndex = totalItems - 1;
  if (newIndex >= totalItems) newIndex = 0;

  // Cập nhật index hiện tại
  mediaViewer.dataset.currentIndex = newIndex;

  // Lấy media item mới
  const mediaItems = document.querySelectorAll('.review-media-item');
  const newMediaItem = mediaItems[newIndex];

  // Cập nhật counter
  document.getElementById('current-slide').textContent = newIndex + 1;

  // Hiển thị media mới với hiệu ứng slide
  const contentContainer = mediaViewer.querySelector('.media-viewer-content');

  // Thêm class transition
  contentContainer.classList.add(
    'transition-opacity',
    'duration-300',
    'ease-in-out'
  );
  contentContainer.style.opacity = '0';

  // Sau khi fade out, cập nhật nội dung và fade in
  setTimeout(() => {
    showMedia(newMediaItem, contentContainer);
    contentContainer.style.opacity = '1';

    // Xóa class transition sau khi hoàn thành
    setTimeout(() => {
      contentContainer.classList.remove(
        'transition-opacity',
        'duration-300',
        'ease-in-out'
      );
    }, 300);
  }, 300);
}

/**
 * Khởi tạo các event listeners cho đánh giá
 */
function initReviewEventListeners() {
  // Form đánh giá
  const reviewForm = document.getElementById('review-form');
  if (reviewForm) {
    // Xóa tất cả event listeners cũ
    const newReviewForm = reviewForm.cloneNode(true);
    reviewForm.parentNode.replaceChild(newReviewForm, reviewForm);

    // Thêm event listener mới
    newReviewForm.addEventListener('submit', function (e) {
      // Chỉ xử lý khi người dùng nhấn nút submit
      if (e.submitter && e.submitter.type === 'submit') {
        e.preventDefault();
        submitReview(this);
      } else {
        e.preventDefault();
        console.log(
          'Form submission prevented: not triggered by submit button'
        );
      }
    });
  }

  // Nút đánh dấu hữu ích
  const helpfulButtons = document.querySelectorAll('.helpful-btn');
  helpfulButtons.forEach((button) => {
    button.addEventListener('click', function () {
      markHelpful(this);
    });
  });

  // Nút trả lời
  const replyButtons = document.querySelectorAll('.reply-btn');
  replyButtons.forEach((button) => {
    button.addEventListener('click', function () {
      toggleReplyForm(this);
    });
  });

  // Nút hủy trả lời
  const cancelReplyButtons = document.querySelectorAll('.cancel-reply-btn');
  cancelReplyButtons.forEach((button) => {
    button.addEventListener('click', function () {
      const replyForm = this.closest('.reply-form');
      replyForm.classList.add('hidden');
    });
  });

  // Form trả lời
  const replyForms = document.querySelectorAll('.reply-submit-form');
  replyForms.forEach((form) => {
    form.addEventListener('submit', function (e) {
      e.preventDefault();
      submitReply(this);
    });
  });

  // Nút xóa đánh giá
  const deleteReviewButtons = document.querySelectorAll('.delete-review-btn');
  deleteReviewButtons.forEach((button) => {
    button.addEventListener('click', function () {
      if (confirm('Bạn có chắc chắn muốn xóa đánh giá này?')) {
        deleteReview(this.dataset.reviewId);
      }
    });
  });

  // Nút xóa phản hồi
  const deleteReplyButtons = document.querySelectorAll('.delete-reply-btn');
  deleteReplyButtons.forEach((button) => {
    button.addEventListener('click', function () {
      if (confirm('Bạn có chắc chắn muốn xóa phản hồi này?')) {
        deleteReply(this.dataset.replyId);
      }
    });
  });

  // Sắp xếp đánh giá
  const sortSelect = document.getElementById('sort-reviews');
  if (sortSelect) {
    sortSelect.addEventListener('change', function () {
      sortReviews(this.value);
    });
  }

  // Phân trang
  const paginationButtons = document.querySelectorAll('.pagination-btn');
  paginationButtons.forEach((button) => {
    button.addEventListener('click', function () {
      loadReviewPage(this.dataset.page);
    });
  });

  // Nút điều hướng phân trang
  // Xử lý cho tab đánh giá có sao
  const ratingsPaginationNavPrev = document.querySelector(
    '#tab-content-ratings .pagination-nav-prev'
  );
  if (ratingsPaginationNavPrev) {
    ratingsPaginationNavPrev.addEventListener('click', function () {
      if (!this.classList.contains('disabled')) {
        const currentPage = parseInt(
          document.querySelector(
            '#tab-content-ratings .pagination-btn.bg-blue-500'
          ).dataset.page
        );
        loadReviewPage(currentPage - 1);
      }
    });
  }

  const ratingsPaginationNavNext = document.querySelector(
    '#tab-content-ratings .pagination-nav-next'
  );
  if (ratingsPaginationNavNext) {
    ratingsPaginationNavNext.addEventListener('click', function () {
      if (!this.classList.contains('disabled')) {
        const currentPage = parseInt(
          document.querySelector(
            '#tab-content-ratings .pagination-btn.bg-blue-500'
          ).dataset.page
        );
        loadReviewPage(currentPage + 1);
      }
    });
  }

  // Xử lý cho tab bình luận
  const commentsPaginationNavPrev = document.querySelector(
    '#tab-content-comments .pagination-nav-prev'
  );
  if (commentsPaginationNavPrev) {
    commentsPaginationNavPrev.addEventListener('click', function () {
      if (!this.classList.contains('disabled')) {
        const currentPage = parseInt(
          document.querySelector(
            '#tab-content-comments .pagination-btn.bg-blue-500'
          ).dataset.page
        );
        loadReviewPage(currentPage - 1);
      }
    });
  }

  const commentsPaginationNavNext = document.querySelector(
    '#tab-content-comments .pagination-nav-next'
  );
  if (commentsPaginationNavNext) {
    commentsPaginationNavNext.addEventListener('click', function () {
      if (!this.classList.contains('disabled')) {
        const currentPage = parseInt(
          document.querySelector(
            '#tab-content-comments .pagination-btn.bg-blue-500'
          ).dataset.page
        );
        loadReviewPage(currentPage + 1);
      }
    });
  }

  // Xem media
  const mediaItems = document.querySelectorAll('.review-media-item');
  mediaItems.forEach((item) => {
    item.addEventListener('click', function () {
      openMediaViewer(this);
    });
  });
}

/**
 * Khởi tạo bộ lọc đánh giá
 */
function initReviewFilters() {
  const filterButtons = document.querySelectorAll('.filter-btn');
  if (filterButtons.length === 0) return;

  filterButtons.forEach((button) => {
    button.addEventListener('click', function () {
      // Xóa active class từ tất cả các nút
      filterButtons.forEach((btn) => btn.classList.remove('active'));

      // Thêm active class cho nút được click
      this.classList.add('active');

      // Lấy giá trị filter
      const filterValue = this.dataset.filter;

      // Lọc đánh giá
      filterReviews(filterValue);
    });
  });
}

/**
 * Lọc đánh giá theo số sao hoặc media
 */
function filterReviews(filterValue) {
  const reviewItems = document.querySelectorAll(
    '.review-item[data-has-rating="1"]'
  );

  if (filterValue === 'all') {
    // Hiển thị tất cả đánh giá
    reviewItems.forEach((item) => {
      item.classList.remove('hidden');
    });
  } else if (filterValue === 'has_media') {
    // Lọc theo đánh giá có hình ảnh/video
    reviewItems.forEach((item) => {
      const hasMedia = item.dataset.hasMedia === '1';

      if (hasMedia) {
        item.classList.remove('hidden');
      } else {
        item.classList.add('hidden');
      }
    });
  } else {
    // Lọc theo số sao
    const starRating = parseInt(filterValue);

    reviewItems.forEach((item) => {
      const itemRating = parseInt(item.dataset.rating);

      if (itemRating === starRating) {
        item.classList.remove('hidden');
      } else {
        item.classList.add('hidden');
      }
    });
  }

  // Kiểm tra nếu không có đánh giá nào hiển thị
  const visibleReviews = document.querySelectorAll(
    '.review-item[data-has-rating="1"]:not(.hidden)'
  );
  const emptyMessage = document.getElementById('empty-filtered-reviews');

  if (visibleReviews.length === 0 && emptyMessage) {
    emptyMessage.classList.remove('hidden');
  } else if (emptyMessage) {
    emptyMessage.classList.add('hidden');
  }
}

/**
 * Khởi tạo nút báo cáo
 */
function initReportButtons() {
  const reportButtons = document.querySelectorAll('.report-btn');

  reportButtons.forEach((button) => {
    button.addEventListener('click', function () {
      reportReview(this.dataset.reviewId);
    });
  });
}

/**
 * Báo cáo đánh giá
 */
function reportReview(reviewId) {
  const csrfToken = document.querySelector('input[name="csrf_token"]').value;
  const button = document.querySelector(
    `.report-btn[data-review-id="${reviewId}"]`
  );

  if (button.classList.contains('reported')) {
    showMessage('info', 'Bạn đã báo cáo đánh giá này rồi');
    return;
  }

  // Hiển thị dialog xác nhận
  const reason = prompt('Vui lòng cho biết lý do báo cáo đánh giá này:');
  if (!reason) return;

  // Gửi request
  fetch(`${BASE_URL}/api/report_review.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `review_id=${reviewId}&reason=${encodeURIComponent(
      reason
    )}&csrf_token=${csrfToken}`,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Cập nhật UI
        button.classList.add('reported');
        button.title = 'Đã báo cáo';

        // Hiển thị thông báo thành công
        showMessage('success', data.message);
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);
      }
    })
    .catch((error) => {
      // Hiển thị thông báo lỗi
      showMessage('error', 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
      console.error('Error:', error);
    });
}

/**
 * Khởi tạo hiển thị thời gian tương đối
 */
function initRelativeTime() {
  const timeElements = document.querySelectorAll('.review-time');

  timeElements.forEach((element) => {
    const timestamp = element.dataset.timestamp;
    if (timestamp) {
      const relativeTime = getRelativeTime(timestamp);

      // Tạo element hiển thị thời gian tương đối
      const relativeTimeElement = document.createElement('span');
      relativeTimeElement.className = 'review-time-relative';
      relativeTimeElement.textContent = `(${relativeTime})`;

      // Thêm vào sau thời gian tuyệt đối
      element.appendChild(relativeTimeElement);
    }
  });
}

/**
 * Tính thời gian tương đối
 */
function getRelativeTime(timestamp) {
  const now = new Date();
  const date = new Date(timestamp * 1000);
  const diff = Math.floor((now - date) / 1000);

  if (diff < 60) {
    return 'vừa xong';
  } else if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return `${minutes} phút trước`;
  } else if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return `${hours} giờ trước`;
  } else if (diff < 2592000) {
    const days = Math.floor(diff / 86400);
    return `${days} ngày trước`;
  } else if (diff < 31536000) {
    const months = Math.floor(diff / 2592000);
    return `${months} tháng trước`;
  } else {
    const years = Math.floor(diff / 31536000);
    return `${years} năm trước`;
  }
}

/**
 * Hiển thị media trong media viewer
 */
function showMedia(mediaItem, container) {
  // Xóa nội dung cũ
  container.innerHTML = '';

  // Kiểm tra loại media
  if (mediaItem.querySelector('video')) {
    // Video
    const video = document.createElement('video');
    video.controls = true;
    video.autoplay = true;

    const source = document.createElement('source');
    source.src = mediaItem.querySelector('video source').src;
    source.type = 'video/mp4';

    video.appendChild(source);
    container.appendChild(video);
  } else {
    // Image
    const img = document.createElement('img');
    img.src = mediaItem.querySelector('img').src;
    container.appendChild(img);
  }
}

/**
 * Xóa đánh giá
 */
function deleteReview(reviewId) {
  const csrfToken = document.querySelector('input[name="csrf_token"]').value;

  // Hiển thị loading
  const reviewItem = document.querySelector(
    `.review-item[data-review-id="${reviewId}"]`
  );
  if (reviewItem) {
    reviewItem.classList.add('opacity-50');
  }

  // Gửi request
  fetch(`${BASE_URL}/api/delete_review.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `review_id=${reviewId}&csrf_token=${csrfToken}`,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Hiển thị thông báo thành công
        showMessage('success', data.message);

        // Xóa đánh giá khỏi DOM
        if (reviewItem) {
          reviewItem.remove();
        }

        // Nếu không còn đánh giá nào, hiển thị thông báo
        const reviewsContainer = document.getElementById('reviews-container');
        if (
          reviewsContainer &&
          reviewsContainer.querySelectorAll('.review-item').length === 0
        ) {
          reviewsContainer.innerHTML = `
          <div class="text-center py-8 text-gray-600">
            <i class="far fa-comment-dots text-4xl mb-3 text-gray-400"></i>
            <p>Chưa có đánh giá nào cho sản phẩm này.</p>
            <p class="mt-2">Hãy là người đầu tiên đánh giá!</p>
          </div>
        `;
        }
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);

        // Bỏ loading
        if (reviewItem) {
          reviewItem.classList.remove('opacity-50');
        }
      }
    })
    .catch((error) => {
      // Hiển thị thông báo lỗi
      showMessage('error', 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
      console.error('Error:', error);

      // Bỏ loading
      if (reviewItem) {
        reviewItem.classList.remove('opacity-50');
      }
    });
}

/**
 * Xóa phản hồi
 */
function deleteReply(replyId) {
  const csrfToken = document.querySelector('input[name="csrf_token"]').value;

  // Hiển thị loading
  const replyItem = document.querySelector(
    `.review-reply[data-reply-id="${replyId}"]`
  );
  if (replyItem) {
    replyItem.classList.add('opacity-50');
  }

  // Gửi request
  fetch(`${BASE_URL}/api/delete_reply.php`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `reply_id=${replyId}&csrf_token=${csrfToken}`,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Hiển thị thông báo thành công
        showMessage('success', data.message);

        // Xóa phản hồi khỏi DOM
        if (replyItem) {
          replyItem.remove();
        }
      } else {
        // Hiển thị thông báo lỗi
        showMessage('error', data.message);

        // Bỏ loading
        if (replyItem) {
          replyItem.classList.remove('opacity-50');
        }
      }
    })
    .catch((error) => {
      // Hiển thị thông báo lỗi
      showMessage('error', 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
      console.error('Error:', error);

      // Bỏ loading
      if (replyItem) {
        replyItem.classList.remove('opacity-50');
      }
    });
}

/**
 * Khởi tạo hiệu ứng khi chọn số sao
 */
function initRatingGuide() {
  const ratingInputs = document.querySelectorAll('.rating input[type="radio"]');
  const ratingLabelElement = document.querySelector('.rating-label');

  // Các nhãn tương ứng với mỗi mức đánh giá
  const ratingLabels = {
    0: 'Chưa đánh giá',
    1: 'Rất tệ',
    2: 'Tệ',
    3: 'Bình thường',
    4: 'Tốt',
    5: 'Rất tốt',
  };

  // Các màu tương ứng với mỗi mức đánh giá (tinh tế hơn)
  const ratingColors = {
    0: '#f8fafc', // Màu mặc định
    1: '#fee2e2', // Đỏ nhạt - Rất tệ
    2: '#ffedd5', // Cam nhạt - Tệ
    3: '#fef9c3', // Vàng nhạt - Bình thường
    4: '#dcfce7', // Xanh lá nhạt - Tốt
    5: '#d1fae5', // Xanh lá đậm nhạt - Rất tốt
  };

  // Các màu chữ tương ứng
  const textColors = {
    0: '#64748b', // Màu mặc định
    1: '#b91c1c', // Đỏ - Rất tệ
    2: '#c2410c', // Cam - Tệ
    3: '#a16207', // Vàng - Bình thường
    4: '#15803d', // Xanh lá - Tốt
    5: '#047857', // Xanh lá đậm - Rất tốt
  };

  if (ratingInputs.length > 0) {
    // Kiểm tra xem có input nào đã được chọn chưa
    const checkedInput = document.querySelector(
      '.rating input[type="radio"]:checked'
    );
    if (checkedInput) {
      const rating = parseInt(checkedInput.value);
      updateRatingDisplay(rating);
    }

    // Thêm event listener cho các input
    ratingInputs.forEach((input) => {
      input.addEventListener('change', function (e) {
        // Ngăn chặn sự kiện submit form
        e.stopPropagation();

        const rating = parseInt(this.value);
        updateRatingDisplay(rating);

        // Ngăn chặn form tự động submit
        setTimeout(() => {
          const form = this.closest('form');
          if (form) {
            const submitEvent = form._submit_attached;
            if (!submitEvent) {
              form._submit_attached = true;
              form.addEventListener('submit', function (submitEvent) {
                // Chỉ submit khi người dùng nhấn nút submit
                if (
                  submitEvent.submitter &&
                  submitEvent.submitter.type === 'submit'
                ) {
                  return true;
                }
                submitEvent.preventDefault();
                return false;
              });
            }
          }
        }, 0);
      });
    });
  }

  // Hàm cập nhật hiển thị đánh giá
  function updateRatingDisplay(rating) {
    // Cập nhật nhãn và màu sắc
    if (ratingLabelElement) {
      const ratingText = ratingLabels[rating] || 'Chưa đánh giá';
      ratingLabelElement.textContent = ratingText;

      // Áp dụng màu sắc tinh tế
      if (rating > 0) {
        ratingLabelElement.style.backgroundColor = ratingColors[rating];
        ratingLabelElement.style.color = textColors[rating];
        ratingLabelElement.style.borderLeft = `3px solid ${textColors[rating]}`;
      } else {
        ratingLabelElement.style.backgroundColor = '#f8fafc';
        ratingLabelElement.style.color = '#64748b';
        ratingLabelElement.style.borderLeft = 'none';
      }
    }
  }
}
