/**
 * <PERSON><PERSON> lý tìm kiếm gợi ý theo thời gian thực
 * Phiên bản cải tiến với:
 * - <PERSON>àm debounce chuẩn
 * - <PERSON><PERSON> lý bất đồng bộ tốt hơn
 * - Tránh memory leak
 */

// Hàm debounce chuẩn
function debounce(func, wait, immediate = false) {
  let timeout;
  return function() {
    const context = this;
    const args = arguments;

    const later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(context, args);
  };
}

// IIFE để tạo scope riêng và tránh ô nhiễm global scope
(function() {
  // Biến để lưu trữ các event listener đã đăng ký
  const registeredListeners = [];

  // Hàm để đăng ký event listener và lưu lại để có thể xóa sau này
  function addEventListenerWithCleanup(element, eventType, handler) {
    element.addEventListener(eventType, handler);
    registeredListeners.push({ element, eventType, handler });
  }

  // Hàm để xóa tất cả event listener đã đăng ký
  function removeAllEventListeners() {
    registeredListeners.forEach(({ element, eventType, handler }) => {
      element.removeEventListener(eventType, handler);
    });
    registeredListeners.length = 0; // Xóa mảng
  }

  // Hàm xử lý tìm kiếm với loading thông minh
  function handleSearch(searchInput, suggestionsContainer) {
    const keyword = searchInput.value.trim();

    // Nếu từ khóa quá ngắn, hiển thị thông báo
    if (keyword.length < 1) {
      showMinLengthMessage(suggestionsContainer);
      return;
    }

    // Hiển thị trạng thái loading với skeleton
    showEnhancedLoadingState(suggestionsContainer, keyword);

    // Record start time for minimum loading duration
    const startTime = Date.now();
    // Adaptive minimum loading time based on keyword length - Tăng thời gian để người dùng kịp nhận biết
    const minLoadingTime = keyword.length === 1 ? 800 :
                          keyword.length === 2 ? 700 : 600;

    // Thiết lập timeout cho request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // Timeout sau 5 giây

    // Gửi request đến API
    fetch(
      `${BASE_URL}/api/search_suggestions.php?keyword=${encodeURIComponent(keyword)}`,
      {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      }
    )
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      clearTimeout(timeoutId); // Xóa timeout khi request thành công

      // Calculate elapsed time and ensure minimum loading time
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // Delay showing results to ensure minimum loading time
      setTimeout(() => {
        // Add fade transition effect
        suggestionsContainer.style.opacity = '0.5';

        setTimeout(() => {
          displaySearchResults(data, keyword, suggestionsContainer);
          // Fade in the results
          suggestionsContainer.style.opacity = '1';
        }, 100);
      }, remainingTime);
    })
    .catch(error => {
      clearTimeout(timeoutId); // Xóa timeout khi có lỗi

      // Calculate elapsed time for error case too
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      setTimeout(() => {
        handleSearchError(error, suggestionsContainer);
      }, remainingTime);
    });
  }

  // Hiển thị thông báo khi từ khóa quá ngắn
  function showMinLengthMessage(container) {
    container.innerHTML = `
      <div class="search-min-length">
        <i class="fas fa-keyboard"></i>
        <p>Vui lòng nhập ít nhất 1 ký tự để tìm kiếm</p>
        <div class="search-input-indicator">
          <span class="search-input-dot active"></span>
          <span class="search-input-line"></span>
        </div>
        <p class="search-suggestion-text">Bắt đầu gõ để tìm kiếm</p>
      </div>
    `;
    container.classList.remove('hidden');
  }

  // Hiển thị trạng thái loading với skeleton
  function showEnhancedLoadingState(container, keyword) {
    const loadingMessages = [
      'Đang tìm kiếm sản phẩm...',
      'Đang phân tích từ khóa...',
      'Đang tải kết quả...'
    ];

    const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];

    container.innerHTML = `
      <div class="search-loading-item">
        <div class="search-product-skeleton">
          <div class="search-skeleton-image"></div>
          <div class="search-skeleton-content">
            <div class="search-skeleton-title"></div>
            <div class="search-skeleton-category"></div>
            <div class="search-skeleton-price"></div>
          </div>
          <div class="search-skeleton-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
        </div>
      </div>
      <div class="search-loading-item">
        <div class="search-product-skeleton">
          <div class="search-skeleton-image"></div>
          <div class="search-skeleton-content">
            <div class="search-skeleton-title"></div>
            <div class="search-skeleton-category"></div>
            <div class="search-skeleton-price"></div>
          </div>
          <div class="search-skeleton-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
        </div>
      </div>
      <div class="search-loading-item">
        <div class="search-product-skeleton">
          <div class="search-skeleton-image"></div>
          <div class="search-skeleton-content">
            <div class="search-skeleton-title"></div>
            <div class="search-skeleton-category"></div>
            <div class="search-skeleton-price"></div>
          </div>
          <div class="search-skeleton-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
        </div>
      </div>
      <div class="search-loading-message">
        <i class="fas fa-search"></i>
        <span>${randomMessage}</span>
      </div>
    `;
    container.classList.remove('hidden');
  }

  // Hiển thị trạng thái loading cũ (fallback)
  function showLoadingState(container) {
    container.innerHTML = `
      <div class="search-loading">
        <div class="search-loading-spinner"></div>
        <p>Đang tìm kiếm...</p>
        <p class="search-suggestion-text">Chúng tôi đang tìm kiếm sản phẩm phù hợp nhất</p>
        <div class="search-loading-pulse"></div>
      </div>
    `;
    container.classList.remove('hidden');
  }

  // Hiển thị kết quả tìm kiếm
  function displaySearchResults(data, keyword, container) {
    // Xóa nội dung cũ
    container.innerHTML = '';

    // Đảm bảo container có các class và attribute cần thiết cho z-index fix
    container.classList.add('search-suggestions');
    container.setAttribute('aria-hidden', 'false');

    // Nếu không có kết quả
    if (data.suggestions.length === 0) {
      // Hiển thị thông báo không tìm thấy kết quả với gợi ý
      container.innerHTML = `
        <div class="search-no-results">
          <i class="fas fa-search"></i>
          <p>Không tìm thấy sản phẩm nào phù hợp</p>
          <p class="search-suggestion-text">Hãy thử với từ khóa khác</p>

          <div class="search-alternative-suggestions">
            <p class="search-alternative-title">Gợi ý tìm kiếm phổ biến:</p>
            <div class="search-tags">
              <a href="${BASE_URL}/products.php?keyword=sofa" class="search-tag"><i class="fas fa-couch"></i> Sofa</a>
              <a href="${BASE_URL}/products.php?keyword=bàn" class="search-tag"><i class="fas fa-table"></i> Bàn</a>
              <a href="${BASE_URL}/products.php?keyword=ghế" class="search-tag"><i class="fas fa-chair"></i> Ghế</a>
              <a href="${BASE_URL}/products.php?keyword=tủ" class="search-tag"><i class="fas fa-archive"></i> Tủ</a>
              <a href="${BASE_URL}/products.php?keyword=đèn" class="search-tag"><i class="fas fa-lightbulb"></i> Đèn</a>
            </div>
          </div>

          <div class="search-spelling-suggestion">
            <p><i class="fas fa-spell-check"></i> Bạn có thể đang tìm: <a href="${BASE_URL}/products.php?keyword=${encodeURIComponent(getSuggestionKeyword(keyword))}" class="search-spelling-link">${getSuggestionKeyword(keyword)}</a></p>
          </div>
        </div>
      `;
      container.classList.remove('hidden');
      container.classList.add('show');
      return;
    }

    // Hiển thị kết quả
    data.suggestions.forEach(product => {
      const productElement = document.createElement('a');
      productElement.href = product.url;
      productElement.className = 'search-item';

      productElement.innerHTML = `
        <div class="search-item-image">
          <div class="skeleton-loading skeleton-wave absolute inset-0 rounded z-0"></div>
          <img
            src="${product.image}"
            alt="${product.name}"
            style="width: 100%; height: 100%; object-fit: cover; position: relative; z-index: 2;"
            onerror="this.style.display='none';"
            onload="this.previousElementSibling.style.display='none';"
          >
        </div>
        <div class="search-item-info">
          <div class="search-item-name">${truncateText(product.name, 40)}</div>
          <div class="search-item-meta">
            <div class="search-item-rating" title="Đánh giá">
              <i class="fas fa-star"></i>
              <span>${product.rating}</span>
            </div>
            <div class="search-item-sales" title="Số lượng đã bán">
              <i class="fas fa-shopping-cart"></i>
              <span>${product.sales}</span>
            </div>
            <div class="search-item-category" title="Danh mục">
              <i class="fas fa-tag"></i> <span>${truncateText(product.category, 15)}</span>
            </div>
          </div>
          <div class="search-item-price" title="${product.price_type === 'contact' ? 'Liên hệ báo giá' : 'Giá sản phẩm'}">${formatPrice(product.price, product.price_type)}</div>
        </div>
      `;

      container.appendChild(productElement);
    });

    // Thêm nút xem tất cả kết quả
    const viewAllElement = document.createElement('a');
    viewAllElement.href = `${BASE_URL}/search.php?keyword=${encodeURIComponent(keyword)}`;
    viewAllElement.className = 'search-view-all';
    viewAllElement.innerHTML = '<i class="fas fa-search"></i> Xem tất cả kết quả';
    container.appendChild(viewAllElement);

    // Hiển thị container
    container.classList.remove('hidden');
    container.classList.add('show');
  }

  // Hàm cắt ngắn văn bản quá dài
  function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  // Hàm định dạng giá tiền
  function formatPrice(price, priceType) {
    // Nếu price_type là 'contact', ưu tiên hiển thị "Liên hệ báo giá"
    if (priceType === 'contact') {
      return '<span class="contact-price">Liên hệ báo giá</span>';
    }

    if (!price) return '';

    // Xử lý trường hợp "liên hệ báo giá" hoặc tương tự
    if (typeof price === 'string') {
      // Nếu giá chứa từ "liên hệ" hoặc không có số
      if (price.toLowerCase().includes('liên hệ') || !/\d/.test(price)) {
        return '<span class="contact-price">Liên hệ báo giá</span>';
      }

      // Nếu giá đã được định dạng với đơn vị tiền tệ
      if (price.includes('đ') || price.includes('₫') || price.includes('VND')) {
        return price;
      }
    }

    // Nếu giá là số hoặc chuỗi số, định dạng lại
    try {
      // Loại bỏ các ký tự không phải số
      const numericPrice = price.toString().replace(/[^\d]/g, '');

      // Nếu không có số, có thể là "Liên hệ báo giá" hoặc tương tự
      if (!numericPrice) {
        return '<span class="contact-price">Liên hệ báo giá</span>';
      }

      // Định dạng số với dấu chấm phân cách hàng nghìn
      const formattedPrice = parseInt(numericPrice).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      return formattedPrice + ' đ';
    } catch (e) {
      // Nếu có lỗi, trả về giá ban đầu
      return price;
    }
  }

  // Hàm tạo gợi ý từ khóa thay thế
  function getSuggestionKeyword(keyword) {
    // Danh sách các từ khóa phổ biến
    const popularKeywords = [
      'sofa', 'bàn', 'ghế', 'tủ', 'đèn', 'giường', 'kệ', 'gối', 'thảm',
      'rèm', 'gương', 'bàn ăn', 'bàn làm việc', 'ghế sofa', 'tủ quần áo'
    ];

    // Nếu từ khóa ngắn, trả về từ khóa phổ biến ngẫu nhiên
    if (keyword.length < 3) {
      return popularKeywords[Math.floor(Math.random() * popularKeywords.length)];
    }

    // Tìm từ khóa gần giống nhất
    let closestKeyword = '';
    let minDistance = Infinity;

    popularKeywords.forEach(popularKeyword => {
      // Tính khoảng cách Levenshtein đơn giản
      const distance = levenshteinDistance(keyword.toLowerCase(), popularKeyword);

      if (distance < minDistance) {
        minDistance = distance;
        closestKeyword = popularKeyword;
      }
    });

    // Nếu khoảng cách quá lớn, trả về từ khóa phổ biến ngẫu nhiên
    if (minDistance > 3) {
      return popularKeywords[Math.floor(Math.random() * popularKeywords.length)];
    }

    return closestKeyword;
  }

  // Hàm tính khoảng cách Levenshtein đơn giản
  function levenshteinDistance(a, b) {
    if (a.length === 0) return b.length;
    if (b.length === 0) return a.length;

    const matrix = [];

    // Khởi tạo ma trận
    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }

    // Tính toán khoảng cách
    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        const cost = a[j - 1] === b[i - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,      // Xóa
          matrix[i][j - 1] + 1,      // Chèn
          matrix[i - 1][j - 1] + cost // Thay thế
        );
      }
    }

    return matrix[b.length][a.length];
  }

  // Xử lý lỗi tìm kiếm
  function handleSearchError(error, container) {
    console.error('Lỗi khi tìm kiếm:', error);

    // Hiển thị thông báo lỗi thân thiện
    if (error.name === 'AbortError') {
      container.innerHTML = `
        <div class="search-error">
          <i class="fas fa-clock"></i>
          <p>Yêu cầu tìm kiếm đã hết thời gian chờ</p>
          <p class="search-suggestion-text">Vui lòng thử lại sau hoặc kiểm tra kết nối mạng</p>
          <div class="search-alternative-suggestions">
            <p class="search-alternative-title">Bạn có thể thử tìm:</p>
            <div class="search-tags">
              <a href="${BASE_URL}/products.php?keyword=sofa" class="search-tag"><i class="fas fa-couch"></i> Sofa</a>
              <a href="${BASE_URL}/products.php?keyword=bàn" class="search-tag"><i class="fas fa-table"></i> Bàn</a>
              <a href="${BASE_URL}/products.php?keyword=ghế" class="search-tag"><i class="fas fa-chair"></i> Ghế</a>
            </div>
          </div>
        </div>
      `;
    } else {
      container.innerHTML = `
        <div class="search-error">
          <i class="fas fa-exclamation-triangle"></i>
          <p>Đã xảy ra lỗi khi tìm kiếm</p>
          <p class="search-suggestion-text">Vui lòng thử lại sau hoặc liên hệ hỗ trợ</p>
          <div class="search-alternative-suggestions">
            <p class="search-alternative-title">Bạn có thể thử tìm:</p>
            <div class="search-tags">
              <a href="${BASE_URL}/products.php?keyword=sofa" class="search-tag"><i class="fas fa-couch"></i> Sofa</a>
              <a href="${BASE_URL}/products.php?keyword=bàn" class="search-tag"><i class="fas fa-table"></i> Bàn</a>
              <a href="${BASE_URL}/products.php?keyword=ghế" class="search-tag"><i class="fas fa-chair"></i> Ghế</a>
            </div>
          </div>
        </div>
      `;
    }

    container.classList.remove('hidden');
    container.classList.add('show');
    container.setAttribute('aria-hidden', 'false');
  }

  // Khởi tạo tìm kiếm
  function initSearch() {
    // Lấy các phần tử tìm kiếm
    const searchInputs = document.querySelectorAll('.search-input');
    const searchForms = document.querySelectorAll('.search-form');

    // Xử lý cho mỗi ô tìm kiếm
    searchInputs.forEach((searchInput, index) => {
      // Tạo container cho kết quả gợi ý
      const suggestionsContainer = document.createElement('div');
      suggestionsContainer.className = 'search-suggestions hidden';
      suggestionsContainer.setAttribute('aria-hidden', 'true');

      // Đảm bảo search-form có position relative để container có thể absolute position
      const searchForm = searchForms[index];
      if (getComputedStyle(searchForm).position === 'static') {
        searchForm.style.position = 'relative';
      }

      // Thêm container vào form
      searchForm.appendChild(suggestionsContainer);

      // Xử lý sự kiện nhập từ khóa với adaptive debounce
      let searchTimeout;
      const adaptiveSearch = function() {
        const keyword = searchInput.value.trim();

        clearTimeout(searchTimeout);

        // Adaptive debounce timing based on keyword length
        let debounceTime;
        if (keyword.length === 1) {
          debounceTime = 600; // Longer delay for single character
        } else if (keyword.length === 2) {
          debounceTime = 400; // Medium delay for 2 characters
        } else {
          debounceTime = 200; // Fast for 3+ characters
        }

        searchTimeout = setTimeout(() => {
          handleSearch(searchInput, suggestionsContainer);
        }, debounceTime);
      };

      // Đăng ký event listener với khả năng cleanup
      addEventListenerWithCleanup(searchInput, 'input', adaptiveSearch);

      // Ẩn gợi ý khi click ra ngoài
      const handleOutsideClick = function(event) {
        if (!searchForm.contains(event.target)) {
          suggestionsContainer.classList.add('hidden');
          suggestionsContainer.classList.remove('show');
          suggestionsContainer.setAttribute('aria-hidden', 'true');
        }
      };
      addEventListenerWithCleanup(document, 'click', handleOutsideClick);

      // Hiển thị lại gợi ý khi focus vào ô tìm kiếm
      const handleFocus = function() {
        if (this.value.trim().length >= 2) {
          handleSearch(searchInput, suggestionsContainer);
        }
      };
      addEventListenerWithCleanup(searchInput, 'focus', handleFocus);
    });
  }

  // Khởi tạo khi DOM đã sẵn sàng
  document.addEventListener('DOMContentLoaded', function() {
    initSearch();

    // Xử lý cleanup khi unload trang
    window.addEventListener('beforeunload', function() {
      removeAllEventListeners();
    });
  });
})();
