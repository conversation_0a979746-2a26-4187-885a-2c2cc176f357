/**
 * Scroll Optimization Module
 * Advanced scroll handling with proper debouncing and hysteresis
 * Created: 2025-07-27
 * 
 * Features:
 * - Unified scroll thresholds
 * - Proper debouncing and throttling
 * - Hysteresis to prevent flickering
 * - Performance optimizations
 * - Smooth state transitions
 */

class ScrollOptimizer {
  constructor(options = {}) {
    // ===== CONFIGURATION =====
    this.config = {
      // Scroll thresholds
      SCROLL_THRESHOLD: options.scrollThreshold || 10,
      SCROLL_HYSTERESIS: options.scrollHysteresis || 5,
      COMPACT_THRESHOLD: options.compactThreshold || 200,
      TOP_BAR_HIDE_THRESHOLD: options.topBarHideThreshold || 50,
      
      // Performance settings
      THROTTLE_DELAY: options.throttleDelay || 16, // ~60fps
      DEBOUNCE_DELAY: options.debounceDelay || 150,
      RAF_TIMEOUT: options.rafTimeout || 1000 / 60, // 60fps fallback
      
      // Easing and timing
      TRANSITION_DURATION: options.transitionDuration || 400,
      EASING_FUNCTION: options.easingFunction || 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      
      // Feature flags
      ENABLE_HYSTERESIS: options.enableHysteresis !== false,
      ENABLE_DIRECTION_DETECTION: options.enableDirectionDetection !== false,
      ENABLE_VELOCITY_TRACKING: options.enableVelocityTracking !== false,
      ENABLE_SMOOTH_TRANSITIONS: options.enableSmoothTransitions !== false
    };

    // ===== STATE MANAGEMENT =====
    this.state = {
      // Scroll position tracking
      currentScrollTop: 0,
      lastScrollTop: 0,
      scrollDelta: 0,
      
      // Direction and velocity
      isScrollingDown: false,
      scrollVelocity: 0,
      velocityHistory: [],
      
      // Header states
      isScrolled: false,
      isCompact: false,
      isTopBarHidden: false,
      
      // Performance tracking
      lastUpdateTime: 0,
      frameId: null,
      isThrottled: false,
      
      // Timers
      debounceTimer: null,
      scrollStopTimer: null
    };

    // ===== ELEMENT REFERENCES =====
    this.elements = {
      header: document.querySelector('.premium-header'),
      topBar: document.querySelector('.top-bar'),
      midHeader: document.querySelector('.mid-header-container'),
      bottomHeader: document.querySelector('.bottom-header-container')
    };

    // ===== CALLBACKS =====
    this.callbacks = {
      onScrollStart: options.onScrollStart || (() => {}),
      onScrollEnd: options.onScrollEnd || (() => {}),
      onStateChange: options.onStateChange || (() => {}),
      onThresholdCross: options.onThresholdCross || (() => {})
    };

    // Initialize
    this.init();
  }

  // ===== INITIALIZATION =====
  init() {
    if (!this.elements.header) {
      console.warn('ScrollOptimizer: Header element not found');
      return;
    }

    this.setupEventListeners();
    this.checkInitialState();
  }

  setupEventListeners() {
    // Use passive listeners for better performance
    window.addEventListener('scroll', this.handleScroll.bind(this), { 
      passive: true,
      capture: false 
    });

    // Handle resize events
    window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250), {
      passive: true
    });

    // Handle visibility change
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
  }

  // ===== SCROLL HANDLING =====
  handleScroll() {
    const now = performance.now();
    
    // Throttle scroll events for performance
    if (this.config.ENABLE_SMOOTH_TRANSITIONS && 
        now - this.state.lastUpdateTime < this.config.THROTTLE_DELAY) {
      return;
    }

    this.state.lastUpdateTime = now;

    // Cancel previous frame if still pending
    if (this.state.frameId) {
      cancelAnimationFrame(this.state.frameId);
    }

    // Schedule update on next frame
    this.state.frameId = requestAnimationFrame(() => {
      this.updateScrollState();
      this.applyScrollEffects();
      this.state.frameId = null;
    });

    // Handle scroll stop detection
    this.handleScrollStop();
  }

  updateScrollState() {
    // Update scroll position
    this.state.lastScrollTop = this.state.currentScrollTop;
    this.state.currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    this.state.scrollDelta = this.state.currentScrollTop - this.state.lastScrollTop;

    // Update direction
    if (this.config.ENABLE_DIRECTION_DETECTION) {
      this.state.isScrollingDown = this.state.scrollDelta > 0;
    }

    // Update velocity
    if (this.config.ENABLE_VELOCITY_TRACKING) {
      this.updateScrollVelocity();
    }

    // Trigger scroll start callback
    if (this.state.scrollDelta !== 0) {
      this.callbacks.onScrollStart(this.state);
    }
  }

  updateScrollVelocity() {
    const now = performance.now();
    const velocity = Math.abs(this.state.scrollDelta) / this.config.THROTTLE_DELAY;
    
    // Keep velocity history for smoothing
    this.state.velocityHistory.push({ velocity, timestamp: now });
    
    // Keep only recent history (last 100ms)
    this.state.velocityHistory = this.state.velocityHistory.filter(
      entry => now - entry.timestamp < 100
    );
    
    // Calculate average velocity
    if (this.state.velocityHistory.length > 0) {
      this.state.scrollVelocity = this.state.velocityHistory.reduce(
        (sum, entry) => sum + entry.velocity, 0
      ) / this.state.velocityHistory.length;
    }
  }

  applyScrollEffects() {
    const { currentScrollTop } = this.state;
    const { 
      SCROLL_THRESHOLD, 
      SCROLL_HYSTERESIS, 
      COMPACT_THRESHOLD,
      TOP_BAR_HIDE_THRESHOLD,
      ENABLE_HYSTERESIS 
    } = this.config;

    // Calculate thresholds with hysteresis
    const scrolledThreshold = ENABLE_HYSTERESIS ? 
      (this.state.isScrolled ? SCROLL_THRESHOLD - SCROLL_HYSTERESIS : SCROLL_THRESHOLD) :
      SCROLL_THRESHOLD;

    const compactThreshold = ENABLE_HYSTERESIS ?
      (this.state.isCompact ? COMPACT_THRESHOLD - SCROLL_HYSTERESIS : COMPACT_THRESHOLD) :
      COMPACT_THRESHOLD;

    // Update scrolled state
    this.updateScrolledState(currentScrollTop, scrolledThreshold);
    
    // Update compact state
    this.updateCompactState(currentScrollTop, compactThreshold);
    
    // Update top bar visibility
    this.updateTopBarState(currentScrollTop, TOP_BAR_HIDE_THRESHOLD);
  }

  updateScrolledState(scrollTop, threshold) {
    const shouldBeScrolled = scrollTop > threshold;
    
    if (shouldBeScrolled !== this.state.isScrolled) {
      this.state.isScrolled = shouldBeScrolled;
      
      // Apply state to header
      if (shouldBeScrolled) {
        this.elements.header.classList.add('scrolled');
      } else {
        this.elements.header.classList.remove('scrolled');
      }
      
      // Trigger callbacks
      this.callbacks.onStateChange('scrolled', shouldBeScrolled, this.state);
      this.callbacks.onThresholdCross('scroll', threshold, scrollTop, this.state);
    }
  }

  updateCompactState(scrollTop, threshold) {
    const shouldBeCompact = scrollTop > threshold;
    
    if (shouldBeCompact !== this.state.isCompact) {
      this.state.isCompact = shouldBeCompact;
      
      // Apply state to header
      if (shouldBeCompact) {
        this.elements.header.classList.add('compact');
      } else {
        this.elements.header.classList.remove('compact');
      }
      
      // Trigger callbacks
      this.callbacks.onStateChange('compact', shouldBeCompact, this.state);
      this.callbacks.onThresholdCross('compact', threshold, scrollTop, this.state);
    }
  }

  updateTopBarState(scrollTop, threshold) {
    const shouldHideTopBar = this.state.isScrollingDown && 
                            scrollTop > threshold &&
                            this.state.scrollVelocity > 0.5; // Only hide if scrolling with some velocity
    
    if (shouldHideTopBar !== this.state.isTopBarHidden) {
      this.state.isTopBarHidden = shouldHideTopBar;
      
      // Apply state to top bar
      if (this.elements.topBar) {
        if (shouldHideTopBar) {
          this.elements.topBar.style.transform = 'translateY(-100%)';
        } else {
          this.elements.topBar.style.transform = 'translateY(0)';
        }
      }
      
      // Trigger callbacks
      this.callbacks.onStateChange('topBarHidden', shouldHideTopBar, this.state);
    }
  }

  handleScrollStop() {
    // Clear existing timer
    clearTimeout(this.state.scrollStopTimer);
    
    // Set new timer
    this.state.scrollStopTimer = setTimeout(() => {
      // Reset velocity
      this.state.scrollVelocity = 0;
      this.state.velocityHistory = [];
      
      // Add scroll pause effect
      this.elements.header.classList.add('scroll-pause');
      
      // Remove after animation
      setTimeout(() => {
        this.elements.header.classList.remove('scroll-pause');
      }, this.config.TRANSITION_DURATION);
      
      // Trigger callback
      this.callbacks.onScrollEnd(this.state);
    }, this.config.DEBOUNCE_DELAY);
  }

  // ===== UTILITY METHODS =====
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  handleResize() {
    // Recalculate thresholds if needed
    this.checkInitialState();
  }

  handleVisibilityChange() {
    if (document.hidden) {
      // Pause animations when tab is hidden
      this.elements.header.style.animationPlayState = 'paused';
    } else {
      // Resume animations when tab is visible
      this.elements.header.style.animationPlayState = 'running';
      this.checkInitialState();
    }
  }

  checkInitialState() {
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    this.state.currentScrollTop = currentScrollTop;
    
    // Apply initial states without transitions
    this.elements.header.style.transition = 'none';
    
    // Check all thresholds
    if (currentScrollTop > this.config.SCROLL_THRESHOLD) {
      this.state.isScrolled = true;
      this.elements.header.classList.add('scrolled');
    }
    
    if (currentScrollTop > this.config.COMPACT_THRESHOLD) {
      this.state.isCompact = true;
      this.elements.header.classList.add('compact');
    }
    
    // Re-enable transitions after a frame
    requestAnimationFrame(() => {
      this.elements.header.style.transition = '';
    });
  }

  // ===== PUBLIC API =====
  destroy() {
    // Clean up event listeners
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    
    // Clear timers
    clearTimeout(this.state.debounceTimer);
    clearTimeout(this.state.scrollStopTimer);
    
    // Cancel animation frame
    if (this.state.frameId) {
      cancelAnimationFrame(this.state.frameId);
    }
  }

  getState() {
    return { ...this.state };
  }

  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export for use
window.ScrollOptimizer = ScrollOptimizer;
