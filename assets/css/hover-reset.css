/**
 * Hover Reset CSS
 * Tắt hover effect khi có class hover-disabled hoặc theo trang hiện tại
 */

/* EXTREME MODE: Ẩn hoàn toàn hover box cho trang sản phẩm */
html.page-products .nav-item .mega-menu,
html.page-products .nav-item:hover .mega-menu {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(10px) !important;
    pointer-events: none !important;
}

/* EXTREME MODE: Ẩn hoàn toàn hover box cho trang giỏ hàng */
html.page-cart .cart-container .mini-cart,
html.page-cart .cart-container:hover .mini-cart {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(10px) scale(0.98) !important;
    pointer-events: none !important;
}

/* Chỉ hiển thị lại khi có class "real-hover-allowed" */
html.page-products .nav-item.real-hover-allowed:hover .mega-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
}

html.page-cart .cart-container.real-hover-allowed:hover .mini-cart {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
    pointer-events: auto !important;
}

/* Tắt hover effect cho mega menu khi có class hover-disabled */
.nav-item.hover-disabled:hover .mega-menu,
.nav-item.hover-disabled .mega-menu:hover {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(10px) !important;
}

/* Tắt hover effect cho nav link khi có class hover-disabled */
.nav-item.hover-disabled:hover .nav-link::after {
    width: 0 !important;
}

/* Fallback cho trình duyệt không hỗ trợ :has() */
.nav-item.hover-disabled .nav-link::after {
    width: 0 !important;
}

/* Đảm bảo trạng thái active vẫn hiển thị gạch chân ngay cả khi có hover-disabled */
.nav-item.hover-disabled.active .nav-link::after,
.nav-item.active.hover-disabled .nav-link::after {
    width: 100% !important;
}

/* Đảm bảo màu sắc active cũng được hiển thị */
.nav-item.hover-disabled.active .nav-link,
.nav-item.active.hover-disabled .nav-link {
    color: #f97316 !important;
    text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
}

/* Tắt hover effect cho mini cart khi có class hover-disabled */
.cart-container.hover-disabled:hover .mini-cart,
.cart-container.hover-disabled .mini-cart:hover {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(10px) scale(0.98) !important;
}

/* Tắt hover effect cho cart badge khi có class hover-disabled */
.cart-container.hover-disabled:hover .cart-badge {
    background-color: var(--primary) !important;
}

/* FORCE ACTIVE STATE - Đảm bảo trạng thái active luôn hiển thị với specificity cao nhất */
html body .premium-header .nav-item.active .nav-link::after,
html body .premium-header .nav-item.active.hover-disabled .nav-link::after,
html body .premium-header .nav-item.hover-disabled.active .nav-link::after {
    width: 100% !important;
    background-color: #f97316 !important;
    background-image: linear-gradient(to right, #f97316, #fdba74, #f97316) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

html body .premium-header .nav-item.active .nav-link,
html body .premium-header .nav-item.active.hover-disabled .nav-link,
html body .premium-header .nav-item.hover-disabled.active .nav-link {
    color: #f97316 !important;
    text-shadow: 0 0 1px rgba(249, 115, 22, 0.1) !important;
}
