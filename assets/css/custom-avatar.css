/*
 * Custom Avatar CSS - Nội Thất Băng Vũ
 * Tùy chỉnh hiển thị avatar người dùng
 */

/* Avatar trong header chính */
.user-avatar .default-user-icon {
    font-size: 40px;
    color: var(--dark); /* <PERSON><PERSON>u đen khi chưa cuộn */
    margin: 0;
    padding: 0;
    line-height: 1;
    display: block;
    transition: color 0.3s ease;
}

/* <PERSON>àu trắng khi đã cuộn */
.premium-header.scrolled .user-avatar .default-user-icon {
    color: var(--white);
}

/* Loại bỏ background và border cho avatar mặc định */
.user-avatar {
    background-color: transparent !important;
    border: none !important;
}

/* Điều chỉnh vị trí online dot cho avatar mặc định */
.user-avatar .online-dot {
    bottom: 0;
    right: 0;
}

/* Avatar trong mobile header */
.avatar-inner.no-border {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}

.avatar-inner.no-border i {
    font-size: 36px !important;
    color: var(--dark); /* <PERSON><PERSON><PERSON> đen khi chưa cuộn */
    margin: 0;
    padding: 0;
    transition: color 0.3s ease;
}

/* Màu trắng khi đã cuộn */
.mobile-header.scrolled .avatar-inner.no-border i {
    color: var(--white);
}

/* Điều chỉnh vị trí online dot cho avatar mặc định trong mobile */
.default-avatar .avatar-status-dot {
    bottom: 0;
    right: 0;
}

/* Hiệu ứng hover cho avatar mặc định trong mobile */
.mobile-nav-item:hover .avatar-inner.no-border i {
    transform: scale(1.1);
    opacity: 1;
    color: var(--primary);
}

/* Hiệu ứng hover cho avatar mặc định trong mobile khi đã cuộn */
.mobile-header.scrolled .mobile-nav-item:hover .avatar-inner.no-border i {
    color: var(--primary-light);
}

/* Hiệu ứng hover cho avatar mặc định khi chưa cuộn */
.user-account-btn:hover .default-user-icon {
    color: var(--primary) !important;
}

/* Hiệu ứng hover cho avatar mặc định khi đã cuộn */
.premium-header.scrolled .user-account-btn:hover .default-user-icon {
    color: var(--white) !important;
}

/* Hiệu ứng active cho avatar mặc định */
.mobile-nav-item.active .avatar-inner.no-border {
    animation: none !important;
    box-shadow: none !important;
}

.mobile-nav-item.active .avatar-inner.no-border i {
    animation: avatar-pulse 2s infinite ease-in-out;
    color: var(--primary);
}

/* Hiệu ứng active cho avatar mặc định trong mobile khi đã cuộn */
.mobile-header.scrolled .mobile-nav-item.active .avatar-inner.no-border i {
    color: var(--primary-light);
}

@keyframes avatar-pulse {
    0% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.9;
    }
}
