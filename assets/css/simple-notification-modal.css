/**
 * Simple Notification Modal Style
 * Thiết kế lại #simple-notification thành dạng modal box giống delete-confirmation-modal
 * Không thay đổi logic hoạt động, chỉ thay đổi giao diện hiển thị
 */

/* Override styling cho #simple-notification với độ ưu tiên cao nhất */
body #simple-notification,
html #simple-notification,
div#simple-notification,
#simple-notification {
    /* Reset tất cả các style có thể gây conflict */
    all: unset !important;

    /* Thiết lập lại các style cần thiết */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2147483647 !important; /* Z-index tối đa như delete-confirmation-modal */

    /* Layout giống delete-confirmation-modal */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.5) !important; /* Backdrop */

    /* Reset margin, padding có thể gây lệch */
    margin: 0 !important;
    padding: 0 !important;

    /* Reset transform có thể gây lệch */
    transform: none !important;

    /* Animation với hiệu suất cao */
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;

    /* Tối ưu hiệu suất */
    will-change: opacity, visibility !important;
    contain: layout style paint !important;

    /* Đảm bảo box-sizing */
    box-sizing: border-box !important;

    /* Reset font và text properties */
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    color: inherit !important;

    /* Reset border và outline */
    border: none !important;
    outline: none !important;
    border-radius: 0 !important;

    /* Reset background properties khác */
    background-image: none !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: cover !important;
}

/* Khi notification hiển thị */
body #simple-notification.show,
html #simple-notification.show,
div#simple-notification.show,
#simple-notification.show {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Modal box chính với thiết kế đẹp hơn */
body #simple-notification .notification-content,
html #simple-notification .notification-content,
div#simple-notification .notification-content,
#simple-notification .notification-content {
    /* Reset tất cả style có thể gây conflict */
    all: unset !important;

    /* Thiết lập lại style cần thiết */
    display: block !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border-radius: 20px !important;
    box-shadow:
        0 20px 40px -8px rgba(0, 0, 0, 0.15),
        0 8px 16px -4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
    max-width: 480px !important;
    width: 90% !important;
    min-width: 350px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    transform: translateY(20px) scale(0.96) !important;
    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease !important;

    /* Tối ưu hiệu suất cho modal content */
    will-change: transform, opacity !important;
    contain: layout style paint !important;
    position: relative !important;

    /* Đảm bảo căn giữa */
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;

    /* Box sizing */
    box-sizing: border-box !important;

    /* Reset text properties */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-align: center !important;
    color: inherit !important;

    /* Backdrop filter cho hiệu ứng glass */
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;

    /* Không có border */
    border: none !important;
}

/* Khi notification hiển thị */
body #simple-notification.show .notification-content,
html #simple-notification.show .notification-content,
div#simple-notification.show .notification-content,
#simple-notification.show .notification-content {
    transform: translateY(0) scale(1) !important;
    opacity: 1 !important;
    animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards !important;
}

/* Animation slide in mượt mà và hiệu suất cao */
@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.96);
    }
    60% {
        opacity: 1;
        transform: translateY(-2px) scale(1.01);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Animation khi đóng */
#simple-notification.closing .notification-content {
    animation: modalSlideOut 0.3s cubic-bezier(0.4, 0, 1, 1) forwards !important;
}

@keyframes modalSlideOut {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-10px) scale(0.98);
    }
}

/* Header section với thiết kế đẹp hơn */
#simple-notification .notification-header {
    padding: 32px 32px 20px 32px !important;
    text-align: center !important;
    position: relative !important;
    overflow: hidden !important;
    border-radius: 20px 20px 0 0 !important;
}

/* Success header với gradient đẹp */
#simple-notification.notification-success .notification-header {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%) !important;
    border-bottom: 1px solid rgba(34, 197, 94, 0.1) !important;
}

/* Error header với gradient đẹp */
#simple-notification.notification-error .notification-header {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 50%, #fecaca 100%) !important;
    border-bottom: 1px solid rgba(239, 68, 68, 0.1) !important;
}

/* Warning header với gradient đẹp */
#simple-notification.notification-warning .notification-header {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 50%, #fde68a 100%) !important;
    border-bottom: 1px solid rgba(245, 158, 11, 0.1) !important;
}

/* Info header với gradient đẹp */
#simple-notification.notification-info .notification-header {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #bfdbfe 100%) !important;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;
}

/* Hiệu ứng pattern overlay cho header */
#simple-notification .notification-header::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                      radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%) !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

/* Đảm bảo nội dung header hiển thị trên pattern */
#simple-notification .notification-header > * {
    position: relative !important;
    z-index: 2 !important;
}

/* Icon container với thiết kế đẹp hơn */
#simple-notification .notification-icon {
    width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 16px auto !important;
    position: relative !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    animation: iconPulse 3s ease-in-out infinite !important;

    /* Tối ưu hiệu suất cho icon */
    will-change: transform, box-shadow !important;
}

/* Success icon với gradient và shadow */
#simple-notification.notification-success .notification-icon {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4) !important;
}

#simple-notification.notification-success .notification-icon i {
    color: white !important;
    font-size: 32px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Error icon với gradient và shadow */
#simple-notification.notification-error .notification-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
}

#simple-notification.notification-error .notification-icon i {
    color: white !important;
    font-size: 32px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Warning icon với gradient và shadow */
#simple-notification.notification-warning .notification-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4) !important;
}

#simple-notification.notification-warning .notification-icon i {
    color: white !important;
    font-size: 32px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Info icon với gradient và shadow */
#simple-notification.notification-info .notification-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

#simple-notification.notification-info .notification-icon i {
    color: white !important;
    font-size: 32px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Animation pulse cho icon - mượt mà hơn */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.18);
    }
}

/* Hiệu ứng ring xung quanh icon */
#simple-notification .notification-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -8px !important;
    left: -8px !important;
    right: -8px !important;
    bottom: -8px !important;
    border-radius: 50% !important;
    border: 2px solid currentColor !important;
    opacity: 0.2 !important;
    animation: iconRing 2s ease-in-out infinite !important;
}

@keyframes iconRing {
    0%, 100% {
        transform: scale(1);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
}

/* Content section với thiết kế đẹp hơn */
#simple-notification .notification-body {
    padding: 24px 32px 32px 32px !important;
    text-align: center !important;
    position: relative !important;
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 0 0 20px 20px !important;
}

/* Title styling với typography đẹp hơn */
#simple-notification .notification-title {
    font-size: 24px !important;
    font-weight: 800 !important;
    color: #1f2937 !important; /* gray-800 */
    margin: 0 0 16px 0 !important;
    line-height: 1.2 !important;
    letter-spacing: -0.02em !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    position: relative !important;
    padding: 0 16px !important;
}

/* Decorative line dưới title */
#simple-notification .notification-title::after {
    content: '' !important;
    position: absolute !important;
    bottom: -8px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 40px !important;
    height: 3px !important;
    border-radius: 2px !important;
    background: linear-gradient(90deg, transparent, currentColor, transparent) !important;
    opacity: 0.3 !important;
}

/* Message styling với typography đẹp hơn */
#simple-notification .notification-message {
    font-size: 16px !important;
    color: #374151 !important; /* gray-700 */
    line-height: 1.7 !important;
    margin: 24px auto 28px auto !important;
    font-weight: 500 !important;
    max-width: 380px !important;
    opacity: 0.95 !important;
    padding: 16px 20px !important;
    background: rgba(255, 255, 255, 0.6) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    position: relative !important;
}

/* Subtle pattern cho message background - gradient removed */
#simple-notification .notification-message::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image: none !important;
    border-radius: 12px !important;
    pointer-events: none !important;
}

/* Hiệu ứng gradient text cho title theo loại - căn giữa và đẹp hơn */
#simple-notification.notification-success .notification-title {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    display: block !important;
    width: 100% !important;
}

#simple-notification.notification-success .notification-title::after {
    background: linear-gradient(90deg, transparent, #10b981, transparent) !important;
}

#simple-notification.notification-error .notification-title {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    display: block !important;
    width: 100% !important;
}

#simple-notification.notification-error .notification-title::after {
    background: linear-gradient(90deg, transparent, #ef4444, transparent) !important;
}

#simple-notification.notification-warning .notification-title {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    display: block !important;
    width: 100% !important;
}

#simple-notification.notification-warning .notification-title::after {
    background: linear-gradient(90deg, transparent, #f59e0b, transparent) !important;
}

#simple-notification.notification-info .notification-title {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    display: block !important;
    width: 100% !important;
}

#simple-notification.notification-info .notification-title::after {
    background: linear-gradient(90deg, transparent, #3b82f6, transparent) !important;
}

/* Close button với thiết kế đẹp hơn */
#simple-notification .notification-close {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border: 2px solid #e2e8f0 !important;
    color: #64748b !important;
    cursor: pointer !important;
    padding: 12px 24px !important;
    border-radius: 12px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    position: relative !important;
    overflow: hidden !important;
    min-width: 120px !important;
    justify-content: center !important;
}

/* Hiệu ứng hover cho nút đóng */
#simple-notification .notification-close:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
    border-color: #cbd5e1 !important;
    color: #475569 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Hiệu ứng active cho nút đóng */
#simple-notification .notification-close:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Icon trong nút đóng */
#simple-notification .notification-close i {
    font-size: 14px !important;
    margin: 0 !important;
    color: inherit !important;
    transition: transform 0.2s ease !important;
}

#simple-notification .notification-close:hover i {
    transform: rotate(90deg) !important;
}

/* Hiệu ứng ripple cho nút đóng */
#simple-notification .notification-close::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 0 !important;
    height: 0 !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.6) !important;
    transform: translate(-50%, -50%) !important;
    transition: width 0.6s, height 0.6s !important;
}

#simple-notification .notification-close:active::before {
    width: 300px !important;
    height: 300px !important;
}

/* Hiệu ứng shake cho modal khi lỗi giống delete-confirmation-modal */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

#simple-notification.shake .notification-content {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both !important;
}

/* Responsive design cho mobile */
@media (max-width: 640px) {
    /* Modal content responsive */
    #simple-notification .notification-content {
        max-width: 95vw !important;
        min-width: 300px !important;
        margin: 16px !important;
        border-radius: 16px !important;
    }

    /* Header responsive */
    #simple-notification .notification-header {
        padding: 24px 20px 20px 20px !important;
    }

    /* Body responsive */
    #simple-notification .notification-body {
        padding: 0 24px 24px 24px !important;
    }

    /* Icon responsive */
    #simple-notification .notification-icon {
        width: 70px !important;
        height: 70px !important;
        margin-bottom: 12px !important;
    }

    #simple-notification .notification-icon i {
        font-size: 28px !important;
    }

    /* Typography responsive */
    #simple-notification .notification-title {
        font-size: 21px !important;
        margin-bottom: 14px !important;
        padding: 0 12px !important;
    }

    #simple-notification .notification-message {
        font-size: 15px !important;
        margin: 20px auto 24px auto !important;
        line-height: 1.6 !important;
        padding: 14px 16px !important;
        max-width: 320px !important;
    }

    /* Button responsive */
    #simple-notification .notification-close {
        padding: 10px 20px !important;
        font-size: 13px !important;
        min-width: 100px !important;
    }
}

/* Responsive design cho tablet */
@media (max-width: 768px) and (min-width: 641px) {
    #simple-notification .notification-content {
        max-width: 85vw !important;
        min-width: 320px !important;
    }

    #simple-notification .notification-icon {
        width: 75px !important;
        height: 75px !important;
    }

    #simple-notification .notification-icon i {
        font-size: 30px !important;
    }

    #simple-notification .notification-title {
        font-size: 21px !important;
    }

    #simple-notification .notification-message {
        font-size: 15px !important;
    }
}

/* Đảm bảo không bị conflict với các style khác */
#simple-notification * {
    box-sizing: border-box !important;
}

/* Ngăn cuộn trang khi modal hiển thị */
body.notification-active {
    overflow: hidden !important;
}

/* Đảm bảo modal luôn hiển thị trên cùng */
#simple-notification {
    z-index: 2147483647 !important;
}

/* Legacy support - ẩn các style cũ */
#simple-notification[style*="background-color"] {
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Reset tất cả inline styles có thể gây conflict với độ ưu tiên tối đa */
body #simple-notification[style],
html #simple-notification[style],
div#simple-notification[style],
#simple-notification[style] {
    background: rgba(0, 0, 0, 0.5) !important;
    color: inherit !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    margin: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    min-width: auto !important;
    max-width: none !important;
    max-height: none !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    position: fixed !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2147483647 !important;
}

/* Đảm bảo không bị ảnh hưởng bởi CSS framework khác */
.container #simple-notification,
.row #simple-notification,
.col #simple-notification,
.flex #simple-notification,
.grid #simple-notification,
.block #simple-notification,
.inline #simple-notification,
.relative #simple-notification,
.absolute #simple-notification {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2147483647 !important;
}

/* Override cho Tailwind CSS và Bootstrap */
#simple-notification.fixed,
#simple-notification.absolute,
#simple-notification.relative,
#simple-notification.static,
#simple-notification.sticky {
    position: fixed !important;
}

#simple-notification.inset-0,
#simple-notification.top-0,
#simple-notification.left-0,
#simple-notification.right-0,
#simple-notification.bottom-0 {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

/* Override cho các class có thể gây conflict */
#simple-notification.w-full,
#simple-notification.h-full,
#simple-notification.w-screen,
#simple-notification.h-screen {
    width: 100vw !important;
    height: 100vh !important;
}

#simple-notification.flex,
#simple-notification.grid,
#simple-notification.block,
#simple-notification.inline-block {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}
