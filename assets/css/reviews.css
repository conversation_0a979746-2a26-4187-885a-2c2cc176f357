/* CSS cho hệ thống đ<PERSON>h giá và bình luận */

/* Elegant Rating Container */
.elegant-rating-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

/* Rating Stars */
.rating {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    margin-bottom: 10px;
}

.rating input {
    display: none;
}

.rating label {
    cursor: pointer;
    width: 32px;
    height: 32px;
    margin: 0 4px;
    position: relative;
    font-size: 28px;
    color: #e2e8f0;
    transition: all 0.2s ease;
}

.rating label:before {
    content: '\f005';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 0;
    left: 0;
}

.rating input:checked~label,
.rating input:checked~label~label {
    color: #ffc107;
}

.rating label:hover,
.rating label:hover~label {
    color: #ffc107;
}

/* Rating Feedback */
.rating-feedback {
    text-align: center;
    margin-top: 8px;
    min-height: 24px;
}

.rating-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 16px;
    background-color: #f8fafc;
    transition: all 0.3s ease;
}

/* Media Upload */
.media-upload-container {
    position: relative;
}

.media-upload {
    display: block;
    width: 100%;
    padding: 0.5rem;
    border: 1px dashed #cbd5e0;
    border-radius: 0.25rem;
    background-color: #f7fafc;
}

.media-upload:hover {
    border-color: #4299e1;
}

/* Review Media Gallery */
.review-media-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.25rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.review-media-item:hover {
    transform: scale(1.05);
}

.review-media-item img,
.review-media-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Standardize media sizes */
.review-media-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.review-media-item.size-standard {
    width: 80px;
    height: 80px;
}

.review-media-item.size-small {
    width: 60px;
    height: 60px;
}

/* Reply Form */
.reply-form {
    background-color: #f9fafb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 0.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Helpful Button */
.helpful-btn {
    display: flex;
    align-items: center;
    transition: color 0.2s;
}

.helpful-btn.active {
    color: #4299e1;
}

.helpful-btn.active i {
    font-weight: 900;
}

/* Report Button */
.report-btn {
    color: #718096;
    transition: color 0.2s;
}

.report-btn:hover {
    color: #e53e3e;
}

.report-btn.reported {
    color: #e53e3e;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
}

.pagination-btn {
    transition: all 0.2s;
    margin: 0 2px;
}

.pagination-btn:hover {
    transform: translateY(-2px);
}

.pagination-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    margin: 0 4px;
    transition: all 0.2s;
}

.pagination-nav:hover {
    background-color: #e5e7eb;
}

.pagination-nav.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Time display */
.review-time {
    color: #718096;
    font-size: 0.875rem;
}

.review-time-relative {
    margin-left: 0.5rem;
    font-size: 0.75rem;
    color: #a0aec0;
}

/* Media Viewer */
.media-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.media-viewer.active {
    opacity: 1;
    visibility: visible;
}

.media-viewer-content {
    max-width: 90%;
    max-height: 90%;
    position: relative;
}

.media-viewer-content img,
.media-viewer-content video {
    max-width: 100%;
    max-height: 90vh;
    display: block;
    margin: 0 auto;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.media-viewer-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 10000;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.media-viewer-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.media-viewer-prev,
.media-viewer-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 10000;
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.media-viewer-prev:hover,
.media-viewer-next:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.media-viewer-prev {
    left: 20px;
}

.media-viewer-next {
    right: 20px;
}

/* Counter */
.media-viewer-counter {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
}

/* Emoji Picker */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    right: 0;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    z-index: 10;
    display: none;
}

.emoji-picker.active {
    display: block;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.25rem;
}

.emoji-item {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.emoji-item:hover {
    background-color: #f7fafc;
}

/* Sticker Picker */
.sticker-picker {
    position: absolute;
    bottom: 100%;
    right: 0;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    z-index: 10;
    display: none;
    width: 300px;
    max-height: 300px;
    overflow-y: auto;
}

.sticker-picker.active {
    display: block;
}

.sticker-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.sticker-item {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: transform 0.2s;
    overflow: hidden;
}

.sticker-item:hover {
    transform: scale(1.05);
}

.sticker-item img {
    max-width: 100%;
    max-height: 100%;
}

/* User Badges */
.user-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 6px;
}

.badge-verified-buyer {
    background-color: #c6f6d5;
    color: #22543d;
}

.badge-admin {
    background-color: #bee3f8;
    color: #2a4365;
}

/* Review Content */
.review-content {
    white-space: pre-line;
    word-break: break-word;
}

/* Review Filter */
.review-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.review-filter.hidden {
    display: none;
}

.filter-btn {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 13px;
    background-color: #f3f4f6;
    color: #4b5563;
    transition: all 0.2s;
}

.filter-btn:hover {
    background-color: #e5e7eb;
}

.filter-btn.active {
    background-color: #3b82f6;
    color: white;
}

/* Review Form Guide */
.review-form-guide {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    border-left: 4px solid #3b82f6;
}

.review-form-guide h4 {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1e3a8a;
}

.review-form-guide ul {
    padding-left: 20px;
    list-style-type: disc;
}

.review-form-guide li {
    margin-bottom: 4px;
    font-size: 14px;
    color: #4b5563;
}

/* Responsive */
@media (max-width: 768px) {
    .rating label {
        width: 25px;
        height: 25px;
        font-size: 20px;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .sticker-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .sticker-picker {
        width: 250px;
    }

    .review-media-item.size-standard {
        width: 60px;
        height: 60px;
    }

    .review-media-item.size-small {
        width: 50px;
        height: 50px;
    }

    .review-filter {
        overflow-x: auto;
        padding-bottom: 8px;
        flex-wrap: nowrap;
    }
}