/**
 * Banner Slider CSS
 * Styles for the banner slider on the homepage
 */

/* Main Banner Slider */
.main-banner-slider {
  position: relative;
  margin-bottom: 2rem;
}

.main-banner-slider .swiper-container {
  width: 100%;
  height: 85vh;
  /* <PERSON>hay đổi thành 85% chiều cao viewport */
  border-radius: 0.5rem;
  overflow: hidden;
}

.main-banner-slider .swiper-slide {
  width: 100%;
  height: 85vh;
  /* Thay đổi thành 85% chiều cao viewport */
}

/* Navigation Buttons */
.main-banner-slider .swiper-button-next,
.main-banner-slider .swiper-button-prev {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.main-banner-slider .swiper-button-next:hover,
.main-banner-slider .swiper-button-prev:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.main-banner-slider .swiper-button-next:after,
.main-banner-slider .swiper-button-prev:after {
  font-size: 1.5rem;
  font-weight: bold;
}

/* Pagination */
.main-banner-slider .swiper-pagination {
  bottom: 1rem;
}

.main-banner-slider .swiper-pagination-bullet {
  width: 0.75rem;
  height: 0.75rem;
  background-color: rgba(255, 255, 255, 0.7);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.main-banner-slider .swiper-pagination-bullet-active {
  background-color: var(--primary, #F37321);
  opacity: 1;
  width: 2rem;
  border-radius: 0.375rem;
}

/* Banner Content */
.main-banner-slider .banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: #fff;
}

.main-banner-slider .banner-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.main-banner-slider .banner-description {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

/* Secondary Banners */
.secondary-banner {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  height: 300px;
  margin-bottom: 1.5rem;
}

.secondary-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.secondary-banner:hover img {
  transform: scale(1.05);
}

.secondary-banner .banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: #fff;
}

.secondary-banner .banner-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.secondary-banner .banner-description {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  opacity: 0.9;
}

/* Banner Section */
.banner-section {
  width: 100%;
}

.banner-section .banner-slide-inner,
.banner-section .swiper-slide {
  height: calc(100vh - 169px) !important;
  /* Thay đổi thành 100% và trừ đi 169px của header để banner luôn hiển thị trọn vẹn */
  width: 100% !important;
}

.banner-section .banner-slide-inner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive Adjustments */
@media (max-width: 768px) {

  .main-banner-slider .swiper-container,
  .main-banner-slider .swiper-slide,
  .banner-section .banner-slide-inner,
  .banner-section .swiper-slide {
    height: 85vh !important;
    /* Duy trì 85% chiều cao viewport trên tablet */
  }

  .main-banner-slider .swiper-button-next,
  .main-banner-slider .swiper-button-prev {
    width: 2.5rem;
    height: 2.5rem;
  }

  .main-banner-slider .swiper-button-next:after,
  .main-banner-slider .swiper-button-prev:after {
    font-size: 1.25rem;
  }

  .main-banner-slider .banner-title {
    font-size: 1.5rem;
  }

  .main-banner-slider .banner-description {
    font-size: 1rem;
  }

  .secondary-banner {
    height: 250px;
  }

  .secondary-banner .banner-title {
    font-size: 1.25rem;
  }

  .secondary-banner .banner-description {
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {

  .main-banner-slider .swiper-container,
  .main-banner-slider .swiper-slide,
  .banner-section .banner-slide-inner,
  .banner-section .swiper-slide {
    height: 85vh !important;
    /* Duy trì 85% chiều cao viewport trên mobile */
  }

  .main-banner-slider .swiper-button-next,
  .main-banner-slider .swiper-button-prev {
    width: 2rem;
    height: 2rem;
  }

  .main-banner-slider .swiper-button-next:after,
  .main-banner-slider .swiper-button-prev:after {
    font-size: 1rem;
  }

  .main-banner-slider .banner-title {
    font-size: 1.25rem;
  }

  .main-banner-slider .banner-description {
    font-size: 0.875rem;
  }

  .secondary-banner {
    height: 200px;
  }

  .secondary-banner .banner-title {
    font-size: 1.125rem;
  }

  .secondary-banner .banner-description {
    font-size: 0.75rem;
  }
}