/**
 * Fix Overflow CSS
 * <PERSON><PERSON><PERSON><PERSON> quyết vấn đề thanh cuộn ngang cho banner full-width mà không ảnh hưởng đến sticky header
 */

/* Sửa lỗi thanh cuộn ngang toàn cục */
html {
    overflow-x: hidden;
    width: 100%;
    scroll-behavior: smooth;
}

/* Đ<PERSON><PERSON> bảo body không bị overflow */
body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
    min-height: 100vh;
}

/* Đảm bảo main container không gây overflow */
.site-main {
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

/* Fix cho banner section - Gi<PERSON>i pháp tối ưu cho full-width banner */
.banner-section {
    position: relative;
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
    max-width: none !important;
    box-sizing: border-box;
    /* Đảm bảo banner không tạo thanh cuộn ngang */
    contain: layout style;
}

/* <PERSON><PERSON>m bảo sticky header vẫn hoạt động bình thường */
.premium-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 1020 !important;
}

/* <PERSON><PERSON> hiệu hóa hiệu ứng transform trên header */
.premium-header.smooth-transition:not(.scrolled) {
    transform: none !important;
}

/* Đảm bảo header vẫn hiển thị khi cuộn */
.premium-header.scrolled {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
}

/* Đảm bảo banner swiper không gây overflow */
.banner-section .swiper-container,
.banner-section .banner-swiper {
    width: 100%;
    overflow: hidden;
    position: relative;
}

.banner-section .swiper-wrapper {
    width: 100%;
    position: relative;
}

.banner-section .swiper-slide {
    width: 100%;
    overflow: hidden;
}

/* Đảm bảo container trong banner vẫn được căn giữa */
.banner-section .container {
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    position: relative;
    z-index: 10;
}

/* Responsive adjustments cho banner full-width */
@media (max-width: 1400px) {
    .banner-section .container {
        max-width: 1200px;
    }
}

@media (max-width: 1200px) {
    .banner-section .container {
        max-width: 960px;
    }
}

@media (max-width: 992px) {
    .banner-section .container {
        max-width: 720px;
    }
}

@media (max-width: 768px) {
    .banner-section .container {
        max-width: 540px;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

@media (max-width: 576px) {
    .banner-section .container {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Đảm bảo banner không gây overflow trên mobile */
    .banner-section {
        margin-left: -50vw;
        margin-right: -50vw;
        left: 50%;
        right: 50%;
    }
}

/* Đảm bảo tất cả các phần tử con của banner không gây overflow */
.banner-section * {
    box-sizing: border-box;
}

/* Đảm bảo banner slide không gây overflow */
.banner-slide-inner {
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* Đảm bảo hình ảnh banner không gây overflow */
.banner-section img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
}

/* Đảm bảo navigation buttons không gây overflow */
.navigation-container {
    overflow: hidden;
    width: 100%;
    position: absolute;
}

/* Đảm bảo pagination không gây overflow */
.banner-pagination-container {
    overflow: hidden;
    width: 100%;
    position: absolute;
}

/* Fix cho các trường hợp edge case */
@media (max-width: 320px) {
    .banner-section {
        margin-left: -50vw;
        margin-right: -50vw;
        left: 50%;
        right: 50%;
        width: 100vw;
    }

    .banner-section .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}
