/*
 * Dropdown Invisible Bridge CSS
 * Tạo cầu nối vô hình giữa trigger elements và dropdown boxes
 * để duy trì hover state khi di chuyển chuột
 */

/* ===== INVISIBLE BRIDGE FOR MEGA MENU ===== */

.nav-item {
    position: relative;
}

/* Tạo invisible bridge cho mega menu */
.nav-item::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 25px; /* Chiều cao bridge = khoảng cách từ nav-item đến mega-menu */
    background: transparent;
    z-index: 999; /* Thấp hơn dropdown nhưng cao hơn content */
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Kích hoạt bridge khi hover vào nav-item */
.nav-item:hover::after {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Đ<PERSON>m bảo mega-menu có z-index cao hơn bridge */
.mega-menu {
    z-index: 1000 !important;
}

/* ===== INVISIBLE BRIDGE FOR USER DROPDOWN ===== */

.user-dropdown {
    position: relative;
}

/* Tạo invisible bridge cho user dropdown */
.user-dropdown::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 25px; /* Chiều cao bridge = khoảng cách từ user-dropdown đến user-dropdown-menu */
    background: transparent;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Kích hoạt bridge khi hover vào user-dropdown */
.user-dropdown:hover::after {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Đảm bảo user-dropdown-menu có z-index cao hơn bridge */
.user-dropdown-menu {
    z-index: 1000 !important;
}

/* ===== INVISIBLE BRIDGE FOR CART DROPDOWN ===== */

.cart-container {
    position: relative;
}

/* Tạo invisible bridge cho cart dropdown */
.cart-container::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 25px; /* Chiều cao bridge = khoảng cách từ cart-container đến mini-cart */
    background: transparent;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Kích hoạt bridge khi hover vào cart-container */
.cart-container:hover::after {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Đảm bảo mini-cart có z-index cao hơn bridge */
.mini-cart {
    z-index: 1000 !important;
}

/* ===== ENHANCED HOVER STATES ===== */

/* Đảm bảo hover state được duy trì khi chuột ở trên bridge */
.nav-item:hover .mega-menu,
.nav-item::after:hover + .mega-menu,
.mega-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
}

.user-dropdown:hover .user-dropdown-menu,
.user-dropdown::after:hover + .user-dropdown-menu,
.user-dropdown-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
}

.cart-container:hover .mini-cart,
.cart-container::after:hover + .mini-cart,
.mini-cart:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

/* Chỉ áp dụng cho desktop */
@media (min-width: 769px) {
    /* Tất cả các bridge chỉ hoạt động trên desktop */
    .nav-item::after,
    .user-dropdown::after,
    .cart-container::after {
        display: block;
    }
}

/* Ẩn bridge trên mobile */
@media (max-width: 768px) {
    .nav-item::after,
    .user-dropdown::after,
    .cart-container::after {
        display: none !important;
    }
}

/* ===== DEBUG MODE (Tạm thời để test) ===== */
/* Uncomment để thấy bridge khi debug */
/*
.nav-item::after,
.user-dropdown::after,
.cart-container::after {
    background: rgba(255, 0, 0, 0.1) !important;
    border: 1px dashed red !important;
}
*/

/* ===== FALLBACK FOR OLDER BROWSERS ===== */

/* Đảm bảo tương thích với các trình duyệt cũ */
.nav-item,
.user-dropdown,
.cart-container {
    /* Đảm bảo position relative được set */
    position: relative;
}

/* Đảm bảo dropdown có position absolute */
.mega-menu,
.user-dropdown-menu,
.mini-cart {
    position: absolute;
}

/* ===== PERFORMANCE OPTIMIZATION ===== */

/* Sử dụng transform3d để kích hoạt hardware acceleration */
.nav-item::after,
.user-dropdown::after,
.cart-container::after {
    transform: translate3d(0, 0, 0);
    will-change: opacity, visibility;
}

/* Tối ưu transition cho performance */
.nav-item::after,
.user-dropdown::after,
.cart-container::after {
    transition: opacity 0.2s ease, visibility 0.2s ease;
}
