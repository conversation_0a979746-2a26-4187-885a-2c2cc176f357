/**
 * CSS cho nội dung được tạo bởi SimpleEditor ở phía người dùng
 * Phát triển bởi: Nội thất Bàng Vũ
 * Version: 1.0.0
 */

/* Container cho hình ảnh */
.simple-editor-image-container {
    display: block;
    margin: 2rem 0;
    max-width: 100%;
}

/* Figure và caption */
.simple-editor-figure {
    display: table;
    margin: 0 auto;
    max-width: 100%;
    text-align: center;
}

.simple-editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.simple-editor-caption {
    display: table-caption;
    caption-side: bottom;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
    font-style: italic;
}

/* <PERSON><PERSON><PERSON> dạng nội dung */
.product-content p {
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.product-content h1,
.product-content h2,
.product-content h3,
.product-content h4,
.product-content h5,
.product-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #2d3748;
}

.product-content h1 {
    font-size: 2rem;
}

.product-content h2 {
    font-size: 1.75rem;
}

.product-content h3 {
    font-size: 1.5rem;
}

.product-content h4 {
    font-size: 1.25rem;
}

.product-content h5 {
    font-size: 1.125rem;
}

.product-content h6 {
    font-size: 1rem;
}

.product-content ul,
.product-content ol {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.product-content ul li,
.product-content ol li {
    margin-bottom: 0.5rem;
}

.product-content a {
    color: #3182ce;
    text-decoration: underline;
}

.product-content a:hover {
    color: #2c5282;
}

/* Định dạng căn chỉnh */
.product-content .text-left {
    text-align: left;
}

.product-content .text-center {
    text-align: center;
}

.product-content .text-right {
    text-align: right;
}

.product-content .text-justify {
    text-align: justify;
}

/* Responsive */
@media (max-width: 768px) {
    .simple-editor-image-container {
        margin: 1.5rem 0;
    }

    .simple-editor-caption {
        padding: 0.5rem;
        font-size: 0.8125rem;
    }

    .product-content h1 {
        font-size: 1.75rem;
    }

    .product-content h2 {
        font-size: 1.5rem;
    }

    .product-content h3 {
        font-size: 1.25rem;
    }

    .product-content h4 {
        font-size: 1.125rem;
    }

    .product-content h5,
    .product-content h6 {
        font-size: 1rem;
    }
}