/**
 * Giải pháp 3: CSS Contain Property
 * Sử dụng CSS contain property để kiểm soát containment mà không clip search suggestions
 */

/*
 * CSS Variables cho solution 3
 */
:root {
    --search-contain-z-index: 1000;
    --search-transition-duration: 0.3s;
    --search-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * Header section với CSS contain
 * Sử dụng contain: layout style để tối ưu performance
 * Không sử dụng contain: paint để tránh clip search suggestions
 */
.header-section-solution3 {
    position: relative;
    overflow: hidden;

    /* CSS Containment - chỉ sử dụng layout và style, không dùng paint */
    contain: layout style !important;

    /* Đảm bảo không có size containment */
    /* contain: layout style size; - KHÔNG sử dụng */

    /* Performance optimizations */
    will-change: auto;
    transform: translateZ(0); /* Tạo stacking context */
}

/*
 * Search container với z-index cao
 */
.search-container-solution3 {
    position: relative;
    z-index: var(--search-contain-z-index);

    /* Tạo stacking context riêng */
    isolation: isolate;
    transform: translateZ(0);
}

/*
 * Search input styling
 */
.search-input-solution3 {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all var(--search-transition-duration) var(--search-transition-timing);
    background: white;
    outline: none;
    position: relative;
    z-index: 1;
}

.search-input-solution3:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/*
 * Search suggestions với z-index cao và không bị contain
 */
.search-suggestions-solution3 {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    z-index: calc(var(--search-contain-z-index) + 100);

    /* Style cơ bản */
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;

    /* Đảm bảo không bị contain */
    contain: none !important;

    /* Tạo stacking context riêng */
    isolation: isolate;
    transform: translateZ(0);

    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) translateZ(0);
    transition: all var(--search-transition-duration) var(--search-transition-timing);

    /* Performance optimizations */
    will-change: transform, opacity;
    backface-visibility: hidden;
}

/*
 * Khi suggestions được hiển thị
 */
.search-suggestions-solution3.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) translateZ(0);
}

/*
 * Suggestion items styling
 */
.search-suggestion-item-solution3 {
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-suggestion-item-solution3:last-child {
    border-bottom: none;
}

.search-suggestion-item-solution3:hover {
    background-color: #f9fafb;
    transform: translateX(4px);
}

.search-suggestion-item-solution3:active {
    background-color: #f3f4f6;
    transform: translateX(2px);
}

/*
 * Products section với z-index thấp hơn
 */
.products-section-solution3 {
    position: relative;
    z-index: 1;

    /* Đảm bảo không tạo stacking context mới */
    transform: none;
    will-change: auto;
}

/*
 * Browser support và fallbacks
 */

/* Fallback cho browsers không hỗ trợ CSS contain */
@supports not (contain: layout) {
    .header-section-solution3 {
        /* Fallback về giải pháp overflow visible */
        overflow: visible;
    }

    .search-suggestions-solution3 {
        z-index: 9999 !important;
    }
}

/* Fallback cho browsers không hỗ trợ isolation */
@supports not (isolation: isolate) {
    .search-container-solution3 {
        position: relative;
        z-index: 9998;
    }

    .search-suggestions-solution3 {
        z-index: 9999 !important;
    }
}

/*
 * Responsive adjustments
 */
@media (max-width: 768px) {
    .search-suggestions-solution3 {
        max-height: 60vh;
        border-radius: 8px;
    }

    .search-suggestion-item-solution3 {
        padding: 16px;
        font-size: 16px; /* Tránh zoom trên iOS */
    }
}

@media (max-width: 480px) {
    .search-suggestions-solution3 {
        max-height: 50vh;
        border-radius: 6px;
    }

    .search-suggestion-item-solution3 {
        padding: 14px 12px;
    }
}

/*
 * Accessibility improvements
 */
.search-suggestions-solution3[aria-hidden="false"] {
    opacity: 1 !important;
    visibility: visible !important;
}

.search-suggestion-item-solution3:focus {
    outline: 2px solid #f59e0b;
    outline-offset: 2px;
    background-color: #fef3c7;
}

.search-suggestion-item-solution3[aria-selected="true"] {
    background-color: #fef3c7;
    border-left: 3px solid #f59e0b;
}

/*
 * High contrast mode support
 */
@media (prefers-contrast: high) {
    .search-suggestions-solution3 {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .search-suggestion-item-solution3:hover {
        background-color: #000;
        color: #fff;
    }
}

/*
 * Reduced motion support
 */
@media (prefers-reduced-motion: reduce) {
    .search-suggestions-solution3 {
        transition: opacity 0.1s ease;
        transform: none !important;
    }

    .search-suggestion-item-solution3 {
        transition: background-color 0.1s ease;
        transform: none !important;
    }
}

/*
 * Dark mode support
 */
@media (prefers-color-scheme: dark) {
    .search-suggestions-solution3 {
        background: #1f2937;
        border-color: #374151;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .search-suggestion-item-solution3 {
        border-bottom-color: #374151;
        color: #f9fafb;
    }

    .search-suggestion-item-solution3:hover {
        background-color: #374151;
    }
}

/*
 * Performance monitoring và debugging
 */
.header-section-solution3.debug {
    border: 2px solid red !important;
}

.search-container-solution3.debug {
    border: 2px solid blue !important;
}

.search-suggestions-solution3.debug {
    border: 2px solid green !important;
}

/*
 * Print styles
 */
@media print {
    .search-suggestions-solution3 {
        display: none !important;
    }
}

/*
 * Scrollbar styling
 */
.search-suggestions-solution3::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions-solution3::-webkit-scrollbar-track {
    background: rgba(243, 115, 33, 0.05);
    border-radius: 3px;
}

.search-suggestions-solution3::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #FF9D5C, #F37321);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.search-suggestions-solution3::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #F37321, #D35400);
}