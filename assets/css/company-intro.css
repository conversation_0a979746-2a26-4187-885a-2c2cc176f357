/* Company Introduction Section CSS - Professional & Trustworthy Design */
.company-intro-section {
    position: relative;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Background pattern */
.company-intro-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0.03;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Content container */
.company-intro-container {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    padding: 3rem 1.5rem;
}

/* Responsive breakpoints theo yêu cầu */
/* XS: <576px - 1 cột (mặc định) */

/* SM: 576px+ - 1 cột */
@media (min-width: 576px) {
    .company-intro-container {
        flex-direction: column;
        padding: 3.5rem 2rem;
        gap: 2.5rem;
    }
}

/* MD: 768px+ - 1 cột */
@media (min-width: 768px) {
    .company-intro-container {
        flex-direction: column;
        padding: 4rem 2rem;
        gap: 3rem;
    }
}

/* MD+: 900px+ - 1 cột */
@media (min-width: 900px) {
    .company-intro-container {
        flex-direction: column;
        padding: 4rem 2.5rem;
        gap: 3rem;
    }
}

/* LG: 992px+ - 1 cột */
@media (min-width: 992px) {
    .company-intro-container {
        flex-direction: column;
        padding: 4rem 2.5rem;
        gap: 3rem;
    }
}

/* LG+: 1024px+ - 1 cột */
@media (min-width: 1024px) {
    .company-intro-container {
        flex-direction: column;
        padding: 4rem 3rem;
        gap: 3rem;
    }
}

/* XL: 1200px+ - 2 cột (giữ nguyên thiết kế hiện tại) */
@media (min-width: 1200px) {
    .company-intro-container {
        flex-direction: row;
        align-items: stretch;
        padding: 4rem 2rem;
        gap: 4rem;
    }
}

/* Left content - Company info */
.company-info {
    flex: 1;
}

/* Section title */
.company-capabilities-title {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.company-intro-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(243, 115, 33, 0.08);
    color: #F37321;
    font-weight: 600;
    border-radius: 0.25rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.company-intro-badge .badge-icon {
    margin-right: 0.5rem;
}

.company-intro-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    position: relative;
    display: inline-block;
    letter-spacing: -0.01em;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .company-intro-title {
        font-size: 2rem;
    }
}

@media (min-width: 1024px) {
    .company-intro-title {
        font-size: 2.25rem;
    }
}

.company-intro-title-highlight {
    color: #F37321;
    position: relative;
}

.company-intro-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 3rem;
    height: 0.25rem;
    background: #F37321;
    border-radius: 0.125rem;
}

/* Competitive advantages */
.competitive-advantages {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.25rem;
    margin-bottom: 2rem;
}

/* SM: 576px+ - 2 cột để tận dụng không gian */
@media (min-width: 576px) {
    .competitive-advantages {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* MD: 768px+ - 2 cột với gap lớn hơn */
@media (min-width: 768px) {
    .competitive-advantages {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* MD+: 900px+ - 3 cột để tận dụng không gian rộng */
@media (min-width: 900px) {
    .competitive-advantages {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* LG: 992px+ - 3 cột */
@media (min-width: 992px) {
    .competitive-advantages {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* LG+: 1024px+ - 3 cột */
@media (min-width: 1024px) {
    .competitive-advantages {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* XL: 1200px+ - 2 cột khi có layout 2 cột chính */
@media (min-width: 1200px) {
    .competitive-advantages {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

.advantage-item {
    display: flex;
    align-items: flex-start;
    padding: 1.25rem;
    background: #ffffff;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.advantage-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0.25rem;
    height: 100%;
    background: #F37321;
    opacity: 0.8;
}

.advantage-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(243, 115, 33, 0.1);
    color: #F37321;
    border-radius: 0.375rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.advantage-content h4 {
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.375rem;
    font-size: 1rem;
    line-height: 1.3;
}

.advantage-content p {
    color: #4B5563;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Ensure text doesn't overflow on small screens */
@media (max-width: 767px) {
    .advantage-content {
        width: calc(100% - 3.5rem);
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
    }
}

/* Materials section */
.materials-section {
    margin-top: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.25rem;
    background: rgba(243, 115, 33, 0.03);
    border-radius: 0.375rem;
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.materials-title {
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.materials-title i {
    color: #F37321;
    margin-right: 0.5rem;
}

.materials-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.material-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: #4B5563;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.material-tag i {
    color: #F37321;
    margin-right: 0.375rem;
    font-size: 0.625rem;
    transition: transform 0.2s ease;
}

/* Call button - Thiết kế sang trọng giống nút đặt lịch */
.call-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(90deg, #F37321, #F59E0B);
    color: white;
    font-weight: 600;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    box-shadow: 0 6px 12px -4px rgba(243, 115, 33, 0.4);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.call-button:hover {
    background: linear-gradient(90deg, #E65A00, #F37321);
    transform: translateY(-3px);
    box-shadow: 0 8px 15px -4px rgba(243, 115, 33, 0.5);
}

/* Hiệu ứng shine cho nút gọi ngay */
.call-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    z-index: -1;
}

.call-button:hover::after {
    animation: call-button-shine 1.5s ease;
}

@keyframes call-button-shine {
    100% {
        transform: translateX(100%);
    }
}

.call-button.button-clicked {
    transform: scale(0.97);
    box-shadow: 0 4px 8px -3px rgba(243, 115, 33, 0.3);
    transition: all 0.2s ease;
}

.call-button .call-icon {
    margin-right: 0.75rem;
    animation: call-pulse 2s infinite;
    transition: transform 0.3s ease;
}

.call-button:hover .call-icon {
    transform: scale(1.1);
}

@keyframes call-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Right content - Image */
.company-image-container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

.company-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    flex-grow: 1;
    height: 100%;
    min-height: 300px;
}

/* XS: <576px - Đặc biệt tối ưu cho mobile */
@media (max-width: 575px) {
    .company-image-wrapper {
        min-height: 250px !important;
        max-height: 250px !important;
        height: 250px !important;
        border-radius: 0.25rem !important;
        margin-top: 0.5rem;
    }
}

/* Responsive adjustments cho image container */
/* SM: 576px+ - Tăng chiều cao cho layout 1 cột */
@media (min-width: 576px) {
    .company-image-wrapper {
        min-height: 350px;
        border-radius: 0.5rem;
    }
}

/* MD: 768px+ - Chiều cao lớn hơn cho màn hình rộng */
@media (min-width: 768px) {
    .company-image-wrapper {
        min-height: 400px;
        border-radius: 0.5rem;
    }
}

/* MD+: 900px+ - Chiều cao tối ưu */
@media (min-width: 900px) {
    .company-image-wrapper {
        min-height: 450px;
        border-radius: 0.75rem;
    }
}

/* LG: 992px+ - Chiều cao lớn nhất cho layout 1 cột */
@media (min-width: 992px) {
    .company-image-wrapper {
        min-height: 500px;
        border-radius: 0.75rem;
    }
}

/* LG+: 1024px+ - Duy trì chiều cao lớn */
@media (min-width: 1024px) {
    .company-image-wrapper {
        min-height: 500px;
        border-radius: 0.75rem;
    }
}

/* XL: 1200px+ - Trở về layout 2 cột, điều chỉnh chiều cao phù hợp */
@media (min-width: 1200px) {
    .company-image-wrapper {
        min-height: 400px;
        border-radius: 0.5rem;
    }
}

.company-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.company-image-wrapper:hover .company-image {
    transform: scale(1.03);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.2));
}

/* Company stats */
.company-stats {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;
    padding: 1.5rem;
    z-index: 1;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.75rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Warranty badge */
.warranty-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    z-index: 1;
}

.warranty-badge .icon {
    color: #F37321;
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.warranty-badge .content {
    display: flex;
    flex-direction: column;
}

.warranty-badge .years {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    line-height: 1.2;
}

.warranty-badge .text {
    font-size: 0.75rem;
    color: #4B5563;
    line-height: 1.2;
}

/* Responsive adjustments cho XS: <576px */
@media (max-width: 575px) {
    /* Container adjustments */
    .company-intro-container {
        padding: 2rem 1rem;
        gap: 1.5rem;
    }

    /* Title adjustments */
    .company-intro-title {
        font-size: 1.5rem;
    }

    .company-intro-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* Advantages grid adjustments - 1 cột trên mobile */
    .competitive-advantages {
        grid-template-columns: 1fr !important;
        gap: 0.875rem;
    }

    .advantage-item {
        padding: 1rem;
    }

    .advantage-icon {
        width: 2.25rem;
        height: 2.25rem;
        margin-right: 0.75rem;
    }

    .advantage-content h4 {
        font-size: 0.9375rem;
    }

    .advantage-content p {
        font-size: 0.8125rem;
    }

    /* Materials section adjustments */
    .materials-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .materials-title {
        font-size: 0.9375rem;
    }

    .materials-list {
        gap: 0.5rem;
    }

    .material-tag {
        padding: 0.25rem 0.5rem;
        font-size: 0.6875rem;
    }

    /* Call button adjustments */
    .call-button {
        width: 100%;
        justify-content: center;
        padding: 0.75rem 1rem;
        font-size: 0.9375rem;
    }

    /* Image container adjustments - đã được xử lý ở rule XS riêng biệt */

    /* Warranty badge adjustments */
    .warranty-badge {
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.375rem 0.625rem;
        border-radius: 0.25rem;
    }

    .warranty-badge .icon {
        font-size: 1.125rem;
        margin-right: 0.375rem;
    }

    .warranty-badge .years {
        font-size: 0.9375rem;
    }

    .warranty-badge .text {
        font-size: 0.625rem;
    }

    /* Stats adjustments */
    .company-stats {
        padding: 0.75rem;
    }

    .stat-value {
        font-size: 1.25rem;
        margin-bottom: 0.125rem;
    }

    .stat-label {
        font-size: 0.5625rem;
    }

/* Optimize for very small screens */
@media (max-width: 374px) {
    .company-intro-container {
        padding: 1.5rem 0.75rem;
    }

    .competitive-advantages {
        gap: 0.75rem;
        grid-template-columns: 1fr !important;
    }

    .advantage-item {
        padding: 0.875rem;
    }

    .advantage-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.625rem;
    }

    .advantage-content h4 {
        font-size: 0.875rem;
    }

    .advantage-content p {
        font-size: 0.75rem;
    }

    .materials-list {
        gap: 0.375rem;
    }

    .material-tag {
        padding: 0.25rem 0.375rem;
        font-size: 0.625rem;
    }

    /* Image wrapper đã được xử lý ở rule XS chung */

    .company-stats {
        padding: 0.5rem;
    }

    .stat-value {
        font-size: 1.125rem;
    }

    .stat-label {
        font-size: 0.5rem;
    }
}

/* Fix for material tags on mobile */
@media (max-width: 320px) {
    .materials-list {
        justify-content: center;
    }

    .material-tag {
        padding: 0.25rem 0.375rem;
        font-size: 0.625rem;
    }

    .competitive-advantages {
        grid-template-columns: 1fr !important;
    }
}
