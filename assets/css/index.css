
    /* <PERSON><PERSON><PERSON>n CSS cho màu sắc */
    :root {
        --color-primary: #3B82F6; /* Màu primary (blue-500) */
        --color-primary-dark: #2563EB; /* Màu primary-dark (blue-600) */
        --color-primary-rgb: 59, 130, 246; /* RGB của màu primary */
        --color-secondary: #F59E0B; /* Màu secondary (amber-500) */
        --color-secondary-rgb: 245, 158, 11; /* RGB của màu secondary */
        --color-accent: #10B981; /* Màu accent (emerald-500) */
        --color-accent-rgb: 16, 185, 129; /* RGB của màu accent */
        --color-danger: #EF4444; /* Màu danger (red-500) */
        --color-danger-rgb: 239, 68, 68; /* RGB của màu danger */
        --color-success: #10B981; /* Màu success (emerald-500) */
        --color-success-rgb: 16, 185, 129; /* RGB của màu success */
        --color-warning: #F59E0B; /* Màu warning (amber-500) */
        --color-warning-rgb: 245, 158, 11; /* RGB của màu warning */
        --color-info: #3B82F6; /* Màu info (blue-500) */
        --color-info-rgb: 59, 130, 246; /* RGB của màu info */
        --color-light: #F3F4F6; /* Màu light (gray-100) */
        --color-light-rgb: 243, 244, 246; /* RGB của màu light */
        --color-dark: #1F2937; /* Màu dark (gray-800) */
        --color-dark-rgb: 31, 41, 55; /* RGB của màu dark */
        --color-white: #FFFFFF; /* Màu white */
        --color-white-rgb: 255, 255, 255; /* RGB của màu white */
        --color-black: #000000; /* Màu black */
        --color-black-rgb: 0, 0, 0; /* RGB của màu black */

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
        --shadow-outline: 0 0 0 3px rgba(66, 153, 225, 0.5);
        --shadow-none: none;

        --radius-none: 0;
        --radius-sm: 0.125rem;
        --radius-md: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-xl: 0.75rem;
        --radius-2xl: 1rem;
        --radius-3xl: 1.5rem;
        --radius-full: 9999px;
    }

    /* Animation classes đã được khôi phục để hoạt động bình thường */

    /* Giới hạn số dòng hiển thị cho tiêu đề sản phẩm */
    .line-clamp-2, .product-title-truncate {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 2.8em; /* Đảm bảo chiều cao chính xác cho 2 dòng */
        line-height: 1.4em; /* Đảm bảo line-height nhất quán */
    }

    /* Đảm bảo tiêu đề sản phẩm hiển thị đúng 2 dòng */
    .product-title-truncate {
        height: 2.8em; /* Chiều cao cố định cho 2 dòng */
        margin-bottom: 0.75rem !important; /* Đảm bảo khoảng cách dưới tiêu đề */
    }

    /* Cải thiện hiển thị tiêu đề sản phẩm trên mobile */
    @media (max-width: 767px) {
        .product-title-truncate {
            height: 2.7em;
            margin-bottom: 0.5rem !important;
        }

        /* Tăng độ nổi bật cho tiêu đề */
        .product-title-link h3 {
            color: #1F2937;
            text-shadow: 0 0.5px 0 rgba(0,0,0,0.05);
        }
    }

    /* Cải thiện grid sản phẩm */
    .product-card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    /* Đảm bảo hiển thị 2 sản phẩm trên một hàng cho điện thoại */
    @media (max-width: 767px) {
        .product-card-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        /* Cải thiện hiển thị thẻ sản phẩm trên mobile */
        .product-card {
            border-radius: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .product-card .p-5,
        .product-card .p-4 {
            padding: 0.85rem;
        }

        .product-card h3 {
            font-size: 0.95rem;
            line-height: 1.35;
            margin-bottom: 0.3rem;
            min-height: 2.6rem;
            font-weight: 600;
            letter-spacing: -0.01em;
        }

        .product-card .text-lg {
            font-size: 0.875rem;
        }

        /* Ẩn một số thông tin không cần thiết trên mobile */
        .product-card .flex.items-center.text-xs.text-gray-500,
        .product-card .flex.items-center.mb-2 .flex.items-center {
            display: none;
        }

        /* Điều chỉnh kích thước nút */
        .product-card .product-actions {
            margin-top: 0.5rem;
        }

        /* Tối ưu hiển thị giá và trạng thái trên mobile */
        .product-card .flex.justify-between.items-center.mt-3.mb-4 {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        /* Tối ưu hiển thị nút trên mobile */
        .product-card .product-actions .px-4 {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            font-size: 0.75rem;
        }

        .product-card .product-actions .py-2\.5 {
            padding-top: 0.375rem;
            padding-bottom: 0.375rem;
        }

        .product-card .product-actions i {
            font-size: 0.75rem;
        }

        /* Đảm bảo nút thêm giỏ hàng và báo giá luôn hiển thị */
        .product-card .product-actions .bg-primary,
        .product-card .product-actions .bg-gradient-to-r {
            display: flex !important;
            opacity: 1 !important;
            transform: none !important;
        }

        .product-card .product-actions span {
            font-size: 0.75rem;
        }

        /* Cải thiện hiển thị đánh giá sao */
        .product-card .fa-star,
        .product-card .fa-star-half-alt {
            font-size: 0.65rem;
        }

        /* Cải thiện hiển thị tiêu đề chính trên mobile */
        .main-title-container {
            padding: 1rem 0;
        }

        .main-title-container h2 {
            font-size: 1.75rem;
            line-height: 1.3;
        }

        .main-title-container p {
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* Cải thiện hiển thị tiêu đề danh mục trên mobile */
        .category-header-container {
            margin-bottom: 1rem;
            padding: 0.5rem 0;
        }

        .category-icon {
            padding: 0.5rem;
            margin-right: 0.75rem;
        }

        .category-icon i {
            font-size: 1rem;
        }

        .category-title {
            font-size: 1.25rem;
            line-height: 1.3;
            flex-wrap: wrap;
        }

        .category-name {
            margin-bottom: 0.25rem;
            width: 100%;
        }

        .product-count {
            margin-left: 0;
            margin-top: 0.25rem;
            font-size: 0.7rem;
            padding: 0.25rem 0.75rem;
        }

        .category-title-decoration {
            width: 2rem;
            margin-top: 0.25rem;
        }

        /* Cải thiện nút xem tất cả trên mobile */
        .view-all-btn {
            width: 100%;
            justify-content: center;
            padding: 0.5rem 1rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .view-all-icon {
            width: 1.25rem;
            height: 1.25rem;
        }
    }

    /* Đã loại bỏ hiệu ứng hover cho nút xem tất cả */

    /* Thiết kế tiêu đề chính - đã loại bỏ hiệu ứng hover */
    .main-title-container {
        position: relative;
        padding: 2rem 0;
    }

    .main-title-text {
        position: relative;
        display: inline-block;
    }

    .main-title-underline {
        opacity: 0.9;
    }

    /* Thiết kế tiêu đề danh mục - đã loại bỏ hiệu ứng hover */
    .category-header-container {
        position: relative;
        overflow: hidden;
        padding: 0.5rem 0;
        border-radius: 0.75rem;
    }

    .category-header-container::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(209, 213, 219, 0.5), transparent);
        z-index: -1;
        opacity: 0.7;
    }

    .category-icon {
        position: relative;
        overflow: hidden;
    }

    .category-title {
        position: relative;
        transition: all 0.3s ease;
    }

    .category-name {
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .category-section:hover .category-name {
        color: var(--primary, #F37321);
        transform: translateX(2px);
    }

    .category-title-decoration {
        transition: all 0.4s ease;
        transform-origin: left center;
    }

    .category-section:hover .category-title-decoration {
        width: 100%;
        opacity: 0.8;
    }

    .product-count {
        transition: all 0.3s ease;
    }

    .category-section:hover .product-count {
        background: linear-gradient(to right, rgba(243, 115, 33, 0.2), rgba(243, 115, 33, 0.3));
        color: #d15a0a;
    }

    .view-all-btn {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .view-all-icon {
        transition: all 0.3s ease;
    }

    .view-all-btn:hover .view-all-icon {
        transform: scale(1.1);
    }

    /* Cải thiện hiệu ứng cho thẻ sản phẩm */
    .product-card {
        position: relative;
        z-index: 1;
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        opacity: 0;
        border-radius: inherit;
        transition: opacity 0.3s ease;
        z-index: -1;
    }

    .product-card:hover::after {
        opacity: 1;
    }

    .product-card:hover {
        transform: translateY(-5px);
    }

    /* Hiệu ứng cho hình ảnh sản phẩm */
    .product-card .product-image img {
        transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card:hover .product-image img {
        transform: scale(1.08);
    }

    /* Hiệu ứng cho nút thao tác */
    .product-card .product-actions button,
    .product-card .product-actions a {
        transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card .product-actions button:hover,
    .product-card .product-actions a:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Màu hover cho tiêu đề sản phẩm */
    .product-title-link.regular-product:hover h3 {
        color: #3B82F6; /* Màu xanh cho sản phẩm thường */
    }

    .product-title-link.quote-product:hover h3 {
        color: #F37321; /* Màu cam cho sản phẩm báo giá */
    }

    /* Thiết kế tinh tế hơn cho thẻ "Giá theo yêu cầu" */
    .price-on-request {
        font-size: 0.75rem;
        letter-spacing: 0.01em;
        box-shadow: none;
        background-opacity: 0.8;
        transition: all 0.3s ease;
    }

    .price-on-request i {
        font-size: 0.7rem;
        opacity: 0.9;
    }

    @media (max-width: 767px) {
        .price-on-request {
            font-size: 0.7rem;
            padding: 0.15rem 0.5rem;
            border-radius: 0.25rem;
        }

        .price-on-request i {
            font-size: 0.65rem;
            margin-right: 0.25rem;
        }
    }

    /* Đảm bảo nút thêm giỏ hàng và báo giá luôn hiển thị trên tất cả các thiết bị */
    .product-card .product-actions .bg-primary,
    .product-card .product-actions .bg-gradient-to-r,
    .product-card .product-actions .add-to-cart-btn {
        display: flex !important;
        opacity: 1 !important;
    }

    /* CSS cho danh mục sản phẩm */
    .category-section {
        margin-bottom: 3rem;
        animation: fadeIn 0.5s ease forwards;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* CSS cho grid sản phẩm mới */
    .modern-product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
    }

    /* CSS cho card sản phẩm mới - Match search-page.css exactly */
    .modern-product-card {
        background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
        border-radius: 1.25rem;
        overflow: hidden;
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        /* Optimized transition - only essential properties */
        transition:
            transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
            box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid rgba(255, 255, 255, 0.2);
        /* Performance optimizations */
        will-change: transform;
        transform: translateZ(0); /* Force hardware acceleration */
        backface-visibility: hidden; /* Prevent flickering */
    }

    .modern-product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
        opacity: 0;
        transition: opacity 0.25s ease;
        will-change: opacity;
    }

    .modern-product-card:hover {
        /* Match search page hover effect - only translateY */
        transform: translateY(-5px);
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.1),
            0 4px 8px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(59, 130, 246, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 1);
    }

    .modern-product-card:hover::before {
        opacity: 1;
    }

    /* Đảm bảo hình ảnh hiển thị đúng */
    .modern-product-card .product-image-wrapper {
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    /* Đảm bảo hình ảnh hiển thị đúng trên Safari */
    @media not all and (min-resolution:.001dpcm) {
        @supports (-webkit-appearance:none) {
            .modern-product-card .product-image-wrapper img.product-image {
                height: 100% !important;
                width: 100% !important;
                object-fit: cover !important;
            }
        }
    }

    /* Fix cho IE và một số trình duyệt cũ */
    @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
        .product-image-wrapper {
            height: 0;
        }

        .product-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    /* Phần hình ảnh sản phẩm - Thiết kế đơn giản và tinh tế hơn */
    .simple-product-image {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 100%; /* Tỷ lệ 1:1 */
        overflow: hidden;
        background-color: #f9f9f9;
        border-top-left-radius: var(--radius-xl);
        border-top-right-radius: var(--radius-xl);
        background-image: linear-gradient(110deg, #f8f8f8 8%, #ffffff 18%, #f8f8f8 33%);
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.03);
    }

    @keyframes shine {
        to {
            background-position-x: -200%;
        }
    }

    /* Đảm bảo hình ảnh hiển thị đúng trên tất cả các trình duyệt */
    @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
        /* IE10+ CSS */
        .simple-product-image {
            height: 280px;
        }
    }

    @supports (-ms-ime-align:auto) {
        /* Edge CSS */
        .simple-product-image {
            height: 280px;
        }
    }

    @-moz-document url-prefix() {
        /* Firefox CSS */
        .simple-image {
            background-size: cover !important;
            background-position: center !important;
        }
    }

    .simple-image-link {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
        z-index: 1;
    }

    .simple-image {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-size: cover !important;
        background-position: center !important;
        transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) !important;
        z-index: 1 !important;
    }

    /* Overlay cho hiệu ứng hover */
    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.1) 100%);
        opacity: 0;
        transition: opacity 0.6s ease;
        z-index: 2;
    }

    .modern-product-card:hover .simple-image {
        transform: scale(1.08);
    }

    .modern-product-card:hover .image-overlay {
        opacity: 1;
    }

    .simple-no-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #aaa;
        font-size: 2rem;
    }

    /* Badge giảm giá - Thiết kế mới tinh tế hơn */
    .discount-badge {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: linear-gradient(135deg, var(--color-danger) 0%, #ff6b6b 100%);
        color: var(--color-white);
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-full);
        z-index: 10;
        box-shadow: 0 4px 8px rgba(var(--color-danger-rgb), 0.3);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
    }

    .modern-product-card:hover .discount-badge {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(var(--color-danger-rgb), 0.4);
    }

    /* Trạng thái sản phẩm - Thiết kế mới tinh tế hơn */
    .stock-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-full);
        z-index: 10;
        display: flex;
        align-items: center;
        gap: 0.35rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
    }

    .modern-product-card:hover .stock-status {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    .in-stock {
        background: linear-gradient(135deg, var(--color-success) 0%, #34d399 100%);
        color: white;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(var(--color-success-rgb), 0.3);
    }

    .out-of-stock {
        background: linear-gradient(135deg, var(--color-danger) 0%, #f87171 100%);
        color: white;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(var(--color-danger-rgb), 0.3);
    }



    /* Nút hành động nhanh - Thiết kế mới tinh tế hơn */
    .quick-actions {
        position: absolute;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
        display: flex;
        gap: 0.75rem;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
        z-index: 20;
    }

    .modern-product-card:hover .quick-actions {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }

    .quick-action-btn {
        width: 2.75rem;
        height: 2.75rem;
        border-radius: var(--radius-full);
        background-color: var(--color-white);
        color: var(--color-dark);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        border: none;
        cursor: pointer;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .quick-action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn:hover::before {
        opacity: 0.5;
    }

    .quick-action-btn.cart-btn {
        background-color: var(--color-primary);
        color: var(--color-white);
    }

    .quick-action-btn.contact-btn {
        background-color: var(--color-info);
        color: var(--color-white);
    }

    .quick-action-btn .tooltip {
        position: absolute;
        top: -2.5rem;
        left: 50%;
        transform: translateX(-50%);
        background-color: var(--color-dark);
        color: var(--color-white);
        font-size: 0.75rem;
        padding: 0.35rem 0.85rem;
        border-radius: var(--radius-md);
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn:hover .tooltip {
        opacity: 1;
        visibility: visible;
    }

    /* Phần thông tin sản phẩm - Match search-page.css exactly */
    .product-info-wrapper {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 1rem 1rem 0.75rem 1rem; /* Match search page actual applied padding */
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
        position: relative;
    }

    .product-info-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 1.5rem;
        right: 1.5rem;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    }

    /* Optimized Spacing for Product Info Sections - Match search page */
    .product-title {
        margin-bottom: 0 !important; /* Remove gap between title and price */
    }

    .premium-price-section {
        margin-top: 0;
        margin-bottom: 0; /* No gap before rating section */
    }

    .product-rating-sales {
        margin-top: 0.5rem; /* Consistent reduced spacing */
    }

    .premium-price-section {
        background: none;
        border: none;
        border-radius: 0;
        padding: 0;
        position: relative;
        margin-top: auto; /* Match search page - đẩy xuống dưới cùng */
        min-height: 4.5rem; /* Đảm bảo chiều cao tối thiểu nhất quán */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-rating-sales {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem; /* Reduced spacing */
        padding: 0;
        border-top: none;
        width: 100%;
    }

    .rating-section {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        flex-shrink: 0;
    }

    .rating-section .stars {
        display: flex;
        gap: 0.125rem;
    }

    .rating-section .stars i {
        font-size: 0.75rem;
        color: #fbbf24;
    }

    .rating-section .rating-text {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    .sales-section {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .sales-section i {
        font-size: 0.75rem;
        color: #3b82f6;
    }

    .sales-section span {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    .product-category {
        font-size: 0.75rem;
        color: var(--color-primary);
        background-color: rgba(var(--color-primary-rgb), 0.08);
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        display: inline-flex;
        align-items: center;
        align-self: flex-start;
        transition: all 0.3s ease;
        margin-top: 0.25rem;
        border: 1px solid rgba(var(--color-primary-rgb), 0.1);
    }

    .product-category:hover {
        background-color: rgba(var(--color-primary-rgb), 0.15);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(var(--color-primary-rgb), 0.1);
    }

    .product-title {
        display: block;
        margin-bottom: 0.25rem;
    }

    .product-title h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--color-dark);
        margin: 0;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: color 0.3s ease;
        min-height: 2.8rem;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: var(--color-warning);
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .product-rating i {
        transition: transform 0.3s ease;
    }

    .modern-product-card:hover .product-rating i {
        transform: rotate(360deg);
    }

    .rating-count {
        color: var(--color-dark);
        opacity: 0.6;
        font-size: 0.75rem;
        margin-left: 0.25rem;
    }

    .product-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.75rem;
        color: var(--color-dark);
        opacity: 0.7;
        background-color: rgba(0, 0, 0, 0.02);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-md);
        border: 1px solid rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
    }

    .modern-product-card:hover .product-meta {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .product-price {
        margin-top: 0.75rem;
        padding: 0.75rem;
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: var(--radius-lg);
        text-align: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .modern-product-card:hover .product-price {
        background-color: rgba(0, 0, 0, 0.04);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    }

    .contact-price {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        color: var(--color-info);
        font-weight: 600;
        font-size: 0.9rem;
        background-color: rgba(var(--color-info-rgb), 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-md);
        width: 100%;
        box-shadow: 0 2px 4px rgba(var(--color-info-rgb), 0.1);
        transition: all 0.3s ease;
    }

    .modern-product-card:hover .contact-price {
        background-color: rgba(var(--color-info-rgb), 0.15);
    }

    .sale-price {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .old-price {
        font-size: 0.875rem;
        color: var(--color-dark);
        opacity: 0.6;
        text-decoration: line-through;
        margin-bottom: 0.25rem;
    }

    .current-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--color-primary);
        text-shadow: 0 1px 1px rgba(var(--color-primary-rgb), 0.1);
    }

    .regular-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--color-dark);
        text-align: center;
    }



    /* Hiệu ứng cho nút và tương tác */
    .category-tab-btn.active {
        animation: pulse 0.5s ease-out;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(var(--color-primary-rgb), 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0);
        }
    }

    .modern-product-card.hovered {
        z-index: 10;
    }

    .add-to-cart-btn.clicked,
    .main-action-btn.clicked {
        animation: buttonClick 0.3s ease-out;
    }

    @keyframes buttonClick {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(0.95);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Hiệu ứng bay vào giỏ hàng */
    .fly-item {
        position: absolute;
        width: 50px;
        height: 50px;
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        z-index: 9999;
        box-shadow: var(--shadow-md);
        pointer-events: none;
        transition: all 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    }

    /* Hiệu ứng loading cho nút thêm giỏ hàng */
    .add-to-cart-btn.loading,
    .main-action-btn.loading {
        position: relative;
        color: transparent !important;
        pointer-events: none;
    }

    .add-to-cart-btn.loading::after,
    .main-action-btn.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 0.8s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Responsive cho tablet - Match search-page.css */
    @media (max-width: 768px) {
        .modern-product-card {
            border-radius: 1rem;
            /* Optimize for touch devices */
            will-change: auto;
        }

        /* Reduced hover effects for tablet */
        .modern-product-card:hover {
            transform: translateY(-3px);
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.08),
                0 2px 4px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 1);
        }

        .modern-product-card:hover .product-image-wrapper img {
            transform: scale(1.02);
        }

        .product-info-wrapper::before {
            left: 1rem;
            right: 1rem;
        }
    }

    /* Responsive cho thiết kế mới - Tối ưu hóa cho thiết bị di động */
    @media (max-width: 767px) {
        .category-tabs {
            gap: 0.5rem;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 0.5rem;
            justify-content: flex-start;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
        }

        .category-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Edge */
        }

        .category-tab-btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }

        .modern-product-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 0 0.25rem;
        }

        .modern-product-card {
            border-radius: var(--radius-lg);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
        }

        /* Tối ưu hiển thị hình ảnh sản phẩm trên điện thoại */
        .simple-product-image {
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .simple-image {
            background-size: cover;
            background-position: center;
        }

        /* Tối ưu hiển thị badge và trạng thái */
        .discount-badge {
            font-size: 0.65rem;
            padding: 0.2rem 0.5rem;
            top: 0.5rem;
            left: 0.5rem;
        }

        .stock-status {
            font-size: 0.65rem;
            padding: 0.2rem 0.5rem;
            top: 0.5rem;
            right: 0.5rem;
            gap: 0.2rem;
        }

        .stock-status i {
            font-size: 0.6rem;
        }

        /* Tối ưu hiển thị thông tin sản phẩm */
        .product-info-wrapper::before {
            left: 1rem;
            right: 1rem;
        }

        /* Optimized Spacing for Product Info Sections - Mobile */
        .product-title {
            margin-bottom: 0 !important; /* Remove gap between title and price */
        }

        .premium-price-section {
            margin-top: 0;
            margin-bottom: 0; /* No gap before rating section */
        }

        .product-rating-sales {
            margin-top: 0.5rem; /* Consistent reduced spacing */
        }

        .premium-price-section {
            background: none;
            border: none;
            border-radius: 0;
            padding: 0;
            position: relative;
            margin-top: auto; /* Match search page - đẩy xuống dưới cùng */
            min-height: 4rem; /* Chiều cao tối thiểu cho mobile */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-rating-sales {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem; /* Reduced mobile spacing */
            padding: 0;
            border-top: none;
            width: 100%;
            gap: 0.5rem;
        }

        .rating-section {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            flex-shrink: 0;
        }

        .rating-section .stars {
            display: flex;
            gap: 0.125rem;
        }

        .rating-section .stars i {
            font-size: 0.625rem;
            color: #fbbf24;
        }

        .rating-section .rating-text {
            font-size: 0.625rem;
            color: #64748b;
            font-weight: 500;
        }

        .sales-section {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex-shrink: 0;
        }

        .sales-section i {
            font-size: 0.625rem;
            color: #3b82f6;
        }

        .sales-section span {
            font-size: 0.625rem;
            color: #64748b;
            font-weight: 500;
        }

        .product-title h3 {
            font-size: 0.85rem;
            min-height: 2.4rem;
            line-height: 1.3;
            margin-bottom: 0.2rem;
        }

        .product-category {
            font-size: 0.65rem;
            padding: 0.15rem 0.5rem;
        }

        .product-meta {
            display: none;
        }

        .product-rating {
            font-size: 0.7rem;
        }

        .rating-count {
            font-size: 0.65rem;
        }

        .product-price {
            margin-top: 0.5rem;
            padding: 0.5rem;
        }

        .current-price, .regular-price {
            font-size: 0.95rem;
        }

        .old-price {
            font-size: 0.75rem;
        }

        .contact-price {
            font-size: 0.8rem;
            padding: 0.35rem 0.5rem;
        }

        /* Tối ưu hiển thị nút thao tác */
        .main-action-btn {
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .quick-actions {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
            bottom: 0.5rem;
            gap: 0.5rem;
        }

        .quick-action-btn {
            width: 2rem;
            height: 2rem;
            font-size: 0.75rem;
        }

        .quick-action-btn .tooltip {
            display: none;
        }

        /* Cải thiện hiệu ứng hover trên mobile */
        .modern-product-card:active {
            transform: scale(0.98);
        }

        .modern-product-card:hover .product-price,
        .modern-product-card:hover .discount-badge,
        .modern-product-card:hover .stock-status {
            transform: none;
        }
    }

    /* Responsive cho mobile - Match search-page.css */
    @media (max-width: 480px) {
        /* Disable hover effects on mobile for better performance */
        .modern-product-card {
            will-change: auto;
        }

        .modern-product-card:hover {
            transform: none;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .modern-product-card:hover .product-image-wrapper img {
            transform: none;
        }

        .modern-product-card:hover::before {
            opacity: 0;
        }
    }

    /* Responsive cho tiêu đề danh mục */
    @media (max-width: 767px) {
        .category-header {
            margin-bottom: 1.5rem;
        }

        .category-header .flex {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .category-header .bg-gradient-to-b {
            padding-right: 0;
            width: 100%;
        }

        .category-header h3 {
            font-size: 1.25rem;
            flex-direction: column;
            align-items: flex-start;
        }

        .category-header h3 span {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    }

    /* CSS cho category products slider */
    .category-products-slider {
        position: relative;
        padding: 0 10px;
        margin: 0 -10px;
        overflow: hidden;
    }

    .category-products-slider .swiper-container {
        overflow: visible;
        padding: 20px 0;
    }

    .category-products-slider .swiper-slide {
        height: auto;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    /* Hiệu ứng chỉ áp dụng cho điện thoại */
    @media (max-width: 767px) {
        .category-products-slider .swiper-slide {
            opacity: 0.85;
            transform: scale(0.95);
        }

        .category-products-slider .swiper-slide-active {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Hiệu ứng chuyển đổi mượt mà */
    .category-products-slider .swiper-wrapper {
        transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    /* Nút điều hướng slider cho danh mục */
    .category-slider-navigation {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        position: relative;
        z-index: 20;
        margin-top: 1.5rem;
    }

    .slider-nav-button {
        width: 3rem;
        height: 3rem;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        color: #3B82F6;
    }

    .slider-nav-button:hover {
        background: #3B82F6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
    }

    /* Responsive cho category products slider */
    @media (max-width: 767px) {
        .category-products-slider {
            margin: 0;
            padding: 0 8px;
        }

        /* Điều chỉnh badge giảm giá trên mobile */
        .sale-badge {
            top: 5px;
            left: 5px;
        }

        .sale-badge-content {
            padding: 0.2rem 0.5rem;
            font-size: 0.65rem;
        }

        .category-products-slider .swiper-container {
            overflow: visible;
            padding: 10px 0;
        }

        .category-products-slider .swiper-slide {
            width: 80%;
        }

        .category-products-slider .product-card {
            margin-right: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        /* Ẩn nút điều hướng trên điện thoại */
        .category-slider-navigation {
            display: none; /* Ẩn hoàn toàn trên điện thoại */
        }

        /* Tối ưu hiển thị card trên điện thoại */
        .category-products-slider .product-card .p-4 {
            padding: 0.75rem;
        }

        .category-products-slider .product-card h3 {
            font-size: 0.85rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
        }

        /* Tối ưu hiển thị danh mục */
        .category-products-slider .product-card .mb-2 {
            margin-bottom: 0.25rem;
        }

        .category-products-slider .product-card .text-xs {
            font-size: 0.65rem;
        }

        /* Tối ưu hiển thị trạng thái và đánh giá */
        .category-products-slider .product-card .flex.justify-between.items-center.mb-3 {
            margin-bottom: 0.5rem;
        }

        .category-products-slider .product-card .inline-flex.items-center.px-2.py-1 {
            padding: 0.15rem 0.5rem;
            font-size: 0.65rem;
        }

        .category-products-slider .product-card .fa-star {
            font-size: 0.65rem;
        }

        /* Tối ưu hiển thị thông tin lượt bán và lượt xem */
        .category-products-slider .product-card .flex.items-center.justify-between.mb-3 {
            margin-bottom: 0.5rem;
        }

        /* Tối ưu hiển thị giá */
        .category-products-slider .product-card .price-container {
            margin-bottom: 0.5rem;
        }

        .category-products-slider .product-card .text-blue-500.font-bold,
        .category-products-slider .product-card .text-gray-500.line-through {
            font-size: 0.85rem;
        }

        /* Tối ưu hiển thị nút */
        .category-products-slider .product-card .flex.space-x-2 {
            gap: 0.35rem;
        }

        .category-products-slider .product-card .flex.space-x-2 button,
        .category-products-slider .product-card .flex.space-x-2 a {
            padding: 0.4rem 0.5rem;
            font-size: 0.75rem;
        }

        .category-products-slider .product-card .flex.space-x-2 button i,
        .category-products-slider .product-card .flex.space-x-2 a i {
            font-size: 0.75rem;
            margin-right: 0.25rem;
        }
    }

    /* Tối ưu cho điện thoại nhỏ */
    @media (max-width: 374px) {
        .category-products-slider .product-card .p-4 {
            padding: 0.5rem;
        }

        .category-products-slider .product-card h3 {
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .category-products-slider .product-card .flex.space-x-2 button span,
        .category-products-slider .product-card .flex.space-x-2 a span {
            font-size: 0.7rem;
        }

        .category-products-slider .product-card .inline-flex.items-center.px-2.py-1 {
            padding: 0.1rem 0.4rem;
            font-size: 0.6rem;
        }

        .sale-badge-content {
            padding: 0.15rem 0.4rem;
            font-size: 0.6rem;
        }
    }

    /* Hiệu ứng cho tất cả card sản phẩm */
    .product-card {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        border-color: rgba(59, 130, 246, 0.3);
    }

    /* Làm nổi bật sản phẩm yêu cầu báo giá */
    .product-card:has(.bg-orange-50) {
        box-shadow: 0 4px 15px rgba(249, 115, 22, 0.08);
    }

    .product-card:has(.bg-orange-50):hover {
        box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
    }

    /* Hiệu ứng cho hình ảnh sản phẩm */
    .product-image-container {
        overflow: hidden;
        position: relative;
    }

    .product-image-container img {
        transition: transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .product-card:hover .product-image-container img {
        transform: scale(1.08);
    }

    /* Hiệu ứng overlay khi hover */
    .product-image-container::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0) 70%, rgba(0,0,0,0.1) 100%);
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    .product-card:hover .product-image-container::after {
        opacity: 1;
    }

    /* Thiết kế mới cho badge giảm giá */
    .sale-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        overflow: hidden;
    }

    .sale-badge-content {
        background: linear-gradient(135deg, #F37321, #FF8A3D);
        color: white;
        font-weight: 700;
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 10px rgba(243, 115, 33, 0.3);
        transform: rotate(0deg);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sale-percent {
        display: inline-block;
        transform: translateY(0);
        transition: transform 0.3s ease;
    }

    .product-card:hover .sale-badge-content {
        transform: scale(1.05);
        box-shadow: 0 6px 15px rgba(243, 115, 33, 0.4);
        background: linear-gradient(135deg, #FF8A3D, #F37321);
    }

    .product-card:hover .sale-percent {
        transform: translateY(-2px);
    }

    /* Hiệu ứng pulse cho badge giảm giá với màu cam chính */
    @keyframes badge-pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(243, 115, 33, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(243, 115, 33, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(243, 115, 33, 0);
        }
    }

    .sale-badge-content {
        animation: badge-pulse 2s infinite;
    }

    /* Hiệu ứng nhấp nháy cho icon điện thoại */
    @keyframes pulse {
        0% {
            opacity: 0.6;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.6;
        }
    }

    .animate-pulse {
        animation: pulse 1.5s infinite ease-in-out;
    }

    /* ===== ĐỒNG BỘ CONTACT PRICE CONTAINER VỚI SEARCH PAGE ===== */
    /* Override để đảm bảo contact-price-container ở trang chủ giống trang tìm kiếm */
    .contact-price-container {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        justify-content: center !important;
        gap: 0.375rem !important;
        width: 100% !important;
        height: auto !important;
        background: none !important;
        border: none !important;
        border-radius: 0 !important;
        position: relative !important;
        overflow: visible !important;
        /* Remove any conflicting properties */
        border-width: initial !important;
        border-style: none !important;
        border-color: initial !important;
        border-image: initial !important;
    }

    /* Đảm bảo decorative line bị ẩn */
    .contact-price-container::before {
        display: none !important;
    }

    /* Đảm bảo contact-price-main styling nhất quán với responsive optimization - Giống products.php */
    .contact-price-main {
        font-size: clamp(0.65rem, 1.5vw, 0.875rem) !important; /* Giống price-label */
        color: #64748b !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.025em !important;
        align-self: flex-start !important;
        position: relative !important;
        transition: color 0.3s ease !important;
    }

    /* LG+ (1024px+): Smaller font for 4-column layout */
    @media (min-width: 1024px) {
        .contact-price-main {
            font-size: clamp(0.6rem, 1.2vw, 0.75rem) !important;
        }
    }

    /* XL+ (1200px+): Restore original size for larger screens */
    @media (min-width: 1200px) {
        .contact-price-main {
            font-size: clamp(0.65rem, 1.5vw, 0.875rem) !important;
        }
    }

    /* Hover effect giống price-label */
    .modern-product-card:hover .contact-price-main {
        color: #3b82f6 !important;
    }

    /* Xóa bỏ icon styling vì không còn sử dụng icon */

    /* Đảm bảo contact-price-subtitle styling nhất quán với responsive optimization - Giống products.php */
    .contact-price-subtitle {
        font-size: clamp(1rem, 2.8vw, 1.5rem) !important; /* Giống main-price */
        font-weight: 800 !important;
        color: #1e40af !important; /* Giữ nguyên màu xanh */
        text-shadow: 0 1px 2px rgba(30, 64, 175, 0.1) !important;
        line-height: 1.2 !important;
        align-self: flex-start !important;
        position: relative !important;
        transition: color 0.3s ease !important;
    }

    /* LG+ (1024px+): Smaller subtitle for 4-column layout */
    @media (min-width: 1024px) {
        .contact-price-subtitle {
            font-size: clamp(0.9rem, 2.2vw, 1.25rem) !important;
        }
    }

    /* XL+ (1200px+): Restore original subtitle size */
    @media (min-width: 1200px) {
        .contact-price-subtitle {
            font-size: clamp(1rem, 2.8vw, 1.5rem) !important;
        }
    }

    /* Tablet responsive for contact-price-subtitle */
    @media (max-width: 768px) {
        .contact-price-subtitle {
            font-size: 1.25rem !important;
        }
    }

    /* Mobile responsive for contact-price-subtitle */
    @media (max-width: 480px) {
        .contact-price-subtitle {
            font-size: 1.125rem !important;
        }
    }


