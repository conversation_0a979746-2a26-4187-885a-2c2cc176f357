/**
 * Pagination Loading Fix CSS
 * Giải quyết vấn đề pagination bị dịch chuyển khi loading skeleton
 * 
 * Vấn đề: Khi click pagination, skeleton loading xuất hiện và thay đổi chiều cao
 * của khu vực sản phẩm, khiến pagination bị dịch chuyển lên/xuống
 * 
 * Giải pháp: Đặt min-height cố định cho products grid trong quá trình loading
 */

/* ===== CORE PAGINATION LOADING FIX ===== */

/* Đảm bảo products grid có transition mượt mà */
#productsGrid {
    transition: min-height 0.3s ease-out, opacity 0.2s ease-out;
    min-height: 200px; /* Chiều cao tối thiểu */
}

/* Gi<PERSON> nguyên chiều cao khi loading skeleton */
.products-grid.loading-skeleton {
    min-height: inherit !important;
    overflow: hidden;
    pointer-events: none;
}

/* Đ<PERSON>m bảo pagination không bị ảnh hưởng */
.pagination-section {
    margin-top: 3rem;
    position: relative;
    z-index: 10;
    /* Đảm bảo pagination luôn ở vị trí cố định */
    clear: both;
}

/* ===== SKELETON CARD IMPROVEMENTS ===== */

/* Đảm bảo skeleton cards có chiều cao nhất quán với sản phẩm thực */
.skeleton-card {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    /* Đơn giản hóa - loại bỏ hover effects */
    transition: none !important;
    transform: none !important;
}

.skeleton-card .product-image-wrapper {
    aspect-ratio: 1;
    min-height: 200px;
    flex-shrink: 0;
    /* Đơn giản hóa background */
    background: #f3f4f6;
}

.skeleton-card .product-info-wrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    /* Match chính xác với sản phẩm thực */
    padding: 1.25rem 1.25rem 1rem 1.25rem !important;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    position: relative;
}

.skeleton-card .product-info-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
}

/* Match premium-price-section min-height và alignment */
.skeleton-card .premium-price-section {
    min-height: 4.5rem !important; /* Desktop default */
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important; /* Căn trái như sản phẩm thực */
    margin-top: auto !important;
    margin-bottom: 0 !important;
}

/* Đơn giản hóa shimmer effect */
.skeleton-card .animate-shimmer {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
}

/* Đơn giản hóa pulse animation */
.skeleton-card .animate-pulse {
    background: #e5e7eb;
}

/* Skeleton sale badge styling */
.skeleton-card .product-image-wrapper .animate-pulse {
    /* Badge có màu đậm hơn một chút để nổi bật */
    background: #d1d5db;
    border-radius: 0.375rem;
}

/* ===== LOADING STATES ===== */

/* Smooth transition khi thay đổi từ skeleton sang content thực */
#productsGrid.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Animation cho skeleton cards */
.skeleton-card {
    animation: skeleton-fade-in 0.4s ease-out forwards;
    opacity: 0;
}

.skeleton-card:nth-child(1) { animation-delay: 0.05s; }
.skeleton-card:nth-child(2) { animation-delay: 0.1s; }
.skeleton-card:nth-child(3) { animation-delay: 0.15s; }
.skeleton-card:nth-child(4) { animation-delay: 0.2s; }
.skeleton-card:nth-child(5) { animation-delay: 0.25s; }
.skeleton-card:nth-child(6) { animation-delay: 0.3s; }

@keyframes skeleton-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

/* Desktop */
@media (min-width: 1024px) {
    .skeleton-card {
        min-height: 450px;
    }

    .skeleton-card .product-image-wrapper {
        min-height: 220px;
    }

    .skeleton-card .premium-price-section {
        min-height: 4.5rem !important; /* Match desktop */
        justify-content: flex-start !important; /* Căn trái */
    }
}

/* Tablet */
@media (min-width: 481px) and (max-width: 1023px) {
    .skeleton-card {
        min-height: 380px;
    }

    .skeleton-card .product-image-wrapper {
        min-height: 190px;
    }

    .skeleton-card .product-info-wrapper {
        padding: 1rem 1rem 0.75rem 1rem !important; /* Match tablet */
    }

    .skeleton-card .product-info-wrapper::before {
        left: 1rem;
        right: 1rem;
    }

    .skeleton-card .premium-price-section {
        min-height: 4rem !important; /* Match tablet */
        justify-content: flex-start !important; /* Căn trái */
    }

    /* Badge size cho tablet */
    .skeleton-card .product-image-wrapper .animate-pulse {
        width: 2.75rem; /* w-11 */
        height: 1.375rem; /* h-5.5 */
    }

    .pagination-section {
        margin-top: 2.5rem;
    }
}

/* Mobile */
@media (max-width: 480px) {
    .skeleton-card {
        min-height: 320px;
    }

    .skeleton-card .product-image-wrapper {
        min-height: 160px;
    }

    .skeleton-card .product-info-wrapper {
        padding: 1rem 1rem 0.75rem 1rem !important; /* Match mobile */
    }

    .skeleton-card .product-info-wrapper::before {
        left: 1rem;
        right: 1rem;
    }

    .skeleton-card .premium-price-section {
        min-height: 3.5rem !important; /* Match mobile */
        justify-content: flex-start !important; /* Căn trái */
    }

    /* Badge size cho mobile */
    .skeleton-card .product-image-wrapper .animate-pulse {
        width: 2.5rem; /* w-10 */
        height: 1.25rem; /* h-5 */
        top: 0.5rem; /* top-2 */
        right: 0.5rem; /* right-2 */
    }

    #productsGrid {
        min-height: 150px;
    }

    .pagination-section {
        margin-top: 2rem;
    }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */

/* Reduce motion cho users có preference */
@media (prefers-reduced-motion: reduce) {
    #productsGrid,
    .skeleton-card {
        transition: none !important;
        animation: none !important;
    }
}

/* Optimize cho performance */
.skeleton-card {
    contain: layout style paint;
    will-change: opacity, transform;
}

.skeleton-card .animate-shimmer {
    contain: layout style paint;
}

/* ===== DEBUG HELPERS (có thể bỏ comment để debug) ===== */

/*
.products-grid.loading-skeleton {
    border: 2px dashed red !important;
}

.skeleton-card {
    border: 1px solid blue !important;
}

#productsGrid[style*="min-height"] {
    background: rgba(255, 255, 0, 0.1) !important;
}
*/
