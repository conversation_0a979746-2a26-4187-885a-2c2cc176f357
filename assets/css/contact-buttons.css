/**
 * Contact Buttons CSS - Nội Thất Bàng <PERSON>ũ
 * Thiết kế các nút liên hệ nhanh và nút cuộn lên đầu trang
 * Thiết kế tinh tế, mềm mại và hiện đại
 */

/* Container cho c<PERSON>c nút liên h<PERSON> (bên ph<PERSON>i) */
.contact-buttons-container {
    position: fixed !important;
    right: 25px !important;
    bottom: 25px !important;
    z-index: 999 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 18px !important;
    pointer-events: none; /* Cho phép click xuyên qua container nhưng không qua các nút */
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Container cho nút cuộn lên đầu trang (bên trái) */
.scroll-top-container {
    position: fixed !important;
    left: 20px !important;
    bottom: 20px !important;
    z-index: 999 !important;
    pointer-events: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Hiệu ứng làm mờ các nút khi cuộn xuống trên mobile */
.fade-buttons {
    opacity: 0.2;
    transform: scale(0.85) translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

/* Hiệu ứng làm mờ các nút khi menu mobile được mở */
.menu-open-fade {
    opacity: 0.1;
    transform: scale(0.8);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Hiệu ứng khi hover vào nút đã bị làm mờ */
.fade-buttons .contact-button:hover,
.fade-buttons .scroll-button:hover,
.fade-buttons:hover,
.menu-open-fade .contact-button:hover,
.menu-open-fade .scroll-button:hover,
.menu-open-fade:hover {
    opacity: 1;
    transform: scale(1);
}

/* Đảm bảo các nút vẫn có thể click được khi bị làm mờ */
.fade-buttons .contact-button,
.fade-buttons .scroll-button,
.menu-open-fade .contact-button,
.menu-open-fade .scroll-button {
    pointer-events: auto;
}

/* Thiết kế chung cho các nút liên hệ */
.contact-button {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    transform: translateX(100px);
    opacity: 0;
    pointer-events: auto; /* Đảm bảo các nút có thể click được */
    overflow: visible;
}

/* Thiết kế cho nút cuộn lên đầu trang */
.scroll-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #F37321, #D35400);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    transform: translateY(70px);
    opacity: 0;
    pointer-events: auto;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

/* Hiệu ứng khi hiển thị các nút */
.contact-button.show {
    transform: translateX(0);
    opacity: 1;
}

.scroll-button.show {
    transform: translateY(0);
    opacity: 1;
}

/* Icon trong nút */
.contact-icon {
    width: 60px;
    height: 60px;
    object-fit: contain;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
    z-index: 2;
    position: relative;
}

/* Tooltip hiển thị khi hover */
.contact-tooltip {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    letter-spacing: 0.3px;
}

/* Tooltip bên phải (cho nút cuộn lên) */
.scroll-button .contact-tooltip {
    left: calc(100% + 10px);
}

/* Tooltip bên trái (cho các nút liên hệ) */
.contact-tooltip-left {
    right: calc(100% + 15px);
}

/* Mũi tên cho tooltip bên phải */
.scroll-button .contact-tooltip::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 8px 8px 8px 0;
    border-style: solid;
    border-color: transparent rgba(0, 0, 0, 0.85) transparent transparent;
}

/* Mũi tên cho tooltip bên trái */
.contact-tooltip-left::before {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 8px 0 8px 8px;
    border-style: solid;
    border-color: transparent transparent transparent rgba(0, 0, 0, 0.85);
}

/* Hiển thị tooltip khi hover */
.scroll-button:hover .contact-tooltip {
    opacity: 1;
    visibility: visible;
    left: calc(100% + 15px);
}

.contact-button:hover .contact-tooltip {
    opacity: 1;
    visibility: visible;
}

.contact-button:hover .contact-tooltip-left {
    right: calc(100% + 20px);
}

/* Hiệu ứng khi hover vào nút */
.contact-button:hover {
    transform: translateY(-5px);
}

.contact-button:hover .contact-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 8px 15px rgba(0, 0, 0, 0.25));
}

/* Hiệu ứng đặc biệt cho nút gọi điện khi hover */
.phone-button:hover::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(76, 175, 80, 0.4) 0%, rgba(76, 175, 80, 0) 70%);
    animation: phone-hover-glow 1.5s infinite alternate ease-in-out;
    z-index: -1;
}

@keyframes phone-hover-glow {
    0% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.3);
        opacity: 0.3;
    }
}

.scroll-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 25px rgba(243, 115, 33, 0.3);
    background: linear-gradient(135deg, #ff8c42, #e56717);
}

/* Hiệu ứng khi click vào nút */
.contact-button:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.1s ease;
}

.contact-button:active .contact-icon {
    transform: scale(0.9);
    transition: all 0.1s ease;
}

.scroll-button:active {
    transform: scale(0.95);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Hiệu ứng ripple động */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
    pointer-events: none;
    z-index: 1;
}

@keyframes ripple {
    to {
        transform: scale(3);
        opacity: 0;
    }
}

/* Hiệu ứng cho từng nút */

/* Nút gọi điện */
.phone-button {
    animation-delay: 0.1s;
    position: relative;
}

/* Hiệu ứng đặc biệt khi cần thu hút sự chú ý */
@keyframes phone-attention {
    0% {
        transform: scale(1);
    }
    10% {
        transform: scale(1.2);
    }
    20% {
        transform: scale(0.9);
    }
    30% {
        transform: scale(1.1);
    }
    40% {
        transform: scale(0.95);
    }
    50% {
        transform: scale(1);
    }
    60% {
        transform: scale(1.2);
    }
    70% {
        transform: scale(0.9);
    }
    80% {
        transform: scale(1.1);
    }
    90% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

.phone-attention .contact-icon {
    animation: phone-attention 1s ease-in-out, phone-pulse 1s infinite alternate !important;
    filter: drop-shadow(0 0 15px rgba(76, 175, 80, 0.8)) brightness(1.2) !important;
}

.phone-attention::before {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    top: -25%;
    left: -25%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(76, 175, 80, 0.6) 0%, rgba(76, 175, 80, 0) 70%);
    animation: phone-attention-halo 1s infinite alternate ease-in-out;
}

@keyframes phone-attention-halo {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    100% {
        transform: scale(1.2);
        opacity: 0.2;
    }
}

/* Hiệu ứng nâng cao cho nút gọi điện */
@keyframes phone-pulse {
    0% {
        filter: drop-shadow(0 0 0 rgba(76, 175, 80, 0.6));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(76, 175, 80, 0.7));
        transform: scale(1.08);
    }
    100% {
        filter: drop-shadow(0 0 0 rgba(76, 175, 80, 0.6));
        transform: scale(1);
    }
}

@keyframes phone-rotate {
    0% {
        transform: rotate(-5deg);
    }
    10% {
        transform: rotate(5deg);
    }
    20% {
        transform: rotate(-5deg);
    }
    30% {
        transform: rotate(5deg);
    }
    40% {
        transform: rotate(-5deg);
    }
    50% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

/* Hiệu ứng hào quang xung quanh nút gọi điện */
.phone-button::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(76, 175, 80, 0.2);
    z-index: -1;
    animation: phone-halo 2s infinite ease-in-out;
}

@keyframes phone-halo {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.4);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

.phone-button .contact-icon {
    animation: phone-pulse 2s infinite;
}

/* Hiệu ứng rung khi có cuộc gọi đến */
.phone-button:hover .contact-icon {
    animation: phone-rotate 1s ease-in-out;
}

/* Hiệu ứng float cho các nút */
@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

/* Nút Zalo */
.zalo-button .contact-icon {
    animation-delay: 0.2s;
    animation: float 3s ease-in-out infinite;
}

/* Nút Messenger */
.messenger-button .contact-icon {
    animation-delay: 0.3s;
    animation: float 3s ease-in-out infinite 0.5s;
}

/* Nút cuộn lên đầu trang */
.scroll-button i {
    color: white;
    font-size: 18px;
}

/* Hiệu ứng hover cho nút cuộn lên đầu trang */
.scroll-button:hover {
    background: linear-gradient(135deg, #ff8c42, #e56717);
}

/* Responsive */
@media (max-width: 768px) {
    .contact-buttons-container {
        right: 15px !important;
        bottom: 85px !important; /* Điều chỉnh để tránh xung đột với Mobile Bottom Navigation (62px + 8px + margin) */
        gap: 12px !important; /* Giảm khoảng cách giữa các nút */
    }

    .scroll-top-container {
        left: 15px !important;
        bottom: 85px !important; /* Cùng chiều cao với contact-buttons-container */
    }

    .contact-button {
        width: 50px; /* Giảm kích thước từ 60px xuống 50px */
        height: 50px;
    }

    .contact-icon {
        width: 40px; /* Giảm kích thước từ 45px xuống 40px */
        height: 40px;
    }

    .scroll-button {
        width: 35px;
        height: 35px;
    }

    .contact-tooltip {
        display: none; /* Ẩn tooltip trên mobile để tránh che nội dung */
    }

    /* Điều chỉnh hiệu ứng hover trên mobile */
    .contact-button:hover {
        transform: none;
    }

    .contact-button:hover .contact-icon {
        transform: scale(1.05);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    }

    /* Tắt hiệu ứng float trên mobile để tránh gây khó chịu */
    .zalo-button .contact-icon,
    .messenger-button .contact-icon {
        animation: none;
    }
}

/* Màn hình siêu nhỏ */
@media (max-width: 360px) {
    .contact-buttons-container {
        right: 10px !important;
        bottom: 85px !important; /* Cùng với mobile để tránh xung đột */
        gap: 10px !important; /* Giảm thêm khoảng cách cho màn hình nhỏ */
    }

    .scroll-top-container {
        left: 10px !important;
        bottom: 85px !important; /* Cùng chiều cao với contact-buttons-container */
    }

    .contact-button {
        width: 45px; /* Giảm thêm cho màn hình siêu nhỏ */
        height: 45px;
    }

    .contact-icon {
        width: 35px; /* Giảm từ 40px xuống 35px */
        height: 35px;
    }

    .scroll-button {
        width: 32px; /* Giảm từ 35px xuống 32px */
        height: 32px;
    }

    .scroll-button i {
        font-size: 13px; /* Giảm từ 14px xuống 13px */
    }
}

/* Màn hình rất nhỏ */
@media (max-width: 320px) {
    .contact-buttons-container {
        right: 8px !important;
        bottom: 85px !important;
        gap: 8px !important;
    }

    .scroll-top-container {
        left: 8px !important;
        bottom: 85px !important;
    }

    .contact-button {
        width: 42px;
        height: 42px;
    }

    .contact-icon {
        width: 32px;
        height: 32px;
    }

    .scroll-button {
        width: 30px;
        height: 30px;
    }

    .scroll-button i {
        font-size: 12px;
    }
}

/* Thiết bị có màn hình lớn */
@media (min-width: 1200px) {
    .contact-buttons-container {
        right: 30px;
        bottom: 30px;
        gap: 20px;
    }

    .scroll-top-container {
        left: 25px;
        bottom: 25px;
    }

    .scroll-button {
        width: 40px;
        height: 40px;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
    }
}
