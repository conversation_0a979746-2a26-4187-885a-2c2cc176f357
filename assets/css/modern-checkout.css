/*
 * Modern Checkout CSS for Nội Thất Bàng Vũ
 * Thiết kế trang thanh toán hiện đại với hiệu ứng và UI/UX chuẩn
 */

:root {
    /* <PERSON><PERSON><PERSON>ắ<PERSON> */
    --checkout-primary: #f97316;
    --checkout-primary-dark: #ea580c;
    --checkout-primary-light: #ffedd5;
    --checkout-primary-ultra-light: #fff7ed;
    --checkout-text: #1e293b;
    --checkout-text-light: #64748b;
    --checkout-border: #e2e8f0;
    --checkout-bg: #f8fafc;
    --checkout-card-bg: #ffffff;
    --checkout-success: #10b981;
    --checkout-error: #ef4444;
    --checkout-warning: #f59e0b;
    --checkout-info: #3b82f6;
    
    /* <PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> và khoảng cách */
    --checkout-border-radius: 0.75rem;
    --checkout-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --checkout-box-shadow-hover: 0 8px 16px rgba(0, 0, 0, 0.1);
    --checkout-transition: all 0.3s ease;
}

/* Container chính cho checkout */
.modern-checkout {
    background-color: var(--checkout-bg);
    padding: 2rem 0;
}

/* Card chính */
.checkout-card {
    background-color: var(--checkout-card-bg);
    border-radius: var(--checkout-border-radius);
    box-shadow: var(--checkout-box-shadow);
    overflow: hidden;
    transition: var(--checkout-transition);
    border: 1px solid var(--checkout-border);
}

.checkout-card:hover {
    box-shadow: var(--checkout-box-shadow-hover);
}

/* Tiêu đề */
.checkout-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--checkout-text);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.checkout-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--checkout-primary);
    border-radius: 3px;
}

/* Form groups */
.checkout-form-group {
    margin-bottom: 1.5rem;
}

.checkout-form-group label {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--checkout-text);
    margin-bottom: 0.5rem;
}

.checkout-form-group input,
.checkout-form-group textarea,
.checkout-form-group select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--checkout-border);
    border-radius: 0.5rem;
    background-color: #fff;
    color: var(--checkout-text);
    font-size: 0.875rem;
    transition: var(--checkout-transition);
}

.checkout-form-group input:focus,
.checkout-form-group textarea:focus,
.checkout-form-group select:focus {
    outline: none;
    border-color: var(--checkout-primary);
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.15);
}

.checkout-form-group input::placeholder,
.checkout-form-group textarea::placeholder {
    color: var(--checkout-text-light);
    opacity: 0.7;
}

/* Checkout steps */
.checkout-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.checkout-steps::before {
    content: '';
    position: absolute;
    top: 1.25rem;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--checkout-border);
    z-index: 1;
}

.checkout-step {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.checkout-step-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid var(--checkout-border);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    color: var(--checkout-text-light);
    font-weight: 600;
    transition: var(--checkout-transition);
}

.checkout-step-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--checkout-text-light);
    text-align: center;
    transition: var(--checkout-transition);
}

.checkout-step.active .checkout-step-icon {
    background-color: var(--checkout-primary);
    border-color: var(--checkout-primary);
    color: white;
}

.checkout-step.active .checkout-step-text {
    color: var(--checkout-primary);
}

.checkout-step.completed .checkout-step-icon {
    background-color: var(--checkout-success);
    border-color: var(--checkout-success);
    color: white;
}

/* Phương thức thanh toán */
.payment-methods {
    margin-top: 1.5rem;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--checkout-border);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: var(--checkout-transition);
}

.payment-method:hover {
    border-color: var(--checkout-primary);
    background-color: var(--checkout-primary-ultra-light);
}

.payment-method.active {
    border-color: var(--checkout-primary);
    background-color: var(--checkout-primary-ultra-light);
}

.payment-method-radio {
    margin-right: 1rem;
}

.payment-method-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--checkout-primary);
}

.payment-method-details {
    flex: 1;
}

.payment-method-title {
    font-weight: 600;
    color: var(--checkout-text);
    margin-bottom: 0.25rem;
}

.payment-method-description {
    font-size: 0.75rem;
    color: var(--checkout-text-light);
}

/* Thông tin ngân hàng */
.bank-info-card {
    background-color: var(--checkout-primary-ultra-light);
    border: 1px solid var(--checkout-primary-light);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
}

.bank-info-title {
    font-weight: 600;
    color: var(--checkout-primary-dark);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.bank-info-title i {
    margin-right: 0.5rem;
}

.bank-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.bank-info-label {
    color: var(--checkout-text-light);
}

.bank-info-value {
    font-weight: 600;
    color: var(--checkout-text);
}

/* Nút đặt hàng */
.checkout-submit-btn {
    background-color: var(--checkout-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.5rem;
    font-weight: 600;
    font-size: 1rem;
    width: 100%;
    cursor: pointer;
    transition: var(--checkout-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.checkout-submit-btn:hover {
    background-color: var(--checkout-primary-dark);
    transform: translateY(-2px);
}

.checkout-submit-btn:active {
    transform: translateY(0);
}

/* Tóm tắt đơn hàng */
.order-summary {
    position: sticky;
    top: 2rem;
}

.order-summary-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--checkout-text);
    margin-bottom: 1.25rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.order-summary-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--checkout-primary);
    border-radius: 3px;
}

.order-summary-list {
    margin-bottom: 1.5rem;
}

.order-summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--checkout-border);
}

.order-summary-item:last-child {
    border-bottom: none;
}

.order-summary-item-name {
    flex: 1;
    font-size: 0.875rem;
    color: var(--checkout-text);
}

.order-summary-item-quantity {
    font-size: 0.75rem;
    color: var(--checkout-text-light);
    margin-left: 0.25rem;
}

.order-summary-item-price {
    font-weight: 600;
    color: var(--checkout-text);
    font-size: 0.875rem;
}

.order-summary-subtotal,
.order-summary-shipping {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    font-size: 0.875rem;
    color: var(--checkout-text);
}

.order-summary-total {
    display: flex;
    justify-content: space-between;
    padding: 1rem 0;
    border-top: 2px solid var(--checkout-border);
    margin-top: 0.5rem;
}

.order-summary-total-label {
    font-weight: 600;
    color: var(--checkout-text);
}

.order-summary-total-price {
    font-weight: 700;
    color: var(--checkout-primary);
    font-size: 1.25rem;
}

.order-summary-vat {
    text-align: right;
    font-size: 0.75rem;
    color: var(--checkout-text-light);
    margin-top: 0.25rem;
}

/* Responsive */
@media (max-width: 768px) {
    .checkout-steps {
        overflow-x: auto;
        padding-bottom: 1rem;
    }
    
    .checkout-step {
        min-width: 100px;
    }
}
