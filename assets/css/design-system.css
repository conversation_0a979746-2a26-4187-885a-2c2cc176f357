/*
 * DESIGN SYSTEM CSS - NỘI THẤT BĂNG VŨ
 * Hệ thống thiết kế chuẩn cho toàn bộ website
 * Import file này vào tất cả các trang để đảm bảo tính nhất quán
 */

/* ===== COLOR SYSTEM ===== */
:root {
    /* 🔥 PRIMARY COLORS - <PERSON><PERSON><PERSON> ch<PERSON>h */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* ⚡ SECONDARY COLORS - <PERSON><PERSON><PERSON> phụ */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-darker: #1a202c;
    --secondary-light: #435868;
    --secondary-lighter: #2d3748;

    /* 💎 ACCENT COLORS - <PERSON><PERSON><PERSON> nhấn */
    --accent: #4CAF50;
    --accent-dark: #388E3C;
    --accent-light: #81C784;
    --blue: #3B82F6;
    --blue-hover: #2563EB;
    --yellow: #FBC02D;
    --yellow-light: #FDD835;

    /* 🎯 NEUTRAL COLORS - Màu trung tính */
    --text-primary: #1F2937;
    --text-secondary: #4B5563;
    --text-muted: #6B7280;
    --text-light: #9CA3AF;
    --text-white: #FFFFFF;
    --bg-white: #FFFFFF;
    --bg-light: #F9FAFB;
    --bg-gray: #F3F4F6;
    --bg-dark: #111827;
    --border-light: #E5E7EB;
    --border-gray: #D1D5DB;
    --border-dark: #374151;

    /* 🚨 STATUS COLORS - Màu trạng thái */
    --success: #10B981;
    --success-light: #D1FAE5;
    --warning: #F59E0B;
    --warning-light: #FEF3C7;
    --error: #EF4444;
    --error-light: #FEE2E2;
    --info: #3B82F6;
    --info-light: #DBEAFE;

    /* 🎭 SHADOWS & EFFECTS - Hiệu ứng */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --glow-primary: 0 0 15px rgba(243, 115, 33, 0.3);
    --glow-white: 0 0 15px rgba(255, 255, 255, 0.3);
    --glow-success: 0 0 15px rgba(16, 185, 129, 0.3);

    /* 🌈 GRADIENTS - Gradient */
    --gradient-primary: linear-gradient(135deg, #F37321 0%, #F39C21 100%);
    --gradient-secondary: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    --gradient-overlay: linear-gradient(to right, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
    --gradient-hero: linear-gradient(135deg, rgba(243,115,33,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* 📝 TYPOGRAPHY - Font */
    --font-primary: 'Be Vietnam Pro', 'Montserrat', sans-serif;
    --font-secondary: 'Roboto', sans-serif;
    --font-mono: 'Fira Code', monospace;
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;

    /* 🎯 SPACING - Khoảng cách */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* 🎪 TRANSITIONS - Hiệu ứng chuyển động */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* 📱 BREAKPOINTS - Responsive */
    --breakpoint-xs: 400px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* ===== COMPONENT STYLES ===== */

/* 🎯 BUTTONS - Nút bấm */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--space-3) var(--space-6);
    border-radius: 0.75rem;
    font-weight: var(--font-semibold);
    font-family: var(--font-primary);
    border: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--glow-primary);
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
}

.btn-secondary {
    background: var(--bg-white);
    color: var(--primary);
    border: 2px solid var(--primary);
    padding: var(--space-3) var(--space-6);
    border-radius: 0.75rem;
    font-weight: var(--font-semibold);
    font-family: var(--font-primary);
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.btn-secondary:hover {
    background: var(--primary);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 1px solid var(--border-gray);
    padding: var(--space-2) var(--space-4);
    border-radius: 0.5rem;
    font-weight: var(--font-medium);
    font-family: var(--font-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.btn-outline:hover {
    border-color: var(--primary);
    background: var(--primary-ultra-light);
    transform: translateY(-1px);
}

/* 🎨 CARDS - Thẻ */
.card {
    background: var(--bg-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    transition: var(--transition-smooth);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.card-premium {
    background: var(--gradient-hero);
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-lg), var(--glow-primary);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-light);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--border-light);
    background: var(--bg-light);
}

/* 📝 INPUTS - Ô nhập liệu */
.input-field {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--border-gray);
    border-radius: 0.5rem;
    font-family: var(--font-primary);
    font-size: var(--text-base);
    transition: var(--transition-normal);
    background: var(--bg-white);
    color: var(--text-primary);
}

.input-field:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.input-field::placeholder {
    color: var(--text-muted);
}

/* 🏷️ BADGES - Nhãn */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: 9999px;
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    font-family: var(--font-primary);
}

.badge-primary {
    background: var(--primary-ultra-light);
    color: var(--primary);
}

.badge-success {
    background: var(--success-light);
    color: var(--success);
}

.badge-warning {
    background: var(--warning-light);
    color: var(--warning);
}

.badge-error {
    background: var(--error-light);
    color: var(--error);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s var(--transition-smooth);
}

.animate-slideInRight {
    animation: slideInRight 0.6s var(--transition-smooth);
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* ===== UTILITY CLASSES ===== */
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-error { color: var(--error) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-white { background-color: var(--bg-white) !important; }

.border-primary { border-color: var(--primary) !important; }
.border-light { border-color: var(--border-light) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

.rounded { border-radius: 0.5rem !important; }
.rounded-lg { border-radius: 0.75rem !important; }
.rounded-xl { border-radius: 1rem !important; }

.font-medium { font-weight: var(--font-medium) !important; }
.font-semibold { font-weight: var(--font-semibold) !important; }
.font-bold { font-weight: var(--font-bold) !important; }

/* ===== RESPONSIVE HELPERS ===== */
.container-custom {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container-custom {
        padding: 0 var(--space-6);
    }
}

@media (min-width: 1024px) {
    .container-custom {
        padding: 0 var(--space-8);
    }
}

/* ===== SECTION SPACING ===== */
.section-spacing {
    padding: var(--space-16) 0;
}

@media (min-width: 768px) {
    .section-spacing {
        padding: var(--space-20) 0;
    }
}

@media (min-width: 1024px) {
    .section-spacing {
        padding: var(--space-24) 0;
    }
}

/* Sidebar */
.sidebar {
    width: 14rem;
    min-height: 100vh;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: var(--primary); /* Đây là màu nền hiện tại */
    overflow-x: hidden;
    transition: 0.5s;
}

.sidebar .sidebar-heading {
    /* ... */
    color: rgba(255, 255, 255, 0.4); /* Màu chữ của heading */
}

.sidebar .nav-item .nav-link {
    /* ... */
    color: rgba(255, 255, 255, 0.8); /* Màu chữ của link */
}

.sidebar .nav-item .nav-link:hover {
    color: #fff; /* Màu chữ của link khi hover */
}

.sidebar .nav-item.active .nav-link {
    font-weight: 700;
    color: #fff; /* Màu chữ của link khi active */
}
