/*
 * Breadcrumb Fix CSS for Nội Thất Bàng Vũ
 * Sửa lỗi hover từ breadcrumb ảnh hưởng đến menu header
 */

/* Đ<PERSON>m bảo breadcrumb container có z-index cao hơn và pointer-events riêng biệt */
.bg-gray-100.py-3 {
    position: relative;
    z-index: 90; /* Thấp hơn header (100) nhưng đủ cao để tách biệt */
    pointer-events: auto;
    isolation: isolate; /* Tạo stacking context mới */
    margin-top: 1px; /* Tạo khoảng cách nhỏ với header */
}

/* Đảm bảo các link trong breadcrumb không ảnh hưởng đến header */
.bg-gray-100.py-3 a,
.bg-gray-100.py-3 span {
    position: relative;
    z-index: 1;
    pointer-events: auto;
}

/* Đảm bảo main content có z-index thấp hơn header */
.site-main {
    position: relative;
    z-index: 80;
}

/* Đ<PERSON><PERSON> bảo header có stacking context riêng biệt */
.premium-header {
    isolation: isolate;
    position: relative;
    z-index: 100;
}

/* Đ<PERSON>m bảo bottom-header-container có z-index cao hơn */
.bottom-header-container {
    position: relative;
    z-index: 10;
}

/* Đảm bảo nav-menu có z-index cao */
.nav-menu {
    position: relative;
    z-index: 15;
}

/* Đảm bảo nav-item có z-index cao */
.nav-item {
    position: relative;
    z-index: 20;
}

/* Đảm bảo nav-link có z-index cao */
.nav-link {
    position: relative;
    z-index: 25;
}

/* Sửa lỗi hover effect - Chỉ áp dụng khi thực sự hover vào nav-link */
.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #f97316;
    background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
    transition: width 0.3s ease, transform 0.3s ease;
    transform: translateX(-50%);
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2);
    pointer-events: none;
}

.nav-link:hover::after,
.nav-item.active .nav-link::after {
    width: 100%;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(249, 115, 22, 0.05);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
    transform: translateY(100%);
    border-radius: var(--radius-md);
    pointer-events: none;
}

.nav-link:hover::before {
    opacity: 1;
    transform: translateY(0);
}

/* Đảm bảo breadcrumb container không bị ảnh hưởng bởi các hiệu ứng hover từ header */
.bg-gray-100.py-3 {
    pointer-events: auto;
}

/* Đảm bảo các phần tử trong breadcrumb có pointer-events riêng */
.bg-gray-100.py-3 .container,
.bg-gray-100.py-3 .flex,
.bg-gray-100.py-3 a,
.bg-gray-100.py-3 span {
    pointer-events: auto;
}

/* Tạo một "barrier" giữa header và breadcrumb */
.premium-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: transparent;
    z-index: 95;
    pointer-events: none;
}

/* Đảm bảo hover effect chỉ áp dụng khi thực sự hover vào nav-link */
.nav-link:hover,
.nav-item.active .nav-link {
    color: #f97316;
    text-shadow: 0 0 1px rgba(249, 115, 22, 0.1);
}

/* Đảm bảo hover effect chỉ áp dụng khi thực sự hover vào nav-link */
.nav-link:hover i {
    transform: translateY(2px);
}

/* Sửa lỗi pointer-events cho breadcrumb */
.bg-gray-100.py-3 {
    position: relative;
    z-index: 90;
    pointer-events: auto;
}

/* Tạo một lớp ngăn cách giữa header và breadcrumb */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: transparent;
    z-index: 95;
    pointer-events: none;
    margin-top: 150px; /* Điều chỉnh theo chiều cao của header */
}

/* Đảm bảo breadcrumb không bị ảnh hưởng bởi hover từ header */
.bg-gray-100.py-3 {
    position: relative;
    z-index: 90;
}

/* Đảm bảo các phần tử trong breadcrumb có pointer-events riêng */
.bg-gray-100.py-3 .container {
    position: relative;
    z-index: 1;
}

/* Đảm bảo các phần tử trong breadcrumb có pointer-events riêng */
.bg-gray-100.py-3 .flex {
    position: relative;
    z-index: 2;
}

/* Đảm bảo các phần tử trong breadcrumb có pointer-events riêng */
.bg-gray-100.py-3 a,
.bg-gray-100.py-3 span {
    position: relative;
    z-index: 3;
}

/* Giải pháp cuối cùng: Ngăn chặn sự kiện hover từ breadcrumb ảnh hưởng đến header */
.premium-header {
    pointer-events: none; /* Tắt pointer-events cho toàn bộ header */
}

/* Bật lại pointer-events cho các phần tử con trong header */
.premium-header .top-bar,
.premium-header .mid-header,
.premium-header .bottom-header,
.premium-header .nav-menu,
.premium-header .nav-item,
.premium-header .nav-link,
.premium-header .user-actions,
.premium-header .action-btn,
.premium-header .search-container,
.premium-header .search-form,
.premium-header .search-input,
.premium-header .search-button,
.premium-header .premium-logo,
.premium-header .user-dropdown,
.premium-header .cart-container,
.premium-header .mobile-menu-toggle,
.premium-header .nav-scroll-arrow,
.premium-header .nav-scroll-left,
.premium-header .nav-scroll-right {
    pointer-events: auto; /* Bật lại pointer-events cho các phần tử con */
}
