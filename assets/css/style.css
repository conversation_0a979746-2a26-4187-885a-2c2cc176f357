/* Custom CSS */

/* Product Card Hover Effect */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Product Image Zoom Effect */
.product-image {
    overflow: hidden;
    position: relative;
    padding-bottom: 100%;
    /* Tỉ lệ 1:1 */
    height: 0;
}

.product-image img {
    transition: transform 0.5s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image:hover img {
    transform: scale(1.05);
}

/* Product Detail Sticky Images */
.sticky-product-images {
    position: static;
    /* Bắt đầu với position static */
    z-index: 10;
    max-height: calc(100vh - 40px);
    /* Giới hạn chiều cao tối đa */
    overflow-y: auto;
    /* Cho phép cuộn nếu nội dung quá dài */
}

/* Container cho ph<PERSON><PERSON> h<PERSON>nh <PERSON>nh sản phẩm */
.md\:w-2\/5 {
    position: relative;
    /* Để có thể định vị absolute bên trong */
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Custom Form Focus */
input:focus,
textarea:focus,
select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Custom Button Styles */
.btn-primary {
    @apply bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-200;
}

.btn-secondary {
    @apply bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition duration-200;
}

.btn-success {
    @apply bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition duration-200;
}

.btn-danger {
    @apply bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition duration-200;
}

/* Custom Badge Styles */
.badge {
    @apply inline-block px-2 py-1 text-xs font-semibold rounded;
}

.badge-primary {
    @apply bg-blue-100 text-blue-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

/* Custom Alert Styles */
.alert {
    @apply px-4 py-3 rounded relative border-l-4 mb-4;
}

.alert-success {
    @apply bg-green-100 border-green-500 text-green-700;
}

.alert-info {
    @apply bg-blue-100 border-blue-500 text-blue-700;
}

.alert-warning {
    @apply bg-yellow-100 border-yellow-500 text-yellow-700;
}

.alert-danger {
    @apply bg-red-100 border-red-500 text-red-700;
}

/* Custom Table Styles */
.table {
    @apply w-full border-collapse;
}

.table th {
    @apply px-4 py-2 bg-gray-100 border border-gray-300 text-left;
}

.table td {
    @apply px-4 py-2 border border-gray-300;
}

.table tr:nth-child(even) {
    @apply bg-gray-50;
}

.table tr:hover {
    @apply bg-gray-100;
}

/* Custom Pagination Styles */
.pagination {
    @apply flex space-x-1 mt-4;
}

.pagination a,
.pagination span {
    @apply px-3 py-1 border border-gray-300 rounded;
}

.pagination a {
    @apply text-blue-500 hover:bg-blue-50;
}

.pagination span.current {
    @apply bg-blue-500 text-white;
}

/* Custom Card Styles */
.card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.card-header {
    @apply px-6 py-4 bg-gray-50 border-b border-gray-200;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
}

/* Custom Form Styles */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-gray-700 text-sm font-bold mb-2;
}

.form-control {
    @apply w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-text {
    @apply text-sm text-gray-600 mt-1;
}

/* Custom Breadcrumb Styles */
.breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-600 mb-4;
}

.breadcrumb-item {
    @apply flex items-center;
}

.breadcrumb-item:not(:last-child)::after {
    content: "/";
    @apply ml-2;
}

.breadcrumb-item a {
    @apply text-blue-500 hover:text-blue-700;
}

/* Custom Tooltip Styles */
.tooltip {
    @apply relative inline-block;
}

.tooltip .tooltip-text {
    @apply invisible absolute z-10 px-3 py-2 bg-gray-900 text-white text-xs rounded opacity-0 transition-opacity duration-300;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
}

.tooltip:hover .tooltip-text {
    @apply visible opacity-100;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

/* Review Media Grid */
.review-media-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-width: 400px;
}

.review-media-item {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    cursor: pointer;
}

.review-media-item.size-small {
    width: 80px;
    height: 80px;
}

.review-media-item img,
.review-media-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.review-media-item:hover img,
.review-media-item:hover video {
    transform: scale(1.05);
}

/* Media Viewer */
.media-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.media-viewer.active {
    opacity: 1;
    visibility: visible;
}

.media-viewer-content {
    max-width: 90%;
    max-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-viewer-content img,
.media-viewer-content video {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

.media-viewer-close,
.media-viewer-prev,
.media-viewer-next {
    position: absolute;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
    z-index: 10000;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.media-viewer-close:hover,
.media-viewer-prev:hover,
.media-viewer-next:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.media-viewer-close {
    top: 20px;
    right: 20px;
}

.media-viewer-prev {
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.media-viewer-next {
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.media-viewer-counter {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
}

/* Review Content */
.review-content {
    white-space: pre-line;
    text-align: justify;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    position: relative;
}

.dropdown-content {
    display: none;
    transition: all 0.3s ease;
}

.dropdown-menu:hover .dropdown-content {
    display: block;
}

/* Add a padding area to prevent the dropdown from closing when moving from button to content */
.dropdown-menu::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 20px;
    display: none;
}

.dropdown-menu:hover::after {
    display: block;
}

/* Add a padding area to the dropdown content to prevent it from closing */
.dropdown-content::before {
    content: "";
    position: absolute;
    top: -20px;
    left: 0;
    width: 100%;
    height: 20px;
}

/* Notification styles are now defined in index.php and realtime-updates.css */

/* Cart Animation */
.cart-animation {
    position: fixed;
    background-color: rgba(59, 130, 246, 0.2);
    border: 2px solid #3B82F6;
    border-radius: 8px;
    z-index: 9998;
    pointer-events: none;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

/* Search Loading Styles - Fallback cho search.js */
.search-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: #666;
    background-color: rgba(243, 115, 33, 0.02);
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

.search-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(243, 115, 33, 0.1);
    border-radius: 50%;
    border-top-color: #F37321;
    animation: spin 0.8s cubic-bezier(0.3, 0, 0.3, 1) infinite;
    margin-bottom: 1rem;
    box-shadow: 0 0 15px rgba(243, 115, 33, 0.1);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}