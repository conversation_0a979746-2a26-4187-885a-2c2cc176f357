/*
 * Modern Footer CSS for Nội Thất Băng Vũ
 * Thiết kế footer hiện đại, phứ<PERSON> tạp và đẹp mắt
 * Phiên bản nâng cấp với nhiều hiệu ứng và chi tiết hơn
 */

/* <PERSON>iến CSS */
:root {
    /* <PERSON><PERSON><PERSON> sắ<PERSON> ch<PERSON> */
    --footer-bg: #1a202c;
    --footer-bg-light: #2d3748;
    --footer-bg-gradient: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    --footer-text: #e2e8f0;
    --footer-text-muted: #a0aec0;
    --footer-link: #e2e8f0;
    --footer-link-hover: #ffffff;

    /* Màu accent và nhấn mạnh */
    --footer-accent: #F37321;
    --footer-accent-hover: #D65A0F;
    --footer-accent-light: rgba(243, 115, 33, 0.1);
    --footer-accent-gradient: linear-gradient(135deg, #F37321 0%, #F39C21 100%);

    /* <PERSON><PERSON>ờng viền và nền */
    --footer-border: rgba(255, 255, 255, 0.1);
    --footer-border-accent: rgba(243, 115, 33, 0.3);
    --footer-input-bg: rgba(255, 255, 255, 0.05);
    --footer-input-border: rgba(255, 255, 255, 0.1);
    --footer-input-focus: rgba(243, 115, 33, 0.3);

    /* Hiệu ứng và đổ bóng */
    --footer-shadow: 0 -10px 40px rgba(0, 0, 0, 0.15);
    --footer-item-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    --footer-hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    --footer-transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);

    /* Kích thước và spacing */
    --footer-wave-height: 80px;
    --footer-section-spacing: 3rem;
    --footer-item-spacing: 1.5rem;
}

/* Container chính của footer */
.modern-footer {
    position: relative;
    background: var(--footer-bg-gradient);
    color: var(--footer-text);
    font-family: 'Be Vietnam Pro', 'Montserrat', sans-serif;
    box-shadow: var(--footer-shadow);
    overflow: hidden;
    border-top: 1px solid var(--footer-border);
}

/* Wave divider trên cùng của footer */
.footer-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--footer-wave-height);
    transform: translateY(-99%);
    filter: drop-shadow(0 -5px 10px rgba(0,0,0,0.1));
    z-index: 0;
}

.footer-wave svg {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: var(--footer-transition);
}

.footer-wave path {
    fill: var(--footer-bg);
}

/* Container nội dung chính */
.footer-content {
    position: relative;
    z-index: 1;
    padding: 5rem 2rem 3rem;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Flexbox layout cho footer */
.footer-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Cột footer */
.footer-column {
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0 1.5rem;
    box-sizing: border-box;
}

.footer-column.col-about {
    width: 30%;
}

.footer-column.col-links {
    width: 16%;
}

.footer-column.col-categories {
    width: 20%;
    /* Giữ nguyên class col-categories nhưng đã đổi thành "Hỗ trợ khách hàng" */
}

.footer-column.col-connect {
    width: 30%;
}

/* Tiêu đề cột */
.footer-column-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
    color: var(--footer-link-hover);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 1.1rem;
}

.footer-column-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--footer-accent-gradient);
    border-radius: 3px;
}

/* Logo footer */
.footer-logo {
    margin-bottom: 1.75rem;
    display: inline-block;
    transition: var(--footer-transition);
    position: relative;
}

.footer-logo:hover {
    transform: translateY(-3px);
}

.footer-logo img {
    height: auto;
    width: 100%;
    max-width: 100%;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* Mô tả công ty */
.footer-about-text {
    color: var(--footer-text-muted);
    line-height: 1.7;
    margin-bottom: 1.75rem;
    font-size: 0.95rem;
    position: relative;
    padding-left: 1rem;
    border-left: 2px solid var(--footer-border-accent);
}

/* Thông tin liên hệ */
.footer-contact-info {
    margin-bottom: 1.75rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-contact-item {
    display: flex;
    align-items: flex-start;
    transition: var(--footer-transition);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--footer-border);
}

.footer-contact-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--footer-item-shadow);
    border-color: var(--footer-border-accent);
}

.footer-contact-icon {
    margin-right: 0.75rem;
    color: var(--footer-accent);
    font-size: 1rem;
    padding-top: 0.125rem;
    min-width: 20px;
    text-align: center;
}

.footer-contact-text {
    color: var(--footer-text-muted);
    line-height: 1.5;
    font-size: 0.95rem;
}

.footer-contact-text a {
    color: var(--footer-text-muted);
    text-decoration: none;
    transition: var(--footer-transition);
}

.footer-contact-text a:hover {
    color: var(--footer-link-hover);
}

/* Danh sách liên kết */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-links li {
    margin-bottom: 0.25rem;
    position: relative;
}

.footer-links a {
    color: var(--footer-text-muted);
    text-decoration: none;
    transition: var(--footer-transition);
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.footer-links a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--footer-accent);
    transform: translateX(-100%);
    transition: var(--footer-transition);
    opacity: 0.7;
}

.footer-links a:hover {
    color: var(--footer-link-hover);
    transform: translateX(5px);
}

.footer-links a:hover::before {
    transform: translateX(0);
}

.footer-links a i {
    margin-right: 0.5rem;
    font-size: 0.75rem;
    color: var(--footer-accent);
    transition: var(--footer-transition);
}

/* Mạng xã hội */
.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
    transition: var(--footer-transition);
    border: 1px solid var(--footer-border);
    position: relative;
    overflow: hidden;
}

.footer-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--footer-accent-gradient);
    opacity: 0;
    transition: var(--footer-transition);
    z-index: 0;
}

.footer-social-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    position: relative;
    z-index: 1;
    transition: var(--footer-transition);
}

.footer-social-link:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--footer-hover-shadow);
    border-color: transparent;
}

.footer-social-link:hover::before {
    opacity: 1;
}



/* Facebook Page Plugin */
.footer-facebook-container {
    border-radius: 12px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--footer-border);
    transition: var(--footer-transition);
    height: auto;
    max-height: 130px;
    margin-bottom: 2rem;
    padding: 0;
    position: relative;
    width: 100%;
}

.footer-facebook-container:hover {
    border-color: var(--footer-border-accent);
    box-shadow: var(--footer-item-shadow);
    transform: translateY(-3px);
}

/* Facebook Page Plugin trong cột connect */
.col-connect .footer-facebook-container {
    width: 100%;
}

/* Facebook iframe */
.footer-facebook-container .fb-page {
    width: 100% !important;
    display: block !important;
}

.footer-facebook-container .fb-page span,
.footer-facebook-container .fb-page iframe {
    width: 100% !important;
    height: 130px !important;
    max-height: 130px !important;
    display: block !important;
    min-width: 100% !important;
}

/* Đảm bảo Facebook iframe luôn có chiều rộng 100% */
.footer-facebook-container .fb_iframe_widget,
.footer-facebook-container .fb_iframe_widget span,
.footer-facebook-container .fb_iframe_widget span iframe[style] {
    width: 100% !important;
}

/* Mạng xã hội trong cột connect */
.col-connect .footer-social {
    margin-top: 1.5rem;
    /* justify-content: flex-start; */
}

/* Chứng nhận */
.footer-certifications {
    margin-top: 1.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
}

.footer-certification {
    margin-bottom: 0.5rem;
}

.footer-certification-link {
    display: inline-block;
    transition: var(--footer-transition);
    padding: 0.5rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--footer-border);
}

.footer-certification-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--footer-item-shadow);
    border-color: var(--footer-border-accent);
}

.footer-certification-image {
    max-width: 120px;
    height: auto;
    border-radius: 4px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Bottom footer */
.footer-bottom {
    position: relative;
    border-top: 1px solid var(--footer-border);
    padding: 1.75rem 0;
    margin-top: 3.5rem;
    background-color: rgba(0, 0, 0, 0.2);
}

.footer-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--footer-border-accent) 20%,
        var(--footer-border-accent) 80%,
        transparent 100%);
    opacity: 0.3;
}

.footer-bottom-content {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-copyright {
    color: var(--footer-text-muted);
    font-size: 0.875rem;
    position: relative;
    text-align: center;
}



/* Responsive */
@media (max-width: 1200px) {
    .footer-column.col-about {
        width: 30%;
    }

    .footer-column.col-links {
        width: 20%;
    }

    .footer-column.col-categories {
        width: 20%;
    }

    .footer-column.col-connect {
        width: 30%;
    }
}

@media (max-width: 992px) {
    .footer-grid {
        justify-content: space-around;
    }

    .footer-column.col-about {
        width: 100%;
        margin-bottom: 2rem;
    }

    .footer-column.col-links,
    .footer-column.col-categories {
        width: 45%;
    }

    .footer-column.col-connect {
        width: 100%;
        margin-top: 2rem;
    }

    .footer-logo {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
    }

    .footer-about-text {
        text-align: center;
        padding: 0;
        border-left: none;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
    }

    .footer-contact-info {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .footer-social {
        justify-content: center;
    }

    .footer-certifications {
        justify-content: center;
    }

    .footer-column-title {
        text-align: center;
    }

    .footer-column-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

@media (max-width: 768px) {
    .footer-content {
        padding: 4rem 1.5rem 2rem;
    }

    .footer-grid {
        justify-content: center;
    }

    .footer-column.col-links,
    .footer-column.col-categories {
        width: 45%;
    }

    .footer-column.col-connect {
        width: 100%;
    }

    /* Đảm bảo Facebook Plugin responsive trên tablet */
    .footer-facebook-container {
        max-width: 100%;
        overflow: hidden;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1.25rem;
        text-align: center;
    }



    .footer-copyright {
        text-align: center;
    }

    .footer-logo img {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .footer-grid {
        flex-direction: column;
        gap: 2.5rem;
    }

    .footer-column.col-about,
    .footer-column.col-links,
    .footer-column.col-categories,
    .footer-column.col-connect {
        width: 100%;
        padding: 0 0.5rem;
    }

    .col-connect .footer-facebook-container {
        margin-bottom: 1.5rem;
        width: 100%;
    }

    /* Đảm bảo Facebook Plugin responsive trên mobile */
    .footer-facebook-container .fb-page,
    .footer-facebook-container .fb-page span,
    .footer-facebook-container .fb-page iframe {
        width: 100% !important;
        max-width: 100% !important;
        height: 130px !important;
    }





    .footer-wave {
        height: calc(var(--footer-wave-height) * 0.7);
    }

    .footer-content {
        padding: 3.5rem 1.25rem 2rem;
    }
}
