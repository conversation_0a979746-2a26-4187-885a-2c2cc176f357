/* 
 * Smooth Scroll Optimization CSS
 * Tối ưu hóa các animation và transition cho trải nghiệm cuộn mượt mà
 */

/* Tối ưu hóa scroll behavior cho toàn bộ trang */
html {
    scroll-behavior: auto; /* Tắt để tránh xung đột với custom scroll */
    scroll-padding-top: 80px; /* Điều chỉnh theo chiều cao header */
}

/* Tối ưu hóa cho mobile */
@media (max-width: 768px) {
    html {
        scroll-padding-top: 60px;
    }
}

/* Tối ưu hóa để tránh layout shift */
body {
    overflow-x: hidden; /* Tránh horizontal scroll gây giật */
}

/* Tối ưu hóa animation cho skeleton loading */
.loading-skeleton {
    transition: opacity 0.3s ease-out !important;
}

.skeleton-card {
    animation: skeleton-pulse 1.2s ease-in-out infinite alternate;
}

@keyframes skeleton-pulse {
    0% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* T<PERSON><PERSON> hóa shimmer effect */
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animate-shimmer {
    animation: shimmer 1.5s infinite;
    background-size: 200% 100%;
}

/* Tối ưu hóa transition cho products grid */
#productsGrid {
    transition: opacity 0.15s ease-out, transform 0.15s ease-out;
    will-change: opacity, transform;
}

/* Tối ưu hóa khi loading */
#productsGrid.loading-skeleton {
    transition: opacity 0.15s ease-out;
}

/* Tối ưu hóa loading states */
.loading {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Tối ưu hóa pagination loading */
.ajax-pagination-link.loading {
    transform: scale(0.95);
    opacity: 0.7;
    pointer-events: none;
}

/* Tối ưu hóa filter results header animation */
.filter-results-header {
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* Tối ưu hóa cho các button states */
button, .btn {
    transition: all 0.15s ease-out;
}

/* Tối ưu hóa cho mobile touch */
@media (max-width: 768px) {
    * {
        -webkit-tap-highlight-color: transparent;
    }
    
    button, .btn, a {
        touch-action: manipulation;
    }
}

/* Tối ưu hóa cho reduced motion */
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
    
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Tối ưu hóa performance với GPU acceleration */
.product-card,
#productsGrid,
.filter-results-header,
.skeleton-card {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Tối ưu hóa cho scroll container */
.products-section {
    contain: layout style paint;
}

/* Tối ưu hóa cho loading spinner */
.loading-spinner {
    animation: spin 1s linear infinite;
    will-change: transform;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Tối ưu hóa cho fade transitions */
.fade-in {
    animation: fadeIn 0.2s ease-out forwards;
}

.fade-out {
    animation: fadeOut 0.15s ease-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-5px);
    }
}

/* Tối ưu hóa cho smooth scrolling trên các trình duyệt cũ */
@supports not (scroll-behavior: smooth) {
    html {
        scroll-behavior: auto;
    }
}

/* Tối ưu hóa cho high refresh rate displays */
@media (min-resolution: 120dpi) {
    .skeleton-card,
    .loading-spinner {
        animation-duration: 1s;
    }
}
