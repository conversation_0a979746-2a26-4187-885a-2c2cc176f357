/* Hiệu ứng pulse nâng cao cho mobile badge - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.mobile-cart-badge.badge-pulse,
.mobile-nav-badge.badge-pulse {
    /* animation: mobileBadgePulse 0.8s ease-in-out; */
    transform-origin: center;
}

/* @keyframes mobileBadgePulse {
    0% {
        transform: scale(1);
    }

    25% {
        transform: scale(1.4);
    }

    50% {
        transform: scale(1);
    }

    75% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
} */

/* Hiệu ứng chuyển động mượt hơn cho hiệu ứng bay vào giỏ hàng */
@keyframes cart-bounce {
    0% {
        transform: scale(1);
    }

    20% {
        transform: scale(1.2);
    }

    40% {
        transform: scale(0.85);
    }

    60% {
        transform: scale(1.1);
    }

    80% {
        transform: scale(0.95);
    }

    100% {
        transform: scale(1);
    }
}

/* CSS cho card danh mục tối ưu trên thiết bị di động */
@media (max-width: 640px) {
    .category-card {
        display: flex;
        flex-direction: column;
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.2s ease;
    }

    .category-card:active {
        transform: scale(0.98);
    }

    .category-btn {
        height: 32px;
        display: flex;
        align-items: center;
        font-size: 0.75rem;
        /* 12px */
        padding-top: 0;
        padding-bottom: 0;
    }

    .category-btn-icon {
        min-width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .category-card p {
        font-size: 10px;
        line-height: 1.3;
        margin-bottom: 6px;
    }
}

/* Viết thêm các media query để tối ưu hiển thị trên màn hình siêu nhỏ (nhỏ hơn 360px) */
@media (max-width: 360px) {
    .category-card h3 {
        font-size: 11px;
        margin-bottom: 3px;
    }

    .category-card p {
        height: 24px;
        /* Giới hạn chiều cao mô tả */
        margin-bottom: 4px;
    }

    .category-btn {
        font-size: 10px;
        height: 28px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .category-btn-icon {
        min-width: 14px;
        height: 14px;
    }

    .category-card .relative {
        height: 24vw;
        /* Đặt chiều cao của ảnh tỷ lệ với chiều rộng màn hình */
    }
}

/* Cải thiện hover/tap effect trên mobile */
@media (hover: none) {
    .category-card:active {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .category-btn:active {
        background-color: var(--primary-color, #F37321);
        color: white;
    }
}

/* Fix cho line height của đoạn mô tả danh mục */
.leading-3\.5 {
    line-height: 1.4;
}

/* Fix cho màn hình nhỏ để luôn hiển thị 2 cột */
@media (max-width: 400px) {
    .grid-cols-2>* {
        min-width: 0;
        /* Ngăn nội dung tràn */
    }
}