/*
 * Order Processing Animation CSS for Nội Thất <PERSON>ng <PERSON>
 * Hi<PERSON><PERSON>ng xử lý đơn hàng
 */

.order-processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.order-processing-content {
    background-color: white;
    border-radius: 16px;
    padding: 40px;
    width: 90%;
    max-width: 550px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.02);
    transform: translateY(20px);
    opacity: 0;
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: hidden;
}

.order-processing-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #f97316, #fb923c);
}

.order-processing-overlay.active .order-processing-content {
    transform: translateY(0);
    opacity: 1;
}

.order-processing-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 40px;
    position: relative;
}

/* Hiệu ứng loading mới */
.processing-animation {
    position: relative;
    width: 100%;
    height: 100%;
}

.processing-animation-circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(transparent, transparent, #f97316);
    animation: rotateLoader 1.5s linear infinite;
}

.processing-animation-circle::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background-color: white;
    border-radius: 50%;
    z-index: 1;
}

.processing-animation-circle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    background-color: #f97316;
    border-radius: 50%;
    z-index: 2;
    box-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
}

.processing-animation-inner {
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
}

.processing-animation-icon {
    font-size: 40px;
    color: #f97316;
    opacity: 0.9;
}

.processing-animation-complete {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #f97316, #fb923c);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 50px;
    opacity: 0;
    transform: scale(0.5);
    transition: opacity 0.5s cubic-bezier(0.16, 1, 0.3, 1), transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 10px 25px rgba(249, 115, 22, 0.3);
    z-index: 10;
}

.processing-animation-complete.active {
    opacity: 1;
    transform: scale(1);
    animation: checkPulse 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes rotateLoader {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes checkPulse {
    0% {
        transform: scale(0.5);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.order-processing-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.order-processing-message {
    font-size: 17px;
    color: #64748b;
    margin-bottom: 30px;
    font-weight: 400;
}

.order-processing-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
    text-align: left;
}

.order-processing-step {
    display: flex;
    align-items: center;
    padding: 14px 18px;
    border-radius: 12px;
    background-color: #f8fafc;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    border: 1px solid #f1f5f9;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    transform: translateZ(0);
}

.order-processing-step.active {
    background-color: #fff7ed;
    border-color: #ffedd5;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.08);
    transform: translateY(-2px) scale(1.01);
}

.order-processing-step.completed {
    background-color: #f0fdf4;
    border-color: #dcfce7;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
}

.order-processing-step-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: #64748b;
    font-size: 14px;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-processing-step.active .order-processing-step-icon {
    background: linear-gradient(135deg, #f97316, #fb923c);
    color: white;
    box-shadow: 0 4px 10px rgba(249, 115, 22, 0.2);
    transform: scale(1.1);
}

.order-processing-step.completed .order-processing-step-icon {
    background: linear-gradient(135deg, #10b981, #34d399);
    color: white;
    box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2);
}

.order-processing-step-text {
    font-size: 15px;
    color: #64748b;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    font-weight: 500;
}

.order-processing-step.active .order-processing-step-text {
    color: #f97316;
    font-weight: 600;
    transform: translateX(3px);
}

.order-processing-step.completed .order-processing-step-text {
    color: #10b981;
    font-weight: 600;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(249, 115, 22, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
    }
}

.order-processing-progress {
    height: 6px;
    background-color: #f1f5f9;
    border-radius: 8px;
    margin: 30px 0;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.order-processing-progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, #f97316, #fb923c);
    width: 0;
    transition: width 0.8s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
    border-radius: 8px;
}

.order-processing-dots {
    display: inline-block;
    position: relative;
    width: 20px;
    text-align: left;
}

.order-processing-dots::after {
    content: '...';
    position: absolute;
    left: 0;
    animation: dots 1.5s infinite;
    width: 20px;
    text-align: left;
}

@keyframes dots {
    0%, 20% {
        content: '.';
    }
    40% {
        content: '..';
    }
    60%, 100% {
        content: '...';
    }
}

/* Hiệu ứng nền */
@keyframes gradientBG {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
