/*
 * Modern Breadcrumb CSS for Nội Thất Bàng Vũ
 * Thiết kế breadcrumb hiện đại với hiệu ứng và icon
 */

:root {
    /* <PERSON><PERSON><PERSON> sắc */
    --breadcrumb-bg: linear-gradient(to right, #f8fafc, #f1f5f9);
    --breadcrumb-text: #64748b;
    --breadcrumb-text-hover: #f97316;
    --breadcrumb-active: #f97316;
    --breadcrumb-divider: #cbd5e1;
    --breadcrumb-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    --breadcrumb-icon-bg: rgba(249, 115, 22, 0.1);
    --breadcrumb-icon-color: #f97316;

    /* <PERSON><PERSON>ch thước và khoảng cách */
    --breadcrumb-padding: 1rem 0;
    --breadcrumb-border-radius: 0.5rem;
    --breadcrumb-item-gap: 0.75rem;
    --breadcrumb-icon-size: 1rem;
    --breadcrumb-font-size: 0.875rem;

    /* <PERSON><PERSON><PERSON>ng */
    --breadcrumb-transition: all 0.3s ease;
}

/* Container ch<PERSON><PERSON> cho breadcrumb */
.modern-breadcrumb {
    background: var(--breadcrumb-bg);
    padding: var(--breadcrumb-padding);
    box-shadow: var(--breadcrumb-shadow);
    position: relative;
    z-index: 90;
    border-bottom: 1px solid rgba(203, 213, 225, 0.3);
}

/* Wrapper cho các item breadcrumb */
.breadcrumb-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--breadcrumb-item-gap);
}

/* Item breadcrumb */
.breadcrumb-item {
    display: flex;
    align-items: center;
    color: var(--breadcrumb-text);
    font-size: var(--breadcrumb-font-size);
    font-weight: 500;
    transition: var(--breadcrumb-transition);
    position: relative;
}

/* Link trong breadcrumb */
.breadcrumb-link {
    display: flex;
    align-items: center;
    color: var(--breadcrumb-text);
    text-decoration: none;
    transition: var(--breadcrumb-transition);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Hover effect cho link */
.breadcrumb-link:hover {
    color: var(--breadcrumb-text-hover);
    background-color: rgba(249, 115, 22, 0.05);
}

/* Icon trong breadcrumb */
.breadcrumb-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    background-color: var(--breadcrumb-icon-bg);
    color: var(--breadcrumb-icon-color);
    border-radius: 50%;
    margin-right: 0.5rem;
    font-size: var(--breadcrumb-icon-size);
    transition: var(--breadcrumb-transition);
}

/* Hiệu ứng hover cho icon */
.breadcrumb-link:hover .breadcrumb-icon {
    background-color: rgba(249, 115, 22, 0.15);
}

/* Divider giữa các item */
.breadcrumb-divider {
    display: flex;
    align-items: center;
    color: var(--breadcrumb-divider);
    margin: 0 0.25rem;
    font-size: 0.75rem;
}

/* Ghi đè lên CSS mặc định để loại bỏ dấu "/" */
.breadcrumb-item:not(:last-child)::after {
    content: none !important;
}

/* Item active (cuối cùng) */
.breadcrumb-item.active {
    color: var(--breadcrumb-active);
    font-weight: 600;
}

.breadcrumb-item.active .breadcrumb-icon {
    background-color: rgba(249, 115, 22, 0.2);
}

/* Hiệu ứng đặc biệt cho item active */
.breadcrumb-item.active::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--breadcrumb-active);
    border-radius: 1px;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.breadcrumb-item.active:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Responsive */
@media (max-width: 768px) {
    .breadcrumb-wrapper {
        gap: 0.5rem;
    }

    .breadcrumb-icon {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.75rem;
        margin-right: 0.35rem;
    }

    .breadcrumb-item {
        font-size: 0.75rem;
    }
}





/* Đảm bảo nội dung breadcrumb hiển thị trên hiệu ứng shine */
.modern-breadcrumb .container {
    position: relative;
    z-index: 2;
}
