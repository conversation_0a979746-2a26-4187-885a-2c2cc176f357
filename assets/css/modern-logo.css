/* 
 * Modern Logo CSS for Nội Thất Bàng Vũ
 * Thiết kế logo hai dòng với phông chữ Be Vietnam Pro
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* Logo Container */
.modern-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    padding: 5px 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    max-width: 100%;
}

/* Logo Image */
.modern-logo-image {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.modern-logo-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(243, 115, 33, 0.3));
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.modern-logo:hover .modern-logo-image img {
    transform: scale(1.08) rotate(3deg);
    filter: drop-shadow(0 4px 8px rgba(243, 115, 33, 0.5));
}

/* Logo Text Container */
.modern-logo-text {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
    min-width: 0;
    flex: 1;
}

/* Main Logo Text */
.modern-logo-title {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 18px;
    font-weight: 800;
    color: var(--primary);
    margin: 0;
    line-height: 1.2;
    letter-spacing: 0.2px;
    text-transform: uppercase;
    position: relative;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modern-logo:hover .modern-logo-title {
    letter-spacing: 0.5px;
    transform: translateY(-1px);
}

/* Logo Tagline */
.modern-logo-tagline {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 12px;
    font-weight: 500;
    color: var(--neutral-dark);
    margin: 4px 0 0 0;
    line-height: 1;
    letter-spacing: 0.5px;
    opacity: 1;
    transition: all 0.4s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modern-logo:hover .modern-logo-tagline {
    color: var(--primary);
    letter-spacing: 0.7px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .modern-logo-title {
        font-size: 16px;
    }
    
    .modern-logo-tagline {
        font-size: 11px;
    }
    
    .modern-logo-image {
        width: 45px;
        height: 45px;
        margin-right: 12px;
    }
}

@media (max-width: 992px) {
    .modern-logo-title {
        font-size: 15px;
    }
    
    .modern-logo-tagline {
        font-size: 10px;
    }
    
    .modern-logo-image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
}

@media (max-width: 768px) {
    .modern-logo-title {
        font-size: 14px;
    }
    
    .modern-logo-tagline {
        font-size: 9px;
    }
    
    .modern-logo-image {
        width: 38px;
        height: 38px;
        margin-right: 10px;
    }
}

@media (max-width: 576px) {
    .modern-logo-title {
        font-size: 13px;
    }
    
    .modern-logo-tagline {
        font-size: 8px;
    }
    
    .modern-logo-image {
        width: 35px;
        height: 35px;
        margin-right: 8px;
    }
}

@media (max-width: 375px) {
    .modern-logo-title {
        font-size: 12px;
    }
    
    .modern-logo-tagline {
        font-size: 7px;
    }
    
    .modern-logo-image {
        width: 30px;
        height: 30px;
        margin-right: 6px;
    }
}

/* Mobile Logo Styles */
.mobile-menu-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 60px;
}

.mobile-menu-logo img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(243, 115, 33, 0.3));
    flex-shrink: 0;
}

.mobile-menu-logo-text {
    display: flex;
    flex-direction: column;
    margin-top: 3px;
}

.mobile-menu-logo-title {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 15px;
    color: var(--white);
    margin: 0;
    font-weight: 800;
    line-height: 1.3;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    white-space: normal;
    word-wrap: break-word;
}

.mobile-menu-logo-tagline {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: 12px;
    color: var(--primary);
    margin: 4px 0 0;
    font-weight: 500;
    letter-spacing: 0.2px;
}

/* Mobile Responsive Styles */
@media (max-width: 375px) {
    .mobile-menu-logo {
        padding: 15px;
        margin-top: 50px;
        gap: 12px;
    }

    .mobile-menu-logo img {
        width: 42px;
        height: 42px;
    }

    .mobile-menu-logo-title {
        font-size: 13px;
        letter-spacing: 0.2px;
        line-height: 1.3;
    }

    .mobile-menu-logo-tagline {
        font-size: 11px;
        letter-spacing: 0.1px;
        margin-top: 3px;
    }
}

@media (max-width: 320px) {
    .mobile-menu-logo {
        padding: 12px;
        margin-top: 45px;
        gap: 10px;
    }

    .mobile-menu-logo img {
        width: 38px;
        height: 38px;
    }

    .mobile-menu-logo-title {
        font-size: 12px;
        letter-spacing: 0.1px;
        line-height: 1.25;
    }

    .mobile-menu-logo-tagline {
        font-size: 10px;
        margin-top: 2px;
        line-height: 1.2;
    }
}
