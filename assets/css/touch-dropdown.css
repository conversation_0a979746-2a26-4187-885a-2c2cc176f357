/**
 * Touch Dropdown CSS - Minimal version
 * Chỉ xử lý logic cơ bản cho touch devices, không thêm hiệu ứng
 */

/* Chỉ áp dụng trên touch devices - Override hover behavior */
@media (hover: none) {
    /* V<PERSON> hiệu hóa hover trên touch devices */
    .cart-container:hover .mini-cart,
    .user-dropdown:hover .user-dropdown-menu {
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* Chỉ hiển thị khi có class dropdown-active */
    .cart-container.dropdown-active .mini-cart,
    .user-dropdown.dropdown-active .user-dropdown-menu {
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }
} 