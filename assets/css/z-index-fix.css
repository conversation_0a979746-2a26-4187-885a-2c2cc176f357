/**
 * Z-index Fix CSS
 * Đ<PERSON><PERSON> nghĩa các z-index để đảm bảo các phần tử dropdown hiển thị đúng cách
 */

:root {
    /* Z-index cho các phần tử dropdown */
    --z-base: 1;
    --z-header: 100;
    --z-dropdown: 1000;
    --z-mega-menu: 1000;
    --z-search-suggestions: 9999; /* Tăng z-index lên rất cao để đảm bảo hiển thị trên tất cả */
    --z-mini-cart: 1000;
    --z-user-dropdown: 1000;
    --z-mobile-menu: 2000;
    --z-fixed: 3000;
    --z-modal: 5000;
}

/* Đảm bảo mega menu hiển thị đúng cách */
.mega-menu {
    z-index: var(--z-mega-menu) !important;
}

/* Đảm bảo search suggestions hiển thị đúng cách */
.search-suggestions {
    z-index: var(--z-search-suggestions) !important;
    position: absolute !important;
    top: 100% !important;
    isolation: isolate !important; /* Tạo stacking context mới */
}

/* Đ<PERSON>m bảo mini cart hiển thị đúng cách */
.mini-cart {
    z-index: var(--z-mini-cart) !important;
}

/* Đảm bảo user dropdown hiển thị đúng cách */
.user-dropdown-menu {
    z-index: var(--z-user-dropdown) !important;
}

/* Đảm bảo search container hiển thị đúng cách */
.search-container {
    position: relative;
    z-index: 1200 !important;
}

/* Đảm bảo search form hiển thị đúng cách */
.search-form {
    position: relative;
    z-index: 1200 !important;
}
