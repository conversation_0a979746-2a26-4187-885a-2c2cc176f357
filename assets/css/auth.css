/*
 * Auth CSS - Nội Thất Băng Vũ
 * Trang đăng nhập và đăng ký
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* <PERSON><PERSON><PERSON>n CSS */
:root {
    /* <PERSON><PERSON><PERSON> sắc ch<PERSON> */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* <PERSON><PERSON><PERSON> sắc phụ */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-light: #435868;

    /* <PERSON><PERSON>u sắc nhấn */
    --accent: #4CAF50;
    --accent-dark: #388E3C;
    --accent-light: #81C784;

    /* <PERSON><PERSON><PERSON> trung t<PERSON>h */
    --white: #FFFFFF;
    --light-gray: #F5F5F5;
    --medium-gray: #E0E0E0;
    --dark-gray: #757575;
    --black: #212121;

    /* <PERSON><PERSON> bóng */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

    /* Bo góc */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;

    /* Khoảng cách */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;

    /* Hiệu ứng chuyển động */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Reset CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Custom Scrollbar - Thiết kế thanh cuộn màu cam mềm mại giống trang chủ */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

/* Track (phần nền của thanh cuộn) */
::-webkit-scrollbar-track {
    background: rgba(243, 115, 33, 0.05);
    border-radius: 10px;
}

/* Thumb (phần thanh cuộn có thể kéo) */
::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
    border-radius: 10px;
    border: 3px solid transparent;
    background-clip: content-box;
    transition: all 0.3s ease;
}

/* Hover effect cho thumb */
::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary));
    border: 2px solid transparent;
    background-clip: content-box;
}

/* Tùy chỉnh cho Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary) rgba(243, 115, 33, 0.05);
}

body {
    font-family: 'Be Vietnam Pro', sans-serif;
    background-color: var(--light-gray);
    color: var(--black);
    min-height: 100vh;
    display: flex;
    align-items: center; /* Căn giữa theo chiều dọc */
    justify-content: center;
    position: relative;
    overflow-x: hidden;
    padding: var(--spacing-lg) 0; /* Thêm padding để tạo khoảng cách */
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, rgba(42, 59, 71, 0.02) 100%);
    transition: all 0.8s ease;
}

/* Enhanced styling for body.loaded state */
body.loaded {
    /* Nền gradient cam rất nhạt và tinh tế */
    background: linear-gradient(135deg,
        #fefcfa 0%,           /* Gần như trắng với chút cam */
        #fdf9f6 20%,          /* Cam cực nhạt */
        #fcf6f1 40%,          /* Cam rất nhạt */
        #fbf3ec 60%,          /* Cam nhạt */
        #faf0e7 80%,          /* Cam nhạt vừa */
        #f9ede2 100%          /* Cam nhạt ấm */
    );
    position: relative;
    /* overflow: hidden; - Đã loại bỏ để cho phép cuộn trang */
}

/* Họa tiết chuyên nghiệp góc trên trái - Corporate Minimal */
body.loaded::before {
    content: '';
    position: fixed;
    top: -100px;
    left: -100px;
    width: 400px;
    height: 400px;
    background:
        /* Gradient mềm mại chính */
        radial-gradient(ellipse 200px 150px at 35% 40%, rgba(243, 115, 33, 0.08) 0%, rgba(243, 115, 33, 0.04) 60%, transparent 85%),
        /* Lớp gradient phụ tạo độ sâu */
        radial-gradient(ellipse 120px 180px at 60% 30%, rgba(255, 152, 102, 0.06) 0%, rgba(255, 152, 102, 0.03) 50%, transparent 75%),
        /* Hiệu ứng ánh sáng tinh tế */
        radial-gradient(circle 250px at 25% 25%, rgba(255, 243, 237, 0.04) 0%, transparent 60%),
        /* Gradient conic tạo chuyển động mềm */
        conic-gradient(from 45deg at 40% 40%,
            rgba(255, 243, 237, 0.02),
            rgba(243, 115, 33, 0.05),
            rgba(255, 228, 214, 0.03),
            rgba(243, 115, 33, 0.04),
            rgba(255, 243, 237, 0.02)
        );
    border-radius: 60% 40% 70% 30%;
    transform: rotate(-8deg);
    z-index: 0;
    pointer-events: none;
    animation: float-corporate-left 25s ease-in-out infinite;
    filter: blur(0.5px);
}

/* Họa tiết chuyên nghiệp góc dưới phải - Corporate Minimal */
body.loaded::after {
    content: '';
    position: fixed;
    bottom: -120px;
    right: -120px;
    width: 450px;
    height: 450px;
    background:
        /* Gradient chính tạo hình khối mềm */
        radial-gradient(ellipse 180px 220px at 40% 35%, rgba(255, 152, 102, 0.07) 0%, rgba(255, 152, 102, 0.035) 65%, transparent 85%),
        /* Gradient phụ tạo chiều sâu */
        radial-gradient(ellipse 150px 120px at 65% 60%, rgba(243, 115, 33, 0.05) 0%, rgba(243, 115, 33, 0.025) 50%, transparent 75%),
        /* Hiệu ứng ánh sáng mềm */
        radial-gradient(circle 200px at 70% 70%, rgba(255, 228, 214, 0.03) 0%, transparent 65%),
        /* Gradient conic tạo chuyển động */
        conic-gradient(from 135deg at 60% 60%,
            rgba(255, 243, 237, 0.015),
            rgba(255, 152, 102, 0.045),
            rgba(255, 206, 184, 0.025),
            rgba(243, 115, 33, 0.03),
            rgba(255, 243, 237, 0.015)
        ),
        /* Lớp gradient tổng thể */
        radial-gradient(ellipse 300px 200px at 50% 50%, rgba(255, 243, 237, 0.02) 0%, transparent 70%);
    border-radius: 45% 55% 65% 35%;
    transform: rotate(12deg);
    z-index: 0;
    pointer-events: none;
    animation: float-corporate-right 30s ease-in-out infinite reverse;
    filter: blur(0.8px);
}

/* Animation cho họa tiết corporate bên trái */
@keyframes float-corporate-left {
    0%, 100% {
        transform: rotate(-8deg) translateY(0px) scale(1);
        opacity: 0.7;
    }
    25% {
        transform: rotate(-5deg) translateY(-3px) scale(1.005);
        opacity: 0.8;
    }
    50% {
        transform: rotate(-11deg) translateY(-5px) scale(1.01);
        opacity: 0.75;
    }
    75% {
        transform: rotate(-6deg) translateY(-2px) scale(1.003);
        opacity: 0.85;
    }
}

/* Animation cho họa tiết corporate bên phải */
@keyframes float-corporate-right {
    0%, 100% {
        transform: rotate(12deg) translateY(0px) scale(1);
        opacity: 0.7;
    }
    30% {
        transform: rotate(15deg) translateY(-2px) scale(1.003);
        opacity: 0.8;
    }
    60% {
        transform: rotate(9deg) translateY(-4px) scale(1.008);
        opacity: 0.75;
    }
    90% {
        transform: rotate(13deg) translateY(-1px) scale(1.002);
        opacity: 0.82;
    }
}

/* Họa tiết bổ sung - Corporate Subtle với geometric patterns */
body.loaded {
    background-image:
        /* Đường cong geometric góc trên phải */
        linear-gradient(135deg, transparent 0%, transparent 45%, rgba(243, 115, 33, 0.03) 46%, rgba(243, 115, 33, 0.03) 48%, transparent 49%) calc(100% - 150px) 50px/120px 80px no-repeat,
        linear-gradient(45deg, transparent 0%, transparent 45%, rgba(255, 152, 102, 0.025) 46%, rgba(255, 152, 102, 0.025) 48%, transparent 49%) calc(100% - 100px) 80px/80px 60px no-repeat,
        /* Đường cong geometric góc dưới trái */
        linear-gradient(45deg, transparent 0%, transparent 45%, rgba(243, 115, 33, 0.025) 46%, rgba(243, 115, 33, 0.025) 48%, transparent 49%) 80px calc(100% - 120px)/100px 70px no-repeat,
        linear-gradient(135deg, transparent 0%, transparent 45%, rgba(255, 206, 184, 0.02) 46%, rgba(255, 206, 184, 0.02) 48%, transparent 49%) 50px calc(100% - 80px)/70px 50px no-repeat,
        /* Gradient nhẹ góc trên phải */
        radial-gradient(ellipse 80px 60px at calc(100% - 120px) 80px, rgba(255, 152, 102, 0.04) 0%, rgba(255, 152, 102, 0.02) 50%, transparent 70%),
        /* Gradient nhẹ góc dưới trái */
        radial-gradient(ellipse 60px 80px at 100px calc(100% - 80px), rgba(243, 115, 33, 0.03) 0%, rgba(243, 115, 33, 0.015) 50%, transparent 70%),
        /* Hiệu ứng ánh sáng trung tâm */
        radial-gradient(circle 300px at 50% 50%, rgba(255, 243, 237, 0.02) 0%, rgba(255, 243, 237, 0.01) 40%, transparent 70%),
        /* Gradient conic tinh tế ở trung tâm */
        conic-gradient(from 0deg at 50% 50%,
            rgba(255, 243, 237, 0.008),
            rgba(243, 115, 33, 0.02),
            rgba(255, 228, 214, 0.012),
            rgba(255, 152, 102, 0.015),
            rgba(255, 243, 237, 0.008)
        );
}

/* Hiệu ứng hover cho auth-container để tương tác với nền */
.auth-container:hover ~ body.loaded::before {
    animation-duration: 8s;
    opacity: 0.8;
}

.auth-container:hover ~ body.loaded::after {
    animation-duration: 10s;
    opacity: 0.8;
}

/* Thêm họa tiết hình học trừu tượng nâng cao */
body.loaded::after {
    background:
        /* Đường cong lớn góc dưới phải */
        conic-gradient(from 45deg at 70% 30%, transparent 0%, rgba(243, 115, 33, 0.015) 25%, transparent 50%, rgba(255, 152, 102, 0.02) 75%, transparent 100%),
        /* Đường cong nhỏ bổ sung */
        radial-gradient(ellipse 200px 150px at 60% 60%, rgba(255, 206, 184, 0.01) 0%, transparent 60%),
        /* Background gốc */
        radial-gradient(ellipse 180px 220px at 40% 35%, rgba(255, 152, 102, 0.07) 0%, rgba(255, 152, 102, 0.035) 65%, transparent 85%),
        radial-gradient(ellipse 150px 120px at 65% 60%, rgba(243, 115, 33, 0.05) 0%, rgba(243, 115, 33, 0.025) 50%, transparent 75%),
        radial-gradient(circle 200px at 70% 70%, rgba(255, 228, 214, 0.03) 0%, transparent 65%),
        conic-gradient(from 135deg at 60% 60%,
            rgba(255, 243, 237, 0.015),
            rgba(255, 152, 102, 0.045),
            rgba(255, 206, 184, 0.025),
            rgba(243, 115, 33, 0.03),
            rgba(255, 243, 237, 0.015)
        ),
        radial-gradient(ellipse 300px 200px at 50% 50%, rgba(255, 243, 237, 0.02) 0%, transparent 70%);
}

/* Thêm element riêng cho họa tiết góc trên phải */
.geometric-pattern-top-right {
    content: '';
    position: fixed;
    top: 40px;
    right: 40px;
    width: 200px;
    height: 150px;
    background:
        /* Đường cong trừu tượng */
        conic-gradient(from 225deg at 30% 70%, transparent 0%, rgba(243, 115, 33, 0.02) 20%, transparent 40%, rgba(255, 152, 102, 0.015) 60%, transparent 80%),
        /* Đường thẳng mảnh */
        linear-gradient(45deg, transparent 48%, rgba(243, 115, 33, 0.025) 49%, rgba(243, 115, 33, 0.025) 51%, transparent 52%),
        linear-gradient(135deg, transparent 48%, rgba(255, 206, 184, 0.02) 49%, rgba(255, 206, 184, 0.02) 51%, transparent 52%);
    z-index: 0;
    pointer-events: none;
    opacity: 0.8;
    animation: geometric-float 20s ease-in-out infinite;
}

@keyframes geometric-float {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: rotate(5deg) scale(1.05);
        opacity: 0.6;
    }
}

/* Responsive adjustments cho họa tiết */
@media (max-width: 768px) {
    body.loaded::before {
        width: 150px;
        height: 150px;
        top: -40px;
        left: -40px;
    }

    body.loaded::after {
        width: 180px;
        height: 180px;
        bottom: -60px;
        right: -60px;
    }

    .geometric-pattern-top-right {
        width: 100px;
        height: 80px;
        top: 20px;
        right: 20px;
    }

    body.loaded {
        background-image:
            /* Geometric patterns cho mobile */
            linear-gradient(135deg, transparent 0%, transparent 45%, rgba(243, 115, 33, 0.02) 46%, rgba(243, 115, 33, 0.02) 48%, transparent 49%) calc(100% - 80px) 30px/60px 40px no-repeat,
            linear-gradient(45deg, transparent 0%, transparent 45%, rgba(255, 152, 102, 0.015) 46%, rgba(255, 152, 102, 0.015) 48%, transparent 49%) calc(100% - 50px) 50px/40px 30px no-repeat,
            linear-gradient(45deg, transparent 0%, transparent 45%, rgba(243, 115, 33, 0.015) 46%, rgba(243, 115, 33, 0.015) 48%, transparent 49%) 40px calc(100% - 60px)/50px 35px no-repeat,
            /* Giảm kích thước họa tiết trên mobile */
            radial-gradient(ellipse 25px 40px at calc(100% - 70px) 50px, rgba(255, 152, 102, 0.025) 0%, transparent 65%),
            radial-gradient(ellipse 20px 15px at 60px calc(100% - 40px), rgba(243, 115, 33, 0.03) 0%, transparent 70%);
    }
}

/* Container chính */
.auth-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    min-height: 650px;
    background-color: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 5px 15px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(0, 0, 0, 0.02);
    margin: 0 var(--spacing-lg); /* Chỉ margin trái phải, không margin trên dưới */
    position: relative;
    z-index: 1;
    border: 1px solid rgba(243, 115, 33, 0.08);
}

.auth-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, rgba(243, 115, 33, 0.03) 0%, rgba(42, 59, 71, 0.03) 100%),
        repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(243, 115, 33, 0.01) 10px, rgba(243, 115, 33, 0.01) 11px);
    z-index: 0;
    pointer-events: none;
}

.auth-container::after {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, rgba(243, 115, 33, 0.1), rgba(42, 59, 71, 0.1), rgba(243, 115, 33, 0.1));
    border-radius: calc(var(--radius-xl) + 5px);
    z-index: -1;
    filter: blur(5px);
    opacity: 0.3;
}

/* Phần form */
.auth-form-section {
    flex: 1;
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    z-index: 1;
    background-color: rgba(255, 255, 255, 0.98);
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.auth-form-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, rgba(243, 115, 33, 0.03) 0%, rgba(42, 59, 71, 0.03) 100%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f37321' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
    opacity: 0.8;
}

.auth-form-section::after {
    content: "";
    position: absolute;
    top: 20px;
    right: 20px;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.03) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

/* Logo */
.auth-logo {
    margin-bottom: var(--spacing-md);
    text-align: left;
    position: relative;
    padding-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
}

.auth-logo::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 2px;
    background: linear-gradient(to right, var(--primary), transparent);
    border-radius: var(--radius-full);
}

.auth-logo-img {
    height: 40px;
    width: auto;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05));
    position: relative;
}

.auth-logo-img:hover {
    transform: translateY(-2px);
}

/* Tiêu đề và phụ đề */
.auth-welcome {
    margin-bottom: var(--spacing-lg);
    text-align: left;
    position: relative;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: rgba(243, 115, 33, 0.03);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary);
}

.auth-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--secondary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    letter-spacing: -0.5px;
    line-height: 1.2;
    position: relative;
}

.auth-title::before {
    content: "";
    position: absolute;
    top: -10px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--primary), var(--primary-light));
    border-radius: var(--radius-full);
}

.auth-subtitle {
    font-size: 14px;
    color: var(--dark-gray);
    line-height: 1.5;
    max-width: 95%;
    position: relative;
    padding-left: 0;
    margin-top: var(--spacing-xs);
    font-weight: 400;
}

/* Welcome Icons */
.welcome-icon {
    width: 32px;
    height: 32px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
    margin-left: var(--spacing-xs);
    position: relative;
    border-radius: 0;
    transition: all 0.3s ease;
    object-fit: contain;
    vertical-align: middle;
    transform: translateY(-2px);
}

/* Waving Hand Animation - Enhanced for login page */
.waving-hand {
    animation: wave-rotate 2.5s ease-in-out infinite;
    transform-origin: 70% 70%;
    /* Ensure no clipping */
    transform: translateY(-2px);
}

@keyframes wave-rotate {
    0%, 100% {
        transform: rotate(0deg) scale(1) translateY(-2px);
    }
    10% {
        transform: rotate(-15deg) scale(1.05) translateY(-2px);
    }
    20% {
        transform: rotate(15deg) scale(1.05) translateY(-2px);
    }
    30% {
        transform: rotate(-15deg) scale(1.05) translateY(-2px);
    }
    40% {
        transform: rotate(10deg) scale(1.05) translateY(-2px);
    }
    50% {
        transform: rotate(-8deg) scale(1.05) translateY(-2px);
    }
    60% {
        transform: rotate(5deg) scale(1.02) translateY(-2px);
    }
    70% {
        transform: rotate(-3deg) scale(1.01) translateY(-2px);
    }
    80%, 100% {
        transform: rotate(0) scale(1) translateY(-2px);
    }
}

/* Hover effect for waving hand */
.auth-title:hover .waving-hand {
    animation: wave-excited 1s ease-in-out;
    animation-iteration-count: 1;
}

@keyframes wave-excited {
    0% {
        transform: rotate(0) scale(1) translateY(-2px);
    }
    10% {
        transform: rotate(-20deg) scale(1.1) translateY(-2px);
    }
    20% {
        transform: rotate(20deg) scale(1.1) translateY(-2px);
    }
    30% {
        transform: rotate(-20deg) scale(1.1) translateY(-2px);
    }
    40% {
        transform: rotate(20deg) scale(1.1) translateY(-2px);
    }
    50% {
        transform: rotate(-15deg) scale(1.08) translateY(-2px);
    }
    60% {
        transform: rotate(10deg) scale(1.05) translateY(-2px);
    }
    70% {
        transform: rotate(-5deg) scale(1.02) translateY(-2px);
    }
    80%, 100% {
        transform: rotate(0) scale(1) translateY(-2px);
    }
}

/* Party Popper Animation - For registration page */
.party-popper {
    animation: celebrate 4s ease-in-out infinite;
    transform-origin: center;
    /* Ensure no clipping */
    transform: translateY(-2px);
}

@keyframes celebrate {
    0%, 100% {
        transform: rotate(0) scale(1) translateY(-2px);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
    }
    10% {
        transform: rotate(-5deg) scale(1.05) translateY(-2px);
        filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.2)) brightness(1.05);
    }
    20% {
        transform: rotate(5deg) scale(1.1) translateY(-2px);
        filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.25)) brightness(1.1);
    }
    30% {
        transform: rotate(-5deg) scale(1.15) translateY(-2px);
        filter: drop-shadow(0 5px 8px rgba(0, 0, 0, 0.3)) brightness(1.15);
    }
    40% {
        transform: rotate(5deg) scale(1.1) translateY(-2px);
        filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.25)) brightness(1.1);
    }
    50% {
        transform: rotate(-3deg) scale(1.05) translateY(-2px);
        filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.2)) brightness(1.05);
    }
    60% {
        transform: rotate(2deg) scale(1.02) translateY(-2px);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) brightness(1.02);
    }
    70%, 100% {
        transform: rotate(0) scale(1) translateY(-2px);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) brightness(1);
    }
}

/* Hover effect for party popper */
.auth-title:hover .party-popper {
    animation: party-burst 1.5s ease-out;
    animation-iteration-count: 1;
}

@keyframes party-burst {
    0% {
        transform: rotate(0) scale(1) translateY(-2px);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) brightness(1);
    }
    10% {
        transform: rotate(-10deg) scale(1.2) translateY(-2px);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3)) brightness(1.2);
    }
    20% {
        transform: rotate(10deg) scale(1.4) translateY(-2px);
        filter: drop-shadow(0 6px 10px rgba(0, 0, 0, 0.4)) brightness(1.3);
    }
    30% {
        transform: rotate(-5deg) scale(1.3) translateY(-2px);
        filter: drop-shadow(0 5px 9px rgba(0, 0, 0, 0.35)) brightness(1.25);
    }
    40% {
        transform: rotate(5deg) scale(1.2) translateY(-2px);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3)) brightness(1.2);
    }
    50% {
        transform: rotate(-3deg) scale(1.1) translateY(-2px);
        filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.25)) brightness(1.15);
    }
    60% {
        transform: rotate(2deg) scale(1.05) translateY(-2px);
        filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2)) brightness(1.1);
    }
    70%, 100% {
        transform: rotate(0) scale(1) translateY(-2px);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) brightness(1);
    }
}

/* Confetti effect for registration page */
.auth-title {
    position: relative;
    overflow: visible;
}

.auth-title:hover::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 50%;
    width: 200px;
    height: 100px;
    background-image:
        radial-gradient(circle, var(--primary) 2px, transparent 2px),
        radial-gradient(circle, var(--accent) 1px, transparent 1px),
        radial-gradient(circle, var(--primary-light) 1.5px, transparent 1.5px),
        radial-gradient(circle, var(--secondary-light) 1px, transparent 1px),
        radial-gradient(circle, var(--primary-lighter) 2px, transparent 2px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px, 15px 5px, 5px 15px, 10px 5px;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: confetti-fall 1.5s ease-out forwards;
    pointer-events: none;
    z-index: 3;
}

@keyframes confetti-fall {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
    10% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -30%) scale(1);
    }
    90% {
        opacity: 0.3;
        transform: translate(-50%, 30%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, 50%) scale(1.5);
    }
}

/* Avatar Upload */
.avatar-upload-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xs);
    background-color: rgba(243, 115, 33, 0.02);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px dashed rgba(243, 115, 33, 0.2);
    transition: var(--transition-normal);
}

.avatar-upload-container:hover {
    background-color: rgba(243, 115, 33, 0.05);
    border-color: rgba(243, 115, 33, 0.3);
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid var(--primary-lighter);
    box-shadow: 0 2px 8px rgba(243, 115, 33, 0.15);
    transition: var(--transition-normal);
}

.avatar-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.25);
}

.avatar-preview.upload-success {
    animation: avatar-pulse 1s ease-in-out;
    border-color: var(--accent);
}

@keyframes avatar-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(243, 115, 33, 0.15);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(243, 115, 33, 0.15);
    }
}

.avatar-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--primary);
    font-size: 32px;
    background-color: var(--primary-ultra-light);
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-input-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 0; /* Cho phép co lại khi cần thiết */
    overflow: hidden; /* Ngăn nội dung tràn ra ngoài */
}

.avatar-upload-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    background-color: var(--primary-ultra-light);
    color: var(--primary);
    border: 1px solid var(--primary-lighter);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    max-width: fit-content;
}

.avatar-upload-button:hover {
    background-color: var(--primary-lightest);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(243, 115, 33, 0.15);
}

.avatar-upload-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.avatar-input {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.avatar-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%; /* Đảm bảo chiếm toàn bộ chiều rộng có sẵn */
    min-width: 0; /* Cho phép co lại khi cần thiết */
}

.avatar-filename {
    font-size: 13px;
    color: var(--secondary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px; /* Giới hạn độ rộng tối đa */
}

.avatar-hint {
    font-size: 12px;
    color: var(--dark-gray);
}

.hidden {
    display: none;
}

/* Form */
.auth-form {
    width: 100%;
    position: relative;
    margin-top: var(--spacing-md);
}

.auth-form::before {
    content: "";
    position: absolute;
    top: -20px;
    right: -20px;
    width: 40px;
    height: 40px;
    background-color: rgba(243, 115, 33, 0.1);
    border-radius: var(--radius-full);
    z-index: -1;
}

.auth-form::after {
    content: "";
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 60px;
    height: 60px;
    background-color: rgba(42, 59, 71, 0.05);
    border-radius: var(--radius-full);
    z-index: -1;
}

.form-group {
    margin-bottom: var(--spacing-md);
    position: relative;
    transition: var(--transition-normal);
}

/* Validation message */
.validation-message {
    font-size: 12px;
    margin-top: 2px;
    padding: 3px 8px;
    border-radius: var(--radius-sm);
    transition: all 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

.validation-message.show {
    opacity: 1;
    max-height: none; /* Cho phép hiển thị không giới hạn chiều cao */
    margin-top: 3px;
    padding-bottom: 3px; /* Thêm padding dưới để tạo khoảng cách */
}

.validation-message.error {
    color: #D32F2F;
    background-color: rgba(244, 67, 54, 0.05);
    border-left: 2px solid #F44336;
}

.validation-message.success {
    color: #388E3C;
    background-color: rgba(76, 175, 80, 0.05);
    border-left: 2px solid #4CAF50;
}

.input-group.checking::after {
    content: "";
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(243, 115, 33, 0.3);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

.form-group:hover .input-icon {
    color: var(--primary);
}

.form-group:hover .form-input {
    border-color: var(--primary-light);
    background-color: rgba(255, 255, 255, 1);
}

.form-label {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
    color: var(--secondary);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-normal);
    gap: var(--spacing-xs);
    letter-spacing: 0.3px;
    opacity: 0.85;
}

.form-label::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 3px;
    background-color: var(--primary);
    border-radius: var(--radius-full);
    margin-left: 2px;
    opacity: 0.7;
}

.form-group:hover .form-label {
    color: var(--primary);
    opacity: 1;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.3s ease;
    border-radius: var(--radius-md);
}

.input-group:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.input-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--dark-gray);
    transition: var(--transition-normal);
    font-size: 16px;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 50%;
    transform: translateY(-50%);
}

.form-input {
    width: 100%;
    height: 50px;
    padding: 0 var(--spacing-md) 0 calc(var(--spacing-md) * 2.5);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    position: relative;
    z-index: 1;
    font-family: 'Be Vietnam Pro', sans-serif;
    line-height: 50px;
    color: var(--secondary);
    font-weight: 500;
}

.form-input::placeholder {
    color: var(--dark-gray);
    opacity: 0.7;
    font-weight: 400;
    font-size: 13px;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.12);
    background-color: var(--white);
}

.form-input:focus + .input-icon {
    color: var(--primary);
    transform: translateY(-50%);
}

.form-group:not(:last-child)::after {
    content: "";
    position: absolute;
    bottom: -16px;
    left: 15%;
    width: 70%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(243, 115, 33, 0.1), transparent);
    opacity: 0.7;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--medium-gray);
    color: var(--secondary);
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
    padding: var(--spacing-xs);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    opacity: 0.8;
    top: 50%;
    transform: translateY(-50%);
}

.password-toggle:hover {
    color: var(--primary);
    background-color: var(--white);
    border-color: var(--primary-light);
    box-shadow: 0 2px 5px rgba(243, 115, 33, 0.2);
    opacity: 1;
}

.password-toggle:active {
    transform: translateY(-50%) scale(0.92);
}

/* Tùy chọn đăng nhập */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: 13px;
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: rgba(245, 245, 245, 0.3);
    border-radius: var(--radius-md);
    border: 1px solid rgba(243, 115, 33, 0.05);
    transition: all 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.form-options:hover {
    background-color: rgba(245, 245, 245, 0.5);
    border-color: rgba(243, 115, 33, 0.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.remember-me {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.remember-me input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1.5px solid var(--medium-gray);
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: rgba(255, 255, 255, 0.8);
}

.remember-me input[type="checkbox"]:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

.remember-me input[type="checkbox"]:checked::before {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.remember-me input[type="checkbox"]:hover {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.08);
}

.remember-me label {
    cursor: pointer;
    user-select: none;
    color: var(--secondary);
    font-weight: 500;
    font-size: 12px;
    letter-spacing: 0.2px;
}

.forgot-password {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
    font-weight: 500;
    position: relative;
    padding-bottom: 1px;
    font-size: 12px;
    letter-spacing: 0.2px;
    opacity: 0.9;
}

.forgot-password::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--primary);
    transition: width 0.25s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.forgot-password:hover {
    opacity: 1;
}

.forgot-password:hover::after {
    width: 100%;
}

/* Nút đăng nhập/đăng ký */
.auth-button {
    width: 100%;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 10px rgba(243, 115, 33, 0.25);
    overflow: hidden;
    z-index: 1;
    height: 48px;
    text-transform: uppercase;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.auth-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    z-index: -1;
    transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.auth-button::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: -1;
}

.auth-button:hover {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(243, 115, 33, 0.3);
}

.auth-button:hover::before {
    left: 100%;
}

.auth-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 8px rgba(243, 115, 33, 0.2);
}

/* Đường phân cách */
.auth-divider {
    display: flex;
    align-items: center;
    margin: var(--spacing-lg) 0;
    position: relative;
    gap: var(--spacing-md);
}

.divider-line {
    flex: 1;
    height: 1px;
    background: linear-gradient(to right, rgba(243, 115, 33, 0.1), var(--medium-gray), rgba(243, 115, 33, 0.1));
    position: relative;
    overflow: hidden;
}

.divider-line::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.auth-divider span {
    padding: 5px 15px;
    background-color: var(--white);
    position: relative;
    z-index: 1;
    font-weight: 500;
    color: var(--secondary);
    text-transform: lowercase;
    letter-spacing: 1px;
    border-radius: var(--radius-full);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(243, 115, 33, 0.1);
    font-size: 13px;
}

.auth-divider span:hover {
    color: var(--primary);
    box-shadow: 0 4px 10px rgba(243, 115, 33, 0.1);
    border-color: rgba(243, 115, 33, 0.2);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Đăng nhập bằng mạng xã hội */
.social-login {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(243, 115, 33, 0.05);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.social-login:hover {
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.03);
    border-color: rgba(243, 115, 33, 0.08);
}

.social-login::after {
    content: "";
    position: absolute;
    top: -10px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-md);
    background-color: var(--white);
    color: var(--secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
    flex: 1;
    height: 44px;
    position: relative;
    overflow: hidden;
}

.social-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.social-button:hover::before {
    transform: translateX(100%);
}

.social-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.social-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.google-button {
    border-color: rgba(66, 133, 244, 0.2);
    color: #4285F4;
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
}

.google-button:hover {
    background: linear-gradient(to bottom, #ffffff, #f8f8f8);
    border-color: rgba(66, 133, 244, 0.4);
    color: #4285F4;
}

.facebook-button {
    border-color: rgba(59, 89, 152, 0.2);
    color: #3b5998;
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
}

.facebook-button:hover {
    background: linear-gradient(to bottom, #ffffff, #f8f8f8);
    border-color: rgba(59, 89, 152, 0.4);
    color: #3b5998;
}

.social-icon {
    width: 20px;
    height: 20px;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.05));
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    opacity: 0.9;
}

.social-button:hover .social-icon {
    transform: scale(1.1);
    opacity: 1;
}

/* Link đăng ký/đăng nhập */
.auth-link {
    text-align: center;
    font-size: 14px;
    color: var(--dark-gray);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    position: relative;
}

.auth-link::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--primary-lightest), transparent);
    border-radius: var(--radius-full);
}

.auth-link p {
    position: relative;
    display: inline-block;
}

.auth-link a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    position: relative;
    padding: 0 2px;
}

.auth-link a::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.auth-link a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Phần hình ảnh */
.auth-image-section {
    flex: 1;
    display: none;
    position: relative;
    overflow: hidden;
}

.auth-image-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(42, 59, 71, 0.5) 0%, rgba(243, 115, 33, 0.4) 100%);
    z-index: 1;
    pointer-events: none;
}

.auth-image-section::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    z-index: 1;
    pointer-events: none;
}

.auth-image-section .image-caption {
    position: absolute;
    bottom: 40px;
    left: 40px;
    color: white;
    z-index: 2;
    max-width: 80%;
    background-color: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    backdrop-filter: blur(5px);
    border-left: 3px solid var(--primary);
}

.auth-image-section .image-caption h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    padding-bottom: 10px;
}

.auth-image-section .image-caption h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary);
}

.auth-image-section .image-caption p {
    font-size: 14px;
    opacity: 0.95;
    line-height: 1.6;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.auth-image-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.auth-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    filter: brightness(0.9);
}

/* Flash messages */
.flash-message {
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    position: relative;
    border-left: 4px solid transparent;
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.flash-message.success {
    background-color: rgba(76, 175, 80, 0.05);
    color: #388E3C;
    border-left-color: #4CAF50;
}

.flash-message.error {
    background-color: rgba(244, 67, 54, 0.05);
    color: #D32F2F;
    border-left-color: #F44336;
}

.flash-message.info {
    background-color: rgba(33, 150, 243, 0.05);
    color: #1976D2;
    border-left-color: #2196F3;
}

.flash-message.warning {
    background-color: rgba(255, 152, 0, 0.05);
    color: #F57C00;
    border-left-color: #FF9800;
}

/* Responsive */
@media (min-width: 992px) {
    .auth-image-section {
        display: block;
    }

    .auth-form-section {
        max-width: 500px;
    }
}

@media (max-width: 991px) {
    .auth-container {
        flex-direction: column;
        margin: var(--spacing-md);
        max-width: 600px;
    }

    .auth-form-section {
        padding: var(--spacing-xl);
        order: 2;
    }

    .auth-image-section {
        display: block;
        height: 200px;
        order: 1;
    }
}

@media (max-width: 767px) {
    .auth-container {
        margin: var(--spacing-sm);
        border-radius: var(--radius-lg);
        min-height: auto;
    }

    .auth-form-section {
        padding: var(--spacing-lg);
        max-width: 100%;
    }

    .auth-title {
        font-size: 28px;
    }

    .auth-welcome {
        padding: var(--spacing-sm);
    }

    .auth-subtitle {
        font-size: 13px;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    body {
        align-items: center; /* Căn giữa theo chiều dọc trên mobile */
        padding: var(--spacing-md) 0; /* Giữ một chút padding trên mobile */
    }

    .auth-container {
        margin: 0;
        border-radius: 0;
        min-height: auto; /* Thay đổi từ 100vh thành auto để cho phép cuộn */
        box-shadow: none;
    }

    .auth-image-section {
        height: 150px;
    }

    .auth-form-section {
        padding: var(--spacing-lg) var(--spacing-md);
        flex: 1;
    }

    .form-options {
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .social-login {
        flex-direction: column;
    }

    .auth-logo-img {
        height: 35px;
    }

    .auth-title {
        font-size: 24px;
    }

    .welcome-icon {
        width: 28px;
        height: 28px;
    }

    .form-group {
        margin-bottom: var(--spacing-sm);
    }

    .form-input {
        height: 45px;
        font-size: 14px;
    }

    .input-icon {
        font-size: 15px;
    }

    .auth-button {
        height: 45px;
        font-size: 14px;
    }

    .auth-divider {
        margin: var(--spacing-md) 0;
    }

    .auth-divider span {
        font-size: 12px;
        padding: 4px 12px;
    }
}

/* Multi-step Form Styles */
.form-step {
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    padding-top: 5px;
    padding-bottom: 5px;
}

.form-step.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Step Indicator */
.step-indicator {
    margin-bottom: var(--spacing-md);
    position: relative;
}

.step-dots {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    margin-bottom: var(--spacing-md);
}

.step-dot {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.dot-inner {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--white);
    border: 2px solid var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-xs);
    position: relative;
    transition: all 0.3s ease;
    z-index: 2;
}

.step-dot.active .dot-inner {
    background-color: var(--primary);
    border-color: var(--primary);
}

.step-dot.active .dot-inner::after {
    content: "";
    width: 10px;
    height: 10px;
    background-color: var(--white);
    border-radius: 50%;
    position: absolute;
}

.step-dot.completed .dot-inner {
    background-color: var(--primary);
    border-color: var(--primary);
}

.step-dot.completed .dot-inner::after {
    content: "✓";
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.step-label {
    font-size: 12px;
    color: var(--dark-gray);
    font-weight: 500;
    transition: all 0.3s ease;
}

.step-dot.active .step-label {
    color: var(--primary);
    font-weight: 600;
}

.step-dot.completed .step-label {
    color: var(--primary);
}

.step-progress {
    position: absolute;
    top: 12px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--medium-gray);
    z-index: 1;
}

.step-progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, var(--primary), var(--primary-light));
    transition: width 0.4s ease;
}

/* Navigation buttons */
.form-nav {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    gap: var(--spacing-md);
}

.next-step-button,
.prev-step-button,
.submit-button {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border: none;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.next-step-button {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    padding-right: var(--spacing-lg);
}

.next-step-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.prev-step-button {
    background-color: transparent;
    color: var(--secondary);
    border: 1px solid var(--medium-gray);
    padding-left: var(--spacing-lg);
}

.prev-step-button:hover {
    background-color: var(--light-gray);
    border-color: var(--dark-gray);
}

.submit-button {
    background: linear-gradient(135deg, var(--accent), var(--accent-dark));
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* Password strength meter */
.password-strength-meter {
    margin-top: var(--spacing-sm);
    height: 4px;
    background-color: var(--light-gray);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.strength-bar {
    height: 100%;
    width: 0;
    border-radius: var(--radius-full);
    transition: all 0.3s ease;
}

.strength-text {
    display: block;
    font-size: 11px;
    color: var(--dark-gray);
    margin-top: 4px;
    text-align: right;
}

/* Password match indicator */
.password-match-indicator {
    margin-top: var(--spacing-sm);
    color: var(--accent);
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: all 0.3s ease;
}

.password-match-indicator.visible {
    opacity: 1;
}

.password-match-indicator.not-matching {
    color: #F44336;
}

/* Hide elements */
.hidden {
    display: none !important;
}

/* Adjustments for multi-step form */
.multi-step-divider {
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.multi-step-social {
    margin-top: var(--spacing-md);
}

/* Input error styling */
.input-error {
    border-color: #F44336 !important;
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

.input-error-message {
    color: #F44336;
    font-size: 12px;
    margin-top: 3px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.input-error-message::before {
    content: "⚠️";
    font-size: 10px;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-3px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(3px, 0, 0);
    }
}

/* Registration Success Message */
.custom-alert.custom-alert-register-success {
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.1) 100%);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-left: 4px solid #4CAF50;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.custom-alert.custom-alert-register-success .alert-icon {
    background: linear-gradient(135deg, #4CAF50, #81C784);
    color: white;
    width: 40px;
    height: 40px;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.custom-alert.custom-alert-register-success .alert-icon i {
    font-size: 20px;
}

.custom-alert.custom-alert-register-success .alert-content {
    font-size: 15px;
    color: #2E7D32;
}

.register-success-btn {
    margin-top: var(--spacing-sm);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

/* Hệ thống thông báo mới */
.alert-container {
    margin-bottom: var(--spacing-md);
    width: 100%;
}

.custom-alert {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-left: 4px solid transparent;
    overflow: hidden;
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.custom-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    transform: translateX(-100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.custom-alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    border-left-color: #4CAF50;
}

.custom-alert-danger {
    background-color: rgba(244, 67, 54, 0.1);
    border-left-color: #F44336;
}

.custom-alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    border-left-color: #FF9800;
}

.custom-alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    border-left-color: #2196F3;
}

.alert-icon {
    margin-right: var(--spacing-md);
    background-color: rgba(255, 255, 255, 0.9);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.custom-alert-success .alert-icon {
    color: #4CAF50;
}

.custom-alert-danger .alert-icon {
    color: #F44336;
}

.custom-alert-warning .alert-icon {
    color: #FF9800;
}

.custom-alert-info .alert-icon {
    color: #2196F3;
}

.alert-icon i {
    font-size: 16px;
}

.alert-content {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

.custom-alert-success .alert-content {
    color: #2E7D32;
}

.custom-alert-danger .alert-content {
    color: #C62828;
}

.custom-alert-warning .alert-content {
    color: #EF6C00;
}

.custom-alert-info .alert-content {
    color: #1565C0;
}

.alert-close {
    background: transparent;
    border: none;
    color: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-left: var(--spacing-sm);
    padding: 0;
    font-size: 12px;
    opacity: 0.7;
}

.alert-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: rgba(0, 0, 0, 0.8);
    opacity: 1;
}

/* Hiệu ứng Fade in/out */
.fade-in {
    animation-name: fadeInDown;
}

.fade-out {
    animation-name: fadeOutUp;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Auto-fill highlight */
.highlight-autofill {
    animation: highlight-pulse 2s ease;
}

@keyframes highlight-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.3);
    }
}

.autofill-message {
    background-color: rgba(33, 150, 243, 0.1);
    color: #1976D2;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-left: 3px solid #2196F3;
    animation: fadeIn 0.5s ease forwards;
    transition: opacity 0.5s ease;
}

.autofill-message.fade-out {
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.autofill-message i {
    color: #1976D2;
    font-size: 16px;
}

/* Modal Quên Mật Khẩu */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    width: 100%;
    max-width: 500px;
    margin: 0 20px;
    position: relative;
    transform: translateY(30px);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.modal-overlay.active .modal-container {
    transform: translateY(0);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.05);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--dark-gray);
    transition: all 0.3s ease;
    z-index: 10;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--black);
    transform: rotate(90deg);
}

.modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-md) var(--spacing-xl);
    text-align: center;
    position: relative;
    background: linear-gradient(to bottom, rgba(243, 115, 33, 0.03), rgba(255, 255, 255, 0));
}

.modal-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 30px;
    box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
    position: relative;
}

.modal-icon::before {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
    background: linear-gradient(135deg, var(--primary-light), transparent);
    border-radius: 50%;
    opacity: 0.5;
    animation: pulse 2s infinite;
}

.modal-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--secondary);
    margin-bottom: var(--spacing-xs);
}

.modal-subtitle {
    font-size: 14px;
    color: var(--dark-gray);
    line-height: 1.5;
    max-width: 80%;
    margin: 0 auto;
}

.modal-body {
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-xl);
}

/* Recovery Steps */
.recovery-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.recovery-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 5;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: 2px solid var(--medium-gray);
    color: var(--dark-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: var(--spacing-xs);
    transition: all 0.3s ease;
    position: relative;
}

.recovery-step.active .step-number {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--white);
    box-shadow: 0 3px 8px rgba(243, 115, 33, 0.3);
    position: relative;
    font-weight: bold;
    transform: scale(1.1);
    transition: all 0.3s ease;
    animation: pulse-active 2s infinite;
}

@keyframes pulse-active {
    0%, 100% {
        box-shadow: 0 3px 8px rgba(243, 115, 33, 0.3);
    }
    50% {
        box-shadow: 0 3px 12px rgba(243, 115, 33, 0.5);
    }
}

.recovery-step.completed .step-number {
    background-color: var(--primary);
    border-color: var(--primary);
    color: transparent; /* Ẩn số thứ tự */
    position: relative;
    overflow: hidden;
}

.recovery-step.completed .step-number::after {
    content: '✓';
    position: absolute;
    font-size: 14px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--white); /* Đảm bảo dấu tích có màu trắng */
    font-weight: bold;
    z-index: 2;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    animation: checkmark-appear 0.3s ease-out;
}

@keyframes checkmark-appear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    70% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}

.step-label {
    font-size: 12px;
    color: var(--dark-gray);
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
}

.recovery-step.active .step-label {
    color: var(--primary);
    font-weight: 600;
}

.recovery-step.completed .step-label {
    color: var(--primary);
}

.recovery-progress {
    flex: 1;
    height: 2px;
    background-color: var(--medium-gray);
    position: relative;
    z-index: 1;
    margin: 0 5px;
}

.recovery-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background-color: var(--primary);
    transition: width 0.5s ease;
    box-shadow: 0 0 5px rgba(243, 115, 33, 0.3);
    border-radius: 2px;
}

.recovery-steps[data-current-step="2"] .recovery-progress:nth-of-type(1)::before,
.recovery-steps[data-current-step="3"] .recovery-progress:nth-of-type(1)::before {
    width: 100%;
}

.recovery-steps[data-current-step="3"] .recovery-progress:nth-of-type(2)::before {
    width: 100%;
}

/* Thêm hiệu ứng khi chuyển bước */
.recovery-progress::before {
    animation: progress-pulse 2s infinite;
}

@keyframes progress-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Hiệu ứng highlight cho step-number */
.step-number.highlight {
    animation: highlight-step 1s ease;
}

@keyframes highlight-step {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(243, 115, 33, 0);
    }
    50% {
        transform: scale(1.15);
        box-shadow: 0 0 0 4px rgba(243, 115, 33, 0.3);
    }
}

/* Hiệu ứng khi hoàn thành một bước */
.recovery-step.completing .step-number {
    animation: completing-step 0.3s ease forwards;
}

@keyframes completing-step {
    0% {
        color: var(--white);
    }
    100% {
        color: transparent;
    }
}

/* Form Styles for Password Recovery */
.forgot-password-form {
    position: relative;
}

.forgot-password-step {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.forgot-password-step.preparing {
    display: block;
    opacity: 0;
}

.forgot-password-step.active {
    display: block;
    opacity: 1;
    animation: fade-in-step 0.4s ease;
}

@keyframes fade-in-step {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.text-hint {
    font-size: 12px;
    color: var(--dark-gray);
    margin-top: var(--spacing-xs);
    line-height: 1.5;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-lg);
}

.primary-button, .secondary-button, .link-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    font-family: 'Be Vietnam Pro', sans-serif;
}

.primary-button {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    box-shadow: 0 3px 10px rgba(243, 115, 33, 0.2);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 115, 33, 0.3);
}

.secondary-button {
    background-color: var(--light-gray);
    color: var(--secondary);
    border: 1px solid var(--medium-gray);
}

.secondary-button:hover {
    background-color: var(--medium-gray);
}

.link-button {
    background: none;
    color: var(--primary);
    padding: var(--spacing-xs);
    text-decoration: none;
}

.link-button:hover {
    text-decoration: underline;
    color: var(--primary-dark);
}

/* Email Confirmation Styles */
.email-sent-confirmation {
    text-align: center;
    padding: var(--spacing-lg) 0;
}

.confirmation-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background-color: rgba(76, 175, 80, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent);
    font-size: 36px;
    position: relative;
}

.confirmation-icon::after {
    content: '';
    position: absolute;
    top: -8px;
    right: -8px;
    bottom: -8px;
    left: -8px;
    border: 2px dashed var(--accent-light);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.email-sent-confirmation h4 {
    font-size: 20px;
    font-weight: 700;
    color: var(--secondary);
    margin-bottom: var(--spacing-sm);
}

.email-sent-confirmation p {
    font-size: 14px;
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.confirmation-actions {
    margin-top: var(--spacing-md);
}

.resend-button {
    margin: var(--spacing-xs) 0;
    position: relative;
}

.resend-timer {
    font-size: 12px;
    color: var(--dark-gray);
    margin-top: var(--spacing-xs);
}

/* Animation */
@keyframes pulse {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.1);
    }
    100% {
        opacity: 0.5;
        transform: scale(1);
    }
}

/* Responsive Styles */
@media (max-width: 576px) {
    .modal-container {
        max-width: 100%;
        margin: 0 15px;
    }

    .modal-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
    }

    .modal-body {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
    }

    .form-actions {
        flex-direction: column-reverse;
        gap: var(--spacing-sm);
    }

    .primary-button, .secondary-button {
        width: 100%;
        justify-content: center;
    }

    .step-label {
        font-size: 10px;
    }
}

/* Admin Tab Bar Styling */
.admin-tab-bar {
    display: flex;
    background-color: var(--bg-white); /* Hoặc var(--bg-light) tùy theo nền chung của admin */
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    padding: 0 var(--space-4); /* Thêm padding ngang nếu cần */
}

.admin-tab-item {
    display: inline-flex; /* Để icon và text căn chỉnh tốt */
    align-items: center;
    gap: var(--space-2); /* Khoảng cách giữa icon và text */
    padding: var(--space-4) var(--space-5); /* Padding cho mỗi tab item */
    font-family: var(--font-primary);
    font-size: var(--text-base); /* Hoặc var(--text-sm) nếu muốn nhỏ hơn */
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    text-decoration: none;
    border-bottom: 3px solid transparent; /* Tạo không gian cho border active */
    margin-bottom: -1px; /* Để border bottom của tab active đè lên border của tab-bar */
    transition: all var(--transition-smooth); /* Sử dụng transition từ design system */
    cursor: pointer;
}

.admin-tab-item i { /* Style cho icon nếu cần */
    font-size: var(--text-lg); /* Kích thước icon */
}

.admin-tab-item:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light); /* Hiệu ứng hover nhẹ nhàng */
    border-bottom-color: var(--primary-light); /* Gợi ý active state khi hover */
}

.admin-tab-item.active {
    color: var(--primary);
    font-weight: var(--font-semibold);
    border-bottom-color: var(--primary);
    background-color: transparent; /* Hoặc var(--primary-ultra-light) nếu muốn nền active khác */
}

/* Responsive adjustments nếu cần */
@media (max-width: var(--breakpoint-md)) {
    .admin-tab-item {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }
    .admin-tab-item i {
        font-size: var(--text-base);
    }
    .admin-tab-item span {
        /* Có thể ẩn text trên mobile và chỉ hiện icon nếu không gian hẹp */
        /* display: none; */ 
    }
}

