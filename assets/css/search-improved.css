/* CSS cải tiến cho tìm kiếm */

/* Container cho kết quả gợi ý */
.search-suggestions {
    position: absolute;
    left: 0;
    right: 0;
    top: calc(100% + 4px); /* D<PERSON>ch xuống một chút như trang tìm kiếm */
    z-index: var(--z-search-suggestions);
    border: 1px solid var(--light-gray);
    border-radius: 0.75rem; /* <PERSON> g<PERSON> cả 4 viền như trang tìm kiếm */
    background-color: var(--white);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); /* Shadow giống trang tìm kiếm */
    max-height: 80vh;
    overflow-y: auto;
    padding: 8px;
    transform: translateY(8px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s cubic-bezier(0.3, 0, 0.3, 1);
    pointer-events: auto;
    scrollbar-width: thin;
    scrollbar-color: #F37321 rgba(243, 115, 33, 0.05);
}

.search-suggestions::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: rgba(243, 115, 33, 0.05);
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #FF9D5C, #F37321);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #F37321, #D35400);
}

.search-suggestions:not(.hidden) {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* CSS cho item trong kết quả tìm kiếm */
.search-item {
    display: grid;
    grid-template-columns: 60px 1fr;
    grid-template-areas:
        "image name"
        "image meta"
        "image price";
    column-gap: 10px;
    row-gap: 4px;
    padding: 10px;
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    transition: all 0.2s cubic-bezier(0.3, 0, 0.3, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    text-decoration: none;
    color: var(--dark);
    position: relative;
    align-items: start;
    background-color: #fff;
    min-height: 80px;
}

.search-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.search-item:hover {
    background-color: var(--primary-ultra-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.search-item:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-item-image {
    grid-area: image;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background-color: var(--light-gray);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    align-self: center;
    justify-self: center;
}

.search-item:hover .search-item-image {
    transform: scale(1.05);
}

.search-item-info {
    grid-column: 2 / -1;
    display: contents;
}

.search-item-name {
    grid-area: name;
    font-weight: 500;
    font-size: var(--text-sm);
    color: var(--dark);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.2s ease;
    width: 100%;
    line-height: 1.3;
    align-self: start;
    padding-top: 2px;
}

.search-item:hover .search-item-name {
    color: var(--primary);
}

.search-item-meta {
    grid-area: meta;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
    font-size: 11px;
    color: var(--medium-gray);
    width: 100%;
    align-self: center;
    margin: 3px 0;
}

.search-item-rating {
    display: flex;
    align-items: center;
    gap: 3px;
    position: relative;
}

.search-item-rating i {
    color: var(--accent-gold);
    font-size: 10px;
}

.search-item-sales {
    display: flex;
    align-items: center;
    gap: 3px;
    position: relative;
    margin-left: 8px;
}

.search-item-sales::before {
    content: '•';
    position: absolute;
    left: -6px;
    color: var(--light-gray);
    font-size: 10px;
}

.search-item-sales i {
    font-size: 9px;
    color: var(--medium-gray);
}

.search-item-category {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 11px;
    color: var(--medium-gray);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    margin-left: 8px;
}

.search-item-category::before {
    content: '•';
    position: absolute;
    left: -6px;
    color: var(--light-gray);
    font-size: 10px;
}

.search-item-category i {
    font-size: 9px;
    color: var(--primary);
    opacity: 0.8;
}

.search-item-category span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100px;
}

.search-item-price {
    grid-area: price;
    font-weight: 600;
    color: var(--primary);
    white-space: nowrap;
    font-size: var(--text-sm);
    display: inline-flex;
    align-items: center;
    justify-self: start;
    transition: all 0.2s ease;
    align-self: start;
    margin-top: 3px;
    gap: 4px;
}

.search-item-price i {
    font-size: 11px;
    opacity: 0.8;
}

.search-item:hover .search-item-price {
    color: var(--primary-dark);
}

/* Định dạng cho giá "Liên hệ báo giá" */
.contact-price {
    color: #2563eb; /* Màu xanh dương */
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.contact-price::before {
    content: "\f095"; /* Biểu tượng điện thoại từ Font Awesome */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 4px;
    font-size: 11px;
}

/* Nút xem tất cả kết quả */
.search-view-all {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-top: var(--spacing-sm);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--primary);
    font-weight: 500;
    font-size: var(--text-sm);
    text-decoration: none;
    transition: all 0.2s ease;
    background-color: transparent;
    border-radius: var(--radius-md);
    position: relative;
}

.search-view-all:hover {
    background-color: var(--primary-ultra-light);
    color: var(--primary-dark);
}

.search-view-all:active {
    background-color: rgba(var(--primary-rgb), 0.15);
}

.search-view-all i {
    margin-right: var(--spacing-xs);
    font-size: var(--text-sm);
    opacity: 0.8;
}

/* Trạng thái không có kết quả */
.search-no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--medium-gray);
    background-color: var(--ultra-light-gray);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
}

.search-suggestion-text {
    font-size: var(--text-xs);
    margin-top: var(--spacing-xs);
    color: var(--medium-gray);
}

/* Gợi ý thay thế */
.search-alternative-suggestions {
    margin-top: var(--spacing-lg);
    width: 100%;
}

.search-alternative-title {
    font-size: var(--text-xs);
    color: var(--medium-gray);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.search-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
}

.search-tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--ultra-light-gray);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    color: var(--dark-gray);
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.3, 0, 0.3, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.search-tag i {
    margin-right: 6px;
    font-size: 10px;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.search-tag:hover {
    background-color: var(--primary-ultra-light);
    color: var(--primary);
    border-color: rgba(var(--primary-rgb), 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.1);
}

.search-tag:hover i {
    opacity: 1;
    transform: scale(1.2);
}

.search-tag:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.05);
}

/* Gợi ý chính tả */
.search-spelling-suggestion {
    margin-top: var(--spacing-lg);
    font-size: var(--text-xs);
    color: var(--medium-gray);
    background-color: rgba(var(--primary-rgb), 0.03);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px dashed rgba(var(--primary-rgb), 0.1);
}

.search-spelling-suggestion i {
    margin-right: 6px;
    color: var(--primary);
}

.search-spelling-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.3, 0, 0.3, 1);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    background-color: rgba(var(--primary-rgb), 0.08);
    display: inline-block;
    margin-left: 4px;
}

.search-spelling-link:hover {
    background-color: rgba(var(--primary-rgb), 0.15);
    box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.1);
    transform: translateY(-1px);
}

.search-spelling-link:active {
    transform: translateY(0);
}

/* Trạng thái lỗi */
.search-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: #e53e3e;
    background-color: rgba(229, 62, 62, 0.05);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
    border: 1px dashed rgba(229, 62, 62, 0.3);
}

.search-error i {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
    color: #e53e3e;
    background-color: rgba(229, 62, 62, 0.1);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Trạng thái từ khóa quá ngắn */
.search-min-length {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--medium-gray);
    background-color: var(--ultra-light-gray);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
    border: 1px dashed var(--light-gray);
}

.search-min-length i {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--medium-gray);
    background-color: rgba(0, 0, 0, 0.05);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.search-input-indicator {
    display: flex;
    align-items: center;
    margin-top: var(--spacing-md);
    background-color: rgba(255, 255, 255, 0.5);
    padding: 6px 12px;
    border-radius: var(--radius-full);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.search-input-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--light-gray);
    margin: 0 4px;
    transition: all 0.3s cubic-bezier(0.3, 0, 0.3, 1);
}

.search-input-dot.active {
    background-color: var(--primary);
    transform: scale(1.2);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.search-input-line {
    width: 20px;
    height: 2px;
    background-color: var(--light-gray);
    margin-left: 4px;
    border-radius: var(--radius-full);
}

/* Trạng thái loading */
.search-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--medium-gray);
    position: relative;
    background-color: rgba(var(--primary-rgb), 0.02);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
}

.search-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(var(--primary-rgb), 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 0.8s cubic-bezier(0.3, 0, 0.3, 1) infinite;
    margin-bottom: var(--spacing-md);
    box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.1);
}

.search-loading-pulse {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(var(--primary-rgb), 0.05);
    animation: pulse 1.5s cubic-bezier(0.3, 0, 0.3, 1) infinite;
    top: calc(var(--spacing-xl) + 20px);
    left: 50%;
    transform: translateX(-50%);
    z-index: -1;
    box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.1);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        transform: translateX(-50%) scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.2;
    }
    100% {
        transform: translateX(-50%) scale(0.8);
        opacity: 0.8;
    }
}

/* Skeleton loading */
.skeleton-loading {
    background: linear-gradient(90deg,
        rgba(var(--primary-rgb), 0.03) 0%,
        rgba(var(--primary-rgb), 0.06) 20%,
        rgba(var(--primary-rgb), 0.03) 40%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.skeleton-wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.skeleton-wave::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: skeleton-wave 1.6s linear 0.5s infinite;
    transform: translateX(-100%);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes skeleton-wave {
    0% {
        transform: translateX(-100%);
    }
    50%, 100% {
        transform: translateX(100%);
    }
}

/* Hiệu ứng fade-in cho search-suggestions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hiệu ứng scale cho search-item */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Áp dụng hiệu ứng cho các phần tử */
.search-suggestions:not(.hidden) .search-item {
    animation: scaleIn 0.3s cubic-bezier(0.3, 0, 0.3, 1) forwards;
    opacity: 0;
}

/* Delay animation cho từng item */
.search-suggestions:not(.hidden) .search-item:nth-child(1) { animation-delay: 0.05s; }
.search-suggestions:not(.hidden) .search-item:nth-child(2) { animation-delay: 0.1s; }
.search-suggestions:not(.hidden) .search-item:nth-child(3) { animation-delay: 0.15s; }
.search-suggestions:not(.hidden) .search-item:nth-child(4) { animation-delay: 0.2s; }
.search-suggestions:not(.hidden) .search-item:nth-child(5) { animation-delay: 0.25s; }

/* Hiệu ứng cho view-all */
.search-suggestions:not(.hidden) .search-view-all {
    animation: fadeInUp 0.4s cubic-bezier(0.3, 0, 0.3, 1) forwards;
    animation-delay: 0.3s;
    opacity: 0;
}

/* Enhanced Loading state styles with skeleton */
.search-loading-item {
    padding: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-loading-item:last-child {
    border-bottom: none;
}

.search-product-skeleton {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-md);
}

.search-skeleton-image {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-md);
    animation: skeleton-loading 1.5s infinite;
    flex-shrink: 0;
}

.search-skeleton-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.search-skeleton-title {
    height: 1rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    animation: skeleton-loading 1.5s infinite;
    width: 80%;
}

.search-skeleton-category {
    height: 0.75rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    animation: skeleton-loading 1.5s infinite;
    width: 60%;
}

.search-skeleton-price {
    height: 0.75rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    animation: skeleton-loading 1.5s infinite;
    width: 40%;
}

.search-skeleton-spinner {
    color: var(--primary);
    font-size: 1rem;
    flex-shrink: 0;
}

.search-loading-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    gap: var(--spacing-sm);
    background-color: rgba(var(--primary-rgb), 0.02);
    color: var(--text-muted);
    font-size: var(--text-sm);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.search-loading-message i {
    color: var(--primary);
    animation: pulse-text 2s infinite;
}

.search-loading-message span {
    animation: pulse-text 2s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse-text {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
}

/* Staggered animation delays */
.search-loading-item:nth-child(1) .search-skeleton-image,
.search-loading-item:nth-child(1) .search-skeleton-title,
.search-loading-item:nth-child(1) .search-skeleton-category,
.search-loading-item:nth-child(1) .search-skeleton-price {
    animation-delay: 0s;
}

.search-loading-item:nth-child(2) .search-skeleton-image,
.search-loading-item:nth-child(2) .search-skeleton-title,
.search-loading-item:nth-child(2) .search-skeleton-category,
.search-loading-item:nth-child(2) .search-skeleton-price {
    animation-delay: 0.2s;
}

.search-loading-item:nth-child(3) .search-skeleton-image,
.search-loading-item:nth-child(3) .search-skeleton-title,
.search-loading-item:nth-child(3) .search-skeleton-category,
.search-loading-item:nth-child(3) .search-skeleton-price {
    animation-delay: 0.4s;
}

/* Smooth transitions for suggestions container */
.search-suggestions {
    transition: opacity 0.3s ease, transform 0.2s ease;
    opacity: 1;
}
