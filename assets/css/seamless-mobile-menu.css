/*
 * Seamless Mobile Menu CSS for Nội Thất Bàng <PERSON>ũ
 * Thiết kế menu sang trọng và hiện đại cho website nội thất
 */

/* <PERSON><PERSON>ến CSS */
:root {
    --mobile-header-height: 60px;
    --mobile-menu-transition: 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    --mobile-menu-bg: var(--white, #ffffff);
    --mobile-menu-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --mobile-menu-padding: 1.5rem;
    --mobile-menu-item-spacing: 1rem;
    --mobile-menu-border-color: rgba(229, 231, 235, 0.5);
    --mobile-menu-highlight: #f97316;
    --mobile-menu-highlight-bg: rgba(249, 115, 22, 0.08);
    --mobile-menu-text: #333333;
    --mobile-menu-text-light: #666666;
    --mobile-menu-icon-size: 1.25rem;
    --mobile-menu-font-size: 1rem;
    --mobile-menu-radius: 0.5rem;
    --mobile-menu-footer-bg: #f8fafc;
}

/* Mobile Menu Container */
.mobile-menu {
    position: fixed;
    top: var(--mobile-header-height);
    /* Bắt đầu ngay dưới header */
    left: 0;
    width: 100%;
    height: calc(100vh - var(--mobile-header-height));
    /* Chiều cao = chiều cao màn hình - chiều cao header */
    background-color: var(--mobile-menu-bg);
    z-index: var(--z-modal);
    overflow: hidden;
    transform: translateY(-10px);
    /* Ẩn menu với dịch chuyển nhỏ */
    opacity: 0;
    visibility: hidden;
    /* Ẩn menu khi không active */
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        visibility 0s 0.4s;
    box-shadow: var(--mobile-menu-shadow);
    display: flex;
    flex-direction: column;
    will-change: transform, opacity;
    /* Tối ưu hiệu suất */
}

.mobile-menu.active {
    transform: translateY(0);
    /* Hiện menu bằng cách đưa về vị trí ban đầu */
    opacity: 1;
    visibility: visible;
    /* Hiện menu khi active */
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        visibility 0s 0s;
}

/* Menu chính */
.mobile-menu-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Prevent body scroll when menu is open */
body.overflow-hidden {
    overflow: hidden;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: var(--mobile-header-height);
    /* Bắt đầu ngay dưới header */
    left: 0;
    width: 100%;
    height: calc(100vh - var(--mobile-header-height));
    /* Chiều cao = chiều cao màn hình - chiều cao header */
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-fixed);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        visibility 0s 0.4s;
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
    will-change: opacity;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1),
        visibility 0s 0s;
}

/* Mobile Menu Close Button - Đặt ở góc phải trên của menu */
.mobile-menu-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background-color: var(--ultra-light-gray);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--dark-gray);
    font-size: var(--text-xl);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-menu-close:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
    transform: rotate(90deg);
}

/* Hiệu ứng ripple khi nhấn vào nút đóng */
.mobile-menu-close::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(243, 115, 33, 0.2);
    border-radius: var(--radius-md);
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.mobile-menu-close:active::after {
    opacity: 1;
    transform: scale(1);
    transition: all 0s;
}

/* Hiệu ứng ripple khi nhấn nút đóng */
.close-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(243, 115, 33, 0.3);
    border-radius: var(--radius-md);
    transform: scale(0);
    animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    will-change: transform, opacity;
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Hiệu ứng fade-in cho các mục menu */
.mobile-menu.active .mobile-menu-item {
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Delay cho từng mục menu để tạo hiệu ứng lần lượt */
.mobile-menu.active .mobile-menu-item:nth-child(1) {
    animation-delay: 0.1s;
}

.mobile-menu.active .mobile-menu-item:nth-child(2) {
    animation-delay: 0.15s;
}

.mobile-menu.active .mobile-menu-item:nth-child(3) {
    animation-delay: 0.2s;
}

.mobile-menu.active .mobile-menu-item:nth-child(4) {
    animation-delay: 0.25s;
}

.mobile-menu.active .mobile-menu-item:nth-child(5) {
    animation-delay: 0.3s;
}

.mobile-menu.active .mobile-menu-item:nth-child(6) {
    animation-delay: 0.35s;
}

.mobile-menu.active .mobile-menu-item:nth-child(7) {
    animation-delay: 0.4s;
}

.mobile-menu.active .mobile-menu-item:nth-child(8) {
    animation-delay: 0.45s;
}

.mobile-menu.active .mobile-menu-item:nth-child(9) {
    animation-delay: 0.5s;
}

.mobile-menu.active .mobile-menu-item:nth-child(10) {
    animation-delay: 0.55s;
}

/* Hiệu ứng cho mobile-menu-header */
.mobile-menu.active .mobile-menu-header {
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Hiệu ứng cho mobile-menu-footer */
.mobile-menu.active .mobile-menu-footer {
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: 0.6s;
    opacity: 0;
}

/* Hiệu ứng cho mobile-search */
.mobile-menu.active .mobile-search {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.3s;
    opacity: 0;
}

/* Responsive cho màn hình tablet và điện thoại */
@media (max-width: 768px) {
    .mobile-menu {
        display: flex;
    }
}

/* Đảm bảo menu không bị che khuất bởi bottom navigation */
@media (max-width: 768px) {
    .mobile-menu {
        padding-bottom: var(--mobile-bottom-nav-height);
    }
}