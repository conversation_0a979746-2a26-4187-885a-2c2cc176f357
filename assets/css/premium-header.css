/*
 * Premium Header CSS for Nội Thất <PERSON>àng Vũ
 * Modern, luxurious design for furniture industry
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* Base Variables */
:root {
    /* Primary Colors - Cam pastel */
    --primary: #f97316;
    --primary-dark: #ea580c;
    --primary-light: #fdba74;
    --primary-ultra-light: #ffedd5;
    --primary-lightest: #fff7ed;

    /* Neutral Colors */
    --dark: #111827;
    --dark-gray: #2d2d2d;
    --medium-gray: #4b5563;
    --light-gray: #d1d5db;
    --ultra-light-gray: #f3f4f6;
    --white: #FFFFFF;

    /* New Colors */
    --header-dark: #111827;
    --header-light: #fff5f5;

    /* Accent Colors */
    --accent-gold: #d4af37;
    --accent-gold-light: #f5e6c3;
    --accent-brown: #8b4513;
    --accent-brown-light: #d2b48c;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;

    /* Font Sizes */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-md: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    --text-4xl: 36px;

    /* Z-index - Hệ thống phân cấp rõ ràng */
    --z-base: 1;
    --z-header: 100;
    --z-dropdown: 200;
    --z-sticky: 300;
    --z-fixed: 400;
    --z-search-container: 500;
    --z-search-form: 510;
    --z-search-suggestions: 520;
    --z-modal: 600;
    --z-popover: 700;
    --z-tooltip: 800;
    --z-overlay: 900;

    /* Breakpoints */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
}

/* Base Styles */
body {
    font-family: 'Be Vietnam Pro', sans-serif;
    color: var(--dark-gray);
    margin: 0;
    padding: 0;
    background-color: var(--white);
}

/* Accessibility - Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Premium Header Container */
.premium-header {
    position: sticky;
    top: 0;
    width: 100%;
    z-index: var(--z-header);
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(0);
    will-change: transform, background-color, box-shadow;
    isolation: isolate; /* Tạo stacking context mới */

    /* Biến CSS cho hiệu ứng mượt mà */
    --header-scale: 1;
    --header-translate-y: 0px;
}

/* Thêm hiệu ứng transition mượt mà */
.premium-header.smooth-transition {
    transition:
        transform 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        box-shadow 0.3s cubic-bezier(0.33, 1, 0.68, 1),
        background-color 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

/* Loại bỏ phần tử ::after để tránh tạo thanh màu đen */

/* Hiệu ứng transform mượt mà khi cuộn - Tạm thời bỏ qua để đảm bảo sticky header hoạt động */
.premium-header.smooth-transition:not(.scrolled) {
    /* transform: translateY(var(--header-translate-y)) scale(var(--header-scale)); */
}

/* Header khi cuộn - đổi màu nền */
.premium-header.scrolled {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.premium-header.scrolled .mid-header-container,
.premium-header.scrolled .bottom-header-container {
    background-color: #202834;
    background-image: none;
    border-color: rgba(255, 255, 255, 0.1);
    position: relative;
}

/* Logo text màu trắng khi cuộn */
.premium-header.scrolled .premium-logo-title {
    color: var(--white);
}

.premium-header.scrolled .premium-logo-tagline {
    color: var(--light-gray);
}

/* Đổi màu nav links khi nền tối */
.premium-header.scrolled .nav-link {
    color: var(--ultra-light-gray);
}

.premium-header.scrolled .nav-link:hover,
.premium-header.scrolled .nav-item.active .nav-link {
    color: #f97316;
}

/* Background cho hover nav-link khi ở nền tối */
.premium-header.scrolled .nav-link::before {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Đổi màu action buttons khi nền tối */
.premium-header.scrolled .action-btn {
    color: var(--ultra-light-gray);
}

.premium-header.scrolled .action-btn:hover {
    color: var(--white);
}

/* Đổi nền search form khi nền tối */
.premium-header.scrolled .search-form {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
}

.premium-header.scrolled .search-input {
    color: var(--white);
}

.premium-header.scrolled .search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Search form khi focus trên nền tối */
.premium-header.scrolled .search-form.focused {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-light);
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.3);
}

/* Đổi màu nav link active indicator khi nền tối */
.premium-header.scrolled .nav-link::after {
    background-color: #f97316;
    bottom: 0;
    height: 2px;
}

/* Điều chỉnh Cart button khi nền tối */
.premium-header.scrolled .action-btn.cart-btn {
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
}

.premium-header.scrolled .action-btn.cart-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px 0 rgba(249, 115, 22, 0.15);
}

/* Màu chữ đẹp hơn cho user-name khi cuộn */
.premium-header.scrolled .user-name span {
    color: var(--white);
}

.premium-header.scrolled .user-account-btn:hover .user-name span,
.premium-header.scrolled .user-account-btn:hover .user-name i {
    color: var(--white);
}

/* Viền cho user avatar khi nền tối */
.premium-header.scrolled .user-avatar {
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Màu sắc online dot khi nền tối */
.premium-header.scrolled .user-avatar .online-dot {
    border-color: var(--header-dark);
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.6);
}

/* User dropdown trên nền tối - Giữ màu nền trắng để đồng nhất với mini cart */
.premium-header.scrolled .user-dropdown-menu {
    background-color: var(--white);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1),
                0 0 1px rgba(0, 0, 0, 0.1);
}

.premium-header.scrolled .user-dropdown-menu::before {
    background-color: var(--white);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
}

.premium-header.scrolled .user-dropdown-header {
    border-bottom: 1px solid var(--light-gray);
}

.premium-header.scrolled .user-dropdown-header-title {
    color: var(--dark);
}

.premium-header.scrolled .user-dropdown-header-email {
    color: var(--medium-gray);
}

.premium-header.scrolled .user-dropdown-item {
    color: var(--dark);
}

.premium-header.scrolled .user-dropdown-item i {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
}

.premium-header.scrolled .user-dropdown-item:hover {
    background-color: var(--primary-ultra-light);
    color: var(--primary);
}

.premium-header.scrolled .user-dropdown-item:hover i {
    background-color: var(--primary-light);
    color: var(--white);
}

.premium-header.scrolled .user-dropdown-divider {
    background-color: var(--light-gray);
}

/* Màu nút search khi nền tối */
.premium-header.scrolled .search-button {
    background-color: var(--primary-light);
    color: var(--dark);
}

.premium-header.scrolled .search-input:focus+.search-button,
.premium-header.scrolled .search-button:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Thêm shadow và highlight cho cart-badge khi nền tối */
.premium-header.scrolled .cart-badge {
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    box-shadow: 0 0 10px rgba(249, 115, 22, 0.4);
}

/* Đảm bảo nền đúng màu khi chưa cuộn */
.mid-header-container {
    background-color: #faf9f8;
    /* background-image: linear-gradient(to bottom, #ffffff, #faf9f8); */
    padding: 0;
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
    will-change: background-color, padding;
    position: relative;
    z-index: 10; /* Tăng z-index để cao hơn bottom-header-container */
}

.bottom-header-container {
    background-color: #faf9f8;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    position: relative;
    overflow: visible;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    z-index: 5; /* Đảm bảo nằm trên lớp ::after nhưng dưới search-container */
    will-change: background-color, border-color;
}

/* Khung tìm kiếm khi chưa cuộn */
.search-form {
    position: relative;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-full);
    padding: var(--spacing-xs) var(--spacing-md);
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03), inset 0 0 0 1px rgba(255, 255, 255, 0.7);
    z-index: 30; /* Tăng z-index để cao hơn search-container */
}

.search-form:focus-within {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: rgba(249, 115, 22, 0.3);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(249, 115, 22, 0.1);
}

/* Loại bỏ hiệu ứng compact tạo khoảng trống khi top-bar biến mất */
.premium-header.compact {
    transform: translateY(0);
}

/* Hiệu ứng khi dừng cuộn */
.premium-header.scroll-pause {
    animation: none !important;
}

/* Hiệu ứng trượt xuống mượt mà khi top bar hiện lại */
.premium-header:not(.scrolled) .top-bar {
    animation: smooth-slide-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes smooth-slide-down {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Giữ nguyên hiệu ứng compact khi có scroll-pause */
.premium-header.compact.scroll-pause {
    animation: none !important;
}

/* Tier 1: Top Bar */
.top-bar {
    background-color: #202834;
    background-image: linear-gradient(to bottom, #2a3441, #202834);
    color: var(--white);
    padding: var(--spacing-xs) 0;
    font-size: var(--text-xs);
    position: relative;
    overflow: hidden;
    height: 36px; /* Chiều cao cố định thay vì max-height */
    opacity: 1;
    transform-origin: top;
    transform: translateY(0) scaleY(1);
    will-change: transform, opacity, clip-path;
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition:
        transform 0.5s cubic-bezier(0.33, 1, 0.68, 1),
        opacity 0.4s cubic-bezier(0.33, 1, 0.68, 1),
        clip-path 0.5s cubic-bezier(0.33, 1, 0.68, 1),
        border-bottom-color 0.3s ease;
    clip-path: inset(0 0 0 0);
    z-index: 1; /* Đảm bảo nằm trên lớp ::after */
}

/* Khi cuộn, ẩn top-bar mượt mà với transition */
.premium-header.scrolled .top-bar {
    transform: translateY(-100%);
    opacity: 0;
    max-height: 0;
    border-bottom-color: transparent;
    pointer-events: none;
    position: absolute; /* Đảm bảo không chiếm không gian khi ẩn */
    top: 0;
    left: 0;
    right: 0;
}

/* Hiệu ứng gradient cho top-bar */
.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
    pointer-events: none;
    animation: pulse-glow 8s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    height: 100%;
    position: relative;
    z-index: 2;
    transition: transform 0.4s cubic-bezier(0.33, 1, 0.68, 1), opacity 0.3s ease;
}

/* Hiệu ứng cho nội dung khi ẩn top bar */
.premium-header.scrolled .top-bar-content {
    transform: translateY(-8px);
    opacity: 0;
}

.top-bar-contact {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-shrink: 0;
    height: 100%;
    padding: var(--spacing-xs) 0;
}

.top-bar-contact a,
.top-bar-contact span {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 500;
    letter-spacing: 0.3px;
    padding: var(--spacing-xs) var(--spacing-sm);
    height: 100%;
    border-radius: var(--radius-md);
    position: relative;
    overflow: hidden;
}

.top-bar-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.08);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    border-radius: var(--radius-md);
    z-index: -1;
}

.top-bar-contact a:hover {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.top-bar-contact a:hover::before {
    transform: scaleX(1);
}

.top-bar-contact i {
    margin-right: var(--spacing-sm);
    color: var(--primary-light);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.top-bar-contact a:hover i {
    color: var(--primary);
    transform: scale(1.1);
}

.top-bar-address {
    display: flex;
    align-items: center;
    flex-grow: 1;
    justify-content: flex-end;
    height: 100%;
    padding: var(--spacing-xs) 0;
}

/* Làm cho top-bar-address-link giống hệt top-bar-contact a */
.top-bar-address-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    display: flex;
    align-items: center;
    font-size: var(--text-xs);
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
    padding: var(--spacing-xs) var(--spacing-sm);
    height: 100%;
    border-radius: var(--radius-md);
    position: relative;
}

/* Hiệu ứng underline giống top-bar-contact a */
.top-bar-address-link::before {
    content: '';
    position: absolute;
    bottom: 2px;
    left: var(--spacing-sm);
    right: var(--spacing-sm);
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.top-bar-address-link:hover {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.top-bar-address-link:hover::before {
    transform: scaleX(1);
}

.top-bar-address-link i {
    margin-right: var(--spacing-sm);
    color: var(--primary-light);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    flex-shrink: 0;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.top-bar-address-link:hover i {
    color: var(--primary);
    transform: scale(1.1);
}

/* Tier 2: Mid Header */
.mid-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
    height: 100%;
}

/* Logo */
.premium-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: var(--transition-normal);
}

.premium-logo-image {
    width: auto;
    /* height: 80px; */
    margin-right: 0;
    border-radius: 0;
    box-shadow: none;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: none;
}

.premium-logo-image img {
    /* max-height: 80px; */
    max-width: 330px;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
    transition: none;
    filter: none;
}

.premium-logo:hover .premium-logo-image,
.premium-logo:hover .premium-logo-image img {
    transform: none;
    box-shadow: none;
    background: none;
    filter: none;
}

.premium-logo-text {
    display: flex;
    flex-direction: column;
}

.premium-logo-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--dark);
    margin: 0;
    line-height: 1.2;
    transition: var(--transition-normal);
    letter-spacing: -0.5px;
}

.premium-logo-tagline {
    font-size: var(--text-sm);
    color: var(--medium-gray);
    margin: 0;
    line-height: 1.2;
    transition: var(--transition-normal);
}

.premium-logo:hover .premium-logo-title {
    color: var(--primary);
}

.premium-logo:hover .premium-logo-tagline {
    color: var(--primary-dark);
}

/* Search */
.search-container {
    position: relative;
    width: 400px;
    transition: var(--transition-normal);
    z-index: 20; /* Tăng z-index để cao hơn mid-header-container */
    pointer-events: auto;
}

.search-ripple {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--radius-full);
    pointer-events: none;
    z-index: 1;
}

.search-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(249, 115, 22, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
}

@keyframes search-ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 0.5;
    }

    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

.search-input {
    flex-grow: 1;
    border: none;
    background: transparent;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-md);
    color: var(--dark);
    outline: none;
    transition: var(--transition-normal);
    position: relative;
    z-index: 2;
}

.search-input::placeholder {
    color: var(--medium-gray);
    transition: var(--transition-fast);
}

.search-input:focus::placeholder {
    opacity: 0.7;
}

.search-input:focus {
    color: var(--dark);
    outline: none;
    border: none;
    box-shadow: none;
}

.search-button {
    background-color: var(--primary);
    background-image: linear-gradient(135deg, #f97316, #ea580c);
    color: var(--white);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: var(--spacing-xs);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
}

.search-input:focus+.search-button,
.search-button:hover {
    background-image: linear-gradient(135deg, #ea580c, #c2410c);
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

/* Tier 3: Bottom Header */
.bottom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    height: 60px;
    position: relative;
    overflow: visible;
}

/* Giữ nguyên chiều cao của bottom-header khi cuộn để đảm bảo đường gạch chân hiển thị đúng */
.scrolled .bottom-header {
    height: 60px;
}

/* Navigation */
.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--dark);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
    position: relative;
    overflow: visible;
}

.nav-link i {
    margin-left: var(--spacing-xs);
    font-size: 0.8em;
    transition: var(--transition-normal);
}

.nav-link:hover,
.nav-item.active .nav-link {
    color: #f97316;
    text-shadow: 0 0 1px rgba(249, 115, 22, 0.1);
}

.nav-link:hover i {
    transform: translateY(2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #f97316;
    background-image: linear-gradient(to right, #f97316, #fdba74, #f97316);
    transition: width 0.3s ease, transform 0.3s ease;
    transform: translateX(-50%);
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(249, 115, 22, 0.2);
}

.nav-link:hover::after,
.nav-item.active .nav-link::after {
    width: 100%;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(249, 115, 22, 0.05);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
    transform: translateY(100%);
    border-radius: var(--radius-md);
}

.nav-link:hover::before {
    opacity: 1;
    transform: translateY(0);
}

/* User Actions */
/* Cải thiện User Actions Container */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Thêm hiệu ứng hover chung */
.user-actions > * {
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

.user-actions > *:hover {
    transform: translateY(-2px);
}

/* Thêm hiệu ứng khi focus vào một phần tử */
.user-actions > *:focus-within {
    outline: none;
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
}

/* Thêm hiệu ứng khi một dropdown đang mở */
.user-actions .user-dropdown.active,
.user-actions .cart-container.active {
    z-index: var(--z-dropdown);
}

.action-btn {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dark);
    text-decoration: none;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
}

.action-btn:hover {
    color: var(--primary);
}

.action-btn .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary);
    color: var(--white);
    font-size: var(--text-xs);
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xs);
    transition: var(--transition-normal);
}

.action-btn:hover .badge {
    background-color: var(--primary-dark);
}

/* Cart */
.cart-container {
    position: relative;
}

.cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary);
    color: var(--white);
    font-size: var(--text-xs);
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xs);
    transition: var(--transition-normal);
}

.cart-container:hover .cart-badge {
    background-color: var(--primary-dark);
}

/* Login Button */
.login-btn,
.user-account-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.login-btn:hover,
.user-account-btn:hover {
    color: var(--primary);
}

.login-btn i,
.user-account-btn i {
    font-size: 1.2em;
}

.login-btn:hover i,
.user-account-btn:hover i {
    transform: scale(1.1);
}

.login-btn span,
.user-account-btn span {
    font-weight: 500;
}

/* User Account */
.user-account-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-account-btn:hover {
    color: var(--primary);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background-color: var(--ultra-light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    position: relative;
    border: 2px solid rgba(249, 115, 22, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: visible; /* Thay đổi từ hidden thành visible để hiển thị chấm trạng thái */
    will-change: transform, box-shadow;
}

.user-avatar .default-user-icon {
    font-size: 24px;
    color: var(--primary);
    opacity: 0.8;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-ultra-light), var(--white));
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.user-account-btn:hover .user-avatar {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
}

.user-account-btn:hover .default-user-icon {
    opacity: 1;
    color: var(--primary-dark);
}

.user-name {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.user-name span {
    font-weight: 500;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-account-btn:hover .user-name span {
    color: var(--primary);
}

.user-name i {
    font-size: 0.8em;
    transition: var(--transition-normal);
}

.user-dropdown:hover .user-name i {
    transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 250px;
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1),
                0 0 1px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-md) 0;
    margin-top: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.98);
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    z-index: var(--z-dropdown);
    overflow: hidden;
}

.user-dropdown-menu::before {
    content: '';
    position: absolute;
    top: -5px;
    right: 20px;
    width: 10px;
    height: 10px;
    background-color: var(--white);
    transform: rotate(45deg);
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
}

.user-dropdown:hover .user-dropdown-menu,
.user-dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dark);
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.user-dropdown-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-ultra-light);
    border-radius: var(--radius-md);
    color: var(--primary);
    transition: all 0.2s ease;
}

.user-dropdown-item:hover {
    background-color: var(--primary-ultra-light);
    color: var(--primary);
}

.user-dropdown-item:hover i {
    background-color: var(--primary-light);
    color: var(--white);
    transform: scale(1.1);
}

/* Thêm header cho dropdown menu */
.user-dropdown-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
    margin-bottom: var(--spacing-sm);
}

.user-dropdown-header-title {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
}

.user-dropdown-header-email {
    font-size: var(--text-sm);
    color: var(--medium-gray);
}

.user-dropdown-divider {
    height: 1px;
    background-color: var(--light-gray);
    margin: var(--spacing-xs) 0;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: none;
    color: var(--dark);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    z-index: var(--z-fixed);
}

.mobile-menu-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-ultra-light);
    border-radius: var(--radius-md);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
}

.mobile-menu-toggle:hover {
    color: var(--primary);
}

.mobile-menu-toggle:hover::before {
    opacity: 1;
}

/* Responsive */

/* Desktop Navigation Spacing Optimization */

/* XXL Desktop: 1400px+ - Giữ nguyên spacing mặc định */
/* Không cần media query vì đây là default */

/* XL Desktop: 1400px+ */
@media (min-width: 1400px) and (max-width: 1599px) {
    .nav-link {
        padding: var(--spacing-md) var(--spacing-lg); /* 16px 24px */
    }
}

/* LG Desktop: 1201px+ (sau khi tablet slide kết thúc) */
@media (min-width: 1201px) and (max-width: 1399px) {
    .nav-link {
        padding: var(--spacing-md) var(--spacing-md); /* 16px 16px */
    }
}

/* Large Desktop - Compatibility với code cũ */
@media (max-width: var(--breakpoint-xl)) {
    .premium-logo-title {
        font-size: var(--text-lg);
    }

    .search-container {
        width: 300px;
    }

    .user-name span {
        max-width: 100px;
    }
}

/* Tablet Landscape - Ẩn email */
@media (max-width: var(--breakpoint-lg)) {
    .search-container {
        width: 250px;
    }

    .nav-link {
        padding: var(--spacing-md) var(--spacing-sm);
        font-size: var(--text-sm);
    }

    .login-btn span,
    .user-name span {
        max-width: 80px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .login-btn i,
    .user-account-btn i {
        font-size: 1.2em;
    }

    .login-btn,
    .user-account-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* Ẩn email trên tablet kích thước vừa và nhỏ */
    .top-bar-email {
        display: none;
    }
}

/* Medium Tablet - Ẩn email */
@media (max-width: 992px) {
    .search-container {
        width: 200px;
    }

    .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--text-xs);
    }

    .user-name span {
        max-width: 60px;
    }

    .top-bar-contact {
        gap: var(--spacing-md);
    }

    .top-bar-address span {
        max-width: 200px;
    }

    /* Ẩn email trên tablet kích thước vừa */
    .top-bar-email {
        display: none;
    }
}

/* Tablet Navigation - Horizontal Scroll */
@media (min-width: 769px) and (max-width: 1200px) {
    .bottom-header {
        padding: 0 var(--spacing-sm); /* Giảm padding để có thêm không gian */
        overflow: hidden;           /* Ẩn overflow để tạo scroll */
    }

    .nav-menu {
        overflow-x: auto;           /* Cho phép scroll ngang */
        overflow-y: hidden;         /* Ẩn scroll dọc */
        flex-wrap: nowrap;          /* Không cho items xuống dòng */
        justify-content: flex-start; /* Thay vì center */
        scrollbar-width: none;      /* Ẩn scrollbar Firefox */
        -ms-overflow-style: none;   /* Ẩn scrollbar IE */
        cursor: grab;               /* Cursor cho biết có thể kéo */
        user-select: none;          /* Không cho select text khi drag */
        width: 100%;                /* Đảm bảo chiếm hết không gian có thể */
        min-width: 0;               /* Cho phép shrink */
    }

    .nav-menu::-webkit-scrollbar {
        display: none;              /* Ẩn scrollbar Chrome/Safari */
    }

    .nav-menu:active {
        cursor: grabbing;           /* Cursor khi đang kéo */
    }

    .nav-item {
        flex-shrink: 0;             /* Không cho menu items bị nén */
    }

    .nav-link {
        white-space: nowrap;        /* Không cho text xuống hàng */
        pointer-events: auto;       /* Đảm bảo links vẫn clickable */
        padding: var(--spacing-md) var(--spacing-sm); /* Giảm padding để tiết kiệm không gian */
    }
}

/* Mobile và Tablet nhỏ */
@media (max-width: 767px) {
    .premium-header {
        position: relative;
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    }

    .top-bar-content {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-md);
    }

    .top-bar-contact {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-md);
    }

    .mid-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .search-container {
        display: block;
        width: 100%;
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    }

    .bottom-header {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .user-name {
        display: none;
    }

    .user-account-btn {
        padding: var(--spacing-xs);
    }
}

/* Mobile và Tablet */
@media (max-width: var(--breakpoint-md)) {
    .premium-logo-image img {
        max-width: 250px;
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    }

    .top-bar {
        font-size: 11px;
    }

    .top-bar-contact {
        gap: var(--spacing-sm);
    }

    .search-form {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .search-input {
        font-size: var(--text-sm);
    }

    /* Ẩn premium header và hiển thị mobile header */
    .premium-header {
        display: none;
    }
}

/* Giỏ hàng - Tạo khoảng cách giữa icon và chữ */
.cart-link {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn.cart-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--primary-ultra-light);
    background-image: linear-gradient(to bottom, #fff7ed, #ffedd5);
    border-radius: var(--radius-lg);
    padding: 10px 20px;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(249, 115, 22, 0.1);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.05);
    font-weight: 500;
}

.action-btn.cart-btn:hover {
    background: var(--primary-lightest);
    background-image: linear-gradient(to bottom, #ffedd5, #fed7aa);
    box-shadow: 0 4px 16px 0 rgba(249, 115, 22, 0.15);
    transform: translateY(-2px);
    border-color: rgba(249, 115, 22, 0.2);
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #f97316, #fdba74);
    color: var(--white);
    font-size: var(--text-xs);
    font-weight: 700;
    min-width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xs);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.4) inset;
    transition: all 0.3s ease;
    will-change: transform;
}

.action-btn.cart-btn:hover .cart-badge {
    animation: cart-badge-bounce 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.5) inset;
    background: linear-gradient(135deg, #ea580c, #f97316);
}

/* Hiệu ứng nhấp nháy khi thêm sản phẩm vào giỏ hàng - ĐÃ TẮT ĐỂ TỐI ƯU HIỆU SUẤT */
.cart-badge.badge-pulse {
    /* animation: cart-badge-pulse 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 2; */
}

@keyframes cart-badge-bounce {
    0% {
        transform: scale(1);
    }

    40% {
        transform: scale(1.3);
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes cart-badge-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.7);
    }
    50% {
        transform: scale(1.4);
        box-shadow: 0 0 0 10px rgba(249, 115, 22, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
    }
}

/* Mini Cart */
.cart-container {
    position: relative;
}

.mini-cart {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1),
                0 0 1px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.98);
    transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
    z-index: var(--z-dropdown);
}

.cart-container:hover .mini-cart,
.mini-cart:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.mini-cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--light-gray);
}

.mini-cart-title {
    font-weight: 600;
    color: var(--dark);
}

.mini-cart-count {
    font-size: var(--text-sm);
    color: var(--medium-gray);
}

.mini-cart-items {
    max-height: 250px;
    overflow-y: auto;
    margin-bottom: var(--spacing-md);
}

.mini-cart-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--ultra-light-gray);
}

.mini-cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    background-color: var(--ultra-light-gray);
}

.mini-cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mini-cart-item-info {
    flex: 1;
}

.mini-cart-item-name {
    font-weight: 500;
    color: var(--dark);
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.mini-cart-item-price {
    font-size: var(--text-sm);
    color: var(--primary);
    font-weight: 600;
}

.mini-cart-item-quantity {
    font-size: var(--text-xs);
    color: var(--medium-gray);
}

.mini-cart-empty {
    padding: var(--spacing-md) 0;
    text-align: center;
}

.mini-cart-empty p {
    color: var(--medium-gray);
    font-size: var(--text-md);
    margin: 0;
}

.mini-cart-more-items {
    padding: var(--spacing-sm) 0;
    text-align: center;
    border-top: 1px dashed var(--ultra-light-gray);
    margin-top: var(--spacing-sm);
}

.mini-cart-view-more {
    display: inline-block;
    color: var(--primary);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    background-color: var(--primary-ultra-light);
    transition: all 0.3s ease;
}

.mini-cart-view-more:hover {
    background-color: var(--primary);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
}

.mini-cart-footer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.mini-cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-top: 1px solid var(--light-gray);
}

.mini-cart-total-label {
    font-weight: 500;
    color: var(--dark);
}

.mini-cart-total-value {
    font-weight: 600;
    color: var(--primary);
}

.mini-cart-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.mini-cart-button {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

/* Nút "Xem giỏ hàng" - Thiết kế sang trọng */
.mini-cart-button.view-cart {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: #475569;
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(71, 85, 105, 0.08);
}

.mini-cart-button.view-cart:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: #334155;
    border-color: #94a3b8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(71, 85, 105, 0.15);
}

/* Nút "Thanh toán" - Thiết kế sang trọng */
.mini-cart-button.checkout {
    background: linear-gradient(135deg, var(--primary), #ea580c);
    color: var(--white);
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
}

.mini-cart-button.checkout::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.mini-cart-button.checkout:hover::before {
    left: 100%;
}

.mini-cart-button.checkout:hover {
    background: linear-gradient(135deg, #d97706, var(--primary));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
    border-color: #d97706;
}

/* Avatar online */
.user-avatar .online-dot {
    position: absolute;
    bottom: -3px;
    right: -3px;
    width: 12px;
    height: 12px;
    background-color: #22c55e;
    border-radius: 50%;
    border: 2px solid var(--white);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    animation: pulse 2s infinite;
    z-index: 2;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

