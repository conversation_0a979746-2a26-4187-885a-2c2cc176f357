/* CSS cho mục lục bài viết - <PERSON><PERSON><PERSON> */

/* Container mục lục */
.blog-toc-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
    position: relative;
}

/* Ti<PERSON>u đề mục lục */
.blog-toc-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.blog-toc-title::before {
    content: '\f02e';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #F37321;
}

/* <PERSON>h sách mục lục */
.blog-toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.blog-toc-sublist {
    list-style: none;
    padding-left: 20px;
    margin: 5px 0;
}

/* <PERSON><PERSON><PERSON> trong mục lục */
.blog-toc-item {
    margin-bottom: 8px;
    line-height: 1.4;
}

/* <PERSON><PERSON><PERSON> kết trong mục lục */
.blog-toc-link {
    color: #495057;
    text-decoration: none;
    display: block;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.blog-toc-link:hover {
    color: #F37321;
    background-color: rgba(243, 115, 33, 0.05);
}

.blog-toc-link.active {
    color: #F37321;
    background-color: rgba(243, 115, 33, 0.1);
    font-weight: 600;
    border-left: 3px solid #F37321;
}

/* Cấp độ mục lục */
.blog-toc-level-2 .blog-toc-link {
    font-weight: 600;
}

.blog-toc-level-3 .blog-toc-link {
    font-weight: 500;
    font-size: 0.9rem;
}

.blog-toc-level-4 .blog-toc-link {
    font-weight: 400;
    font-size: 0.85rem;
}

/* Nút đóng/mở mục lục trên mobile */
.blog-toc-toggle {
    display: none;
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #F37321;
    color: white;
    border: none;
    border-radius: 4px;
    width: 36px;
    height: 36px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.blog-toc-toggle:hover {
    background-color: #e06518;
}

.blog-toc-toggle.active {
    background-color: #e06518;
}

/* Thanh tiến độ đọc */
.blog-reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0;
    height: 4px;
    background-color: #F37321;
    z-index: 1000;
    transition: width 0.1s ease;
}

/* Mục lục sticky */
.blog-toc-sticky {
    position: sticky;
    top: 80px;
}

/* Thời gian đọc */
.blog-reading-time {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: #6c757d;
    font-size: 0.9rem;
}

.blog-reading-time i {
    margin-right: 5px;
    color: #F37321;
}

/* Responsive */
@media (max-width: 768px) {
    .blog-toc-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .blog-toc-list {
        display: none;
    }
    
    .blog-toc-list.active {
        display: block;
    }
    
    .blog-toc-container {
        padding: 15px;
    }
    
    .blog-toc-title {
        margin-right: 40px;
    }
}
