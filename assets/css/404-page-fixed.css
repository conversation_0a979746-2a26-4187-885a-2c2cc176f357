/**
 * 404 Error Page CSS - Nội Thất <PERSON> (Fixed Version)
 * Thiết kế hiện đại với gradient cam và furniture motifs
 * Đ<PERSON> khắc phục vấn đề ảnh hưởng đến header
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* Keyframes cho animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Container chính - Scoped CSS để tránh ảnh hưởng đến header */
.error-404-container {
    /* CSS Variables - Scoped để tránh xung đột */
    --error-primary: #F37321;
    --error-primary-dark: #D65A0F;
    --error-primary-light: #FF8A3D;
    --error-primary-lighter: #FFA66B;
    --error-primary-lightest: #FFD0AD;
    --error-primary-ultra-light: #FFF4EC;
    --error-secondary: #2A3B47;
    --error-secondary-dark: #1E2A32;
    --error-secondary-light: #435868;
    --error-white: #FFFFFF;
    --error-light-gray: #F8F9FA;
    --error-medium-gray: #E0E0E0;
    --error-dark-gray: #757575;
    --error-black: #212121;
    --error-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --error-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --error-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --error-shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);
    --error-transition-fast: all 0.2s ease;
    --error-transition-normal: all 0.3s ease;
    --error-transition-slow: all 0.5s ease;

    /* Layout */
    min-height: calc(100vh - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg,
        var(--error-primary-ultra-light) 0%,
        var(--error-white) 25%,
        var(--error-primary-ultra-light) 50%,
        var(--error-white) 75%,
        var(--error-primary-ultra-light) 100%);
    font-family: 'Be Vietnam Pro', sans-serif;
    overflow: hidden;
    padding: 2rem 1rem;
    margin-top: 0;
    isolation: isolate;
}

/* Reset cho các phần tử trong container 404 */
.error-404-container *,
.error-404-container *::before,
.error-404-container *::after {
    box-sizing: border-box;
}

.error-404-container input,
.error-404-container button,
.error-404-container a {
    font-family: inherit;
}

/* Background Pattern */
.error-404-container .error-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.03;
    background-image:
        radial-gradient(circle at 20% 20%, var(--error-primary) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, var(--error-primary) 2px, transparent 2px),
        radial-gradient(circle at 40% 60%, var(--error-primary) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 30px 30px;
    animation: float 6s ease-in-out infinite;
}

/* Main Content */
.error-404-container .error-content {
    text-align: center;
    max-width: 800px;
    width: 100%;
    position: relative;
    z-index: 2;
    animation: fadeInUp 0.8s ease-out;
}

/* Error Animation Container */
.error-404-container .error-animation {
    margin-bottom: 3rem;
    position: relative;
}

/* Furniture Icon */
.error-404-container .furniture-icon {
    font-size: 4rem;
    color: var(--error-primary);
    margin-bottom: 2rem;
    animation: bounceIn 1s ease-out 0.2s both;
}

.error-404-container .furniture-icon i {
    filter: drop-shadow(0 4px 8px rgba(243, 115, 33, 0.3));
}

/* 404 Number */
.error-404-container .error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.error-404-container .error-number span {
    font-size: 8rem;
    font-weight: 800;
    line-height: 1;
    background: linear-gradient(135deg, var(--error-primary), var(--error-primary-dark));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 4px 8px rgba(243, 115, 33, 0.3);
    animation: bounceIn 0.6s ease-out both;
}

.error-404-container .number-4 {
    animation-delay: 0.1s;
}

.error-404-container .number-0 {
    animation-delay: 0.2s;
    transform: scale(1.2);
}

.error-404-container .number-4-2 {
    animation-delay: 0.3s;
}

/* Error Message */
.error-404-container .error-message {
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.error-404-container .error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--error-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.error-404-container .error-description {
    font-size: 1.1rem;
    color: var(--error-dark-gray);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Action Buttons */
.error-404-container .error-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.error-404-container .error-actions a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--error-transition-normal);
    position: relative;
    overflow: hidden;
}

.error-404-container .btn-primary {
    background: linear-gradient(135deg, var(--error-primary), var(--error-primary-dark));
    color: var(--error-white);
    box-shadow: var(--error-shadow-md);
}

.error-404-container .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--error-shadow-lg);
}

.error-404-container .btn-secondary {
    background: linear-gradient(135deg, var(--error-secondary), var(--error-secondary-dark));
    color: var(--error-white);
    box-shadow: var(--error-shadow-md);
}

.error-404-container .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--error-shadow-lg);
}

.error-404-container .btn-outline {
    background: var(--error-white);
    color: var(--error-primary);
    border: 2px solid var(--error-primary);
    box-shadow: var(--error-shadow-sm);
}

.error-404-container .btn-outline:hover {
    background: var(--error-primary);
    color: var(--error-white);
    transform: translateY(-2px);
}

/* Popular Categories */
.error-404-container .popular-categories {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.error-404-container .popular-categories h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--error-secondary);
    margin-bottom: 2rem;
    position: relative;
}

.error-404-container .popular-categories h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--error-primary), var(--error-primary-light));
    border-radius: 2px;
}

.error-404-container .category-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    max-width: 800px;
    margin: 0 auto;
}

/* Category Card Design */
.error-404-container .category-card {
    position: relative;
    display: block;
    background: var(--error-white);
    border-radius: 20px;
    text-decoration: none;
    color: var(--error-secondary);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.08),
        0 3px 10px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.error-404-container .category-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(243, 115, 33, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--error-primary);
}

/* Category Image */
.error-404-container .category-image {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.error-404-container .category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.error-404-container .category-card:hover .category-image img {
    transform: scale(1.1);
}

/* Category Placeholder */
.error-404-container .category-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--error-primary-ultra-light) 0%, #f8f9fa 100%);
    color: var(--error-primary);
    font-size: 3rem;
    transition: all 0.4s ease;
}

.error-404-container .category-card:hover .category-placeholder {
    color: var(--error-primary-dark);
    transform: scale(1.1);
}

/* Category Overlay */
.error-404-container .category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(243, 115, 33, 0) 0%,
        rgba(243, 115, 33, 0.1) 50%,
        rgba(243, 115, 33, 0.2) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
}

.error-404-container .category-card:hover .category-overlay {
    opacity: 1;
}

/* Category Content */
.error-404-container .category-content {
    padding: 1.5rem;
    text-align: center;
}

.error-404-container .category-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--error-secondary);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.error-404-container .category-card:hover .category-name {
    color: var(--error-primary);
}

.error-404-container .category-description {
    font-size: 0.9rem;
    color: var(--error-dark-gray);
    margin: 0;
    line-height: 1.4;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.error-404-container .category-card:hover .category-description {
    opacity: 1;
}

/* Floating Elements */
.error-404-container .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.error-404-container .floating-element {
    position: absolute;
    font-size: 2rem;
    color: var(--error-primary);
    opacity: 0.1;
    animation: float 4s ease-in-out infinite;
}

.error-404-container .element-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.error-404-container .element-2 {
    top: 30%;
    right: 15%;
    animation-delay: 1s;
}

.error-404-container .element-3 {
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.error-404-container .element-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

/* Responsive Design */
@media (max-width: 768px) {
    .error-404-container {
        padding: 1rem;
        min-height: calc(100vh - 80px);
    }

    .error-404-container .error-number span {
        font-size: 5rem;
    }

    .error-404-container .error-title {
        font-size: 2rem;
    }

    .error-404-container .error-description {
        font-size: 1rem;
    }

    .error-404-container .error-actions {
        flex-direction: column;
        align-items: center;
    }

    .error-404-container .error-actions a {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .error-404-container .category-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
        max-width: 500px;
    }

    .error-404-container .category-image {
        height: 140px;
    }

    .error-404-container .category-content {
        padding: 1.2rem;
    }

    .error-404-container .category-name {
        font-size: 1rem;
    }

    .error-404-container .category-description {
        font-size: 0.85rem;
    }

    .error-404-container .furniture-icon {
        font-size: 3rem;
    }

    .error-404-container .floating-element {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .error-404-container .error-number {
        gap: 0.5rem;
    }

    .error-404-container .error-number span {
        font-size: 4rem;
    }

    .error-404-container .error-title {
        font-size: 1.8rem;
    }

    .error-404-container .error-description {
        font-size: 0.95rem;
        padding: 0 1rem;
    }

    .error-404-container .category-links {
        grid-template-columns: 1fr;
        gap: 1rem;
        max-width: 320px;
    }

    .error-404-container .category-image {
        height: 120px;
    }

    .error-404-container .category-content {
        padding: 1rem;
    }

    .error-404-container .category-name {
        font-size: 0.95rem;
    }

    .error-404-container .category-description {
        font-size: 0.8rem;
    }

    .error-404-container .category-placeholder {
        font-size: 2.5rem;
    }

    .error-404-container .furniture-icon {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }
}

/* Hover Effects cho Desktop */
@media (min-width: 769px) {
    .error-404-container .error-actions a::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .error-404-container .error-actions a:hover::before {
        left: 100%;
    }

    .error-404-container .category-card:hover {
        animation: pulse 0.6s ease-in-out;
    }

    .error-404-container .furniture-icon:hover i {
        animation: rotate 1s ease-in-out;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .error-404-container * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .error-404-container .floating-element {
        animation: none;
    }

    .error-404-container .error-bg-pattern {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .error-404-container .error-number span {
        color: var(--error-black) !important;
        background: none !important;
        -webkit-background-clip: unset !important;
        background-clip: unset !important;
    }

    .error-404-container .btn-primary,
    .error-404-container .btn-secondary {
        background: var(--error-black) !important;
        color: var(--error-white) !important;
        border: 2px solid var(--error-black) !important;
    }

    .error-404-container .btn-outline {
        background: var(--error-white) !important;
        color: var(--error-black) !important;
        border: 2px solid var(--error-black) !important;
    }
}
