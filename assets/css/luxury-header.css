/* 
 * <PERSON><PERSON><PERSON> Header CSS for Nội Thất <PERSON>ng <PERSON>ũ
 * Modern, elegant design for furniture industry
 */

/* Import Be Vietnam Pro font */
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap');

/* Base Variables */
:root {
    /* Primary Colors */
    --primary: #F37321;
    --primary-dark: #D35400;
    --primary-light: #FF9D5C;
    --primary-ultra-light: #FFF0E8;

    /* Neutral Colors */
    --dark: #1A1A1A;
    --dark-gray: #333333;
    --medium-gray: #666666;
    --light-gray: #CCCCCC;
    --ultra-light-gray: #F8F8F8;
    --white: #FFFFFF;

    /* Accent Colors */
    --accent-gold: #D4AF37;
    --accent-beige: #F5F5DC;
    --accent-cream: #FFFDD0;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;

    /* Font Sizes */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-md: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    --text-4xl: 36px;

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Base Styles */
body {
    font-family: 'Be Vietnam Pro', sans-serif;
    color: var(--dark-gray);
    margin: 0;
    padding: 0;
    background-color: var(--white);
}

/* Luxury Header Container */
.luxury-header {
    position: sticky;
    top: 0;
    width: 100%;
    z-index: var(--z-sticky);
    background-color: var(--white);
    transition: var(--transition-normal);
}

.luxury-header.scrolled {
    box-shadow: var(--shadow-md);
}

/* Top Bar */
.top-bar {
    background-color: var(--dark);
    color: var(--white);
    padding: var(--spacing-xs) 0;
    font-size: var(--text-xs);
    transition: var(--transition-normal);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.top-bar-contact {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.top-bar-contact a,
.top-bar-contact span {
    color: var(--white);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: var(--transition-fast);
}

.top-bar-contact a:hover {
    color: var(--primary-light);
}

.top-bar-contact i {
    margin-right: var(--spacing-xs);
    font-size: var(--text-sm);
}

.top-bar-social {
    display: flex;
    gap: var(--spacing-md);
}

.top-bar-social a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.1);
}

.top-bar-social a:hover {
    background-color: var(--primary);
    transform: translateY(-2px);
}

/* Main Header */
.main-header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
}

.main-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

/* Logo */
.luxury-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    padding: var(--spacing-xs) 0;
    transition: var(--transition-normal);
}

.luxury-logo-image {
    width: 50px;
    height: 50px;
    margin-right: var(--spacing-md);
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.luxury-logo-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(243, 115, 33, 0.3));
    transition: var(--transition-slow);
}

.luxury-logo:hover .luxury-logo-image img {
    transform: scale(1.05) rotate(3deg);
    filter: drop-shadow(0 4px 8px rgba(243, 115, 33, 0.5));
}

.luxury-logo-text {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

.luxury-logo-title {
    font-size: var(--text-lg);
    font-weight: 800;
    color: var(--primary);
    margin: 0;
    line-height: 1.2;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: var(--transition-normal);
}

.luxury-logo-tagline {
    font-size: var(--text-xs);
    font-weight: 500;
    color: var(--medium-gray);
    margin: var(--spacing-xs) 0 0;
    line-height: 1;
    letter-spacing: 0.5px;
    transition: var(--transition-normal);
}

.luxury-logo:hover .luxury-logo-title {
    color: var(--primary-dark);
}

.luxury-logo:hover .luxury-logo-tagline {
    color: var(--dark-gray);
}

/* Navigation */
.luxury-nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    margin-left: var(--spacing-xl);
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-lg);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    color: var(--dark-gray);
    text-decoration: none;
    font-size: var(--text-md);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-xs);
    transition: var(--transition-normal);
    position: relative;
}

.nav-link i {
    margin-left: var(--spacing-xs);
    font-size: var(--text-xs);
    transition: var(--transition-normal);
}

.nav-link:hover,
.nav-item.active .nav-link {
    color: var(--primary);
}

.nav-link:hover i {
    transform: rotate(180deg);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-item.active .nav-link::after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background-color: var(--white);
    min-width: 220px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: var(--z-dropdown);
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--white);
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dark-gray);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: 400;
    transition: var(--transition-fast);
    border-radius: var(--radius-sm);
}

.dropdown-item:hover {
    background-color: var(--primary-ultra-light);
    color: var(--primary);
    transform: translateX(5px);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--light-gray);
    margin: var(--spacing-xs) 0;
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-left: var(--spacing-xl);
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--dark-gray);
    font-size: var(--text-xl);
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: var(--transition-normal);
}

.action-btn:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
}

.action-btn .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary);
    color: var(--white);
    font-size: var(--text-xs);
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search Bar */
.search-container {
    position: relative;
    margin-left: var(--spacing-lg);
}

.search-toggle {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--dark-gray);
    font-size: var(--text-xl);
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
}

.search-toggle:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
}

.search-panel {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition-normal);
    z-index: var(--z-dropdown);
}

.search-panel.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-form {
    display: flex;
    position: relative;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--light-gray);
    border-radius: var(--radius-full);
    font-family: 'Be Vietnam Pro', sans-serif;
    font-size: var(--text-sm);
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.2);
}

.search-button {
    position: absolute;
    right: var(--spacing-xs);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--medium-gray);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-button:hover {
    color: var(--primary);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--dark-gray);
    font-size: var(--text-xl);
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
}

.mobile-menu-toggle:hover {
    color: var(--primary);
    background-color: var(--primary-ultra-light);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .luxury-logo-title {
        font-size: var(--text-md);
    }

    .luxury-logo-tagline {
        font-size: 10px;
    }

    .nav-menu {
        gap: var(--spacing-md);
    }

    .nav-link {
        font-size: var(--text-sm);
    }

    .user-actions {
        margin-left: var(--spacing-md);
    }
}

@media (max-width: 992px) {
    .luxury-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .top-bar-contact {
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-md);
    }

    .top-bar-contact {
        flex-wrap: wrap;
        justify-content: center;
    }

    .main-header-container {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .luxury-logo-image {
        width: 40px;
        height: 40px;
        margin-right: var(--spacing-sm);
    }

    .luxury-logo-title {
        font-size: var(--text-sm);
    }

    .luxury-logo-tagline {
        font-size: 9px;
    }

    .user-actions {
        gap: var(--spacing-sm);
        margin-left: var(--spacing-sm);
    }

    .action-btn {
        width: 36px;
        height: 36px;
        font-size: var(--text-lg);
    }
}

@media (max-width: 576px) {
    .top-bar-social {
        display: none;
    }

    .top-bar-contact {
        gap: var(--spacing-sm);
        font-size: 10px;
    }

    .luxury-logo-image {
        width: 36px;
        height: 36px;
    }

    .luxury-logo-title {
        font-size: 11px;
    }

    .luxury-logo-tagline {
        font-size: 8px;
    }

    .user-actions {
        gap: var(--spacing-xs);
    }

    .action-btn {
        width: 32px;
        height: 32px;
        font-size: var(--text-md);
    }

    .search-container {
        margin-left: var(--spacing-xs);
    }
}