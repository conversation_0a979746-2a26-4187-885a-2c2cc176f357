/* Interior Handbook CSS - <PERSON><PERSON><PERSON>t <PERSON>ng <PERSON> */

/* Main Container */
.interior-handbook-section {
    position: relative;
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    overflow: hidden;
}

/* Background Elements */
.handbook-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.handbook-bg::before {
    content: '';
    position: absolute;
    top: -30%;
    right: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.03) 0%, rgba(243, 115, 33, 0) 70%);
    z-index: -1;
}

.handbook-bg::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0) 70%);
    z-index: -1;
}

/* Section Header */
.handbook-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.handbook-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(243, 115, 33, 0.1);
    color: #F37321;
    font-weight: 600;
    border-radius: 9999px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.handbook-badge .badge-icon {
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.handbook-heading {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.handbook-heading::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 100%;
    height: 0.25rem;
    background: rgba(243, 115, 33, 0.3);
    border-radius: 9999px;
}

.handbook-description {
    max-width: 36rem;
    margin: 0 auto;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.5;
}

/* Main Content Container */
.handbook-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Featured Article */
.handbook-featured {
    position: relative;
    margin-bottom: 3rem;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    background: #fff;
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    min-height: 400px;
    transition: transform 0.1s ease-out;
    transform-style: preserve-3d;
    will-change: transform;
}

.handbook-featured-image {
    position: relative;
    overflow: hidden;
}

.handbook-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.handbook-featured:hover .handbook-featured-image img {
    transform: scale(1.05);
}

.handbook-featured-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.handbook-featured-label {
    display: inline-flex;
    align-items: center;
    background: rgba(243, 115, 33, 0.1);
    color: #F37321;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.35rem 0.75rem;
    border-radius: 9999px;
    margin-bottom: 1rem;
}

.handbook-featured-label i {
    margin-right: 0.5rem;
}

.handbook-featured-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.handbook-featured-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.handbook-featured-title a:hover {
    color: #F37321;
}

.handbook-featured-excerpt {
    color: #6B7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: calc(1.6em * 4);
    word-break: break-word;
}

.handbook-featured-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    color: #6B7280;
}

.handbook-featured-author {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.handbook-featured-author-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    overflow: hidden;
    margin-right: 0.5rem;
    border: 2px solid #E5E7EB;
}

.handbook-featured-author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.handbook-featured-date {
    display: flex;
    align-items: center;
}

.handbook-featured-date i {
    margin-right: 0.5rem;
    color: #F37321;
}

.handbook-featured-button {
    display: inline-flex;
    align-items: center;
    background: #F37321;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(243, 115, 33, 0.2);
}

.handbook-featured-button:hover {
    background: #e06518;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(243, 115, 33, 0.3);
}

.handbook-featured-button i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.handbook-featured-button:hover i {
    transform: translateX(3px);
}

/* Articles Grid */
.handbook-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Article Card */
.handbook-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.handbook-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.handbook-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.handbook-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.handbook-card:hover .handbook-card-image img {
    transform: scale(1.05);
}

.handbook-card-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.handbook-card-category {
    display: inline-flex;
    align-items: center;
    background: rgba(243, 115, 33, 0.1);
    color: #F37321;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    margin-bottom: 0.75rem;
}

.handbook-card-category i {
    margin-right: 0.35rem;
    font-size: 0.7rem;
}

.handbook-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.handbook-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.handbook-card-title a:hover {
    color: #F37321;
}

.handbook-card-excerpt {
    color: #6B7280;
    margin-bottom: 1.25rem;
    line-height: 1.6;
    font-size: 0.9rem;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: calc(1.6em * 3);
    word-break: break-word;
}

.handbook-card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6B7280;
    margin-top: auto;
}

.handbook-card-date {
    display: flex;
    align-items: center;
}

.handbook-card-date i {
    margin-right: 0.35rem;
    color: #F37321;
}

.handbook-card-views {
    display: flex;
    align-items: center;
}

.handbook-card-views i {
    margin-right: 0.35rem;
    color: #3B82F6;
}

/* View All Button */
.handbook-view-all {
    text-align: center;
    margin-top: 2rem;
}

.handbook-view-all-button {
    display: inline-flex;
    align-items: center;
    background: white;
    color: #1F2937;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
}

.handbook-view-all-button:hover {
    background: #F8FAFC;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: #D1D5DB;
}

.handbook-view-all-button i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.handbook-view-all-button:hover i {
    transform: translateX(3px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .handbook-container {
        max-width: 960px;
    }

    .handbook-heading {
        font-size: 2.2rem;
    }

    .handbook-featured {
        min-height: 350px;
    }

    .handbook-featured-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 992px) {
    .handbook-container {
        max-width: 720px;
    }

    .handbook-heading {
        font-size: 2rem;
    }

    .handbook-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .handbook-featured {
        grid-template-columns: 1fr 1fr;
    }

    .handbook-featured-content {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .interior-handbook-section {
        padding: 3rem 0;
    }

    .handbook-container {
        max-width: 540px;
    }

    .handbook-heading {
        font-size: 1.8rem;
    }

    .handbook-description {
        font-size: 0.95rem;
    }

    .handbook-featured {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .handbook-featured-image {
        height: 250px;
    }

    .handbook-featured-content {
        padding: 1.5rem;
    }

    .handbook-featured-title {
        font-size: 1.4rem;
    }

    .handbook-featured-excerpt {
        margin-bottom: 1rem;
    }

    .handbook-featured-meta {
        margin-bottom: 1rem;
    }

    .handbook-grid {
        gap: 1rem;
    }

    .handbook-card-title {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .interior-handbook-section {
        padding: 2.5rem 0;
    }

    .handbook-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .handbook-heading {
        font-size: 1.5rem;
    }

    .handbook-description {
        font-size: 0.9rem;
    }

    .handbook-grid {
        grid-template-columns: 1fr;
    }

    .handbook-featured-image {
        height: 200px;
    }

    .handbook-featured-content {
        padding: 1.25rem;
    }

    .handbook-featured-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .handbook-featured-excerpt {
        font-size: 0.9rem;
        line-height: 1.5;
        -webkit-line-clamp: 4;
        max-height: calc(1.5em * 4);
    }

    .handbook-featured-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .handbook-featured-author {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .handbook-card {
        margin-bottom: 1rem;
    }

    .handbook-card-image {
        height: 180px;
    }

    .handbook-card-content {
        padding: 1.25rem;
    }

    .handbook-card-title {
        font-size: 1rem;
    }

    .handbook-card-excerpt {
        font-size: 0.85rem;
        line-height: 1.5;
        -webkit-line-clamp: 3;
        max-height: calc(1.5em * 3);
    }

    .handbook-view-all-button {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 375px) {
    .handbook-heading {
        font-size: 1.4rem;
    }

    .handbook-featured-image {
        height: 180px;
    }

    .handbook-featured-content {
        padding: 1rem;
    }

    .handbook-featured-title {
        font-size: 1.15rem;
    }

    .handbook-featured-excerpt {
        font-size: 0.85rem;
    }

    .handbook-card-image {
        height: 160px;
    }

    .handbook-card-content {
        padding: 1rem;
    }

    .handbook-card-title {
        font-size: 0.95rem;
    }

    .handbook-card-excerpt {
        font-size: 0.8rem;
    }

    .handbook-card-meta {
        font-size: 0.7rem;
    }

    .handbook-featured-button {
        width: 100%;
        justify-content: center;
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

/* Màn hình cực nhỏ */
@media (max-width: 320px) {
    .handbook-heading {
        font-size: 1.3rem;
    }

    .handbook-description {
        font-size: 0.85rem;
    }

    .handbook-featured-image {
        height: 160px;
    }

    .handbook-featured-title {
        font-size: 1.1rem;
    }

    .handbook-featured-excerpt {
        font-size: 0.8rem;
        line-height: 1.4;
        margin-bottom: 0.75rem;
    }

    .handbook-featured-meta {
        font-size: 0.7rem;
    }

    .handbook-featured-author-avatar {
        width: 1.75rem;
        height: 1.75rem;
    }

    .handbook-card-image {
        height: 140px;
    }

    .handbook-card-title {
        font-size: 0.9rem;
    }

    .handbook-card-excerpt {
        font-size: 0.75rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }

    .handbook-card-content {
        padding: 0.75rem;
    }

    .handbook-view-all-button {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

/* Thêm một số hiệu ứng tương tác cho thiết bị cảm ứng */
@media (hover: none) {
    .handbook-card:hover {
        transform: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .handbook-card:active {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
    }

    .handbook-featured-button:active,
    .handbook-view-all-button:active {
        transform: translateY(-2px);
    }

    .handbook-card:hover .handbook-card-image img,
    .handbook-featured:hover .handbook-featured-image img {
        transform: none;
    }

    .handbook-card:active .handbook-card-image img {
        transform: scale(1.03);
    }

    .handbook-featured:active .handbook-featured-image img {
        transform: scale(1.03);
    }
}
