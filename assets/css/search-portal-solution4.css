/**
 * <PERSON><PERSON><PERSON>i pháp 4: Portal Pattern
 * Di chuyển search suggestions ra ngoài DOM tree và quản lý bằng JavaScript portal pattern
 */

/*
 * CSS Variables cho solution 4
 */
:root {
    --search-portal-z-index: 99999;
    --search-transition-duration: 0.25s;
    --search-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --search-portal-backdrop: rgba(0, 0, 0, 0.1);
}

/*
 * Search container - giữ nguyên overflow hidden
 */
.search-container-solution4 {
    position: relative;
    z-index: 100;
}

/*
 * Search input styling
 */
.search-input-solution4 {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all var(--search-transition-duration) var(--search-transition-timing);
    background: white;
    outline: none;
    position: relative;
    z-index: 1;
}

.search-input-solution4:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/*
 * Portal container - được append vào body
 */
.search-portal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--search-portal-z-index);
    pointer-events: none; /* Cho phép click through */

    /* Không hiển thị backdrop mặc định */
    background: transparent;

    /* Performance optimizations */
    will-change: auto;
    backface-visibility: hidden;
}

/*
 * Portal backdrop (optional)
 */
.search-portal-container.with-backdrop {
    background: var(--search-portal-backdrop);
    pointer-events: auto;
}

/*
 * Search suggestions trong portal
 */
.search-suggestions-portal {
    position: absolute;
    z-index: 1;
    pointer-events: auto;

    /* Style cơ bản */
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    min-width: 300px;

    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all var(--search-transition-duration) var(--search-transition-timing);

    /* Đảm bảo không bị clip */
    contain: none !important;

    /* Performance optimizations */
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/*
 * Khi suggestions được hiển thị
 */
.search-suggestions-portal.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/*
 * Enhanced suggestion items
 */
.search-suggestion-item-portal {
    padding: 14px 16px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 14px;
    position: relative;
    overflow: hidden;
}

.search-suggestion-item-portal:last-child {
    border-bottom: none;
}

.search-suggestion-item-portal::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.1), transparent);
    transition: left 0.5s ease;
}

.search-suggestion-item-portal:hover::before {
    left: 100%;
}

.search-suggestion-item-portal:hover {
    background-color: #fef3c7;
    transform: translateX(6px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.search-suggestion-item-portal:active {
    background-color: #fde68a;
    transform: translateX(3px) scale(0.98);
}

/*
 * Enhanced suggestion content
 */
.search-suggestion-icon-portal {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    background: #f3f4f6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.search-suggestion-item-portal:hover .search-suggestion-icon-portal {
    background: #f59e0b;
    color: white;
    transform: scale(1.1);
}

.search-suggestion-text-portal {
    flex: 1;
    font-size: 15px;
    color: #374151;
    font-weight: 500;
    line-height: 1.4;
}

.search-suggestion-category-portal {
    font-size: 12px;
    color: #9ca3af;
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.search-suggestion-item-portal:hover .search-suggestion-category-portal {
    background: #fed7aa;
    color: #ea580c;
}

/*
 * Loading state trong portal
 */
.search-suggestions-portal.loading {
    opacity: 0.8;
}

.search-loading-item-portal {
    padding: 14px 16px;
    display: flex;
    align-items: center;
    gap: 14px;
}

.search-loading-skeleton-portal {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer-portal 1.5s infinite;
    border-radius: 6px;
}

.search-loading-icon-portal {
    width: 24px;
    height: 24px;
}

.search-loading-text-portal {
    height: 18px;
    flex: 1;
}

@keyframes loading-shimmer-portal {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/*
 * Scrollbar styling cho portal
 */
.search-suggestions-portal::-webkit-scrollbar {
    width: 8px;
}

.search-suggestions-portal::-webkit-scrollbar-track {
    background: rgba(243, 115, 33, 0.05);
    border-radius: 4px;
}

.search-suggestions-portal::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #FF9D5C, #F37321);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.search-suggestions-portal::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #F37321, #D35400);
}

/*
 * Responsive adjustments
 */
@media (max-width: 768px) {
    .search-suggestions-portal {
        max-height: 70vh;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        min-width: 280px;
        max-width: calc(100vw - 32px);
    }

    .search-suggestion-item-portal {
        padding: 16px;
        font-size: 16px; /* Tránh zoom trên iOS */
    }

    .search-suggestion-text-portal {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .search-suggestions-portal {
        max-height: 60vh;
        border-radius: 8px;
        min-width: 260px;
    }

    .search-suggestion-item-portal {
        padding: 14px 12px;
        gap: 12px;
    }

    .search-suggestion-icon-portal {
        width: 20px;
        height: 20px;
    }
}

/*
 * Accessibility improvements
 */
.search-suggestions-portal[aria-hidden="false"] {
    opacity: 1 !important;
    visibility: visible !important;
}

.search-suggestion-item-portal:focus {
    outline: 3px solid #f59e0b;
    outline-offset: 2px;
    background-color: #fef3c7;
}

.search-suggestion-item-portal[aria-selected="true"] {
    background-color: #fef3c7;
    border-left: 4px solid #f59e0b;
}

/*
 * High contrast mode support
 */
@media (prefers-contrast: high) {
    .search-suggestions-portal {
        border: 3px solid #000;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
    }

    .search-suggestion-item-portal:hover {
        background-color: #000;
        color: #fff;
    }

    .search-suggestion-item-portal:hover .search-suggestion-icon-portal {
        background: #fff;
        color: #000;
    }
}

/*
 * Reduced motion support
 */
@media (prefers-reduced-motion: reduce) {
    .search-suggestions-portal {
        transition: opacity 0.1s ease;
        transform: none !important;
    }

    .search-suggestion-item-portal {
        transition: background-color 0.1s ease;
        transform: none !important;
    }

    .search-suggestion-item-portal::before {
        display: none;
    }

    .search-loading-skeleton-portal {
        animation: none;
        background: #e0e0e0;
    }
}

/*
 * Dark mode support
 */
@media (prefers-color-scheme: dark) {
    .search-suggestions-portal {
        background: #1f2937;
        border-color: #374151;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    }

    .search-suggestion-item-portal {
        border-bottom-color: #374151;
        color: #f9fafb;
    }

    .search-suggestion-item-portal:hover {
        background-color: #374151;
    }

    .search-suggestion-text-portal {
        color: #f9fafb;
    }

    .search-suggestion-category-portal {
        background: #374151;
        color: #d1d5db;
    }

    .search-suggestion-icon-portal {
        background: #374151;
        color: #d1d5db;
    }
}

/*
 * Print styles
 */
@media print {
    .search-portal-container {
        display: none !important;
    }
}

/*
 * Animation variants
 */
.search-suggestions-portal.fade-in {
    animation: portalFadeIn var(--search-transition-duration) var(--search-transition-timing) forwards;
}

.search-suggestions-portal.slide-up {
    animation: portalSlideUp var(--search-transition-duration) var(--search-transition-timing) forwards;
}

@keyframes portalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes portalSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}