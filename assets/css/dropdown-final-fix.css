/**
 * DROPDOWN FINAL FIX CSS
 * File CSS cuối cùng để ghi đè TẤT CẢ các quy tắc positioning của dropdown
 * Đ<PERSON><PERSON> bảo tất cả 3 dropdown có cùng vị trí hiển thị
 */

/* Chỉ áp dụng cho desktop */
@media (min-width: 769px) {
    
    /* ===== RESET POSITIONING - ĐIỀU CHỈNH RIÊNG TỪNG DROPDOWN ===== */

    /* User dropdown và mini-cart - giữ nguyên vị trí hiện tại (đã đúng) */
    .user-dropdown-menu,
    .mini-cart {
        /* Vị trí hiện tại đã đúng, chỉ reset transform */
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        transform: scale(0.95) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1) !important;
        z-index: 1000 !important;
    }

    /* Mega-menu - điều chỉnh để gần header như 2 box kia */
    .mega-menu {
        /* Đặt mega-menu gần header hơn */
        position: absolute !important;
        top: calc(100% + 5px) !important;  /* Gần header hơn */
        left: 0 !important;

        /* Reset tất cả margin và transform */
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        transform: scale(0.95) !important;

        /* Đảm bảo opacity và visibility đồng nhất */
        opacity: 0 !important;
        visibility: hidden !important;

        /* Transition đồng nhất */
        transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1) !important;

        /* Z-index đồng nhất */
        z-index: 1000 !important;
    }
    
    /* ===== HIỆU ỨNG KHI HOVER ===== */
    
    /* Mega Menu Hover */
    .nav-item:hover .mega-menu,
    .mega-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* User Dropdown Hover */
    .user-dropdown:hover .user-dropdown-menu,
    .user-dropdown-menu:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Mini Cart Hover */
    .cart-container:hover .mini-cart,
    .mini-cart:hover {
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* ===== ARROW POSITIONING ===== */
    .mega-menu::before,
    .user-dropdown-menu::before,
    .mini-cart::before {
        top: -5px !important;
        width: 10px !important;
        height: 10px !important;
        transform: rotate(45deg) !important;
        z-index: 1 !important;
    }
    
    /* Arrow cho mega-menu (bên trái) */
    .mega-menu::before {
        left: 20px !important;
        right: auto !important;
    }
    
    /* Arrow cho user-dropdown và mini-cart (bên phải) */
    .user-dropdown-menu::before,
    .mini-cart::before {
        right: 20px !important;
        left: auto !important;
    }
    
    /* ===== ĐẢM BẢO KHÔNG CÓ XUNG ĐỘT ===== */
    
    /* Ghi đè hoàn toàn mega-menu positioning từ mega-menu.css */
    .mega-menu {
        left: 0 !important;
    }
    
    /* Ghi đè hoàn toàn user-dropdown positioning từ premium-header.css */
    .user-dropdown-menu {
        right: 0 !important;
    }
    
    /* Ghi đè hoàn toàn mini-cart positioning từ premium-header.css */
    .mini-cart {
        right: 0 !important;
    }
}

/* ===== DEBUG - TẮT ===== */
/* Debug mode đã được tắt - các dropdown đã có vị trí đồng nhất */
