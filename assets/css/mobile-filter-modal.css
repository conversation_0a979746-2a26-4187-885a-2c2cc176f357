/* ===== MOBILE FILTER MODAL STYLES ===== */

/* Mobile Filter Button Container */
.mobile-filter-container {
    display: none;
    width: 100%;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

/* Mobile Filter Button - Enhanced */
.mobile-filter-btn {
    position: relative;
    z-index: 10;
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 20px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    overflow: hidden;
}

/* Gradient animation overlay */
.mobile-filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.mobile-filter-btn:hover::before {
    left: 100%;
}

.mobile-filter-btn:hover {
    transform: translate3d(0, -3px, 0) scale(1.02);
    box-shadow: 0 12px 30px rgba(249, 115, 22, 0.4);
}

.mobile-filter-btn:active {
    transform: translate3d(0, -1px, 0) scale(0.95);
    transition-duration: 0.1s;
}

/* Click animation class */
.mobile-filter-btn.clicking {
    transform: translate3d(0, 0, 0) scale(0.95);
    transition: transform 0.15s ease;
}

/* Filter icon animation - Option 4: Scale + Shadow */
.mobile-filter-btn .filter-icon {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
    will-change: transform;
    transform: scale(1);
}

/* Default state hover */
.mobile-filter-btn:not(.has-filters):hover .filter-icon {
    transform: scale(1.15);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Default state active */
.mobile-filter-btn:not(.has-filters):active .filter-icon {
    transform: scale(0.95);
    transition-duration: 0.1s;
}

/* Filter text với hiệu ứng */
.mobile-filter-btn .filter-text {
    transition: all 0.3s ease;
    position: relative;
}

.mobile-filter-btn .filter-text.updated {
    animation: textPulse 0.4s ease;
}

/* Hiệu ứng khi có filters - Giống y hệt nút "Áp dụng bộ lọc" */
.mobile-filter-btn.has-filters {
    background: linear-gradient(to right, #f97316, #ea580c);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

/* Hiệu ứng shimmer giống nút gốc */
.mobile-filter-btn.has-filters::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transform: skewX(-12deg);
    transition: transform 0.7s;
}

.mobile-filter-btn.has-filters:hover::before {
    transform: translateX(200%) skewX(-12deg);
}

/* Has filters state - Icon styling */
.mobile-filter-btn.has-filters .filter-icon {
    color: white;
    transform: scale(1.05);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
}

.mobile-filter-btn.has-filters .filter-text {
    font-weight: 500;
    color: white;
    font-size: 14px;
}

/* Has filters hover - Scale + Shadow animation */
.mobile-filter-btn.has-filters:hover {
    background: linear-gradient(to right, #ea580c, #c2410c);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translate3d(0, -2px, 0) scale(1.02);
}

.mobile-filter-btn.has-filters:hover .filter-icon {
    transform: scale(1.2);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* Has filters active state */
.mobile-filter-btn.has-filters:active .filter-icon {
    transform: scale(0.95);
    transition-duration: 0.1s;
}

/* Smooth state transitions */
.mobile-filter-btn {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-filter-btn.has-filters {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* GPU optimization for Scale + Shadow animation */
.mobile-filter-btn .filter-icon {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, filter;
}

/* Enhanced GPU acceleration for has-filters state */
.mobile-filter-btn.has-filters .filter-icon {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, filter;
}

/* Filter description */
.mobile-filter-description {
    margin-top: 8px;
    text-align: center;
    font-size: 12px;
    color: #6b7280;
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.mobile-filter-description i {
    font-size: 10px;
    opacity: 0.6;
}

.mobile-filter-btn .filter-icon {
    margin-right: 8px;
    font-size: 16px;
}

.mobile-filter-btn .filter-count {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 2px 8px;
    margin-left: 8px;
    font-size: 12px;
    font-weight: 700;
}

/* Modal Overlay - GPU Optimized */
.filter-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 99998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.25s ease;
    will-change: opacity;
    transform: translateZ(0); /* GPU layer */
}

.filter-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Modal Container - GPU Optimized */
.filter-modal {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 400px;
    height: 100vh;
    background: white;
    z-index: 99999;
    transform: translate3d(100%, 0, 0); /* GPU acceleration */
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    will-change: transform;
    backface-visibility: hidden; /* GPU optimization */
}

.filter-modal.active {
    transform: translate3d(0, 0, 0);
}

/* Modal Header - Enhanced Design */
.filter-modal-header {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
    border-bottom: 1px solid #fed7aa;
    padding: 20px 24px;
    z-index: 10;
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.08);
    backdrop-filter: blur(10px);
}

.filter-modal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.filter-modal-title h2 {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    letter-spacing: -0.025em;
}

.filter-modal-title .title-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f97316, #ea580c);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
    position: relative;
    overflow: hidden;
}

.filter-modal-title .title-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.filter-modal-title .title-icon:hover::before {
    left: 100%;
}

.filter-modal-title .title-icon i {
    color: white;
    font-size: 16px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.filter-modal-close {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.filter-modal-close::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.filter-modal-close:hover {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-color: #fca5a5;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.filter-modal-close:hover::before {
    opacity: 1;
}

.filter-modal-close:active {
    transform: scale(0.95);
    transition-duration: 0.1s;
}

.filter-modal-close i {
    color: #6b7280;
    font-size: 18px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.filter-modal-close:hover i {
    color: #dc2626;
    transform: rotate(90deg);
}

/* Modal Content */
.filter-modal-content {
    padding: 0;
    flex: 1;
    overflow-y: auto;
}

/* Sidebar trong modal */
.filter-modal-content .sidebar-filters {
    display: block !important;
    border: none;
    border-radius: 0;
    box-shadow: none;
    background: transparent;
    overflow: visible;
}

/* Điều chỉnh header sidebar trong modal */
.filter-modal-content .sidebar-filters > div:first-child {
    display: none; /* Ẩn header gốc vì đã có header modal */
}

/* Đảm bảo content sidebar hiển thị đúng */
.filter-modal-content .sidebar-filters .p-6.space-y-6 {
    padding: 20px;
}

/* Tối ưu hiệu ứng dropdown trong modal */
.filter-modal-content .filter-section-visible,
.filter-modal-content .filter-section-hidden,
.filter-modal-content .subcategory-visible,
.filter-modal-content .subcategory-hidden {
    transform: translateZ(0); /* GPU layer */
    backface-visibility: hidden;
}

/* Đảm bảo staggered animation hoạt động trong modal */
.filter-modal-content .filter-section-visible > *,
.filter-modal-content .subcategory-visible > * {
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* Smooth scrolling trong modal khi có nhiều content */
.filter-modal-content {
    scroll-behavior: smooth;
}

/* Tối ưu chevron trong modal */
.filter-modal-content .fas.fa-chevron-right {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Ẩn các nút không cần thiết trong modal content vì đã có ở footer */
.filter-modal-content #resetFilters {
    display: none !important;
}

/* Ẩn header của sidebar trong modal */
.filter-modal-content .sidebar-filters > .px-8.py-6.bg-white.border-b.border-gray-200 {
    display: none !important;
}

/* Ẩn phần Apply Filters buttons trong modal content */
.filter-modal-content #applyFilters {
    display: none !important;
}

.filter-modal-content #resetFiltersBtn {
    display: none !important;
}

/* Ẩn container chứa các nút apply/reset filters */
.filter-modal-content #applyFilters:parent,
.filter-modal-content div:has(#applyFilters) {
    display: none !important;
}

/* Modal Footer - Enhanced Design */
.filter-modal-footer {
    position: sticky;
    bottom: 0;
    background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
    border-top: 1px solid #fed7aa;
    padding: 20px 24px;
    display: flex;
    gap: 16px;
    box-shadow: 0 -4px 20px rgba(249, 115, 22, 0.08);
    backdrop-filter: blur(10px);
}

.filter-modal-footer .btn {
    flex: 1;
    padding: 14px 20px;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    letter-spacing: -0.025em;
    /* Prevent layout shifts during loading */
    min-height: 48px;
    box-sizing: border-box;
}

.filter-modal-footer .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.filter-modal-footer .btn:hover::before {
    left: 100%;
}

/* Reset button - Simple and clean design */
.filter-modal-footer .btn-secondary {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
    font-weight: 500;
    transition: all 0.2s ease;
}

.filter-modal-footer .btn-secondary:hover {
    background: #f1f5f9;
    color: #475569;
    border-color: #cbd5e1;
}

.filter-modal-footer .btn-secondary:active {
    background: #e2e8f0;
}

.filter-modal-footer .btn-primary {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
    border: 1px solid #ea580c;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
}

.filter-modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #ea580c, #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(249, 115, 22, 0.4);
    border-color: #dc2626;
}

.filter-modal-footer .btn-primary:active {
    transform: translateY(0);
    transition-duration: 0.1s;
}

.filter-modal-footer .btn i {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.filter-modal-footer .btn:hover i {
    transform: scale(1.1);
}

/* Header và Footer Animation Effects */
.filter-modal-header {
    animation: slideInFromTop 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.filter-modal-footer {
    animation: slideInFromBottom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Title Icon Animation */
.filter-modal-title .title-icon {
    animation: iconBounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes iconBounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) rotate(-180deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.1) rotate(-90deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* Button Ripple Effect */
.filter-modal-footer .btn {
    position: relative;
    overflow: hidden;
}

.filter-modal-footer .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.filter-modal-footer .btn:active::after {
    width: 300px;
    height: 300px;
}

/* Simple Loading States - No complex animations */
.filter-modal-footer .btn.loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.filter-modal-footer .btn.loading:hover {
    transform: none !important;
    box-shadow: inherit !important;
}

/* Simple spinner - no layout shifts */
.filter-modal-footer .btn .btn-spinner {
    display: none;
    margin-left: 6px;
}

.filter-modal-footer .btn.loading .btn-spinner {
    display: inline-block;
}

.filter-modal-footer .btn .btn-spinner i {
    font-size: 12px;
    animation: simpleRotate 1s linear infinite;
}

@keyframes simpleRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Disable all decorative effects when loading */
.filter-modal-footer .btn.loading::before,
.filter-modal-footer .btn.loading::after {
    display: none !important;
}

/* Responsive Breakpoints */
@media (max-width: 1023px) {
    /* Hiển thị container filter trên tablet */
    .mobile-filter-container {
        display: block;
    }

    /* Ẩn sidebar gốc trên tablet */
    .sidebar-filters {
        display: none !important;
    }
}

/* Tablet specific styles (769px - 1023px) */
@media (min-width: 769px) and (max-width: 1023px) {
    .filter-modal {
        max-width: 420px; /* Slightly wider for tablet */
    }

    .filter-modal-footer {
        padding: 18px 22px; /* Consistent padding */
        gap: 14px; /* Consistent gap */
    }

    .filter-modal-footer .btn {
        padding: 13px 18px; /* Consistent button padding */
        font-size: 14px; /* Consistent font size */
        font-weight: 600;
        border-radius: 10px; /* Slightly smaller border radius */
        flex: 1; /* Ensure buttons take equal space */
        min-height: 48px; /* Consistent height */
        white-space: nowrap; /* Prevent text wrapping */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .filter-modal-footer .btn i {
        font-size: 13px; /* Consistent icon size */
    }

    .filter-modal-footer .btn span {
        font-size: 14px; /* Ensure text size consistency */
    }

    /* Ensure modal header is also consistent */
    .filter-modal-header {
        padding: 18px 22px;
    }

    .filter-modal-title h2 {
        font-size: 19px; /* Consistent title size */
    }
}

@media (max-width: 768px) {
    /* Điều chỉnh modal cho mobile */
    .filter-modal {
        max-width: 100%;
        width: 100%;
    }

    .mobile-filter-btn {
        padding: 10px 16px;
        font-size: 13px;
        min-width: 100px;
    }

    .filter-modal-header {
        padding: 16px 20px;
    }

    .filter-modal-footer {
        padding: 16px 20px;
    }

    .filter-modal-title h2 {
        font-size: 18px;
    }

    .filter-modal-title .title-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
    }

    .filter-modal-close {
        width: 40px;
        height: 40px;
    }

    .filter-modal-footer .btn {
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .mobile-filter-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 80px;
    }

    .filter-modal-title h2 {
        font-size: 16px;
    }

    .filter-modal-title .title-icon {
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }

    .filter-modal-close {
        width: 32px;
        height: 32px;
    }
}

/* Animation cho filter text */
@keyframes textPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animation cho shimmer effect */
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Animation cho icon pulse */
@keyframes iconPulse {
    0%, 100% {
        transform: rotate(0deg) scale(1.1);
        opacity: 1;
    }
    50% {
        transform: rotate(0deg) scale(1.15);
        opacity: 0.8;
    }
}

/* Smooth scroll cho modal content */
.filter-modal {
    scroll-behavior: smooth;
}

.filter-modal::-webkit-scrollbar {
    width: 4px;
}

.filter-modal::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.filter-modal::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.filter-modal::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Prevent body scroll when modal is open */
body.filter-modal-open {
    overflow: hidden;
}

/* Prevent scroll chaining from modal to body */
.filter-modal {
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
}

.filter-modal-content {
    overscroll-behavior: contain;
}

/* Đảm bảo modal luôn ở trên cùng */
.filter-modal-overlay,
.filter-modal {
    z-index: 999999 !important;
}

/* Override any potential z-index conflicts */
.filter-modal-overlay.active,
.filter-modal.active {
    z-index: 999999 !important;
}

/* Loading state cho modal */
.filter-modal.loading .filter-modal-content {
    opacity: 0.6;
    pointer-events: none;
}

.filter-modal.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #f97316;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
