/*
 * Tablet Navigation Auto-scroll CSS for Nội Thất Bàng Vũ
 * Visual enhancements for auto-scroll functionality
 */

/* TABLET ONLY - Auto-scroll enhancements */
@media (min-width: 768px) and (max-width: 1200px) {
    
    /* Smooth scroll behavior for navigation menu */
    .nav-menu {
        scroll-behavior: smooth !important;
    }
    
    /* Enhanced active item styling during auto-scroll */
    .nav-item.active .nav-link {
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    }
    
    /* Subtle glow effect for active item when auto-scrolling */
    .nav-item.active .nav-link::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, rgba(249, 115, 22, 0.1), rgba(249, 115, 22, 0.05));
        border-radius: 8px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }
    
    /* Show glow when active item is being focused/scrolled to */
    .nav-item.active:focus-within .nav-link::before,
    .nav-item.active .nav-link:focus::before {
        opacity: 1;
    }
    
    /* Enhanced focus styles for keyboard navigation */
    .nav-link:focus {
        outline: 2px solid rgba(249, 115, 22, 0.5);
        outline-offset: 2px;
        border-radius: 4px;
    }
    
    /* Smooth transitions for all nav items during scroll */
    .nav-item {
        transition: transform 0.2s ease;
    }
    
    /* Subtle scale effect when item is being scrolled to */
    .nav-item:focus-within {
        transform: scale(1.02);
    }
    
    /* Arrow indicators enhancement during auto-scroll */
    .nav-scroll-arrow {
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    }
    
    /* Pulse effect for arrows when auto-scroll is happening */
    @keyframes arrow-pulse {
        0% { transform: translateY(-50%) scale(1); }
        50% { transform: translateY(-50%) scale(1.1); }
        100% { transform: translateY(-50%) scale(1); }
    }
    
    /* Apply pulse animation when arrows are active during auto-scroll */
    .nav-scroll-arrow.auto-scroll-active {
        animation: arrow-pulse 0.6s ease-in-out;
    }
    
    /* Smooth opacity transitions for arrow visibility */
    .nav-scroll-arrow.show {
        transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease !important;
    }
    
    /* Enhanced scrollbar styling for better visual feedback */
    .nav-menu::-webkit-scrollbar {
        height: 2px;
        display: block !important;
    }
    
    .nav-menu::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 1px;
    }
    
    .nav-menu::-webkit-scrollbar-thumb {
        background: rgba(249, 115, 22, 0.3);
        border-radius: 1px;
        transition: background 0.3s ease;
    }
    
    .nav-menu::-webkit-scrollbar-thumb:hover {
        background: rgba(249, 115, 22, 0.5);
    }
    
    /* Hide scrollbar during auto-scroll to avoid visual distraction */
    .nav-menu.auto-scrolling::-webkit-scrollbar {
        display: none !important;
    }
    
    /* Loading indicator for auto-scroll (optional) */
    .nav-menu.auto-scrolling::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.6), transparent);
        animation: loading-sweep 1s ease-in-out;
        z-index: 10;
        pointer-events: none;
    }
    
    @keyframes loading-sweep {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
    
    /* Ensure smooth transitions don't interfere with touch scrolling */
    .nav-menu.user-scrolling {
        scroll-behavior: auto !important;
    }
    
    .nav-menu.user-scrolling .nav-item {
        transition: none !important;
    }
}

/* DESKTOP - Ensure no interference */
@media (min-width: 1201px) {
    .nav-menu {
        scroll-behavior: auto;
    }
    
    .nav-item.active .nav-link::before {
        display: none;
    }
}
