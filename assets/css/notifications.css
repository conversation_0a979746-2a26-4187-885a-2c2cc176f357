/**
 * CSS cho thông báo hiện đại
 */

/* Container cho thông báo */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
    max-width: calc(100vw - 40px);
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
    transition: top 0.3s ease;
}

/* <PERSON>h<PERSON>ng báo c<PERSON> bản */
.notification {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.06);
    padding: 16px;
    margin-bottom: 10px;
    transform: translateX(120%);
    opacity: 0;
    animation: slide-in 0.4s ease-out forwards;
    position: relative;
    overflow: hidden;
    pointer-events: auto;
    display: flex;
    align-items: center;
    border-left: 4px solid transparent;
}

/* <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> đặc biệt cho thông báo giỏ hàng */
.notification.cart-notification {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-left-color: var(--primary-color, #f97316);
    box-shadow: 0 10px 25px rgba(249, 115, 22, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(-20px);
    opacity: 0;
    will-change: transform, opacity;
    animation: slide-down 0.4s ease-out forwards;
}

/* Các loại thông báo */
.notification.success {
    border-left-color: #10b981;
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.info {
    border-left-color: #3b82f6;
}

.notification.warning {
    border-left-color: #f59e0b;
}

/* Icon thông báo */
.notification-icon {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
    font-size: 16px;
}

/* Màu sắc icon theo loại thông báo */
.notification.cart-notification .notification-icon {
    background-color: rgba(249, 115, 22, 0.1);
    color: var(--primary-color, #f97316);
    animation: pulse-icon 1s ease-in-out;
}

.notification.success .notification-icon {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.notification.error .notification-icon {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.notification.info .notification-icon {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.notification.warning .notification-icon {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

/* Nội dung thông báo */
.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #1f2937;
}

.notification-message {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

.notification.cart-notification .notification-message {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14.5px;
}

.notification.cart-notification .text-primary {
    color: var(--primary-color, #f97316);
}

.notification.cart-notification .hover\:underline:hover {
    text-decoration: underline;
}

/* Nút đóng thông báo */
.notification-close {
    color: #9ca3af;
    font-size: 14px;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    color: #1f2937;
}

/* Thanh tiến trình */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: rgba(0, 0, 0, 0.05);
}

.notification-progress-bar {
    height: 100%;
    width: 100%;
    transform-origin: left;
    animation: progress 3s linear forwards;
}

.notification.cart-notification .notification-progress-bar {
    background-color: var(--primary-color, #f97316);
    animation: progress 3s linear forwards;
}

.notification.success .notification-progress-bar {
    background-color: #10b981;
}

.notification.error .notification-progress-bar {
    background-color: #ef4444;
}

.notification.info .notification-progress-bar {
    background-color: #3b82f6;
}

.notification.warning .notification-progress-bar {
    background-color: #f59e0b;
}

/* Hiệu ứng hiển thị */
.notification.show {
    transform: translateX(0);
    opacity: 1;
}

/* Animations */
@keyframes slide-in {
    0% {
        transform: translateX(120%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-down {
    0% {
        transform: translateY(-20px);
        opacity: 0;
        will-change: transform, opacity;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
        will-change: transform, opacity;
    }
}

@keyframes slide-out {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(120%);
        opacity: 0;
    }
}

@keyframes progress {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

@keyframes pulse-icon {
    0% {
        transform: scale(0.8);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .notification-container {
        width: 320px;
    }
    
    .notification {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .notification-container {
        right: 0.5rem;
        width: calc(100% - 30px);
    }
    
    .notification {
        padding: 14px;
    }
    
    .notification-icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
    }
}
