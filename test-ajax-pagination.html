<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Pagination</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Test AJAX Pagination System</h1>
    
    <div class="test-section">
        <h2>1. Test API Endpoint</h2>
        <p>Kiểm tra xem API endpoint có hoạt động không</p>
        <button onclick="testApiEndpoint()">Test API</button>
        <div id="api-test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test JavaScript Loading</h2>
        <p>Kiểm tra xem JavaScript có load đúng không</p>
        <button onclick="testJavaScriptLoading()">Test JS</button>
        <div id="js-test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test AJAX Request</h2>
        <p>Thử gửi AJAX request với các parameters khác nhau</p>
        <button onclick="testAjaxRequest(1)">Test Page 1</button>
        <button onclick="testAjaxRequest(2)">Test Page 2</button>
        <button onclick="testAjaxRequest(999)">Test Invalid Page</button>
        <div id="ajax-test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Error Handling</h2>
        <p>Kiểm tra xử lý lỗi</p>
        <button onclick="testErrorHandling()">Test Error</button>
        <div id="error-test-result"></div>
    </div>

    <div id="test-results">
        <h2>Test Results</h2>
        <div id="results-container"></div>
    </div>

    <script>
        // Set BASE_URL for testing
        window.BASE_URL = window.location.origin + '/noithatbangvu';

        function addResult(message, type = 'info') {
            const container = document.getElementById('results-container');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function testApiEndpoint() {
            const resultDiv = document.getElementById('api-test-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch(`${window.BASE_URL}/ajax-products.php?page=1`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">✅ API endpoint hoạt động! Status: ${response.status}</div>`;
                    addResult(`API test passed: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API error: ${response.status} ${response.statusText}</div>`;
                    addResult(`API test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                addResult(`API test error: ${error.message}`, 'error');
            }
        }

        function testJavaScriptLoading() {
            const resultDiv = document.getElementById('js-test-result');
            
            // Check if AjaxPagination class exists
            if (typeof AjaxPagination !== 'undefined') {
                resultDiv.innerHTML = '<div class="success">✅ JavaScript loaded successfully!</div>';
                addResult('JavaScript loading test passed', 'success');
            } else {
                resultDiv.innerHTML = '<div class="error">❌ JavaScript not loaded or AjaxPagination class not found</div>';
                addResult('JavaScript loading test failed', 'error');
            }
        }

        async function testAjaxRequest(page) {
            const resultDiv = document.getElementById('ajax-test-result');
            resultDiv.innerHTML = `<p>Testing page ${page}...</p>`;
            
            try {
                const url = `${window.BASE_URL}/ajax-products.php?page=${page}&items_per_page=12`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Page ${page} loaded: ${data.data.products_count} products</div>`;
                    addResult(`AJAX request for page ${page} successful: ${data.data.products_count} products`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Page ${page} failed: ${data.error}</div>`;
                    addResult(`AJAX request for page ${page} failed: ${data.error}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Request error: ${error.message}</div>`;
                addResult(`AJAX request error: ${error.message}`, 'error');
            }
        }

        async function testErrorHandling() {
            const resultDiv = document.getElementById('error-test-result');
            resultDiv.innerHTML = '<p>Testing error handling...</p>';
            
            try {
                // Test with invalid URL
                const response = await fetch(`${window.BASE_URL}/invalid-endpoint.php`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Error handling works: ${response.status}</div>`;
                    addResult(`Error handling test passed: ${response.status}`, 'success');
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Expected error but got success</div>';
                    addResult('Error handling test failed: expected error', 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="success">✅ Error caught: ${error.message}</div>`;
                addResult(`Error handling test passed: ${error.message}`, 'success');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            addResult('Test page loaded', 'info');
            setTimeout(() => {
                testJavaScriptLoading();
            }, 1000);
        });
    </script>
</body>
</html>
