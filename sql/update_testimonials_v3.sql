-- <PERSON><PERSON><PERSON> tra và thêm cột review_photos nếu chưa tồn tại
SET @dbname = DATABASE();
SET @tablename = "testimonials";
SET @columnname = "review_photos";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  "SELECT 1",
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " TEXT NULL AFTER `customer_photo`")
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- <PERSON><PERSON><PERSON> tra và thêm cột review_videos nếu chưa tồn tại
SET @dbname = DATABASE();
SET @tablename = "testimonials";
SET @columnname = "review_videos";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  "SELECT 1",
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " TEXT NULL AFTER `review_photos`")
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Cập nhật cột customer_video để lưu trữ URL video thay vì ID YouTube
ALTER TABLE `testimonials` 
MODIFY COLUMN `customer_video` TEXT NULL;
