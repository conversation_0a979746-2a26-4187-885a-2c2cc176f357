<?php
// File test để kiểm tra tính năng nhân bản
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    echo "Bạn cần đăng nhập với quyền admin để test tính năng này.";
    exit;
}

echo "<h2>Test tính năng nhân bản sản phẩm</h2>";

// Lấy một sản phẩm để test
$products = get_products(['limit' => 1]);
if (empty($products)) {
    echo "<p>Không có sản phẩm nào để test. Vui lòng tạo sản phẩm trước.</p>";
    exit;
}

$test_product = $products[0];
echo "<h3>Sản phẩm test: " . htmlspecialchars($test_product['name']) . " (ID: {$test_product['id']})</h3>";

// Test hàm duplicate_product_with_suffix
echo "<h4>Test nhân bản với suffix:</h4>";

// Kiểm tra xem hàm có tồn tại không
if (function_exists('duplicate_product_with_suffix')) {
    echo "<p style='color: green;'>✓ Hàm duplicate_product_with_suffix đã được định nghĩa trong file AJAX</p>";
} else {
    echo "<p style='color: red;'>✗ Hàm duplicate_product_with_suffix chưa được định nghĩa</p>";
}

// Test hàm duplicate_file
if (function_exists('duplicate_file')) {
    echo "<p style='color: green;'>✓ Hàm duplicate_file đã tồn tại</p>";
} else {
    echo "<p style='color: red;'>✗ Hàm duplicate_file không tồn tại</p>";
}

// Test hàm get_product_by_id
$product_test = get_product_by_id($test_product['id']);
if ($product_test) {
    echo "<p style='color: green;'>✓ Hàm get_product_by_id hoạt động tốt</p>";
} else {
    echo "<p style='color: red;'>✗ Hàm get_product_by_id có vấn đề</p>";
}

// Test hàm add_product
echo "<p style='color: blue;'>ℹ Để test đầy đủ, bạn cần truy cập trang products.php và thử nhân bản sản phẩm qua giao diện</p>";

echo "<hr>";
echo "<p><a href='products.php'>← Quay lại trang quản lý sản phẩm</a></p>";
?>
