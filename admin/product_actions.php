<?php
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

$action = isset($_GET['action']) ? trim($_GET['action']) : null;
$product_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$csrf_token = isset($_GET['token']) ? trim($_GET['token']) : null;

// Lấy query string trước đó để redirect lại đúng bộ lọc
$redirect_url = BASE_URL . '/admin/products.php';
$query_params = [];
if (isset($_SESSION['last_products_query'])) {
    parse_str($_SESSION['last_products_query'], $query_params);
}
// Thêm auto_scroll parameter để trigger auto-scroll
$query_params['auto_scroll'] = '1';
$redirect_url .= '?' . http_build_query($query_params);

// Kiểm tra CSRF token
if (!check_csrf_token($csrf_token)) {
    set_flash_message('error', 'CSRF token không hợp lệ hoặc đã hết hạn. Vui lòng thử lại.');
    redirect($redirect_url);
}

if (empty($action) || $product_id <= 0) {
    set_flash_message('error', 'Hành động hoặc ID sản phẩm không hợp lệ.');
    redirect($redirect_url);
}

switch ($action) {
    case 'duplicate':
        $result = duplicate_product($product_id);
        if ($result['success']) {
            set_flash_message('success', 'Nhân bản sản phẩm thành công. Sản phẩm mới đã được tạo với cùng trạng thái như sản phẩm gốc.');
        } else {
            set_flash_message('error', 'Lỗi nhân bản sản phẩm: ' . ($result['message'] ?? 'Không rõ lỗi'));
        }
        break;

    case 'toggle_featured':
        $product = get_product_by_id($product_id);
        if ($product) {
            $new_featured_status = $product['featured'] == 1 ? 0 : 1;
            $update_data = ['featured' => $new_featured_status];

            // Chỉ cập nhật trường featured, giữ nguyên các trường khác
            // Để làm điều này một cách an toàn, ta nên có một hàm update_product_field riêng
            // Hoặc là truyền toàn bộ dữ liệu của product và chỉ thay đổi featured
            // Tạm thời, ta sẽ cập nhật trực tiếp bằng SQL để đơn giản hoá, nhưng cần cẩn trọng
            global $conn;
            try {
                $stmt = $conn->prepare("UPDATE products SET featured = :featured WHERE id = :id");
                $stmt->bindParam(':featured', $new_featured_status, PDO::PARAM_INT);
                $stmt->bindParam(':id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                set_flash_message('success', 'Đã cập nhật trạng thái nổi bật của sản phẩm.');
            } catch (PDOException $e) {
                error_log("Lỗi khi cập nhật featured status: " . $e->getMessage());
                set_flash_message('error', 'Lỗi cập nhật trạng thái nổi bật: ' . $e->getMessage());
            }
        } else {
            set_flash_message('error', 'Sản phẩm không tồn tại.');
        }
        break;

    default:
        set_flash_message('error', 'Hành động không được hỗ trợ.');
        break;
}

redirect($redirect_url);
?>