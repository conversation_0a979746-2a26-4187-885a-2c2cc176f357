<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

$page_title = 'Chạy Migration - Thêm SKU';

// Xử lý chạy migration
$migration_result = '';
$migration_success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        // Đọc file migration
        $migration_file = '../migrations/add_sku_to_products.sql';
        
        if (!file_exists($migration_file)) {
            throw new Exception('File migration không tồn tại.');
        }
        
        $sql_content = file_get_contents($migration_file);
        
        // <PERSON>hay thế PHP code trong SQL
        $sql_content = str_replace('<?php echo date(\'Y-m-d H:i:s\'); ?>', date('Y-m-d H:i:s'), $sql_content);
        
        // Tách các câu lệnh SQL
        $sql_statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        $results = [];
        
        foreach ($sql_statements as $sql) {
            if (empty($sql) || strpos($sql, '--') === 0) {
                continue; // Bỏ qua comment và dòng trống
            }
            
            try {
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                
                // Nếu là câu lệnh SELECT, lấy kết quả
                if (stripos($sql, 'SELECT') === 0) {
                    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    $results[] = [
                        'sql' => $sql,
                        'type' => 'SELECT',
                        'data' => $result
                    ];
                } else {
                    $results[] = [
                        'sql' => $sql,
                        'type' => 'EXECUTE',
                        'affected_rows' => $stmt->rowCount()
                    ];
                }
            } catch (PDOException $e) {
                // Nếu lỗi là "column already exists", bỏ qua
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $results[] = [
                        'sql' => $sql,
                        'type' => 'SKIP',
                        'message' => 'Trường SKU đã tồn tại, bỏ qua.'
                    ];
                } else {
                    throw $e;
                }
            }
        }
        
        $migration_result = $results;
        $migration_success = true;
        
    } catch (Exception $e) {
        $migration_result = 'Lỗi: ' . $e->getMessage();
        $migration_success = false;
    }
}

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Chạy Migration - Thêm trường SKU</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Migration: Thêm trường SKU vào bảng products</h6>
                </div>
                <div class="card-body">
                    <?php if (!$migration_success && empty($migration_result)): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Thông tin Migration</h5>
                        <p>Migration này sẽ thực hiện các thao tác sau:</p>
                        <ul>
                            <li>Thêm trường <code>sku</code> vào bảng <code>products</code></li>
                            <li>Tự động tạo SKU cho các sản phẩm hiện có theo format: <strong>NTBV-SP001, NTBV-SP002, ...</strong></li>
                            <li>Tạo index cho trường SKU để tăng hiệu suất</li>
                            <li>Hiển thị danh sách sản phẩm với SKU mới</li>
                        </ul>
                        <p class="mb-0"><strong>Lưu ý:</strong> Nếu trường SKU đã tồn tại, migration sẽ bỏ qua việc tạo trường mới.</p>
                    </div>
                    
                    <form method="POST">
                        <button type="submit" name="run_migration" class="btn btn-primary" onclick="return confirm('Bạn có chắc chắn muốn chạy migration này?')">
                            <i class="fas fa-play"></i> Chạy Migration
                        </button>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách sản phẩm
                        </a>
                    </form>
                    <?php endif; ?>

                    <?php if ($migration_success): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Migration thành công!</h5>
                        <p>Trường SKU đã được thêm vào bảng products thành công.</p>
                    </div>

                    <h6>Kết quả thực thi:</h6>
                    <?php foreach ($migration_result as $result): ?>
                    <div class="card mb-2">
                        <div class="card-body">
                            <h6 class="card-title">
                                <?php if ($result['type'] === 'SELECT'): ?>
                                <span class="badge badge-info">SELECT</span>
                                <?php elseif ($result['type'] === 'EXECUTE'): ?>
                                <span class="badge badge-success">EXECUTE</span>
                                <?php else: ?>
                                <span class="badge badge-warning">SKIP</span>
                                <?php endif; ?>
                            </h6>
                            <code class="d-block mb-2"><?php echo htmlspecialchars($result['sql']); ?></code>
                            
                            <?php if ($result['type'] === 'SELECT' && !empty($result['data'])): ?>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <?php foreach (array_keys($result['data'][0]) as $column): ?>
                                            <th><?php echo htmlspecialchars($column); ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result['data'] as $row): ?>
                                        <tr>
                                            <?php foreach ($row as $value): ?>
                                            <td><?php echo htmlspecialchars($value); ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php elseif ($result['type'] === 'EXECUTE'): ?>
                            <small class="text-muted">Số dòng bị ảnh hưởng: <?php echo $result['affected_rows']; ?></small>
                            <?php elseif ($result['type'] === 'SKIP'): ?>
                            <small class="text-warning"><?php echo htmlspecialchars($result['message']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <div class="mt-3">
                        <a href="products.php" class="btn btn-success">
                            <i class="fas fa-list"></i> Xem danh sách sản phẩm
                        </a>
                        <a href="product-add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm sản phẩm mới
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (!$migration_success && !empty($migration_result) && is_string($migration_result)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Lỗi Migration</h5>
                        <p><?php echo htmlspecialchars($migration_result); ?></p>
                    </div>
                    
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại danh sách sản phẩm
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Hướng dẫn</h6>
                </div>
                <div class="card-body">
                    <h6>Format SKU:</h6>
                    <ul>
                        <li><strong>NTBV</strong> - Viết tắt "Nội Thất Bàng Vũ"</li>
                        <li><strong>SP</strong> - Viết tắt "Sản Phẩm"</li>
                        <li><strong>001, 002, ...</strong> - Số thứ tự tự động</li>
                    </ul>
                    
                    <h6>Ví dụ SKU:</h6>
                    <ul>
                        <li>NTBV-SP001</li>
                        <li>NTBV-SP002</li>
                        <li>NTBV-SP003</li>
                    </ul>
                    
                    <div class="alert alert-warning">
                        <small><strong>Lưu ý:</strong> Sau khi chạy migration, bạn có thể thêm/sửa sản phẩm với trường SKU mới.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once 'partials/footer.php'; ?>
