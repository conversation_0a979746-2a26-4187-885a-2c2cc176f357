<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Trang quản trị';

// Lấy thống kê đơn hàng hôm nay
$today = date('Y-m-d');
$yesterday = date('Y-m-d', strtotime('-1 day'));

// Đơn hàng hôm nay
$today_orders = count_orders_by_date($today);
$yesterday_orders = count_orders_by_date($yesterday);
$order_comparison = $today_orders - $yesterday_orders;

// Sản phẩm bán chạy nhất hôm nay
$best_selling_product = get_best_selling_product($today);

// Khách hàng mới hôm nay
$new_customers_today = count_new_customers_by_date($today);

// Include header
include_once 'partials/header.php';

// L<PERSON>y thống kê
$total_products = count_products();
$categories = get_categories();
$total_categories = count($categories);
$total_orders = count_orders();
$total_users = count_users(); // Sử dụng hàm count_users() để lấy tổng số người dùng
$pending_orders = count_orders_by_status('pending'); // Đếm đơn hàng chờ xử lý
$processing_orders = count_orders_by_status('processing'); // Đếm đơn hàng đang xử lý
$completed_orders = count_orders_by_status('completed'); // Đếm đơn hàng hoàn thành

// Lấy đơn hàng mới nhất
$latest_orders = get_orders(10); // Lấy 10 đơn hàng mới nhất

// Dữ liệu mẫu cho biểu đồ (sẽ cần thay thế bằng dữ liệu thật)
$revenue_data_points = [
    ["label" => "Tháng 1", "y" => 1500],
    ["label" => "Tháng 2", "y" => 2000],
    ["label" => "Tháng 3", "y" => 1800],
    ["label" => "Tháng 4", "y" => 2200],
    ["label" => "Tháng 5", "y" => 2500],
    ["label" => "Tháng 6", "y" => 2300],
];

$order_status_data_points = [
    ["label" => "Chờ xử lý", "y" => $pending_orders, "color" => "#ffc107"],
    ["label" => "Đang xử lý", "y" => $processing_orders, "color" => "#17a2b8"],
    ["label" => "Hoàn thành", "y" => $completed_orders, "color" => "#28a745"],
    // Thêm các trạng thái khác nếu có
];

?>
<style>
    /* Modern Dashboard Styles - Nội Thất Băng Vũ Theme */
    :root {
        /* Primary Colors - Cam chính từ brand */
        --primary: #F37321;
        --primary-dark: #D65A0F;
        --primary-darker: #D35400;
        --primary-light: #FF8A3D;
        --primary-lighter: #FFA66B;
        --primary-lightest: #FFD0AD;
        --primary-ultra-light: #FFF4EC;

        /* Secondary Colors - Xanh đậm */
        --secondary: #2A3B47;
        --secondary-dark: #1E2A32;
        --secondary-light: #435868;

        /* Accent Colors */
        --accent: #4CAF50;
        --accent-dark: #388E3C;
        --accent-light: #81C784;

        /* Status Colors */
        --success: #10B981;
        --warning: #F59E0B;
        --danger: #EF4444;
        --info: #3B82F6;

        /* Gradients với màu brand */
        --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
        --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
        --success-gradient: linear-gradient(135deg, #10B981 0%, #059669 100%);
        --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
        --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);

        /* Neutral Colors */
        --gray-50: #F9FAFB;
        --gray-100: #F3F4F6;
        --gray-200: #E5E7EB;
        --gray-300: #D1D5DB;
        --gray-400: #9CA3AF;
        --gray-500: #6B7280;
        --gray-600: #4B5563;
        --gray-700: #374151;
        --gray-800: #1F2937;
        --gray-900: #111827;

        /* Shadows */
        --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
        --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

        /* Border Radius */
        --border-radius: 1.25rem;
        --border-radius-sm: 0.5rem;
        --border-radius-md: 0.75rem;
        --border-radius-lg: 1rem;

        /* Transitions */
        --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        --transition-fast: all 0.2s ease;
    }

    /* Dashboard Header */
    .dashboard-header {
        background: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
        color: white;
        padding: 2.5rem;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
    }

    .dashboard-header::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
        border-radius: 50%;
    }

    .dashboard-header h1 {
        margin: 0;
        font-size: 2.25rem;
        font-weight: 600;
        position: relative;
        z-index: 1;
        letter-spacing: -0.02em;
    }

    .dashboard-subtitle {
        margin: 0.75rem 0 0 0;
        opacity: 0.8;
        font-size: 1rem;
        position: relative;
        z-index: 1;
        font-weight: 400;
        letter-spacing: 0.01em;
    }

    /* Modern Stats Cards - Redesigned */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.08),
            0 4px 6px -2px rgba(0, 0, 0, 0.03);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(243, 115, 33, 0.08);
    }

    .stat-card:hover {
        transform: translateY(-6px) scale(1.01);
        box-shadow:
            0 20px 25px -5px rgba(0, 0, 0, 0.12),
            0 10px 10px -5px rgba(0, 0, 0, 0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--primary-gradient);
        transition: all 0.4s ease;
        border-radius: 1.5rem 1.5rem 0 0;
    }

    .stat-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -30%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
        border-radius: 50%;
        transition: all 0.4s ease;
    }

    .stat-card:hover::after {
        transform: scale(1.2);
        opacity: 0.8;
    }

    .stat-card.products::before { background: var(--primary-gradient); }
    .stat-card.categories::before { background: var(--info-gradient); }
    .stat-card.orders::before { background: var(--warning-gradient); }
    .stat-card.users::before { background: var(--success-gradient); }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: white;
        background: var(--primary-gradient);
        box-shadow:
            0 6px 10px -2px rgba(243, 115, 33, 0.25),
            0 2px 4px -1px rgba(243, 115, 33, 0.15);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: inherit;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    .stat-card:hover .stat-icon {
        transform: scale(1.1) rotate(3deg);
        box-shadow:
            0 10px 15px -3px rgba(243, 115, 33, 0.35),
            0 4px 6px -2px rgba(243, 115, 33, 0.25);
    }

    .stat-card:hover .stat-icon::before {
        opacity: 1;
    }

    .stat-card.products .stat-icon {
        background: var(--primary-gradient);
        box-shadow: 0 10px 15px -3px rgba(243, 115, 33, 0.3), 0 4px 6px -2px rgba(243, 115, 33, 0.2);
    }
    .stat-card.categories .stat-icon {
        background: var(--info-gradient);
        box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3), 0 4px 6px -2px rgba(59, 130, 246, 0.2);
    }
    .stat-card.orders .stat-icon {
        background: var(--warning-gradient);
        box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.3), 0 4px 6px -2px rgba(245, 158, 11, 0.2);
    }
    .stat-card.users .stat-icon {
        background: var(--success-gradient);
        box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3), 0 4px 6px -2px rgba(16, 185, 129, 0.2);
    }

    .stat-content h3 {
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.08em;
        color: var(--gray-600);
        margin: 0 0 0.75rem 0;
        position: relative;
        padding-left: 0.75rem;
    }

    .stat-content h3::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 1rem;
        background: var(--primary);
        border-radius: 2px;
    }

    .stat-card.categories .stat-content h3::before { background: var(--info); }
    .stat-card.orders .stat-content h3::before { background: var(--warning); }
    .stat-card.users .stat-content h3::before { background: var(--success); }

    .stat-value {
        font-size: 2rem;
        font-weight: 800;
        color: var(--gray-800);
        margin: 0 0 0.75rem 0;
        line-height: 1;
        background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
    }

    .stat-change {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        margin-top: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 1.5rem;
        background: rgba(16, 185, 129, 0.1);
        color: var(--success);
        width: fit-content;
        transition: all 0.3s ease;
    }

    .stat-change:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2);
    }

    .stat-change.positive {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success);
    }
    .stat-change.negative {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger);
    }

    .stat-change i {
        font-size: 0.75rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Quick Actions Panel */
    .quick-actions {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        margin-bottom: 2rem;
        border: 1px solid var(--gray-200);
    }

    .quick-actions h3 {
        margin: 0 0 1.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .quick-actions h3::before {
        content: '';
        width: 3px;
        height: 1.25rem;
        background: var(--primary-gradient);
        border-radius: 2px;
    }

    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--gray-50);
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius-md);
        text-decoration: none;
        color: var(--gray-700);
        transition: var(--transition);
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        background: var(--primary-ultra-light);
        border-color: var(--primary-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
        text-decoration: none;
        color: var(--primary-dark);
    }

    .action-btn i {
        width: 20px;
        text-align: center;
        font-size: 1.125rem;
        color: var(--primary);
        transition: transform 0.3s ease;
    }

    .action-btn:hover i {
        transform: scale(1.1);
    }

    /* Modern Charts Section */
    .charts-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .chart-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        overflow: hidden;
    }

    .chart-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-title {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: #2d3748;
    }

    .chart-body {
        padding: 1.5rem;
    }

    .chart-container {
        position: relative;
        height: 350px;
        width: 100%;
    }

    /* Modern Table Card Styles */
    .modern-table-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: 1px solid var(--gray-200);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .modern-table-card:hover {
        box-shadow: var(--card-shadow-hover);
        transform: translateY(-2px);
    }

    .modern-table-card .card-header {
        padding: 1.75rem;
        border-bottom: 1px solid var(--gray-200);
        background: linear-gradient(to right, var(--gray-50), white);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .modern-table-card .card-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
    }

    .modern-table-card .card-header h2::before {
        content: '';
        position: absolute;
        left: -1.75rem;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background: var(--primary);
        border-radius: 2px;
    }

    .modern-table-card .card-header h2 i {
        color: var(--primary);
        font-size: 1.75rem;
        background: var(--primary-ultra-light);
        padding: 0.75rem;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.1);
    }

    .modern-table-card .table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
    }

    .modern-table-card .table th {
        background: var(--gray-50);
        padding: 1.25rem 1.5rem;
        font-weight: 600;
        color: var(--gray-700);
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        border-bottom: 2px solid var(--gray-200);
        white-space: nowrap;
    }

    .modern-table-card .table td {
        padding: 1.25rem 1.5rem;
        color: var(--gray-700);
        border-bottom: 1px solid var(--gray-200);
        font-size: 0.875rem;
        vertical-align: middle;
    }

    .modern-table-card .table tr:last-child td {
        border-bottom: none;
    }

    .modern-table-card .table tr:hover td {
        background: var(--primary-ultra-light);
    }

    /* Status Badges - Consistent with products.php and categories.php */
    .modern-table-card .table .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.5rem;
        border-radius: 16px;
        font-size: 0.75rem;
        font-weight: 600;
        transition: all 0.2s ease;
        white-space: nowrap;
    }

    .modern-table-card .table .status-badge i {
        font-size: 0.75rem;
    }

    .modern-table-card .table .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* Order Status Colors - Subtle gradients */
    .modern-table-card .table .status-badge.pending {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .modern-table-card .table .status-badge.processing {
        background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
        color: #004085;
        border: 1px solid #b3d9ff;
    }

    .modern-table-card .table .status-badge.completed {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .modern-table-card .table .status-badge.cancelled {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .modern-table-card .table .status-badge.shipping {
        background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    .modern-table-card .table .status-badge.delivered {
        background: linear-gradient(135deg, #d1f2eb 0%, #c3e9dd 100%);
        color: #0c5460;
        border: 1px solid #c3e9dd;
    }

    .modern-table-card .table .action-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .modern-table-card .table .action-button {
        padding: 0.5rem;
        border-radius: 0.75rem;
        color: var(--gray-600);
        transition: var(--transition-fast);
        background: var(--gray-100);
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--gray-200);
    }

    .modern-table-card .table .action-button:hover {
        background: var(--primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
        border-color: var(--primary);
    }

    .modern-table-card .table .action-button i {
        font-size: 1rem;
    }

    .modern-table-card .search-box {
        position: relative;
        width: 300px;
    }

    .modern-table-card .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--gray-200);
        border-radius: 0.75rem;
        font-size: 0.875rem;
        transition: var(--transition);
        background: white;
    }

    .modern-table-card .search-box input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    }

    .modern-table-card .search-box i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: 0.875rem;
    }

    .modern-table-card .view-all-btn {
        padding: 0.75rem 1.5rem;
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 0.75rem;
        font-weight: 500;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
        text-decoration: none;
    }

    .modern-table-card .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
        color: white;
        text-decoration: none;
    }

    .modern-table-card .view-all-btn i {
        font-size: 1rem;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .modern-table-card .card-header {
            flex-direction: column;
            align-items: stretch;
        }

        .modern-table-card .search-box {
            width: 100%;
        }

        .modern-table-card .table {
            display: block;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .modern-table-card .table th,
        .modern-table-card .table td {
            white-space: nowrap;
        }

        .modern-table-card .action-buttons {
            flex-wrap: wrap;
        }

        .charts-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            grid-template-columns: 1fr;
        }

        .quick-actions {
            padding: 1.25rem;
        }

        .quick-actions h3 {
            font-size: 1.125rem;
            margin-bottom: 1.25rem;
        }
    }

    /* Modern Badges */
    .badge-modern {
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .badge-modern-pending { background: #fff3cd; color: #856404; }
    .badge-modern-processing { background: #d1ecf1; color: #0c5460; }
    .badge-modern-completed { background: #d4edda; color: #155724; }
    .badge-modern-cancelled { background: #f8d7da; color: #721c24; }
    .badge-modern-shipping { background: #cce5ff; color: #004085; }
    .badge-modern-delivered { background: #d1f2eb; color: #0c5460; }

    /* Modern Buttons */
    .btn-modern {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-modern-primary {
        background: var(--primary-gradient);
        color: white;
        border: none;
    }

    .btn-modern-primary:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(243, 115, 33, 0.3);
    }

    .btn-modern-info {
        background: var(--info-gradient);
        color: white;
        border: none;
    }

    .btn-modern-info:hover {
        background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
        color: white;
    }

    .btn-modern-success {
        background: var(--success-gradient);
        color: white;
        border: none;
    }

    .btn-modern-success:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .btn-modern-danger {
        background: var(--danger-gradient);
        color: white;
        border: none;
    }

    .btn-modern-danger:hover {
        background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
        color: white;
    }

    /* Additional Utilities */
    .gap-1 { gap: 0.25rem !important; }
    .gap-2 { gap: 0.5rem !important; }
    .gap-3 { gap: 1rem !important; }

    .btn-sm.btn-modern {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    /* Table responsive improvements */
    .table-responsive {
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    /* Input group improvements */
    .input-group .form-control {
        border-radius: 0.5rem 0 0 0.5rem;
    }

    .input-group .btn {
        border-radius: 0 0.5rem 0.5rem 0;
    }

    /* Dropdown improvements */
    .dropdown-menu {
        border-radius: 0.75rem;
        box-shadow: var(--card-shadow);
        border: none;
        padding: 0.5rem;
    }

    .dropdown-item {
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        transition: var(--transition);
    }

    .dropdown-item:hover {
        background: #f8f9fa;
        transform: translateX(4px);
    }

    /* Animation for stats cards */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stat-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }

    /* Loading state for charts */
    .chart-container.loading::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid var(--gray-200);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .chart-container canvas {
        position: relative;
        z-index: 2;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .charts-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            grid-template-columns: 1fr;
        }

        .dashboard-header {
            padding: 1.5rem;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        .table-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .table-header .d-flex {
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group {
            width: 100% !important;
        }

        .btn-modern span {
            display: none;
        }

        .btn-modern i {
            margin: 0;
        }
    }

    @media (max-width: 576px) {
        .stat-card {
            padding: 1.5rem;
        }

        .stat-value {
            font-size: 2rem;
        }

        .chart-container {
            height: 250px;
        }

        .dashboard-header h1 {
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
        }
    }

    /* Welcome Header Styles - Redesigned */
    .welcome-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
        border-radius: 1rem;
        padding: 1.25rem 1.5rem;
        border: 1px solid rgba(243, 115, 33, 0.1);
        position: relative;
        overflow: hidden;
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.08),
            0 4px 6px -2px rgba(0, 0, 0, 0.03),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .welcome-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
            radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%),
            linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
        pointer-events: none;
        opacity: 0.8;
    }

    .welcome-header::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(243, 115, 33, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
    }

    .welcome-header:hover {
        transform: translateY(-2px);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.15),
            0 20px 25px -5px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .welcome-content {
        display: flex;
        align-items: center;
        gap: 1.25rem;
        position: relative;
        z-index: 2;
    }

    .welcome-content::after {
        content: '';
        position: absolute;
        right: -1.5rem;
        height: 60%;
        width: 2px;
        background: linear-gradient(to bottom,
            transparent,
            rgba(243, 115, 33, 0.3),
            rgba(243, 115, 33, 0.6),
            rgba(243, 115, 33, 0.3),
            transparent);
        border-radius: 1px;
        opacity: 0.7;
    }

    .welcome-icon {
        font-size: 1.25rem;
        color: white;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        padding: 0.75rem;
        border-radius: 0.75rem;
        width: 2.75rem;
        height: 2.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        box-shadow:
            0 6px 10px -2px rgba(243, 115, 33, 0.25),
            0 2px 4px -1px rgba(243, 115, 33, 0.15);
    }

    .welcome-icon::before {
        content: '';
        position: absolute;
        inset: -3px;
        border-radius: inherit;
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
        opacity: 0;
        transition: all 0.4s ease;
        z-index: -1;
    }

    .welcome-icon::after {
        content: '';
        position: absolute;
        inset: 2px;
        border-radius: calc(1rem - 2px);
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        pointer-events: none;
    }

    .welcome-header:hover .welcome-icon {
        transform: rotate(15deg) scale(1.1);
        box-shadow:
            0 20px 25px -5px rgba(243, 115, 33, 0.4),
            0 10px 10px -5px rgba(243, 115, 33, 0.3);
    }

    .welcome-header:hover .welcome-icon::before {
        opacity: 1;
        inset: -5px;
    }

    .welcome-text {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        position: relative;
    }

    .welcome-greeting {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        line-height: 1.2;
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
    }

    .welcome-greeting::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
        border-radius: 1px;
        transition: width 0.6s ease;
    }

    .welcome-header:hover .welcome-greeting::before {
        width: 100%;
    }

    .wave-hand {
        display: inline-block;
        margin-left: 0.5rem;
        animation: cuteWave 3s ease-in-out infinite;
        transform-origin: bottom center;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    .wave-hand img {
        width: 28px;
        height: 28px;
        display: block;
        object-fit: contain;
        transform-origin: inherit;
    }

    @keyframes cuteWave {
        0%, 100% {
            transform: rotate(0deg) scale(1);
        }
        5% {
            transform: rotate(15deg) scale(1.1);
        }
        10% {
            transform: rotate(-10deg) scale(1.05);
        }
        15% {
            transform: rotate(20deg) scale(1.1);
        }
        20% {
            transform: rotate(-5deg) scale(1.05);
        }
        25% {
            transform: rotate(15deg) scale(1.1);
        }
        30% {
            transform: rotate(0deg) scale(1);
        }
        35% {
            transform: rotate(8deg) scale(1.05);
        }
        40% {
            transform: rotate(-3deg) scale(1.02);
        }
        45% {
            transform: rotate(5deg) scale(1.05);
        }
        50% {
            transform: rotate(0deg) scale(1);
        }
        85% {
            transform: rotate(0deg) scale(1);
        }
    }

    .welcome-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
        margin: 0;
        font-weight: 400;
        line-height: 1.4;
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .welcome-header:hover .welcome-subtitle {
        color: var(--gray-700);
        opacity: 1;
    }

    .welcome-header:hover .wave-hand {
        animation: cuteWaveHover 1.5s ease-in-out infinite;
    }

    @keyframes cuteWaveHover {
        0%, 100% {
            transform: rotate(0deg) scale(1);
        }
        10% {
            transform: rotate(25deg) scale(1.2);
        }
        20% {
            transform: rotate(-15deg) scale(1.1);
        }
        30% {
            transform: rotate(30deg) scale(1.2);
        }
        40% {
            transform: rotate(-10deg) scale(1.1);
        }
        50% {
            transform: rotate(20deg) scale(1.15);
        }
        60% {
            transform: rotate(-5deg) scale(1.05);
        }
        70% {
            transform: rotate(10deg) scale(1.1);
        }
        80% {
            transform: rotate(0deg) scale(1);
        }
    }

    .welcome-stats {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .stat-item {
        padding: 0.875rem 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 0.75rem;
        border: 1px solid rgba(243, 115, 33, 0.15);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        min-width: 130px;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 0.375rem;
        box-shadow:
            0 2px 4px -1px rgba(0, 0, 0, 0.08),
            0 1px 2px -1px rgba(0, 0, 0, 0.04);
    }

    .stat-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(243, 115, 33, 0.05) 0%,
            rgba(243, 115, 33, 0.02) 50%,
            transparent 100%);
        opacity: 0;
        transition: all 0.4s ease;
    }

    .stat-item::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.4s ease;
        border-radius: 0 0 1rem 1rem;
    }

    .stat-item:hover {
        transform: translateY(-4px);
        border-color: var(--primary-light);
        box-shadow:
            0 10px 25px -3px rgba(243, 115, 33, 0.15),
            0 4px 6px -2px rgba(243, 115, 33, 0.1);
        background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    }

    .stat-item:hover::before {
        opacity: 1;
    }

    .stat-item:hover::after {
        transform: scaleX(1);
    }

    .stat-label {
        font-size: 0.7rem;
        color: var(--gray-500);
        font-weight: 500;
        letter-spacing: 0.02em;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        position: relative;
        padding-left: 0.75rem;
    }

    .stat-label::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 3px;
        background: var(--primary);
        border-radius: 50%;
        opacity: 0.5;
    }

    .stat-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-800);
        display: flex;
        align-items: baseline;
        gap: 0.5rem;
        position: relative;
        padding-left: 0.75rem;
    }

    .stat-value::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 1rem;
        background: var(--primary);
        border-radius: 2px;
        opacity: 0.3;
    }

    .stat-comparison {
        font-size: 0.7rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        font-weight: 500;
        background: rgba(16, 185, 129, 0.1);
        color: var(--success);
        position: relative;
        overflow: hidden;
        margin-left: 0.75rem;
    }

    .stat-comparison::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .stat-comparison:hover::before {
        transform: translateX(100%);
    }

    .stat-comparison.negative {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger);
    }

    .stat-comparison i {
        font-size: 0.625rem;
        animation: bounce 1s infinite;
    }

    .stat-decoration {
        position: absolute;
        bottom: -15px;
        right: -15px;
        width: 60px;
        height: 60px;
        background: radial-gradient(circle at bottom right, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-item:hover .stat-decoration {
        opacity: 1;
    }

    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-2px); }
    }

    .stat-item:nth-child(1) { animation: fadeInRight 0.5s 0.1s both; }
    .stat-item:nth-child(2) { animation: fadeInRight 0.5s 0.2s both; }
    .stat-item:nth-child(3) { animation: fadeInRight 0.5s 0.3s both; }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(10px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .welcome-header {
            flex-direction: column;
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .welcome-content::after {
            display: none;
        }

        .welcome-stats {
            overflow-x: auto;
            padding-bottom: 0.5rem;
            margin-top: 0;
            position: relative;
            width: 100%;
        }

        .welcome-stats::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 30px;
            height: 100%;
            background: linear-gradient(to right, transparent, white);
            pointer-events: none;
        }

        .stat-item {
            min-width: 180px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .welcome-header {
            padding: 1.25rem;
            border-radius: 1rem;
        }

        .welcome-greeting {
            font-size: 1.25rem;
        }

        .welcome-subtitle {
            font-size: 0.875rem;
        }

        .welcome-icon {
            width: 3rem;
            height: 3rem;
            font-size: 1.25rem;
        }

        .stat-item {
            padding: 1rem 1.25rem;
            min-width: 150px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1.25rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            padding: 2rem;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .stat-value {
            font-size: 2.5rem;
        }

        .stat-content h3 {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 576px) {
        .welcome-header {
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .welcome-content {
            gap: 1rem;
        }

        .welcome-greeting {
            font-size: 1.125rem;
        }

        .welcome-icon {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 1.125rem;
        }

        .stat-item {
            padding: 0.875rem 1rem;
            min-width: 140px;
        }

        .stat-label {
            font-size: 0.65rem;
        }

        .stat-value {
            font-size: 1rem;
        }

        .stat-card {
            padding: 1.5rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .stat-value {
            font-size: 2rem;
        }

        .stat-change {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
    }
</style>

<!-- Content -->
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="welcome-header">
        <div class="welcome-content">
            <div class="welcome-icon">
                <i class="fas fa-sun"></i>
            </div>
            <div class="welcome-text">
                <div class="welcome-greeting">
                    Xin chào, Admin <?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : 'Admin'; ?>
                    <span class="wave-hand">
                        <img src="../assets/images/logo-socal-login/Waving-Hand-3d.webp" alt="👋" />
                    </span>
                </div>
                <div class="welcome-subtitle">
                    Chào mừng bạn quay trở lại trang quản trị
                </div>
            </div>
        </div>
        <div class="welcome-stats">
            <div class="stat-item">
                <div class="stat-label">Đơn hàng hôm nay</div>
                <div class="stat-value">
                    <?php echo $today_orders; ?>
                    <?php if($order_comparison > 0): ?>
                        <span class="stat-comparison">
                            <i class="fas fa-arrow-up"></i>
                            +<?php echo $order_comparison; ?>
                        </span>
                    <?php elseif($order_comparison < 0): ?>
                        <span class="stat-comparison negative">
                            <i class="fas fa-arrow-down"></i>
                            <?php echo $order_comparison; ?>
                        </span>
                    <?php endif; ?>
                </div>
                <div class="stat-decoration"></div>
            </div>

            <div class="stat-item">
                <div class="stat-label">Sản phẩm bán chạy</div>
                <div class="stat-value">
                    <?php echo $best_selling_product; ?>
                </div>
                <div class="stat-decoration"></div>
            </div>

            <div class="stat-item">
                <div class="stat-label">Khách hàng mới</div>
                <div class="stat-value">
                    <?php echo $new_customers_today; ?>
                </div>
                <div class="stat-decoration"></div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card products">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Sản phẩm</h3>
                    <div class="stat-value"><?php echo $total_products; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12% so với tháng trước</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
            </div>
        </div>

        <div class="stat-card categories">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Danh mục</h3>
                    <div class="stat-value"><?php echo $total_categories; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+5% so với tháng trước</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-folder-open"></i>
                </div>
            </div>
        </div>

        <div class="stat-card orders">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Đơn hàng</h3>
                    <div class="stat-value"><?php echo $total_orders; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+18% so với tháng trước</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>

        <div class="stat-card users">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Người dùng</h3>
                    <div class="stat-value"><?php echo $total_users; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8% so với tháng trước</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="charts-grid">
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Doanh thu (6 tháng gần nhất)</h3>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="revenueDropdown" data-toggle="dropdown">
                        <i class="fas fa-calendar-alt"></i> 6 tháng
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#">3 tháng</a>
                        <a class="dropdown-item" href="#">6 tháng</a>
                        <a class="dropdown-item" href="#">12 tháng</a>
                    </div>
                </div>
            </div>
            <div class="chart-body">
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Tình trạng đơn hàng</h3>
                <div class="text-muted">
                    <i class="fas fa-info-circle"></i> Tổng: <?php echo $total_orders; ?> đơn
                </div>
            </div>
            <div class="chart-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="orderStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3>Thao tác nhanh</h3>
        <div class="action-buttons">
            <a href="products.php" class="action-btn">
                <i class="fas fa-plus"></i>
                <span>Thêm sản phẩm mới</span>
            </a>
            <a href="orders.php" class="action-btn">
                <i class="fas fa-list"></i>
                <span>Quản lý đơn hàng</span>
            </a>
            <a href="categories.php" class="action-btn">
                <i class="fas fa-tags"></i>
                <span>Quản lý danh mục</span>
            </a>
            <a href="users.php" class="action-btn">
                <i class="fas fa-users"></i>
                <span>Quản lý người dùng</span>
            </a>
            <a href="settings.php" class="action-btn">
                <i class="fas fa-cog"></i>
                <span>Cài đặt hệ thống</span>
            </a>
            <a href="reports.php" class="action-btn">
                <i class="fas fa-chart-bar"></i>
                <span>Báo cáo thống kê</span>
            </a>
        </div>
    </div>

    <!-- Latest Orders -->
    <div class="modern-table-card">
        <div class="card-header">
            <h2>
                <i class="fas fa-shopping-cart"></i>
                Đơn hàng mới nhất
            </h2>
            <div class="d-flex align-items-center gap-3">
                <div class="search-box">
                            <i class="fas fa-search"></i>
                    <input type="text" placeholder="Tìm kiếm đơn hàng...">
                    </div>
                <a href="orders.php" class="view-all-btn">
                    <i class="fas fa-list-ul"></i>
                    <span>Xem tất cả</span>
                </a>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Khách hàng</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Ngày đặt</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                        <?php if (count($latest_orders) > 0): ?>
                        <?php foreach ($latest_orders as $order): ?>
                        <tr>
                        <td><strong>#<?php echo $order['id']; ?></strong></td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <div class="customer-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <div class="customer-name"><?php echo $order['full_name']; ?></div>
                                    <div class="customer-email text-muted small"><?php echo $order['email']; ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="order-total">
                                <strong><?php echo format_currency($order['total']); ?></strong>
                            </div>
                        </td>
                            <td>
                                <?php
                                $status_info = get_order_status_info($order['status'], 'admin');
                                $badge_modern_class = '';
                            switch (strtolower(trim($order['status']))) {
                                    case 'pending':
                                    case 'chờ xử lý':
                                    $badge_modern_class = 'pending';
                                    $status_icon = 'fas fa-clock';
                                        break;
                                    case 'processing':
                                    case 'đang xử lý':
                                    $badge_modern_class = 'processing';
                                    $status_icon = 'fas fa-cog';
                                        break;
                                    case 'completed':
                                    case 'hoàn thành':
                                    $badge_modern_class = 'completed';
                                    $status_icon = 'fas fa-check-circle';
                                        break;
                                    case 'cancelled':
                                    case 'đã hủy':
                                    $badge_modern_class = 'cancelled';
                                    $status_icon = 'fas fa-times-circle';
                                        break;
                                    case 'shipping':
                                    case 'đang giao':
                                    $badge_modern_class = 'shipping';
                                    $status_icon = 'fas fa-truck';
                                        break;
                                    case 'delivered':
                                    case 'đã giao':
                                    $badge_modern_class = 'delivered';
                                    $status_icon = 'fas fa-box-check';
                                        break;
                                    default:
                                    $badge_modern_class = 'secondary';
                                    $status_icon = 'fas fa-info-circle';
                                }
                                ?>
                            <span class="status-badge <?php echo $badge_modern_class; ?>">
                                <i class="<?php echo $status_icon; ?>"></i>
                                <?php echo htmlspecialchars($status_info['text']); ?>
                            </span>
                            </td>
                        <td>
                            <div class="order-date">
                                <div class="date"><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></div>
                                <div class="time text-muted small"><?php echo date('H:i', strtotime($order['created_at'])); ?></div>
                            </div>
                        </td>
                            <td>
                            <div class="action-buttons">
                                <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-button" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                <a href="#" class="action-button" onclick="confirmOrder(<?php echo $order['id']; ?>)" title="Duyệt đơn">
                                        <i class="fas fa-check"></i>
                                    </a>
                                <a href="#" class="action-button" onclick="cancelOrder(<?php echo $order['id']; ?>)" title="Hủy đơn">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Không có đơn hàng nào</p>
                            </div>
                        </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Thêm loading state cho charts
    document.querySelectorAll('.chart-container').forEach(container => {
        container.classList.add('loading');
    });

    // Dữ liệu doanh thu (ví dụ)
    const revenueData = {
        labels: <?php echo json_encode(array_column($revenue_data_points, "label")); ?>,
        datasets: [{
            label: 'Doanh thu',
            data: <?php echo json_encode(array_column($revenue_data_points, "y")); ?>,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            fill: true,
            tension: 0.4 // Làm cho đường cong mượt hơn
        }]
    };

    // Cấu hình biểu đồ doanh thu
    const revenueConfig = {
        type: 'line',
        data: revenueData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#2d3748',
                    bodyColor: '#2d3748',
                    borderColor: '#e2e8f0',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return 'Doanh thu: ' + new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND'
                            }).format(context.parsed.y * 1000000);
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#f1f5f9'
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        callback: function(value) {
                            return value + 'M';
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8,
                    backgroundColor: '#F37321',
                    borderColor: '#ffffff',
                    borderWidth: 2
                },
                line: {
                    borderWidth: 3,
                    borderColor: '#F37321',
                    backgroundColor: 'rgba(243, 115, 33, 0.1)',
                    fill: true,
                    tension: 0.4
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    };

    // Vẽ biểu đồ doanh thu
    var revenueChartCtx = document.getElementById('revenueChart');
    if (revenueChartCtx) {
        const revenueChart = new Chart(revenueChartCtx.getContext('2d'), revenueConfig);

        // Xóa loading state sau khi biểu đồ được vẽ
        setTimeout(() => {
            revenueChartCtx.closest('.chart-container').classList.remove('loading');
        }, 1500);
    }

    // Dữ liệu tình trạng đơn hàng
    const orderStatusData = {
        labels: <?php echo json_encode(array_column($order_status_data_points, "label")); ?>,
        datasets: [{
            label: 'Tình trạng đơn hàng',
            data: <?php echo json_encode(array_column($order_status_data_points, "y")); ?>,
            backgroundColor: <?php echo json_encode(array_column($order_status_data_points, "color")); ?>,
            hoverOffset: 4
        }]
    };

    // Cấu hình biểu đồ tròn tình trạng đơn hàng
    const orderStatusConfig = {
        type: 'doughnut',
        data: orderStatusData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#2d3748',
                    bodyColor: '#2d3748',
                    borderColor: '#e2e8f0',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' đơn (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000,
                easing: 'easeInOutQuart'
            },
            elements: {
                arc: {
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }
            }
        }
    };

    // Vẽ biểu đồ tình trạng đơn hàng
    var orderStatusChartCtx = document.getElementById('orderStatusChart');
    if (orderStatusChartCtx) {
        const orderStatusChart = new Chart(orderStatusChartCtx.getContext('2d'), orderStatusConfig);

        // Xóa loading state sau khi biểu đồ được vẽ
        setTimeout(() => {
            orderStatusChartCtx.closest('.chart-container').classList.remove('loading');
        }, 1500);
    }

    // Xóa tất cả loading states sau 3 giây (fallback)
    setTimeout(() => {
        document.querySelectorAll('.chart-container.loading').forEach(container => {
            container.classList.remove('loading');
        });
    }, 3000);

    // Thêm hiệu ứng cho search input
    const searchInput = document.querySelector('input[placeholder="Tìm kiếm đơn hàng..."]');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
            this.parentElement.style.boxShadow = 'none';
        });
    }

    // Thêm hiệu ứng hover cho action buttons
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Counter animation cho stats
    function animateCounter(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 20);
    }

    // Animate all stat values
    document.querySelectorAll('.stat-value').forEach(el => {
        const target = parseInt(el.textContent);
        if (!isNaN(target)) {
            el.textContent = '0';
            setTimeout(() => {
                animateCounter(el, target);
            }, 500);
        }
    });
});

// Placeholder cho các hàm xử lý đơn hàng (cần được triển khai)
function confirmOrder(orderId) {
    if(confirm('Bạn có chắc chắn muốn duyệt đơn hàng #' + orderId + '?')) {
        // Gọi AJAX để cập nhật trạng thái đơn hàng
        console.log('Đã duyệt đơn hàng: ' + orderId);
        // Ví dụ: window.location.href = 'order-update.php?id=' + orderId + '&status=processing';
        // Hoặc sử dụng fetch API để gửi yêu cầu mà không tải lại trang
    }
}

function cancelOrder(orderId) {
    if(confirm('Bạn có chắc chắn muốn hủy đơn hàng #' + orderId + '?')) {
        // Gọi AJAX để cập nhật trạng thái đơn hàng
        console.log('Đã hủy đơn hàng: ' + orderId);
        // Ví dụ: window.location.href = 'order-update.php?id=' + orderId + '&status=cancelled';
    }
}
</script>
