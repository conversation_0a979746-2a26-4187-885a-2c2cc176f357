<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý bài viết';

// Include file header
include_once 'partials/header.php';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Xử lý xóa bài viết
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $post_id = (int)$_GET['id'];

    try {
        // Xóa liên kết với danh mục
        $stmt = $conn->prepare("DELETE FROM blog_post_categories WHERE post_id = :post_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        // Xóa liên kết với tag
        $stmt = $conn->prepare("DELETE FROM blog_post_tags WHERE post_id = :post_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        // Xóa bình luận
        $stmt = $conn->prepare("DELETE FROM blog_comments WHERE post_id = :post_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        // Xóa bài viết
        $stmt = $conn->prepare("DELETE FROM blog_posts WHERE id = :post_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        set_flash_message('success', 'Xóa bài viết thành công.');
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-posts.php');
}

// Xử lý thay đổi trạng thái
if (isset($_GET['action']) && $_GET['action'] === 'toggle_status' && isset($_GET['id'])) {
    $post_id = (int)$_GET['id'];

    try {
        // Lấy trạng thái hiện tại
        $stmt = $conn->prepare("SELECT status FROM blog_posts WHERE id = :post_id");
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();
        $current_status = $stmt->fetchColumn();

        // Đảo ngược trạng thái
        $new_status = $current_status ? 0 : 1;

        // Cập nhật trạng thái
        $stmt = $conn->prepare("UPDATE blog_posts SET status = :status WHERE id = :post_id");
        $stmt->bindParam(':status', $new_status, PDO::PARAM_INT);
        $stmt->bindParam(':post_id', $post_id, PDO::PARAM_INT);
        $stmt->execute();

        set_flash_message('success', 'Cập nhật trạng thái bài viết thành công.');
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-posts.php');
}

// Xử lý phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// Xử lý tìm kiếm
$search = isset($_GET['search']) ? $_GET['search'] : '';
$where_clause = '';
$params = [];

if (!empty($search)) {
    $where_clause = " WHERE title LIKE :search OR content LIKE :search";
    $params[':search'] = "%$search%";
}

// Lấy tổng số bài viết
$count_sql = "SELECT COUNT(*) FROM blog_posts" . $where_clause;
$stmt = $conn->prepare($count_sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_items = $stmt->fetchColumn();
$total_pages = ceil($total_items / $items_per_page);

// Lấy danh sách bài viết
$sql = "SELECT p.*, u.full_name as author_name
        FROM blog_posts p
        LEFT JOIN users u ON p.author_id = u.id" .
        $where_clause .
        " ORDER BY p.created_at DESC
        LIMIT :offset, :limit";

$stmt = $conn->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
$stmt->execute();
$posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý bài viết</h1>

    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>
        <div class="alert alert-<?php echo $flash_message['type'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash_message['message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Danh sách bài viết</h6>
            <a href="<?php echo BASE_URL; ?>/admin/blog-post-edit.php" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Thêm bài viết mới
            </a>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <form action="" method="GET" class="form-inline">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Tìm kiếm bài viết..." value="<?php echo htmlspecialchars($search); ?>">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="50">ID</th>
                            <th>Tiêu đề</th>
                            <th>Tác giả</th>
                            <th>Ngày tạo</th>
                            <th width="100">Lượt xem</th>
                            <th width="100">Trạng thái</th>
                            <th width="150">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($posts)): ?>
                            <tr>
                                <td colspan="7" class="text-center">Không có bài viết nào.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($posts as $post): ?>
                                <tr>
                                    <td><?php echo $post['id']; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/blog-post-edit.php?id=<?php echo $post['id']; ?>">
                                            <?php echo $post['title']; ?>
                                        </a>
                                        <?php if ($post['is_featured']): ?>
                                            <span class="badge badge-warning ml-2">Nổi bật</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $post['author_name']; ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($post['created_at'])); ?></td>
                                    <td><?php echo number_format($post['view_count']); ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/admin/blog-posts.php?action=toggle_status&id=<?php echo $post['id']; ?>" class="btn btn-sm <?php echo $post['status'] ? 'btn-success' : 'btn-secondary'; ?>">
                                            <?php echo $post['status'] ? 'Hiển thị' : 'Ẩn'; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/admin/blog-post-edit.php?id=<?php echo $post['id']; ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>" target="_blank" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/blog-posts.php?action=delete&id=<?php echo $post['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa bài viết này?');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $start_page + 4);
                        if ($end_page - $start_page < 4) {
                            $start_page = max(1, $end_page - 4);
                        }
                        ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
