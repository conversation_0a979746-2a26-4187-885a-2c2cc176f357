<?php
// Include init
require_once '../includes/init.php';

// <PERSON><PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý banner';

// Sắp xếp
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'desc';

// Phân trang
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Lấy danh sách banner
$banners = get_all_banners_sorted($limit, $offset, $sort_by, $sort_order);
$total_banners = count_banners();
$total_pages = ceil($total_banners / $limit);

// Xử lý form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    $errors = [];

    // Xóa banner
    if ($action === 'delete' && isset($_POST['banner_id'])) {
        $banner_id = intval($_POST['banner_id']);
        $banner = get_banner_by_id($banner_id);

        if ($banner) {
            // Xóa hình ảnh banner nếu có
            if (!empty($banner['image'])) {
                $image_path = '../uploads/banners/' . $banner['image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }

            // Xóa banner từ database
            $result = delete_banner($banner_id);

            if ($result['success']) {
                set_flash_message('success', $result['message']);
                redirect('banners.php');
            } else {
                set_flash_message('error', $result['message']);
                redirect('banners.php');
            }
        } else {
            set_flash_message('error', 'Không tìm thấy banner!');
            redirect('banners.php');
        }
    }

    // Thay đổi trạng thái banner
    else if ($action === 'change_status' && isset($_POST['banner_id'])) {
        $banner_id = intval($_POST['banner_id']);
        $banner = get_banner_by_id($banner_id);

        if ($banner) {
            $new_status = $banner['status'] == 1 ? 0 : 1;
            // Lấy tất cả các trường hiện có của banner
            $update_data = $banner;

            // Cập nhật trạng thái mới
            $update_data['status'] = $new_status;

            $result = update_banner($banner_id, $update_data);

            if ($result['success']) {
                set_flash_message('success', 'Đã thay đổi trạng thái banner!');
                redirect('banners.php');
            } else {
                set_flash_message('error', $result['message']);
                redirect('banners.php');
            }
        } else {
            set_flash_message('error', 'Không tìm thấy banner!');
            redirect('banners.php');
        }
    }
}

// Tạo URL sắp xếp
function get_sort_url($field) {
    global $sort_by, $sort_order, $page;
    $new_order = ($sort_by === $field && $sort_order === 'asc') ? 'desc' : 'asc';
    return "banners.php?sort={$field}&order={$new_order}&page={$page}";
}

// Tạo biểu tượng sắp xếp
function get_sort_icon($field) {
    global $sort_by, $sort_order;
    if ($sort_by !== $field) {
        return '<i class="fas fa-sort text-gray-400"></i>';
    }
    return $sort_order === 'asc' ? '<i class="fas fa-sort-up text-primary"></i>' : '<i class="fas fa-sort-down text-primary"></i>';
}

// Include header
include_once 'partials/header.php';

// Thêm thư viện jQuery UI và style cho sortable
?>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<!-- CSS tùy chỉnh cho chức năng sắp xếp -->
<style>
    .banner-table tbody.sortable {
        cursor: move;
    }
    .banner-table tr.ui-sortable-helper {
        display: table;
        border-collapse: collapse;
        background-color: #f8f9fc;
        opacity: 0.9;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .banner-table tr.ui-sortable-placeholder {
        visibility: visible !important;
        background-color: #e9ecef;
        height: 100px;
    }
    .draggable-icon {
        cursor: move;
        color: #aaa;
        margin-right: 5px;
    }
    .draggable-icon:hover {
        color: #4e73df;
    }
    .sort-saved-message {
        position: fixed;
        top: 120px; /* Tăng top để không đè lên nút */
        right: 20px;
        padding: 15px 20px;
        background-color: #1cc88a;
        color: white;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1050;
        opacity: 0;
        transform: translateX(20px);
        transition: opacity 0.3s, transform 0.3s;
        pointer-events: none; /* Thêm thuộc tính này để cho phép click xuyên qua */
    }
    .sort-saved-message.show {
        opacity: 1;
        transform: translateX(0);
    }
</style>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý banner</h1>
        <a href="banner-add.php" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Thêm banner mới
        </a>
    </div>

    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>
    <div class="alert alert-<?php echo $flash_message['type'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
        <?php echo $flash_message['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Thông tin</div>
                            <ul class="mb-0 mt-2">
                                <li>Banner <strong>Mặc định</strong>: Hiển thị hình ảnh kèm theo nội dung HTML/CSS/JS (slogan, button)</li>
                                <li>Banner <strong>Marketing</strong>: Chỉ hiển thị hình ảnh đơn thuần, không có nội dung bổ sung</li>
                                <li>Tất cả banner <strong>Hiển thị</strong> sẽ xuất hiện trong slideshow ở trang chủ</li>
                                <li>Banner sẽ hiển thị theo thứ tự đã sắp xếp (kéo thả các dòng để thay đổi thứ tự)</li>
                            </ul>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-info-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách banner -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Danh sách banner (<?php echo $total_banners; ?> banner)</h6>
            <div class="small text-muted">Sắp xếp:
                <a href="<?php echo get_sort_url('id'); ?>" class="ml-2">ID <?php echo get_sort_icon('id'); ?></a> |
                <a href="<?php echo get_sort_url('banner_type'); ?>">Loại <?php echo get_sort_icon('banner_type'); ?></a> |
                <a href="<?php echo get_sort_url('status'); ?>">Trạng thái <?php echo get_sort_icon('status'); ?></a> |
                <a href="<?php echo get_sort_url('sort_order'); ?>">Thứ tự <?php echo get_sort_icon('sort_order'); ?></a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered banner-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="5%">ID</th>
                            <th width="20%">Hình ảnh</th>
                            <th width="20%">Loại banner</th>
                            <th width="20%">Liên kết</th>
                            <th width="10%">Trạng thái</th>
                            <th width="15%">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="sortable">
                        <?php if (count($banners) > 0): ?>
                            <?php foreach ($banners as $banner): ?>
                            <tr data-id="<?php echo $banner['id']; ?>">
                                <td>
                                    <i class="fas fa-grip-vertical draggable-icon"></i>
                                    <?php echo $banner['id']; ?>
                                </td>
                                <td>
                                    <?php if (!empty($banner['image'])): ?>
                                    <div style="width: 200px; height: 113px; position: relative; border-radius: 4px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <img src="<?php echo BASE_URL . '/uploads/banners/' . $banner['image']; ?>" alt="Banner <?php echo $banner['id']; ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                    </div>
                                    <?php else: ?>
                                    <div style="width: 200px; height: 113px; position: relative; background: #f8f9fc; border-radius: 4px; display: flex; align-items: center; justify-content: center; border: 1px dashed #d1d3e2;">
                                        <i class="fas fa-image fa-2x text-gray-300"></i>
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($banner['banner_type']) && $banner['banner_type'] === 'marketing'): ?>
                                        <span class="badge bg-info text-white px-3 py-2">
                                            <i class="fas fa-ad mr-1"></i> Banner Marketing
                                        </span>
                                        <div class="small text-muted mt-2">Chỉ hiển thị hình ảnh</div>
                                    <?php else: ?>
                                        <span class="badge bg-success text-white px-3 py-2">
                                            <i class="fas fa-code mr-1"></i> Banner Mặc định
                                        </span>
                                        <div class="small text-muted mt-2">Hiển thị kèm nội dung HTML/CSS/JS</div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($banner['link'])): ?>
                                        <div class="text-primary mt-1">
                                            <i class="fas fa-link mr-1"></i>
                                            <a href="<?php echo $banner['link']; ?>" target="_blank" title="<?php echo htmlspecialchars($banner['link']); ?>">
                                                <?php echo mb_strimwidth(htmlspecialchars($banner['link']), 0, 30, '...'); ?>
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted"><em>Không có liên kết</em></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <form method="POST" action="banners.php?sort=<?php echo $sort_by; ?>&order=<?php echo $sort_order; ?>&page=<?php echo $page; ?>">
                                        <input type="hidden" name="action" value="change_status">
                                        <input type="hidden" name="banner_id" value="<?php echo $banner['id']; ?>">
                                        <button type="submit" class="btn btn-sm <?php echo $banner['status'] == 1 ? 'btn-success' : 'btn-secondary'; ?> d-flex align-items-center">
                                            <i class="fas <?php echo $banner['status'] == 1 ? 'fa-eye' : 'fa-eye-slash'; ?> mr-1"></i>
                                            <?php echo $banner['status'] == 1 ? 'Hiển thị' : 'Ẩn'; ?>
                                        </button>
                                    </form>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="banner-edit.php?id=<?php echo $banner['id']; ?>" class="btn btn-sm btn-primary mr-1">
                                            <i class="fas fa-edit"></i> Sửa
                                        </a>
                                        <form method="POST" action="banners.php?sort=<?php echo $sort_by; ?>&order=<?php echo $sort_order; ?>&page=<?php echo $page; ?>" class="d-inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa banner này?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="banner_id" value="<?php echo $banner['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i> Xóa
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center">Không có banner nào. <a href="banner-add.php">Thêm banner mới</a></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Phân trang -->
            <?php if ($total_pages > 1): ?>
            <nav>
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="banners.php?sort=<?php echo $sort_by; ?>&order=<?php echo $sort_order; ?>&page=<?php echo $page - 1; ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                        <a class="page-link" href="banners.php?sort=<?php echo $sort_by; ?>&order=<?php echo $sort_order; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="banners.php?sort=<?php echo $sort_by; ?>&order=<?php echo $sort_order; ?>&page=<?php echo $page + 1; ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Thông báo khi lưu thành công -->
<div id="sortSavedMessage" class="sort-saved-message">
    <i class="fas fa-check-circle mr-2"></i> Thứ tự banner đã được lưu
</div>

<!-- JavaScript cho chức năng sắp xếp -->
<script>
$(document).ready(function() {
    // Khởi tạo sortable
    $(".sortable").sortable({
        cursor: "move",
        handle: ".draggable-icon",
        placeholder: "ui-sortable-placeholder",
        update: function(event, ui) {
            saveBannerOrder();
        }
    });

    // Hàm lưu thứ tự banner
    function saveBannerOrder() {
        // Thu thập dữ liệu từ bảng
        var banners = [];
        $(".sortable tr").each(function(index) {
            var id = $(this).data("id");
            if (id) {
                banners.push({
                    id: id,
                    order: index
                });
            }
        });

        // Gửi AJAX request để cập nhật thứ tự
        $.ajax({
            url: "api/update_banner_order.php",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                banners: banners
            }),
            success: function(response) {
                if (response.success) {
                    // Hiển thị thông báo
                    showSavedMessage();
                } else {
                    console.error("Lỗi khi cập nhật thứ tự:", response.message);
                    alert("Lỗi khi cập nhật thứ tự: " + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error("Lỗi AJAX:", error);
                alert("Lỗi khi cập nhật thứ tự banner. Vui lòng thử lại sau.");
            }
        });
    }

    // Hiển thị thông báo đã lưu
    function showSavedMessage() {
        var message = $("#sortSavedMessage");
        message.addClass("show");

        setTimeout(function() {
            message.removeClass("show");
        }, 3000);
    }
});
</script>

<?php include_once 'partials/footer.php'; ?>
