<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Kiểm tra ID sản phẩm
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('error', 'ID sản phẩm không hợp lệ.');

    // Kiểm tra xem có tham số category_id trong URL không
    $redirect_url = 'products.php';
    $filter_category_id = null;

    // Lấy category_id từ URL (nếu có)
    if (isset($_GET['category_id']) && !empty($_GET['category_id'])) {
        $filter_category_id = intval($_GET['category_id']);
    }

    // Nếu có category_id để lọc, thêm vào URL redirect
    if ($filter_category_id) {
        $redirect_url .= '?category_id=' . $filter_category_id;
    }

    redirect($redirect_url);
}

$product_id = intval($_GET['id']);
$product = get_product_by_id($product_id);

if (!$product) {
    set_flash_message('error', 'Sản phẩm không tồn tại.');

    // Kiểm tra xem có tham số category_id trong URL không
    $redirect_url = 'products.php';
    $filter_category_id = null;

    // Lấy category_id từ URL (nếu có)
    if (isset($_GET['category_id']) && !empty($_GET['category_id'])) {
        $filter_category_id = intval($_GET['category_id']);
    }

    // Nếu có category_id để lọc, thêm vào URL redirect
    if ($filter_category_id) {
        $redirect_url .= '?category_id=' . $filter_category_id;
    }

    redirect($redirect_url);
}

// Xóa sản phẩm
$result = delete_product($product_id);

// Xóa hình ảnh sản phẩm nếu có
if ($result['success'] && !empty($product['image'])) {
    $image_path = '../uploads/products/' . $product['image'];
    if (file_exists($image_path)) {
        unlink($image_path);
    }
}

// Thông báo kết quả và chuyển hướng
set_flash_message($result['success'] ? 'success' : 'error', $result['message']);

// Lấy query string trước đó để redirect lại đúng bộ lọc
$redirect_url = 'products.php';
$query_params = [];
if (isset($_SESSION['last_products_query']) && !empty($_SESSION['last_products_query'])) {
    parse_str($_SESSION['last_products_query'], $query_params);
}
// Thêm auto_scroll parameter để trigger auto-scroll
$query_params['auto_scroll'] = '1';
$redirect_url .= '?' . http_build_query($query_params);

redirect($redirect_url);
?>
