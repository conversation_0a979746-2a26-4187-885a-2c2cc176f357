<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý bình luận blog';

// Include file header
include_once 'partials/header.php';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Xử lý phê duyệt bình luận
if (isset($_GET['action']) && $_GET['action'] === 'approve' && isset($_GET['id'])) {
    $comment_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("UPDATE blog_comments SET status = 1 WHERE id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();

        set_flash_message('success', 'Phê duyệt bình luận thành công.');
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-comments.php');
}

// Xử lý từ chối bình luận
if (isset($_GET['action']) && $_GET['action'] === 'reject' && isset($_GET['id'])) {
    $comment_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("UPDATE blog_comments SET status = 2 WHERE id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();

        set_flash_message('success', 'Từ chối bình luận thành công.');
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-comments.php');
}

// Xử lý xóa bình luận
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $comment_id = (int)$_GET['id'];

    try {
        // Xóa các bình luận phản hồi
        $stmt = $conn->prepare("DELETE FROM blog_comments WHERE parent_id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();

        // Xóa bình luận
        $stmt = $conn->prepare("DELETE FROM blog_comments WHERE id = :comment_id");
        $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
        $stmt->execute();

        set_flash_message('success', 'Xóa bình luận thành công.');
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-comments.php');
}

// Xử lý phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 10;
$offset = ($page - 1) * $items_per_page;

// Xử lý lọc theo trạng thái
$status = isset($_GET['status']) ? (int)$_GET['status'] : null;
$where_clause = '';
$params = [];

if ($status !== null) {
    $where_clause = " WHERE c.status = :status";
    $params[':status'] = $status;
}

// Lấy tổng số bình luận
$count_sql = "SELECT COUNT(*) FROM blog_comments c" . $where_clause;
$stmt = $conn->prepare($count_sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total_items = $stmt->fetchColumn();
$total_pages = ceil($total_items / $items_per_page);

// Lấy danh sách bình luận
$sql = "SELECT c.*, p.title as post_title, p.slug as post_slug, u.full_name, u.avatar
        FROM blog_comments c
        LEFT JOIN blog_posts p ON c.post_id = p.id
        LEFT JOIN users u ON c.user_id = u.id" .
        $where_clause .
        " ORDER BY c.created_at DESC
        LIMIT :offset, :limit";

$stmt = $conn->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
$stmt->execute();
$comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Đếm số lượng bình luận theo trạng thái
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM blog_comments GROUP BY status");
$stmt->execute();
$status_counts = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $status_counts[$row['status']] = $row['count'];
}
$total_count = array_sum($status_counts);
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý bình luận blog</h1>

    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>
        <div class="alert alert-<?php echo $flash_message['type'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash_message['message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Danh sách bình luận</h6>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <div class="btn-group" role="group" aria-label="Filter comments">
                    <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php" class="btn <?php echo $status === null ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Tất cả (<?php echo $total_count; ?>)
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?status=0" class="btn <?php echo $status === 0 ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Chờ duyệt (<?php echo $status_counts[0] ?? 0; ?>)
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?status=1" class="btn <?php echo $status === 1 ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Đã duyệt (<?php echo $status_counts[1] ?? 0; ?>)
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?status=2" class="btn <?php echo $status === 2 ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Đã từ chối (<?php echo $status_counts[2] ?? 0; ?>)
                    </a>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Người bình luận</th>
                            <th>Nội dung</th>
                            <th>Bài viết</th>
                            <th>Ngày tạo</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($comments)): ?>
                            <tr>
                                <td colspan="6" class="text-center">Không có bình luận nào.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($comments as $comment): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="mr-2">
                                                <?php if (!empty($comment['avatar'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $comment['avatar']; ?>" alt="Avatar" class="img-profile rounded-circle" style="width: 40px; height: 40px;">
                                                <?php else: ?>
                                                    <img src="<?php echo BASE_URL; ?>/admin/assets/img/undraw_profile.svg" alt="Avatar" class="img-profile rounded-circle" style="width: 40px; height: 40px;">
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <div class="font-weight-bold">
                                                    <?php echo !empty($comment['full_name']) ? $comment['full_name'] : $comment['author_name']; ?>
                                                </div>
                                                <?php if (!empty($comment['author_email'])): ?>
                                                    <div class="small text-muted"><?php echo $comment['author_email']; ?></div>
                                                <?php endif; ?>
                                                <?php if ($comment['parent_id']): ?>
                                                    <span class="badge badge-info">Phản hồi</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo nl2br(htmlspecialchars($comment['content'])); ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $comment['post_slug']; ?>" target="_blank">
                                            <?php echo $comment['post_title']; ?>
                                        </a>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($comment['created_at'])); ?></td>
                                    <td>
                                        <?php if ($comment['status'] == 0): ?>
                                            <span class="badge badge-warning">Chờ duyệt</span>
                                        <?php elseif ($comment['status'] == 1): ?>
                                            <span class="badge badge-success">Đã duyệt</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Đã từ chối</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($comment['status'] == 0): ?>
                                            <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?action=approve&id=<?php echo $comment['id']; ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?action=reject&id=<?php echo $comment['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php elseif ($comment['status'] == 1): ?>
                                            <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?action=reject&id=<?php echo $comment['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?action=approve&id=<?php echo $comment['id']; ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo BASE_URL; ?>/admin/blog-comments.php?action=delete&id=<?php echo $comment['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa bình luận này?');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $status !== null ? '&status=' . $status : ''; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $start_page + 4);
                        if ($end_page - $start_page < 4) {
                            $start_page = max(1, $end_page - 4);
                        }
                        ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo $status !== null ? '&status=' . $status : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $status !== null ? '&status=' . $status : ''; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
