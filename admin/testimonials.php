<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = '<PERSON>uản lý cảm nhận khách hàng';

// Include file header
include_once 'partials/header.php';

// Include file helper cho testimonials
include_once '../includes/testimonials-helper.php';

// Xử lý các hành động
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Xử lý xóa cảm nhận
if ($action === 'delete' && $id > 0) {
    if (delete_testimonial($id)) {
        set_flash_message('success', 'Đã xóa cảm nhận khách hàng thành công!');
    } else {
        set_flash_message('error', 'Có lỗi xảy ra khi xóa cảm nhận khách hàng!');
    }
    redirect(BASE_URL . '/admin/testimonials.php');
}

// Xử lý thay đổi trạng thái
if ($action === 'toggle_status' && $id > 0) {
    $testimonial = get_testimonial($id);
    if ($testimonial) {
        $new_status = $testimonial['status'] == 1 ? 0 : 1;
        if (update_testimonial($id, ['status' => $new_status])) {
            set_flash_message('success', 'Đã cập nhật trạng thái cảm nhận khách hàng thành công!');
        } else {
            set_flash_message('error', 'Có lỗi xảy ra khi cập nhật trạng thái cảm nhận khách hàng!');
        }
        redirect(BASE_URL . '/admin/testimonials.php');
    }
}

// Xử lý thêm/sửa cảm nhận
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $testimonial_data = [
        'customer_name' => $_POST['customer_name'] ?? '',
        'customer_age' => $_POST['customer_age'] ?? null,
        'customer_address' => $_POST['customer_address'] ?? '',
        'rating' => $_POST['rating'] ?? 5,
        'content' => $_POST['content'] ?? '',
        'product_tags' => $_POST['product_tags'] ?? '',
        'status' => isset($_POST['status']) ? 1 : 0,
    ];

    // Xử lý upload ảnh avatar khách hàng
    if (isset($_FILES['customer_photo']) && $_FILES['customer_photo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/testimonials/';

        // Tạo thư mục nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_name = time() . '_avatar_' . basename($_FILES['customer_photo']['name']);
        $target_file = $upload_dir . $file_name;

        if (move_uploaded_file($_FILES['customer_photo']['tmp_name'], $target_file)) {
            $testimonial_data['customer_photo'] = $file_name;
        }
    } else if (isset($_POST['existing_customer_photo']) && !empty($_POST['existing_customer_photo'])) {
        // Giữ lại ảnh avatar hiện tại nếu không có ảnh mới
        $testimonial_data['customer_photo'] = $_POST['existing_customer_photo'];
    }

    // Xử lý upload nhiều ảnh đánh giá
    $review_photos = [];

    // Kiểm tra xem có ảnh mới được tải lên không
    $has_new_photos = isset($_FILES['review_photos']) &&
                     isset($_FILES['review_photos']['name']) &&
                     is_array($_FILES['review_photos']['name']) &&
                     count(array_filter($_FILES['review_photos']['name'])) > 0;

    if ($has_new_photos) {
        $upload_dir = '../uploads/testimonials/';

        // Tạo thư mục nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_count = count($_FILES['review_photos']['name']);

        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['review_photos']['error'][$i] === UPLOAD_ERR_OK && !empty($_FILES['review_photos']['name'][$i])) {
                $file_name = time() . '_review_' . $i . '_' . basename($_FILES['review_photos']['name'][$i]);
                $target_file = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['review_photos']['tmp_name'][$i], $target_file)) {
                    $review_photos[] = $file_name;
                }
            }
        }

        if (!empty($review_photos)) {
            // Nếu đang cập nhật và có ảnh hiện tại, thay thế hoàn toàn bằng ảnh mới
            $testimonial_data['review_photos'] = implode(',', $review_photos);
        }
    } else if (isset($_POST['existing_review_photos']) && !empty($_POST['existing_review_photos'])) {
        // Giữ lại ảnh đánh giá hiện tại nếu không có ảnh mới
        $testimonial_data['review_photos'] = $_POST['existing_review_photos'];
    } else {
        // Đảm bảo trường review_photos được đặt thành null nếu không có ảnh
        $testimonial_data['review_photos'] = null;
    }

    // Xử lý video URL
    if (!empty($_POST['customer_video'])) {
        $testimonial_data['customer_video'] = $_POST['customer_video'];
    }

    // Xử lý nhiều video URL
    if (!empty($_POST['review_videos'])) {
        $review_videos = explode("\n", trim($_POST['review_videos']));
        $review_videos = array_map('trim', $review_videos);
        $review_videos = array_filter($review_videos);

        if (!empty($review_videos)) {
            $testimonial_data['review_videos'] = implode(',', $review_videos);
        }
    }

    // Thêm mới hoặc cập nhật
    if (isset($_POST['testimonial_id']) && intval($_POST['testimonial_id']) > 0) {
        // Cập nhật
        $testimonial_id = intval($_POST['testimonial_id']);
        if (update_testimonial($testimonial_id, $testimonial_data)) {
            set_flash_message('success', 'Đã cập nhật cảm nhận khách hàng thành công!');
        } else {
            set_flash_message('error', 'Có lỗi xảy ra khi cập nhật cảm nhận khách hàng!');
        }
    } else {
        // Thêm mới
        if (add_testimonial($testimonial_data)) {
            set_flash_message('success', 'Đã thêm cảm nhận khách hàng thành công!');
        } else {
            set_flash_message('error', 'Có lỗi xảy ra khi thêm cảm nhận khách hàng!');
        }
    }

    redirect(BASE_URL . '/admin/testimonials.php');
}

// Lấy danh sách cảm nhận khách hàng
$testimonials = get_testimonials(0, 0, null);
?>

<!-- Nội dung chính -->
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><?php echo $page_title; ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="index.php">Trang chủ</a></li>
                        <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php display_flash_message(); ?>

            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Danh sách cảm nhận khách hàng</h3>
                            <div class="card-tools">
                                <a href="add-testimonial.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> Thêm mới
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th style="width: 50px">ID</th>
                                            <th style="width: 80px">Ảnh đại diện</th>
                                            <th>Khách hàng</th>
                                            <th>Nội dung</th>
                                            <th style="width: 100px">Đánh giá</th>
                                            <th style="width: 100px">Media</th>
                                            <th style="width: 100px">Trạng thái</th>
                                            <th style="width: 150px">Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (count($testimonials) > 0): ?>
                                            <?php foreach ($testimonials as $testimonial): ?>
                                            <tr>
                                                <td><?php echo $testimonial['id']; ?></td>
                                                <td>
                                                    <?php if (!empty($testimonial['customer_photo'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/testimonials/<?php echo $testimonial['customer_photo']; ?>" alt="<?php echo htmlspecialchars($testimonial['customer_name']); ?>" class="img-thumbnail" style="max-width: 50px;">
                                                    <?php else: ?>
                                                    <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.jpg" alt="Default" class="img-thumbnail" style="max-width: 50px;">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($testimonial['customer_name']); ?></strong>
                                                    <?php if (!empty($testimonial['customer_age'])): ?>
                                                    <br><small><?php echo $testimonial['customer_age']; ?> tuổi</small>
                                                    <?php endif; ?>
                                                    <?php if (!empty($testimonial['customer_address'])): ?>
                                                    <br><small><?php echo htmlspecialchars($testimonial['customer_address']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars(substr($testimonial['content'], 0, 100)) . (strlen($testimonial['content']) > 100 ? '...' : ''); ?></td>
                                                <td>
                                                    <?php
                                                    $rating = intval($testimonial['rating']);
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        if ($i <= $rating) {
                                                            echo '<i class="fas fa-star text-warning"></i>';
                                                        } else {
                                                            echo '<i class="far fa-star text-warning"></i>';
                                                        }
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $mediaCount = 0;

                                                    // Đếm số lượng ảnh đánh giá
                                                    if (!empty($testimonial['review_photos'])) {
                                                        $photos = explode(',', $testimonial['review_photos']);
                                                        $mediaCount += count($photos);
                                                    }

                                                    // Đếm số lượng video
                                                    if (!empty($testimonial['customer_video'])) {
                                                        $mediaCount++;
                                                    }

                                                    if (!empty($testimonial['review_videos'])) {
                                                        $videos = explode(',', $testimonial['review_videos']);
                                                        $mediaCount += count($videos);
                                                    }

                                                    if ($mediaCount > 0) {
                                                        echo '<span class="badge badge-info">' . $mediaCount . ' media</span>';
                                                    } else {
                                                        echo '<span class="badge badge-secondary">Không có</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <a href="testimonials.php?action=toggle_status&id=<?php echo $testimonial['id']; ?>" class="btn btn-sm <?php echo $testimonial['status'] == 1 ? 'btn-success' : 'btn-secondary'; ?>">
                                                        <?php echo $testimonial['status'] == 1 ? 'Hiển thị' : 'Ẩn'; ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <a href="edit-testimonial.php?id=<?php echo $testimonial['id']; ?>" class="btn btn-info btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="testimonials.php?action=delete&id=<?php echo $testimonial['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa cảm nhận này?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="8" class="text-center">Chưa có cảm nhận khách hàng nào.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Không sử dụng modal nữa -->

<!-- CSS cho preview ảnh -->
<style>
.preview-item {
    position: relative;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
}

.preview-item img {
    height: 80px;
    width: auto;
    border-radius: 4px;
}

.preview-item button {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 5px;
    font-size: 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 0 4px 0 4px;
}
</style>

<!-- Không cần script xử lý nữa -->

<?php
// Include file footer
include_once 'partials/footer.php';
?>
