<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

$page_title = 'Migration Đơn Giản - Thêm SKU';

// Xử lý chạy migration
$migration_result = '';
$migration_success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    try {
        $results = [];
        
        // Bước 1: Kiểm tra xem trường sku đã tồn tại chưa
        $check_sql = "SHOW COLUMNS FROM products LIKE 'sku'";
        $stmt = $conn->prepare($check_sql);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            // Bước 2: Thêm trường sku
            $add_column_sql = "ALTER TABLE products ADD COLUMN sku VARCHAR(50) NULL AFTER slug";
            $stmt = $conn->prepare($add_column_sql);
            $stmt->execute();
            $results[] = [
                'sql' => $add_column_sql,
                'type' => 'EXECUTE',
                'message' => 'Đã thêm trường sku vào bảng products'
            ];
            
            // Bước 3: Tạo SKU cho các sản phẩm hiện có
            $get_products_sql = "SELECT id FROM products WHERE sku IS NULL OR sku = '' ORDER BY id";
            $stmt = $conn->prepare($get_products_sql);
            $stmt->execute();
            $products = $stmt->fetchAll();
            
            $counter = 1;
            foreach ($products as $product) {
                $sku = 'NTBV-SP' . str_pad($counter, 3, '0', STR_PAD_LEFT);
                $update_sql = "UPDATE products SET sku = :sku WHERE id = :id";
                $stmt = $conn->prepare($update_sql);
                $stmt->bindParam(':sku', $sku);
                $stmt->bindParam(':id', $product['id']);
                $stmt->execute();
                $counter++;
            }
            
            $results[] = [
                'sql' => 'UPDATE products SET sku = ... (multiple)',
                'type' => 'EXECUTE',
                'message' => 'Đã tạo SKU cho ' . count($products) . ' sản phẩm'
            ];
            
            // Bước 4: Thêm constraint UNIQUE
            $unique_sql = "ALTER TABLE products ADD CONSTRAINT sku_unique UNIQUE (sku)";
            $stmt = $conn->prepare($unique_sql);
            $stmt->execute();
            $results[] = [
                'sql' => $unique_sql,
                'type' => 'EXECUTE',
                'message' => 'Đã thêm constraint UNIQUE cho trường sku'
            ];
            
            // Bước 5: Thay đổi trường thành NOT NULL
            $not_null_sql = "ALTER TABLE products MODIFY COLUMN sku VARCHAR(50) NOT NULL";
            $stmt = $conn->prepare($not_null_sql);
            $stmt->execute();
            $results[] = [
                'sql' => $not_null_sql,
                'type' => 'EXECUTE',
                'message' => 'Đã thay đổi trường sku thành NOT NULL'
            ];
            
            // Bước 6: Tạo index
            $index_sql = "CREATE INDEX idx_products_sku ON products(sku)";
            $stmt = $conn->prepare($index_sql);
            $stmt->execute();
            $results[] = [
                'sql' => $index_sql,
                'type' => 'EXECUTE',
                'message' => 'Đã tạo index cho trường sku'
            ];
            
        } else {
            $results[] = [
                'sql' => 'SHOW COLUMNS FROM products LIKE \'sku\'',
                'type' => 'SKIP',
                'message' => 'Trường sku đã tồn tại, bỏ qua migration'
            ];
        }
        
        // Bước 7: Kiểm tra kết quả
        $check_result_sql = "SELECT id, name, sku FROM products ORDER BY id LIMIT 10";
        $stmt = $conn->prepare($check_result_sql);
        $stmt->execute();
        $sample_products = $stmt->fetchAll();
        
        $results[] = [
            'sql' => $check_result_sql,
            'type' => 'SELECT',
            'data' => $sample_products,
            'message' => 'Kiểm tra kết quả migration'
        ];
        
        $migration_result = $results;
        $migration_success = true;
        
    } catch (Exception $e) {
        $migration_result = 'Lỗi: ' . $e->getMessage();
        $migration_success = false;
    }
}

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Migration Đơn Giản - Thêm trường SKU</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Migration: Thêm trường SKU vào bảng products</h6>
                </div>
                <div class="card-body">
                    <?php if (!$migration_success && empty($migration_result)): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Thông tin Migration</h5>
                        <p>Migration này sẽ thực hiện các thao tác sau:</p>
                        <ul>
                            <li>Kiểm tra xem trường <code>sku</code> đã tồn tại chưa</li>
                            <li>Thêm trường <code>sku</code> vào bảng <code>products</code></li>
                            <li>Tự động tạo SKU cho các sản phẩm hiện có theo format: <strong>NTBV-SP001, NTBV-SP002, ...</strong></li>
                            <li>Thêm constraint UNIQUE cho trường SKU</li>
                            <li>Thay đổi trường SKU thành NOT NULL</li>
                            <li>Tạo index cho trường SKU để tăng hiệu suất</li>
                            <li>Hiển thị danh sách sản phẩm với SKU mới</li>
                        </ul>
                    </div>
                    
                    <form method="POST">
                        <button type="submit" name="run_migration" class="btn btn-primary" onclick="return confirm('Bạn có chắc chắn muốn chạy migration này?')">
                            <i class="fas fa-play"></i> Chạy Migration
                        </button>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách sản phẩm
                        </a>
                    </form>
                    <?php endif; ?>

                    <?php if ($migration_success): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Migration thành công!</h5>
                        <p>Trường SKU đã được thêm vào bảng products thành công.</p>
                    </div>

                    <h6>Kết quả thực thi:</h6>
                    <?php foreach ($migration_result as $result): ?>
                    <div class="card mb-2">
                        <div class="card-body">
                            <h6 class="card-title">
                                <?php if ($result['type'] === 'SELECT'): ?>
                                <span class="badge badge-info">SELECT</span>
                                <?php elseif ($result['type'] === 'EXECUTE'): ?>
                                <span class="badge badge-success">EXECUTE</span>
                                <?php else: ?>
                                <span class="badge badge-warning">SKIP</span>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($result['message'] ?? ''); ?>
                            </h6>
                            <code class="d-block mb-2"><?php echo htmlspecialchars($result['sql']); ?></code>
                            
                            <?php if ($result['type'] === 'SELECT' && !empty($result['data'])): ?>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <?php foreach (array_keys($result['data'][0]) as $column): ?>
                                            <th><?php echo htmlspecialchars($column); ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result['data'] as $row): ?>
                                        <tr>
                                            <?php foreach ($row as $value): ?>
                                            <td><?php echo htmlspecialchars($value); ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <div class="mt-3">
                        <a href="products.php" class="btn btn-success">
                            <i class="fas fa-list"></i> Xem danh sách sản phẩm
                        </a>
                        <a href="product-add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm sản phẩm mới
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if (!$migration_success && !empty($migration_result) && is_string($migration_result)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Lỗi Migration</h5>
                        <p><?php echo htmlspecialchars($migration_result); ?></p>
                    </div>
                    
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại danh sách sản phẩm
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Hướng dẫn</h6>
                </div>
                <div class="card-body">
                    <h6>Format SKU:</h6>
                    <ul>
                        <li><strong>NTBV</strong> - Viết tắt "Nội Thất Bàng Vũ"</li>
                        <li><strong>SP</strong> - Viết tắt "Sản Phẩm"</li>
                        <li><strong>001, 002, ...</strong> - Số thứ tự tự động</li>
                    </ul>
                    
                    <h6>Ví dụ SKU:</h6>
                    <ul>
                        <li>NTBV-SP001</li>
                        <li>NTBV-SP002</li>
                        <li>NTBV-SP003</li>
                    </ul>
                    
                    <div class="alert alert-warning">
                        <small><strong>Lưu ý:</strong> Sau khi chạy migration, bạn có thể thêm/sửa sản phẩm với trường SKU mới.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once 'partials/footer.php'; ?>
