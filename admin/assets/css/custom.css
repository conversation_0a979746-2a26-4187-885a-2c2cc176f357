/* Custom CSS để ghi đè một số thuộc tính */

/* <PERSON><PERSON>ều chỉnh bảng để hiển thị gọn hơn */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    table-layout: auto;
}

.table th,
.table td {
    white-space: nowrap;
    padding: 0.5rem;
}

/* Giới hạn chiều rộng của một số cột */
.table .col-email {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .col-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .col-address {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .col-phone {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .col-actions {
    width: 100px;
}

/* Điều chỉnh sidebar và content wrapper */
.sidebar {
    width: 220px;
}

#content-wrapper {
    margin-left: 220px;
    width: calc(100% - 220px);
}

/* Fix for sidebar menu */
.sidebar .nav-item .collapse {
    position: relative;
    left: 0;
    z-index: 1;
    top: 0;
    animation: none;
}

.sidebar .nav-item .collapse .collapse-inner {
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.sidebar .nav-item .collapse .collapse-inner,
.sidebar .nav-item .collapsing .collapse-inner {
    padding: .5rem 0;
    min-width: 10rem;
    font-size: 0.85rem;
    margin: 0 0 1rem 0;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-header,
.sidebar .nav-item .collapsing .collapse-inner .collapse-header {
    margin: 0;
    white-space: nowrap;
    padding: .5rem 1.5rem;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 0.65rem;
    color: #b7b9cc;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item {
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    display: block;
    color: #3a3b45;
    text-decoration: none;
    border-radius: 0.35rem;
    white-space: nowrap;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item:hover,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item:hover {
    background-color: #eaecf4;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item:active,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item:active {
    background-color: #dddfeb;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item.active,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item.active {
    color: #4e73df;
    font-weight: 700;
}

/* Fix for nav-link with dropdown */
.sidebar .nav-item .nav-link[data-toggle="collapse"]::after {
    width: 1rem;
    text-align: center;
    float: right;
    vertical-align: 0;
    border: 0;
    font-weight: 900;
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
}

.sidebar .nav-item .nav-link[data-toggle="collapse"].collapsed::after {
    content: '\f105';
}

@media (max-width: 768px) {
    .sidebar {
        width: 0;
    }

    #content-wrapper {
        margin-left: 0;
        width: 100%;
    }

    .sidebar.toggled {
        width: 220px;
    }

    #content-wrapper.toggled {
        margin-left: 220px;
        width: calc(100% - 220px);
    }
}

/* Điều chỉnh container */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Điều chỉnh card */
.card {
    margin-bottom: 1rem;
}

.card-body {
    padding: 1rem;
}

/* Điều chỉnh nút */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Điều chỉnh dropdown menu */
.dropdown-menu {
    min-width: 10rem;
}

/* Điều chỉnh form */
.form-group {
    margin-bottom: 0.75rem;
}

/* Điều chỉnh phân trang */
.pagination {
    margin-top: 1rem;
    margin-bottom: 0;
}

/* Điều chỉnh badge */
.badge {
    font-size: 90%;
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
}

/* Làm nổi bật badge trạng thái */
.badge-success {
    background-color: #28a745;
    color: #fff;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    border: 1px solid #1e7e34;
}

.badge-danger {
    background-color: #dc3545;
    color: #fff;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    border: 1px solid #bd2130;
}

/* Hiệu ứng hover cho badge */
.badge-success:hover,
.badge-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    cursor: default;
}

/* Thêm biểu tượng cho badge trạng thái */
.badge-success::before {
    content: "\f058";
    /* FontAwesome check-circle icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    /* Cần thiết cho FontAwesome 5 */
    margin-right: 4px;
    display: inline-block;
}

.badge-danger::before {
    content: "\f057";
    /* FontAwesome times-circle icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    /* Cần thiết cho FontAwesome 5 */
    margin-right: 4px;
    display: inline-block;
}

/* Làm cho badge lớn hơn trong bảng */
.table td .badge {
    font-size: 95%;
    padding: 0.4em 0.7em;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

/* Điều chỉnh dropdown toggle */
.dropdown-toggle::after {
    display: none;
}

/* Điều chỉnh dropdown menu */
.dropdown-menu {
    font-size: 0.85rem;
}

/* Điều chỉnh dropdown item */
.dropdown-item {
    padding: 0.25rem 1rem;
}

/* Điều chỉnh bảng trong trang chi tiết */
.table-detail th {
    width: 30%;
}

/* Điều chỉnh hình ảnh sản phẩm */
.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
}

/* CSS cho thông báo (alert-container) */
.alert-container {
    margin-bottom: 20px;
    position: relative;
}

.custom-alert {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    border-left: 5px solid transparent;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.custom-alert-success {
    border-left-color: #28a745;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.custom-alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.custom-alert-warning {
    border-left-color: #ffc107;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
}

.custom-alert-info {
    border-left-color: #17a2b8;
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
}

.alert-icon {
    margin-right: 15px;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

/* Đảm bảo icon nằm chính giữa */
.alert-icon i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
}

.custom-alert-success .alert-icon {
    color: #155724;
    background-color: #ffffff;
    border: 2px solid #28a745;
}

.custom-alert-danger .alert-icon {
    color: #721c24;
    background-color: #ffffff;
    border: 2px solid #dc3545;
}

.custom-alert-warning .alert-icon {
    color: #856404;
    background-color: #ffffff;
    border: 2px solid #ffc107;
}

.custom-alert-info .alert-icon {
    color: #0c5460;
    background-color: #ffffff;
    border: 2px solid #17a2b8;
}

.alert-content {
    flex: 1;
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
    padding: 0 5px;
}

.custom-alert-success .alert-content {
    color: #155724;
}

.custom-alert-danger .alert-content {
    color: #721c24;
}

.custom-alert-warning .alert-content {
    color: #856404;
}

.custom-alert-info .alert-content {
    color: #0c5460;
}

.alert-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 6px;
    margin-left: 10px;
    opacity: 0.7;
    transition: all 0.3s;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-alert-success .alert-close {
    color: #155724;
}

.custom-alert-danger .alert-close {
    color: #721c24;
}

.custom-alert-warning .alert-close {
    color: #856404;
}

.custom-alert-info .alert-close {
    color: #0c5460;
}

.alert-close:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
}

/* Hiệu ứng xuất hiện và biến mất */
.fade-in {
    animation: fadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-out {
    animation: fadeOut 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.98);
    }
    50% {
        opacity: 0.8;
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-15px) scale(0.98);
    }
}

/* Thêm hiệu ứng rung khi có lỗi */
.custom-alert-danger.fade-in {
    animation: fadeInShake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes fadeInShake {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.98);
    }
    50% {
        opacity: 0.8;
        transform: translateY(0) scale(1);
    }
    60%, 70%, 80% {
        transform: translateX(-2px);
    }
    65%, 75%, 85% {
        transform: translateX(2px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Thiết kế mới cho admin dashboard - tông màu sáng */

/* Dashboard card nổi bật */
.dashboard-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card .icon {
    font-size: 2rem;
    opacity: 0.8;
}

.dashboard-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    color: #5a5c69;
}

.dashboard-card .card-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.dashboard-card .card-percent {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
}

.dashboard-card .card-percent i {
    margin-right: 0.25rem;
}

/* Thiết kế card màu sắc */
.card-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
}

.card-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    color: white;
}

.card-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    color: white;
}

.card-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
    color: white;
}

.card-primary .card-title,
.card-success .card-title,
.card-warning .card-title,
.card-danger .card-title {
    color: rgba(255, 255, 255, 0.8);
}

/* Cải thiện hiệu ứng nút */
.btn {
    border-radius: 5px;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #3a5fd7;
    border-color: #3a5fd7;
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #17a673;
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
}

/* Nút với icon */
.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon i {
    margin-right: 0.5rem;
}

/* Thiết kế bảng hiện đại */
.table-modern {
    border-collapse: separate;
    border-spacing: 0 5px;
}

.table-modern thead th {
    border: none;
    background-color: #f8f9fc;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #5a5c69;
}

.table-modern tbody tr {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    border-radius: 5px;
    transition: all 0.2s;
}

.table-modern tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
}

.table-modern tbody td {
    border: none;
    background-color: #ffffff;
    padding: 1rem;
    vertical-align: middle;
}

.table-modern tbody td:first-child {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.table-modern tbody td:last-child {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

/* Badges hiện đại */
.badge-modern {
    padding: 0.4em 0.7em;
    font-weight: 500;
    border-radius: 30px;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.badge-modern-success {
    background-color: #e6f7ef;
    color: #1cc88a;
    border: 1px solid #d1f0e2;
}

.badge-modern-warning {
    background-color: #fef7e6;
    color: #f6c23e;
    border: 1px solid #faecc8;
}

.badge-modern-danger {
    background-color: #fceae8;
    color: #e74a3b;
    border: 1px solid #f8d6d2;
}

.badge-modern-primary {
    background-color: #e9ecf9;
    color: #4e73df;
    border: 1px solid #d6ddf5;
}

/* Topbar cải tiến */
.topbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

.topbar .navbar-nav .nav-item .nav-link {
    color: #5a5c69;
    font-weight: 500;
    padding: 0 1rem;
    height: 4.375rem;
    display: flex;
    align-items: center;
}

.topbar .navbar-nav .nav-item .nav-link:hover {
    color: #4e73df;
}

.topbar .navbar-nav .nav-item .nav-link i {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.img-profile {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border: 2px solid #eaecf4;
}

/* Progress bar hiện đại */
.progress {
    height: 0.8rem;
    border-radius: 10px;
    background-color: #eaecf4;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-bar {
    border-radius: 10px;
    background-size: 15px 15px;
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    from {
        background-position: 0 0;
    }
    to {
        background-position: 30px 0;
    }
}

/*=======================
   Modern Tab Bar Styles
========================*/

/* Thiết kế mới cho Tab Bar chung trong khu vực Admin */
.nav-tabs {
    border-bottom: 2px solid #e3e6f0; /* Tông màu nhạt để phân tách */
}

.nav-tabs .nav-item + .nav-item {
    margin-left: 0.25rem; /* Khoảng cách giữa các tab */
}

.nav-tabs .nav-link {
    border: none; /* Loại bỏ viền mặc định */
    background: transparent; /* Trong suốt để dễ phối màu */
    color: #5a5c69; /* Màu chữ mặc định */
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    position: relative;
    transition: color 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #4e73df; /* Màu chủ đạo khi hover */
}

/* Hiệu ứng gạch chân khi Active */
.nav-tabs .nav-link.active {
    color: #4e73df; /* Màu chủ đạo cho tab active */
    font-weight: 600;
}

.nav-tabs .nav-link.active::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: -2px; /* Bám sát đường viền dưới */
    height: 3px;
    background: #4e73df; /* Gạch chân cùng màu chủ đạo */
    border-radius: 3px 3px 0 0;
}

/* Badge bên trong tab */
.nav-tabs .nav-link .badge {
    background: #e9ecf9; /* Nền badge nhẹ */
    color: #4e73df;
    font-weight: 600;
}

.nav-tabs .nav-link.active .badge {
    background: #4e73df; /* Đảo màu khi active */
    color: #ffffff;
}

/* Bo góc cho tab-content khi đặt ngay dưới nav-tabs (tuỳ layout) */
.nav-tabs + .tab-content {
    border: 1px solid #e3e6f0;
    border-top: none; /* Tránh trùng với đường viền nav-tabs */
    border-radius: 0 0 0.35rem 0.35rem;
    padding: 1.5rem;
}

/*========================
   Product Page Styles
========================*/

/* Card bộ lọc sản phẩm */
.card-filter {
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: .35rem;
}

.card-filter .card-header {
    background-color: transparent;
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 0.75rem;
    padding-top: 0.75rem;
}

.card-filter .card-body .form-inline .form-group {
    margin-bottom: 0.5rem; /* Giảm khoảng cách cho form inline */
}

.card-filter .card-body .form-inline .btn {
    margin-bottom: 0.5rem; /* Đồng bộ margin bottom cho button */
}

/* Thumbnail sản phẩm trong bảng */
.product-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: .25rem;
    border: 1px solid #e3e6f0;
    padding: 2px;
    background-color: #fff;
    transition: transform 0.2s ease-in-out;
}

.product-thumbnail:hover {
    transform: scale(1.5); /* Phóng to nhẹ khi hover */
    z-index: 10;
    position: relative; /* Cần để z-index có tác dụng */
}

/* Nút thao tác trong bảng */
.table-modern .btn-icon {
    padding: 0.3rem 0.5rem; /* Điều chỉnh padding cho nút icon nhỏ hơn */
    font-size: 0.8rem; /* Điều chỉnh font-size cho icon */
}

.table-modern .btn-icon i {
    margin-right: 0; /* Bỏ margin-right nếu chỉ có icon */
}

/* Làm cho tên sản phẩm nổi bật hơn và có thể click */
.table-modern tbody td a.font-weight-bold {
    color: #4e73df;
    text-decoration: none;
}

.table-modern tbody td a.font-weight-bold:hover {
    color: #224abe;
    text-decoration: underline;
}

/* Căn phải cho cột giá */
.table-modern th.text-right,
.table-modern td.text-right {
    text-align: right !important;
}

/* Căn giữa cho các cột cần thiết */
.table-modern th.text-center,
.table-modern td.text-center {
    text-align: center !important;
    vertical-align: middle;
}

/* Thêm chút padding cho card chứa bảng */
.card > .card-header + .card-body {
    /* Chỉ áp dụng nếu card-body ngay sau card-header */
    /* padding-top: 0.5rem; */ /*Có thể bỏ nếu thấy không cần thiết*/
}

.card .card-header h6 {
    font-size: 1rem; /* Tăng nhẹ kích thước font tiêu đề card */
}


/* Nút "Đặt lại" trong bộ lọc */
.form-inline .btn-secondary.btn-icon i {
    margin-right: 0.3rem;
}

.form-inline .btn-primary.btn-icon i{
    margin-right: 0.3rem;
}

.btn-icon.btn-sm i {
    margin-right: 0.3rem; /* Cho nút "Thêm sản phẩm" */
}