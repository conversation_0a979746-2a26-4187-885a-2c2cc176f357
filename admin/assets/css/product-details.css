/**
 * CSS cho phần mô tả chi tiết sản phẩm
 */

/* Tabs */
.product-details-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.product-details-tab-btn {
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    color: #4a5568;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.product-details-tab-btn:hover {
    color: #2d3748;
    background-color: #f7fafc;
}

.product-details-tab-btn.active {
    color: #3182ce;
    border-bottom-color: #3182ce;
    background-color: #ebf8ff;
}

.product-details-tab-content {
    display: none;
    padding: 1rem 0;
}

.product-details-tab-content.active {
    display: block;
}

/* Card styles */
.product-details-card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.product-details-card-header {
    background-color: #f8fafc;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
}

.product-details-card-header i {
    margin-right: 0.5rem;
    color: #4299e1;
}

.product-details-card-body {
    padding: 1rem;
}

/* Form elements */
.product-details-form-group {
    margin-bottom: 1rem;
}

.product-details-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
}

.product-details-form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out;
}

.product-details-form-control:focus {
    border-color: #4299e1;
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

.product-details-textarea {
    min-height: 100px;
    resize: vertical;
}

/* Buttons */
.product-details-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.product-details-btn-primary {
    background-color: #4299e1;
    color: #fff;
    border: none;
}

.product-details-btn-primary:hover {
    background-color: #3182ce;
}

.product-details-btn-danger {
    background-color: #f56565;
    color: #fff;
    border: none;
}

.product-details-btn-danger:hover {
    background-color: #e53e3e;
}

.product-details-btn-secondary {
    background-color: #edf2f7;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.product-details-btn-secondary:hover {
    background-color: #e2e8f0;
}

.product-details-btn i {
    margin-right: 0.5rem;
}

/* List items */
.feature-row,
.specification-row,
.step-row,
.note-row,
.condition-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.feature-row input,
.step-row input,
.note-row input,
.condition-row input {
    flex-grow: 1;
    margin-right: 0.5rem;
}

.specification-row .col-md-5,
.specification-row .col-md-6,
.specification-row .col-md-1 {
    padding-right: 0.5rem;
}

.remove-feature-btn,
.remove-spec-btn,
.remove-step-btn,
.remove-note-btn,
.remove-condition-btn {
    background-color: #f56565;
    color: #fff;
    border: none;
    border-radius: 0.25rem;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.remove-feature-btn:hover,
.remove-spec-btn:hover,
.remove-step-btn:hover,
.remove-note-btn:hover,
.remove-condition-btn:hover {
    background-color: #e53e3e;
}

/* Helper classes */
.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.flex-grow {
    flex-grow: 1;
}

.mr-2 {
    margin-right: 0.5rem;
}

.pr-2 {
    padding-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .product-details-tabs {
        flex-wrap: wrap;
    }
    
    .product-details-tab-btn {
        flex-basis: 50%;
        text-align: center;
    }
    
    .specification-row {
        flex-wrap: wrap;
    }
    
    .specification-row .col-md-5,
    .specification-row .col-md-6 {
        flex-basis: 100%;
        margin-bottom: 0.5rem;
    }
    
    .specification-row .col-md-1 {
        flex-basis: 100%;
        text-align: right;
    }
}
