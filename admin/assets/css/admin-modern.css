/**
 * Admin Modern CSS - Nội Thất Bàng <PERSON>
 * Thiết kế hiện đại cho sidebar và topbar trong trang admin
 * Sử dụng tông màu sáng (trắng, xám nhạt, xanh nhạt)
 */

:root {
    /* <PERSON><PERSON><PERSON> sắc ch<PERSON>h */
    --admin-primary: #f8f9fa;
    --admin-secondary: #e9ecef;
    --admin-accent: #4e73df;
    --admin-accent-hover: #3a5fc8;
    --admin-text: #495057;
    --admin-text-muted: #6c757d;
    --admin-border: #dee2e6;
    --admin-active: #4e73df;
    --admin-hover: #edf2ff;

    /* Hi<PERSON>u ứng */
    --admin-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --admin-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --admin-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
    --admin-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);

    /* <PERSON><PERSON><PERSON> thước */
    --sidebar-width: 250px;
    --topbar-height: 60px;
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
    width: var(--sidebar-width);
    min-height: 100vh;
    position: fixed;
    z-index: 100;
    top: 0;
    left: 0;
    background-color: var(--admin-primary);
    overflow-x: hidden;
    transition: var(--admin-transition);
    box-shadow: var(--admin-shadow-md);
    border-right: 1px solid var(--admin-border);
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Tùy chỉnh thanh cuộn cho sidebar */
.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Logo trong sidebar */
.sidebar .sidebar-brand {
    height: var(--topbar-height);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.sidebar .sidebar-brand .sidebar-logo {
    max-width: 100%;
    height: auto;
    max-height: 40px;
}

/* Divider trong sidebar */
.sidebar hr.sidebar-divider {
    margin: 0.5rem 1rem;
    border-top: 1px solid var(--admin-border);
}

/* Heading trong sidebar */
.sidebar .sidebar-heading {
    padding: 0.75rem 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    color: var(--admin-text-muted);
}

/* Nav item trong sidebar */
.sidebar .nav-item {
    position: relative;
    margin-bottom: 0.25rem;
}

.sidebar .nav-item .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--admin-text);
    font-weight: 500;
    border-radius: var(--border-radius-md);
    margin: 0 0.5rem;
    transition: var(--admin-transition);
}

.sidebar .nav-item .nav-link i {
    font-size: 0.85rem;
    margin-right: 0.75rem;
    color: var(--admin-text-muted);
    transition: var(--admin-transition);
    width: 1.25rem;
    text-align: center;
}

.sidebar .nav-item .nav-link:hover {
    background-color: var(--admin-hover);
    color: var(--admin-accent);
}

.sidebar .nav-item .nav-link:hover i {
    color: var(--admin-accent);
}

.sidebar .nav-item.active .nav-link {
    background-color: var(--admin-hover);
    color: var(--admin-accent);
    font-weight: 600;
}

.sidebar .nav-item.active .nav-link i {
    color: var(--admin-accent);
}

/* Dropdown menu trong sidebar */
.sidebar .nav-item .collapse {
    margin-left: 2rem;
}

.sidebar .nav-link[data-toggle="collapse"]::after {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    content: '\f107';
    margin-left: auto;
    transition: var(--admin-transition);
}

.sidebar .nav-link[data-toggle="collapse"].collapsed::after {
    content: '\f105';
}

.sidebar .collapse .collapse-inner {
    background-color: var(--admin-secondary);
    border-radius: var(--border-radius-md);
    margin: 0.25rem 0.5rem 0.5rem 0;
    padding: 0.5rem;
}

.sidebar .collapse-header {
    color: var(--admin-text-muted) !important;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
}

.sidebar .collapse-item {
    display: block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    color: var(--admin-text);
    font-size: 0.85rem;
    border-radius: var(--border-radius-sm);
    transition: var(--admin-transition);
}

.sidebar .collapse-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--admin-accent);
}

.sidebar .collapse-item.active {
    color: var(--admin-accent);
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Avatar admin ở dưới cùng sidebar */
.sidebar-user {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    display: flex;
    align-items: center;
    background-color: var(--admin-primary);
    border-top: 1px solid var(--admin-border);
}

.sidebar-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.75rem;
    border: 2px solid var(--admin-border);
}

.sidebar-user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-user-info {
    flex: 1;
}

.sidebar-user-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
}

.sidebar-user-role {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin: 0;
}

.sidebar-user-logout {
    color: var(--admin-text-muted);
    font-size: 1.1rem;
    transition: var(--admin-transition);
}

.sidebar-user-logout:hover {
    color: var(--admin-accent);
}

/* ===== TOPBAR STYLES ===== */
.topbar {
    height: var(--topbar-height);
    background-color: var(--admin-primary);
    box-shadow: var(--admin-shadow-sm);
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 90;
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

/* Thanh tìm kiếm trong topbar */
.topbar-search {
    flex: 1;
    max-width: 400px;
    position: relative;
}

.topbar-search-input {
    width: 100%;
    height: 38px;
    padding: 0.375rem 1rem 0.375rem 2.5rem;
    font-size: 0.85rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--border-radius-lg);
    background-color: var(--admin-secondary);
    color: var(--admin-text);
    transition: var(--admin-transition);
}

.topbar-search-input:focus {
    outline: none;
    border-color: var(--admin-accent);
    background-color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.topbar-search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-text-muted);
    font-size: 0.85rem;
}

/* Icons trong topbar */
.topbar-icons {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.topbar-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--admin-text);
    font-size: 1rem;
    margin-left: 0.5rem;
    transition: var(--admin-transition);
    position: relative;
}

.topbar-icon:hover {
    background-color: var(--admin-hover);
    color: var(--admin-accent);
}

.topbar-icon-badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #e74a3b;
    color: #fff;
    font-size: 0.65rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--admin-primary);
}

/* Avatar trong topbar */
.topbar-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: 1rem;
    border: 2px solid var(--admin-border);
    cursor: pointer;
    transition: var(--admin-transition);
}

.topbar-avatar:hover {
    border-color: var(--admin-accent);
    box-shadow: 0 0 0 4px rgba(78, 115, 223, 0.25);
}

.topbar-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Content wrapper */
#content-wrapper {
    margin-left: var(--sidebar-width);
    padding-top: var(--topbar-height);
    min-height: 100vh;
    transition: var(--admin-transition);
    background-color: #f8f9fc;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.toggled {
        transform: translateX(0);
    }
    
    .topbar {
        left: 0;
    }
    
    #content-wrapper {
        margin-left: 0;
    }
    
    .topbar-search {
        max-width: 200px;
    }
}
