/**
 * CSS cho CustomEditor - Tr<PERSON>nh soạn thảo văn bản đơn giản
 */

/* Container chính */
.custom-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Thanh công cụ */
.custom-editor-toolbar {
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

/* Nhóm nút */
.custom-editor-button-group {
    display: inline-flex;
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Nút */
.custom-editor-button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 3px;
    color: #333;
    font-size: 14px;
    transition: background-color 0.2s;
}

.custom-editor-button:hover {
    background-color: #e9ecef;
}

.custom-editor-button:active {
    background-color: #dee2e6;
}

.custom-editor-button i {
    font-size: 14px;
}

/* <PERSON><PERSON> chọn */
.custom-editor-select {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 5px 8px;
    background-color: #fff;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    height: 32px;
}

.custom-editor-select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Dấu phân cách */
.custom-editor-separator {
    display: inline-block;
    width: 1px;
    height: 20px;
    background-color: #ddd;
    margin: 0 5px;
    vertical-align: middle;
}

/* Khu vực soạn thảo */
.custom-editor-area {
    padding: 15px;
    min-height: 200px;
    max-height: 600px;
    overflow-y: auto;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
}

.custom-editor-area:focus {
    outline: none;
}

.custom-editor-area:empty:before {
    content: attr(placeholder);
    color: #aaa;
    font-style: italic;
}

/* Chế độ xem HTML */
.custom-editor-html-view {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: none;
    outline: none;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    resize: vertical;
}

/* Bộ chọn màu */
.custom-editor-color-picker {
    display: inline-block;
    position: relative;
}

.custom-editor-color-picker-panel {
    position: absolute;
    top: 30px;
    left: 0;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    width: 150px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Định dạng nội dung trong trình soạn thảo */
.custom-editor-area h1,
.custom-editor-area h2,
.custom-editor-area h3,
.custom-editor-area h4,
.custom-editor-area h5,
.custom-editor-area h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    color: #333;
}

.custom-editor-area h1 {
    font-size: 2em;
}

.custom-editor-area h2 {
    font-size: 1.5em;
}

.custom-editor-area h3 {
    font-size: 1.17em;
}

.custom-editor-area h4 {
    font-size: 1em;
}

.custom-editor-area h5 {
    font-size: 0.83em;
}

.custom-editor-area h6 {
    font-size: 0.67em;
}

.custom-editor-area p {
    margin-bottom: 1em;
}

.custom-editor-area ul,
.custom-editor-area ol {
    padding-left: 2em;
    margin-bottom: 1em;
}

.custom-editor-area ul {
    list-style-type: disc;
}

.custom-editor-area ol {
    list-style-type: decimal;
}

.custom-editor-area li {
    margin-bottom: 0.5em;
}

.custom-editor-area a {
    color: #007bff;
    text-decoration: none;
}

.custom-editor-area a:hover {
    text-decoration: underline;
}

.custom-editor-area img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
}

.custom-editor-area table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.custom-editor-area table th,
.custom-editor-area table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.custom-editor-area table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.custom-editor-area blockquote {
    border-left: 4px solid #ddd;
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    color: #666;
    margin: 1em 0;
}

/* Responsive */
@media (max-width: 768px) {
    .custom-editor-toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .custom-editor-button-group {
        margin-bottom: 10px;
    }
}
