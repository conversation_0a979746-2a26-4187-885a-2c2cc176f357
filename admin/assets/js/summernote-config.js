/**
 * <PERSON><PERSON><PERSON> hình và khởi tạo Summernote
 */
$(document).ready(function() {
    console.log('Summernote config loaded');

    // Đ<PERSON>m bảo jQuery và Summernote đã được tải
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
        return;
    }

    if (typeof $.summernote === 'undefined') {
        console.error('Summernote is not loaded!');
        return;
    }

    console.log('jQuery and Summernote are available');

    // Cấu hình chung cho Summernote
    const summernoteConfig = {
        height: 400,
        lang: 'vi-VN', // Sử dụng ngôn ngữ tiếng Việt
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'video']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        fontNames: ['Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana'],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '24', '36'],
        callbacks: {
            onInit: function() {
                console.log('Summernote successfully initialized');
            }
        }
    };

    // Khởi tạo Summernote cho blog
    if ($('#summernote').length) {
        console.log('Blog Summernote element found, initializing...');

        try {
            // Khởi tạo Summernote cho blog
            $('#summernote').summernote({
                ...summernoteConfig,
                placeholder: 'Nhập nội dung bài viết...',
                callbacks: {
                    ...summernoteConfig.callbacks,
                    onChange: function(contents) {
                        // Cập nhật nội dung vào textarea khi có thay đổi
                        $('#content').val(contents);
                        console.log('Blog content updated');
                    },
                    onImageUpload: function(files) {
                        // Xử lý upload ảnh
                        for (let i = 0; i < files.length; i++) {
                            uploadImage(files[i], this);
                        }
                    }
                }
            });

            console.log('Blog Summernote initialized successfully');

            // Xử lý form submit
            $('form').on('submit', function() {
                // Đảm bảo nội dung được cập nhật vào textarea trước khi submit
                $('#content').val($('#summernote').summernote('code'));
                console.log('Form submitted, blog content updated');
            });

        } catch (error) {
            console.error('Error initializing Blog Summernote:', error);
        }
    } else {
        console.log('Blog Summernote element not found');
    }

    // Khởi tạo Summernote cho tổng quan sản phẩm
    if ($('#product-overview-editor').length) {
        console.log('Product Overview Summernote element found, initializing...');

        try {
            // Khởi tạo Summernote cho tổng quan sản phẩm
            $('#product-overview-editor').summernote({
                ...summernoteConfig,
                height: 300,
                placeholder: 'Nhập tổng quan về sản phẩm...',
                callbacks: {
                    ...summernoteConfig.callbacks,
                    onImageUpload: function(files) {
                        // Xử lý upload ảnh
                        for (let i = 0; i < files.length; i++) {
                            uploadProductImage(files[i], this);
                        }
                    }
                }
            });

            console.log('Product Overview Summernote initialized successfully');

        } catch (error) {
            console.error('Error initializing Product Overview Summernote:', error);
        }
    } else {
        console.log('Product Overview Summernote element not found');
    }

    // Hàm xử lý upload ảnh cho blog
    function uploadImage(file, editor) {
        let formData = new FormData();
        formData.append('file', file);
        formData.append('action', 'upload_image');

        console.log('Uploading blog image...');

        $.ajax({
            url: BASE_URL + '/admin/ajax/upload-image.php',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                try {
                    let data = JSON.parse(response);
                    if (data.success) {
                        // Chèn ảnh vào editor
                        $(editor).summernote('insertImage', data.url, data.filename);
                        console.log('Blog image uploaded successfully:', data.url);
                    } else {
                        alert('Lỗi: ' + data.message);
                        console.error('Error uploading blog image:', data.message);
                    }
                } catch (e) {
                    console.error('Error processing response:', e);
                    alert('Có lỗi xảy ra khi tải lên ảnh.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                alert('Có lỗi xảy ra khi tải lên ảnh.');
            }
        });
    }

    // Hàm xử lý upload ảnh cho sản phẩm
    function uploadProductImage(file, editor) {
        let formData = new FormData();
        formData.append('file', file);
        formData.append('action', 'upload_product_image');
        formData.append('type', 'product_content');

        console.log('Uploading product image...');

        $.ajax({
            url: BASE_URL + '/admin/ajax/upload-product-image.php',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                try {
                    let data = JSON.parse(response);
                    if (data.success) {
                        // Chèn ảnh vào editor
                        $(editor).summernote('insertImage', data.url, data.filename);
                        console.log('Product image uploaded successfully:', data.url);
                    } else {
                        alert('Lỗi: ' + data.message);
                        console.error('Error uploading product image:', data.message);
                    }
                } catch (e) {
                    console.error('Error processing response:', e);
                    alert('Có lỗi xảy ra khi tải lên ảnh.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                alert('Có lỗi xảy ra khi tải lên ảnh.');
            }
        });
    }

    // Tự động tạo slug từ tiêu đề
    $('#title').on('blur', function() {
        const slugInput = $('#slug');
        if (slugInput.val() === '') {
            const title = $(this).val();
            const slug = title
                .toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.val(slug);
            console.log('Slug generated from title:', slug);
        }
    });
});
