/**
 * Order Detail JavaScript
 * Xử lý các tương tác trong trang chi tiết đơn hàng
 */

$(document).ready(function() {
    console.log('Order Detail JS loaded');

    // Kiểm tra xem có nút cập nhật trạng thái đơn hàng không
    const updateButtons = $('.update-order-status');
    if (updateButtons.length) {
        console.log('Found', updateButtons.length, 'update order status buttons');

        // Gắn sự kiện click cho các nút cập nhật trạng thái
        updateButtons.on('click', function(e) {
            e.preventDefault();

            const button = $(this);
            const orderId = button.data('order-id');
            const status = button.data('status');

            // Nếu là nút hủy đơn hàng, hiển thị xác nhận
            if (status === 'cancelled' && !confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                return;
            }

            // Lấy CSRF token từ meta tag
            const csrfToken = $('meta[name="csrf-token"]').attr('content');
            const baseUrl = $('meta[name="base-url"]').attr('content') || '';

            // Kiểm tra xem có CSRF token không
            if (!csrfToken) {
                alert('Lỗi bảo mật: Không tìm thấy CSRF token');
                return;
            }

            // Hiển thị loading
            showLoading();

            // Log để debug
            console.log('Cập nhật trạng thái đơn hàng:', {
                order_id: orderId,
                status: status,
                csrf_token: csrfToken
            });

            // Xác định đường dẫn AJAX
            const ajaxUrl = baseUrl + '/ajax/update_order_status.php';
            console.log('AJAX URL:', ajaxUrl);

            // Gửi yêu cầu AJAX
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    order_id: orderId,
                    status: status,
                    csrf_token: csrfToken
                },
                dataType: 'json',
                success: function(response) {
                    // Ẩn loading
                    hideLoading();

                    // Log response để debug
                    console.log('Response:', response);

                    if (response.success) {
                        // Cập nhật UI
                        updateOrderStatusUI(orderId, response.status, response.status_text, response.status_class);

                        // Hiển thị thông báo thành công
                        showNotification(response.message, 'success');

                        // Ẩn các nút cập nhật trạng thái nếu đơn hàng đã hoàn thành hoặc hủy
                        if (status === 'completed' || status === 'cancelled') {
                            $(`.status-actions[data-order-id="${orderId}"]`).hide();
                        }
                    } else {
                        // Hiển thị thông báo lỗi
                        showNotification(response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Ẩn loading
                    hideLoading();

                    // Log lỗi để debug
                    console.error('AJAX Error:', {
                        xhr: xhr,
                        status: status,
                        error: error
                    });

                    // Hiển thị thông báo lỗi chi tiết hơn
                    let errorMessage = 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.status === 403) {
                        errorMessage = 'Lỗi bảo mật: CSRF token không hợp lệ hoặc đã hết hạn.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Không tìm thấy trang xử lý yêu cầu.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';
                    }

                    showNotification(errorMessage, 'error');

                    // Nếu lỗi liên quan đến CSRF token, tải lại trang để lấy token mới
                    if (xhr.status === 403) {
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                }
            });
        });
    } else {
        console.log('No update order status buttons found');
    }

    /**
     * Cập nhật giao diện trạng thái đơn hàng
     */
    function updateOrderStatusUI(orderId, status, statusText, statusClass) {
        // Cập nhật badge trạng thái
        const statusBadge = $(`.order-status[data-order-id="${orderId}"]`);
        if (statusBadge.length) {
            statusBadge.removeClass().addClass(`order-status badge ${statusClass}`).text(statusText);
        }
    }

    /**
     * Hiển thị loading
     */
    function showLoading() {
        // Kiểm tra xem đã có overlay loading chưa
        if ($('#loading-overlay').length === 0) {
            // Tạo overlay loading
            const loadingHtml = `
                <div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">
                    <div class="spinner-border text-light" role="status">
                        <span class="sr-only">Đang xử lý...</span>
                    </div>
                </div>
            `;

            // Thêm vào body
            $('body').append(loadingHtml);
        } else {
            // Hiển thị overlay loading
            $('#loading-overlay').show();
        }
    }

    /**
     * Ẩn loading
     */
    function hideLoading() {
        $('#loading-overlay').hide();
    }

    /**
     * Hiển thị thông báo
     */
    function showNotification(message, type) {
        // Xác định class theo loại thông báo
        let alertClass = 'alert-info';
        if (type === 'success') alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-danger';
        if (type === 'warning') alertClass = 'alert-warning';

        // Tạo HTML thông báo
        const notificationHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // Thêm thông báo vào container
        const container = $('.notification-container');
        if (container.length) {
            container.append(notificationHtml);
        } else {
            // Nếu không có container, tạo mới và thêm vào đầu content
            $('.container-fluid').prepend(`<div class="notification-container">${notificationHtml}</div>`);
        }

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
});
