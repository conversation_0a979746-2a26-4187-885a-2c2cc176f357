/**
 * Product Details JS
 * Xử lý giao diện quản trị cho phần mô tả chi tiết sản phẩm
 */

document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo tabs
    initProductDetailsTabs();
    
    // Khởi tạo các thành phần tương tác
    initFeaturesList();
    initSpecificationsTable();
    initUsageGuideSteps();
    initWarrantyInfo();
    
    // Xử lý form submit
    handleFormSubmit();
});

/**
 * Khởi tạo tabs cho phần mô tả chi tiết sản phẩm
 */
function initProductDetailsTabs() {
    const tabButtons = document.querySelectorAll('.product-details-tab-btn');
    const tabContents = document.querySelectorAll('.product-details-tab-content');
    
    if (tabButtons.length === 0 || tabContents.length === 0) return;
    
    // Hiển thị tab đầu tiên mặc định
    tabContents[0].classList.add('active');
    tabButtons[0].classList.add('active');
    
    // Xử lý sự kiện click cho các tab
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Xóa class active khỏi tất cả các tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Thêm class active cho tab được chọn
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

/**
 * Khởi tạo danh sách đặc điểm nổi bật
 */
function initFeaturesList() {
    const featuresContainer = document.getElementById('features-container');
    const addFeatureBtn = document.getElementById('add-feature-btn');
    
    if (!featuresContainer || !addFeatureBtn) return;
    
    // Xử lý thêm đặc điểm mới
    addFeatureBtn.addEventListener('click', function() {
        const featureRow = document.createElement('div');
        featureRow.className = 'feature-row mb-2 flex items-center';
        featureRow.innerHTML = `
            <input type="text" name="features[]" class="form-control flex-grow mr-2" placeholder="Nhập đặc điểm nổi bật">
            <button type="button" class="btn btn-danger remove-feature-btn">
                <i class="fas fa-times"></i>
            </button>
        `;
        featuresContainer.appendChild(featureRow);
        
        // Xử lý xóa đặc điểm
        const removeBtn = featureRow.querySelector('.remove-feature-btn');
        removeBtn.addEventListener('click', function() {
            featuresContainer.removeChild(featureRow);
        });
    });
    
    // Xử lý xóa đặc điểm cho các hàng có sẵn
    document.querySelectorAll('.remove-feature-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('.feature-row');
            featuresContainer.removeChild(row);
        });
    });
}

/**
 * Khởi tạo bảng thông số kỹ thuật
 */
function initSpecificationsTable() {
    const specificationsContainer = document.getElementById('specifications-container');
    const addSpecificationBtn = document.getElementById('add-specification-btn');
    
    if (!specificationsContainer || !addSpecificationBtn) return;
    
    // Xử lý thêm thông số mới
    addSpecificationBtn.addEventListener('click', function() {
        const specRow = document.createElement('div');
        specRow.className = 'specification-row mb-2 flex items-center';
        specRow.innerHTML = `
            <div class="col-md-5 pr-2">
                <input type="text" name="spec_keys[]" class="form-control" placeholder="Tên thông số">
            </div>
            <div class="col-md-6 pr-2">
                <input type="text" name="spec_values[]" class="form-control" placeholder="Giá trị">
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-danger remove-spec-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        specificationsContainer.appendChild(specRow);
        
        // Xử lý xóa thông số
        const removeBtn = specRow.querySelector('.remove-spec-btn');
        removeBtn.addEventListener('click', function() {
            specificationsContainer.removeChild(specRow);
        });
    });
    
    // Xử lý xóa thông số cho các hàng có sẵn
    document.querySelectorAll('.remove-spec-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('.specification-row');
            specificationsContainer.removeChild(row);
        });
    });
}

/**
 * Khởi tạo các bước hướng dẫn sử dụng
 */
function initUsageGuideSteps() {
    // Xử lý các bước sử dụng
    const stepsContainer = document.getElementById('usage-steps-container');
    const addStepBtn = document.getElementById('add-step-btn');
    
    if (stepsContainer && addStepBtn) {
        // Xử lý thêm bước mới
        addStepBtn.addEventListener('click', function() {
            const stepRow = document.createElement('div');
            stepRow.className = 'step-row mb-2 flex items-center';
            stepRow.innerHTML = `
                <input type="text" name="usage_steps[]" class="form-control flex-grow mr-2" placeholder="Nhập bước hướng dẫn sử dụng">
                <button type="button" class="btn btn-danger remove-step-btn">
                    <i class="fas fa-times"></i>
                </button>
            `;
            stepsContainer.appendChild(stepRow);
            
            // Xử lý xóa bước
            const removeBtn = stepRow.querySelector('.remove-step-btn');
            removeBtn.addEventListener('click', function() {
                stepsContainer.removeChild(stepRow);
            });
        });
        
        // Xử lý xóa bước cho các hàng có sẵn
        document.querySelectorAll('.remove-step-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('.step-row');
                stepsContainer.removeChild(row);
            });
        });
    }
    
    // Xử lý các lưu ý sử dụng
    const notesContainer = document.getElementById('usage-notes-container');
    const addNoteBtn = document.getElementById('add-note-btn');
    
    if (notesContainer && addNoteBtn) {
        // Xử lý thêm lưu ý mới
        addNoteBtn.addEventListener('click', function() {
            const noteRow = document.createElement('div');
            noteRow.className = 'note-row mb-2 flex items-center';
            noteRow.innerHTML = `
                <input type="text" name="usage_notes[]" class="form-control flex-grow mr-2" placeholder="Nhập lưu ý khi sử dụng">
                <button type="button" class="btn btn-danger remove-note-btn">
                    <i class="fas fa-times"></i>
                </button>
            `;
            notesContainer.appendChild(noteRow);
            
            // Xử lý xóa lưu ý
            const removeBtn = noteRow.querySelector('.remove-note-btn');
            removeBtn.addEventListener('click', function() {
                notesContainer.removeChild(noteRow);
            });
        });
        
        // Xử lý xóa lưu ý cho các hàng có sẵn
        document.querySelectorAll('.remove-note-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('.note-row');
                notesContainer.removeChild(row);
            });
        });
    }
}

/**
 * Khởi tạo thông tin bảo hành
 */
function initWarrantyInfo() {
    // Xử lý các điều kiện bảo hành
    const conditionsContainer = document.getElementById('warranty-conditions-container');
    const addConditionBtn = document.getElementById('add-condition-btn');
    
    if (conditionsContainer && addConditionBtn) {
        // Xử lý thêm điều kiện mới
        addConditionBtn.addEventListener('click', function() {
            const conditionRow = document.createElement('div');
            conditionRow.className = 'condition-row mb-2 flex items-center';
            conditionRow.innerHTML = `
                <input type="text" name="warranty_conditions[]" class="form-control flex-grow mr-2" placeholder="Nhập điều kiện bảo hành">
                <button type="button" class="btn btn-danger remove-condition-btn">
                    <i class="fas fa-times"></i>
                </button>
            `;
            conditionsContainer.appendChild(conditionRow);
            
            // Xử lý xóa điều kiện
            const removeBtn = conditionRow.querySelector('.remove-condition-btn');
            removeBtn.addEventListener('click', function() {
                conditionsContainer.removeChild(conditionRow);
            });
        });
        
        // Xử lý xóa điều kiện cho các hàng có sẵn
        document.querySelectorAll('.remove-condition-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('.condition-row');
                conditionsContainer.removeChild(row);
            });
        });
    }
}

/**
 * Xử lý form submit
 */
function handleFormSubmit() {
    const productForm = document.querySelector('form[action*="product-add.php"], form[action*="product-edit.php"]');
    
    if (!productForm) return;
    
    productForm.addEventListener('submit', function(e) {
        // Không cần ngăn chặn submit mặc định, chỉ cần đảm bảo dữ liệu được gửi đúng
        console.log('Form submitted, product details data prepared');
    });
}
