/**
 * Unified Notifications System - Nội Thất Bàng Vũ Admin
 * <PERSON><PERSON> thống thông báo thống nhất cho trang admin
 * Đ<PERSON><PERSON> bảo tất cả thông báo đều tự động ẩn sau 2 giây
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Unified Notifications System initialized');

    // Debug: <PERSON><PERSON> tra các thông báo hiện có
    const existingNotifications = document.querySelectorAll('.alert, .custom-alert, .notification, .flash-message, [class*="alert-"], [role="alert"]');
    console.log('Found existing notifications:', existingNotifications.length, existingNotifications);

    // Hàm xử lý tự động ẩn thông báo
    function autoHideNotification(element, delay = 5000) {
        if (!element || !element.parentNode) return;

        setTimeout(function() {
            if (element.parentNode) {
                // Thêm class fade-out nếu có
                if (element.classList.contains('alert') || element.classList.contains('custom-alert')) {
                    element.classList.add('fade-out');
                    setTimeout(function() {
                        if (element.parentNode) {
                            element.remove();
                        }
                    }, 300);
                } else {
                    // Nếu không có class fade-out, ẩn trực tiếp
                    element.style.transition = 'opacity 0.3s ease-out';
                    element.style.opacity = '0';
                    setTimeout(function() {
                        if (element.parentNode) {
                            element.remove();
                        }
                    }, 300);
                }
            }
        }, delay);
    }

    // Xử lý tất cả thông báo hiện có trên trang
    function processExistingNotifications() {
        // Tìm tất cả các loại thông báo
        const selectors = [
            '.alert',
            '.custom-alert',
            '.notification',
            '.flash-message',
            '[class*="alert-"]',
            '[role="alert"]'
        ];

        selectors.forEach(selector => {
            const notifications = document.querySelectorAll(selector);
            notifications.forEach(notification => {
                // Bỏ qua nếu đã được xử lý
                if (notification.hasAttribute('data-auto-hide-processed')) {
                    return;
                }

                // Đánh dấu đã xử lý
                notification.setAttribute('data-auto-hide-processed', 'true');

                // Áp dụng auto-hide
                autoHideNotification(notification);

                console.log('Auto-hide applied to notification:', notification);
            });
        });
    }

    // Xử lý thông báo hiện có
    processExistingNotifications();

    // Xử lý lại sau một khoảng thời gian ngắn để bắt các thông báo được tạo muộn
    setTimeout(processExistingNotifications, 500);

    // Observer để theo dõi thông báo mới được thêm vào DOM
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Kiểm tra nếu node chính là thông báo
                    const selectors = [
                        '.alert',
                        '.custom-alert',
                        '.notification',
                        '.flash-message',
                        '[class*="alert-"]',
                        '[role="alert"]'
                    ];

                    let isNotification = false;
                    selectors.forEach(selector => {
                        if (node.matches && node.matches(selector)) {
                            isNotification = true;
                        }
                    });

                    if (isNotification && !node.hasAttribute('data-auto-hide-processed')) {
                        node.setAttribute('data-auto-hide-processed', 'true');
                        autoHideNotification(node);
                        console.log('Auto-hide applied to new notification:', node);
                    }

                    // Kiểm tra các thông báo con
                    selectors.forEach(selector => {
                        const childNotifications = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                        childNotifications.forEach(childNotification => {
                            if (!childNotification.hasAttribute('data-auto-hide-processed')) {
                                childNotification.setAttribute('data-auto-hide-processed', 'true');
                                autoHideNotification(childNotification);
                                console.log('Auto-hide applied to child notification:', childNotification);
                            }
                        });
                    });
                }
            });
        });
    });

    // Bắt đầu theo dõi
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Hàm global để tạo thông báo mới (có thể được gọi từ các file khác)
    window.showUnifiedNotification = function(message, type = 'info', duration = 5000) {
        // Xóa thông báo cũ nếu có
        const existingNotifications = document.querySelectorAll('.unified-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Tạo thông báo mới
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} unified-notification fade-in`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('data-auto-hide-processed', 'true');

        // Icon theo loại thông báo
        let icon = 'fa-info-circle';
        if (type === 'success') icon = 'fa-check-circle';
        else if (type === 'danger' || type === 'error') icon = 'fa-exclamation-circle';
        else if (type === 'warning') icon = 'fa-exclamation-triangle';

        notification.innerHTML = `
            <i class="fas ${icon} mr-2"></i>
            ${message}
            <button type="button" class="close ml-auto" onclick="this.parentElement.remove();">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        // Thêm vào container thông báo hoặc đầu trang
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.querySelector('.container-fluid');
        }
        if (container) {
            container.insertBefore(notification, container.firstChild);
        } else {
            document.body.insertBefore(notification, document.body.firstChild);
        }

        // Áp dụng auto-hide
        autoHideNotification(notification, duration);

        return notification;
    };

    console.log('Unified Notifications System ready');
});
