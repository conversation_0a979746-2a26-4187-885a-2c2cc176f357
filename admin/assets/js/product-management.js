/**
 * Product Management JavaScript
 * Xử lý các tương tác với sản phẩm không cần tải lại trang
 */

$(document).ready(function() {
    /**
     * Thêm sản phẩm mới
     */
    $('#add-product-form').on('submit', function(e) {
        e.preventDefault();

        // Hiển thị loading
        showLoading();

        // Sử dụng FormData để xử lý cả dữ liệu và file
        const formData = new FormData(this);

        $.ajax({
            url: '../ajax/add_product.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                // Ẩn loading
                hideLoading();

                if (response.success) {
                    // Hiển thị thông báo thành công
                    showNotification(response.message, 'success');

                    // Thêm sản phẩm mới vào bảng sản phẩm (nếu đang ở trang danh sách)
                    if ($('.product-table').length) {
                        addProductToTable(response.product);
                    } else {
                        // Nếu không ở trang danh sách, hiển thị nút quay lại danh sách
                        showBackToListButton();
                    }

                    // Reset form
                    $('#add-product-form')[0].reset();

                    // Reset các trường file
                    $('.custom-file-label').text('Chọn file');

                    // Reset trình soạn thảo nếu có
                    if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.content) {
                        CKEDITOR.instances.content.setData('');
                    }

                    // Reset SimpleEditor nếu có
                    if ($('.simple-editor').length) {
                        $('.simple-editor').html('');
                    }
                } else {
                    // Hiển thị thông báo lỗi
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                // Ẩn loading
                hideLoading();

                // Hiển thị thông báo lỗi
                showNotification('Có lỗi xảy ra. Vui lòng thử lại sau.', 'error');
            }
        });
    });

    /**
     * Thêm sản phẩm mới vào bảng
     */
    function addProductToTable(product) {
        // Kiểm tra xem bảng sản phẩm có tồn tại không
        const productTable = $('.product-table tbody');
        if (!productTable.length) return;

        // Xóa thông báo "Không có sản phẩm nào" nếu có
        const emptyRow = productTable.find('tr td[colspan]');
        if (emptyRow.length) {
            emptyRow.closest('tr').remove();
        }

        // Tạo HTML cho hàng mới
        const newRow = `
            <tr>
                <td>${product.id}</td>
                <td>
                    ${product.image ? `<img src="${product.image_url}" alt="${product.name}" width="50">` : '<div class="bg-gray-300 text-center p-2"><i class="fas fa-image text-gray-500"></i></div>'}
                </td>
                <td>${product.name}</td>
                <td>${product.category_name}</td>
                <td>${product.price}</td>
                <td>${product.sale_price}</td>
                <td>${product.quantity}</td>
                <td>
                    ${product.status == 1 ? '<span class="badge badge-success">Hiển thị</span>' : '<span class="badge badge-danger">Ẩn</span>'}
                </td>
                <td>
                    <a href="product-edit.php?id=${product.id}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="product-delete.php?id=${product.id}" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?');">
                        <i class="fas fa-trash"></i>
                    </a>
                </td>
            </tr>
        `;

        // Thêm hàng mới vào đầu bảng
        productTable.prepend(newRow);

        // Highlight hàng mới thêm
        productTable.find('tr:first').addClass('highlight-row');

        // Xóa highlight sau 3 giây
        setTimeout(function() {
            productTable.find('tr:first').removeClass('highlight-row');
        }, 3000);
    }

    /**
     * Hiển thị nút quay lại danh sách
     */
    function showBackToListButton() {
        // Kiểm tra xem đã có nút quay lại chưa
        if ($('.back-to-list-container').length === 0) {
            // Tạo container và nút
            const buttonHtml = `
                <div class="back-to-list-container mt-4 text-center">
                    <p class="text-success mb-2">Sản phẩm đã được thêm thành công!</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-list mr-1"></i> Quay lại danh sách sản phẩm
                    </a>
                </div>
            `;

            // Thêm vào sau form
            $('#add-product-form').after(buttonHtml);
        }
    }

    /**
     * Hiển thị loading
     */
    function showLoading() {
        // Kiểm tra xem đã có overlay loading chưa
        if ($('#loading-overlay').length === 0) {
            // Tạo overlay loading
            const loadingHtml = `
                <div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">
                    <div class="spinner-border text-light" role="status">
                        <span class="sr-only">Đang xử lý...</span>
                    </div>
                </div>
            `;

            // Thêm vào body
            $('body').append(loadingHtml);
        } else {
            // Hiển thị overlay loading
            $('#loading-overlay').show();
        }
    }

    /**
     * Ẩn loading
     */
    function hideLoading() {
        $('#loading-overlay').hide();
    }

    /**
     * Hiển thị thông báo - Sử dụng hệ thống thống nhất
     */
    function showNotification(message, type) {
        // Sử dụng hệ thống thông báo thống nhất nếu có
        if (typeof window.showUnifiedNotification === 'function') {
            // Chuyển đổi type nếu cần
            let unifiedType = type;
            if (type === 'error') unifiedType = 'danger';

            return window.showUnifiedNotification(message, unifiedType, 5000);
        }

        // Fallback về phương thức cũ nếu hệ thống thống nhất chưa sẵn sàng
        let alertClass = 'alert-info';
        if (type === 'success') alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-danger';
        if (type === 'warning') alertClass = 'alert-warning';

        // Tạo HTML thông báo
        const notificationHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert" data-auto-close="true">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // Thêm thông báo vào container
        const container = $('.notification-container');
        if (container.length) {
            container.append(notificationHtml);
        } else {
            // Nếu không có container, tạo mới và thêm vào đầu content
            $('.container-fluid').prepend(`<div class="notification-container">${notificationHtml}</div>`);
        }

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    /**
     * Thêm CSS cho highlight row
     */
    const highlightStyle = `
        <style>
            .highlight-row {
                animation: highlightFade 3s;
            }
            @keyframes highlightFade {
                0% { background-color: rgba(40, 167, 69, 0.2); }
                100% { background-color: transparent; }
            }
        </style>
    `;
    $('head').append(highlightStyle);
});
