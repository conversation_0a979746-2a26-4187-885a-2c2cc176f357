<?php
// Include init
require_once '../includes/init.php';

// <PERSON><PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Thêm cảm nhận khách hàng mới';

// Include file header
include_once 'partials/header.php';

// Include file helper
include_once '../includes/testimonials-helper.php';

// Quyền admin đã được kiểm tra trong file check_admin.php

// Khởi tạo biến
$errors = [];
$success = false;

// Xử lý form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $customer_name = isset($_POST['customer_name']) ? trim($_POST['customer_name']) : '';
    $customer_age = isset($_POST['customer_age']) ? trim($_POST['customer_age']) : '';
    $customer_address = isset($_POST['customer_address']) ? trim($_POST['customer_address']) : '';
    $customer_video = isset($_POST['customer_video']) ? trim($_POST['customer_video']) : '';
    $review_videos = isset($_POST['review_videos']) ? trim($_POST['review_videos']) : '';
    $rating = isset($_POST['rating']) ? intval($_POST['rating']) : 5;
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $product_tags = isset($_POST['product_tags']) ? trim($_POST['product_tags']) : '';
    $status = isset($_POST['status']) ? 1 : 0;

    // Validate dữ liệu
    if (empty($customer_name)) {
        $errors[] = 'Vui lòng nhập tên khách hàng.';
    }

    if (empty($content)) {
        $errors[] = 'Vui lòng nhập nội dung cảm nhận.';
    }

    // Nếu không có lỗi, tiến hành thêm mới
    if (empty($errors)) {
        // Chuẩn bị dữ liệu thêm mới
        $testimonial_data = [
            'customer_name' => $customer_name,
            'customer_age' => $customer_age,
            'customer_address' => $customer_address,
            'customer_video' => $customer_video,
            'rating' => $rating,
            'content' => $content,
            'product_tags' => $product_tags,
            'status' => $status
        ];

        // Xử lý review videos (đã được lưu dưới dạng chuỗi phân cách bằng dấu phẩy)
        if (!empty($review_videos)) {
            $testimonial_data['review_videos'] = $review_videos;
        } else {
            $testimonial_data['review_videos'] = null;
        }

        // Xử lý dữ liệu thumbnails cho video bổ sung
        $review_videos_thumbnails_data = isset($_POST['review_videos_thumbnails_data']) ? $_POST['review_videos_thumbnails_data'] : '';
        $review_videos_thumbnails = [];

        if (!empty($review_videos_thumbnails_data)) {
            $thumbnails_data = json_decode($review_videos_thumbnails_data, true);

            if (is_array($thumbnails_data)) {
                $upload_dir = '../uploads/testimonials/';

                // Tạo thư mục nếu chưa tồn tại
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                foreach ($thumbnails_data as $index => $thumbnail) {
                    if (!empty($thumbnail['thumbnail']) && !empty($thumbnail['fileName'])) {
                        // Lấy dữ liệu base64 từ chuỗi data URL
                        $base64_string = preg_replace('#^data:image/\w+;base64,#i', '', $thumbnail['thumbnail']);
                        $decoded_data = base64_decode($base64_string);

                        // Tạo tên file
                        $file_name = time() . '_video_thumb_' . $index . '_' . basename($thumbnail['fileName']);
                        $target_file = $upload_dir . $file_name;

                        // Lưu file
                        if (file_put_contents($target_file, $decoded_data)) {
                            $review_videos_thumbnails[] = $file_name;
                        }
                    }
                }

                if (!empty($review_videos_thumbnails)) {
                    $testimonial_data['review_videos_thumbnails'] = implode(',', $review_videos_thumbnails);
                }
            }
        }

        // Xử lý upload ảnh avatar
        if (isset($_FILES['customer_photo']) && $_FILES['customer_photo']['error'] === UPLOAD_ERR_OK && !empty($_FILES['customer_photo']['name'])) {
            $upload_dir = '../uploads/testimonials/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = time() . '_' . basename($_FILES['customer_photo']['name']);
            $target_file = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['customer_photo']['tmp_name'], $target_file)) {
                $testimonial_data['customer_photo'] = $file_name;
            }
        }

        // Xử lý upload nhiều ảnh đánh giá
        $review_photos = [];

        // Kiểm tra xem có ảnh mới được tải lên không
        $has_new_photos = isset($_FILES['review_photos']) &&
                         isset($_FILES['review_photos']['name']) &&
                         is_array($_FILES['review_photos']['name']) &&
                         count(array_filter($_FILES['review_photos']['name'])) > 0;

        if ($has_new_photos) {
            $upload_dir = '../uploads/testimonials/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_count = count($_FILES['review_photos']['name']);

            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['review_photos']['error'][$i] === UPLOAD_ERR_OK && !empty($_FILES['review_photos']['name'][$i])) {
                    $file_name = time() . '_review_' . $i . '_' . basename($_FILES['review_photos']['name'][$i]);
                    $target_file = $upload_dir . $file_name;

                    if (move_uploaded_file($_FILES['review_photos']['tmp_name'][$i], $target_file)) {
                        $review_photos[] = $file_name;
                    }
                }
            }

            if (!empty($review_photos)) {
                $testimonial_data['review_photos'] = implode(',', $review_photos);
            }
        }

        // Xử lý upload ảnh đại diện cho video chính
        if (isset($_FILES['customer_video_thumbnail']) && $_FILES['customer_video_thumbnail']['error'] === UPLOAD_ERR_OK && !empty($_FILES['customer_video_thumbnail']['name'])) {
            $upload_dir = '../uploads/testimonials/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = time() . '_video_thumb_' . basename($_FILES['customer_video_thumbnail']['name']);
            $target_file = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['customer_video_thumbnail']['tmp_name'], $target_file)) {
                $testimonial_data['customer_video_thumbnail'] = $file_name;
            }
        }

        // Xử lý upload nhiều ảnh đại diện cho video bổ sung
        $review_videos_thumbnails = [];

        // Kiểm tra xem có ảnh đại diện mới được tải lên không
        $has_new_video_thumbnails = isset($_FILES['review_videos_thumbnails']) &&
                         isset($_FILES['review_videos_thumbnails']['name']) &&
                         is_array($_FILES['review_videos_thumbnails']['name']) &&
                         count(array_filter($_FILES['review_videos_thumbnails']['name'])) > 0;

        if ($has_new_video_thumbnails) {
            $upload_dir = '../uploads/testimonials/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_count = count($_FILES['review_videos_thumbnails']['name']);

            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['review_videos_thumbnails']['error'][$i] === UPLOAD_ERR_OK && !empty($_FILES['review_videos_thumbnails']['name'][$i])) {
                    $file_name = time() . '_video_thumb_' . $i . '_' . basename($_FILES['review_videos_thumbnails']['name'][$i]);
                    $target_file = $upload_dir . $file_name;

                    if (move_uploaded_file($_FILES['review_videos_thumbnails']['tmp_name'][$i], $target_file)) {
                        $review_videos_thumbnails[] = $file_name;
                    }
                }
            }

            if (!empty($review_videos_thumbnails)) {
                $testimonial_data['review_videos_thumbnails'] = implode(',', $review_videos_thumbnails);
            }
        }

        // Thêm mới cảm nhận khách hàng
        if (add_testimonial($testimonial_data)) {
            $success = true;
            set_flash_message('success', 'Thêm cảm nhận khách hàng thành công.');
            redirect('testimonials.php');
        } else {
            $errors[] = 'Có lỗi xảy ra khi thêm cảm nhận khách hàng.';
        }
    }
}
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Thêm cảm nhận khách hàng mới</h1>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success">
            Thêm cảm nhận khách hàng thành công.
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Thông tin cảm nhận khách hàng</h6>
        </div>
        <div class="card-body">
            <form action="add-testimonial.php" method="post" enctype="multipart/form-data" id="testimonialForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_name">Tên khách hàng <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" value="<?php echo isset($_POST['customer_name']) ? htmlspecialchars($_POST['customer_name']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="customer_age">Tuổi khách hàng</label>
                            <input type="number" class="form-control" id="customer_age" name="customer_age" value="<?php echo isset($_POST['customer_age']) ? htmlspecialchars($_POST['customer_age']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="customer_address">Địa chỉ khách hàng</label>
                            <input type="text" class="form-control" id="customer_address" name="customer_address" value="<?php echo isset($_POST['customer_address']) ? htmlspecialchars($_POST['customer_address']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="rating">Đánh giá</label>
                            <select class="form-control" id="rating" name="rating">
                                <option value="5" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 5) ? 'selected' : ''; ?>>5 sao</option>
                                <option value="4" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 4) ? 'selected' : ''; ?>>4 sao</option>
                                <option value="3" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 3) ? 'selected' : ''; ?>>3 sao</option>
                                <option value="2" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 2) ? 'selected' : ''; ?>>2 sao</option>
                                <option value="1" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 1) ? 'selected' : ''; ?>>1 sao</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="customer_photo">Ảnh avatar khách hàng</label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="customer_photo" name="customer_photo" accept="image/*">
                                    <label class="custom-file-label" for="customer_photo">Chọn ảnh avatar</label>
                                </div>
                            </div>
                            <div id="photo_preview" class="mt-2" style="display: none;">
                                <img src="" alt="Preview" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                            <small class="form-text text-muted">Ảnh đại diện của khách hàng</small>
                        </div>

                        <div class="form-group">
                            <label for="review_photos">Ảnh đánh giá của khách hàng</label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="review_photos" name="review_photos[]" accept="image/*" multiple>
                                    <label class="custom-file-label" for="review_photos">Chọn nhiều ảnh</label>
                                </div>
                            </div>
                            <div id="review_photos_preview" class="mt-2 d-flex flex-wrap">
                                <!-- Ảnh preview sẽ hiển thị ở đây -->
                            </div>
                            <small class="form-text text-muted">Có thể chọn nhiều ảnh đánh giá của khách hàng</small>
                        </div>

                        <div class="form-group">
                            <label for="customer_video">URL Video chính</label>
                            <input type="text" class="form-control" id="customer_video" name="customer_video" placeholder="Ví dụ: https://res.cloudinary.com/demo/video/upload/v1/example.mp4" value="<?php echo isset($_POST['customer_video']) ? htmlspecialchars($_POST['customer_video']) : ''; ?>">
                            <small class="form-text text-muted">Nhập URL video từ Cloudinary hoặc dịch vụ lưu trữ khác</small>
                        </div>

                        <div class="form-group">
                            <label for="customer_video_thumbnail">Ảnh đại diện cho video chính</label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="customer_video_thumbnail" name="customer_video_thumbnail" accept="image/*">
                                    <label class="custom-file-label" for="customer_video_thumbnail">Chọn ảnh đại diện</label>
                                </div>
                            </div>
                            <div id="video_thumbnail_preview" class="mt-2" style="display: none;">
                                <img src="" alt="Preview" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                            <small class="form-text text-muted">Ảnh đại diện sẽ hiển thị thay cho video trước khi phát</small>
                        </div>

                        <div class="form-group">
                            <label>Video đánh giá bổ sung</label>
                            <div id="additional-videos-container">
                                <!-- Các video bổ sung sẽ được thêm vào đây -->
                            </div>
                            <button type="button" id="add-video-btn" class="btn btn-outline-primary btn-sm mt-2" onclick="addVideoItem()">
                                <span>+</span> Thêm video bổ sung
                            </button>
                            <small class="form-text text-muted">Nhấn nút + để thêm video bổ sung</small>

                            <!-- Input ẩn để lưu trữ dữ liệu video bổ sung -->
                            <input type="hidden" id="review_videos" name="review_videos" value="<?php echo isset($_POST['review_videos']) ? htmlspecialchars($_POST['review_videos']) : ''; ?>">
                            <input type="hidden" id="review_videos_thumbnails_data" name="review_videos_thumbnails_data" value="">
                        </div>

                        <div class="form-group">
                            <label for="product_tags">Tag sản phẩm đã mua</label>
                            <input type="text" class="form-control" id="product_tags" name="product_tags" placeholder="Ví dụ: Tủ bếp, Bàn ăn" value="<?php echo isset($_POST['product_tags']) ? htmlspecialchars($_POST['product_tags']) : ''; ?>">
                            <small class="form-text text-muted">Nhập các tag cách nhau bằng dấu phẩy</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="status" name="status" <?php echo (!isset($_POST['status']) || $_POST['status'] == 1) ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="status">Hiển thị</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="content">Nội dung cảm nhận <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="content" name="content" rows="5" required><?php echo isset($_POST['content']) ? htmlspecialchars($_POST['content']) : ''; ?></textarea>
                </div>

                <div class="form-group">
                    <a href="testimonials.php" class="btn btn-secondary">Quay lại</a>
                    <button type="submit" class="btn btn-primary" id="saveTestimonial">Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- CSS cho preview ảnh -->
<style>
.preview-item {
    position: relative;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
}

.preview-item img {
    height: 80px;
    width: auto;
    border-radius: 4px;
}

.additional-video-item {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f8fafc;
    position: relative;
}

.additional-video-item .remove-video-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #e53e3e;
    cursor: pointer;
    font-size: 18px;
}

.additional-video-item .video-number {
    position: absolute;
    top: 10px;
    left: 15px;
    background-color: #3B82F6;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.additional-video-item .form-group {
    margin-top: 25px;
}
</style>

<!-- Script xử lý -->
<script>
// Biến lưu trữ dữ liệu video bổ sung
var additionalVideos = [];
var videoCounter = 0;

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM đã tải xong');

    // Khởi tạo từ dữ liệu đã có (nếu có)
    var existingVideos = document.getElementById('review_videos').value;
    if (existingVideos) {
        var videoUrls = existingVideos.split(',');
        for (var i = 0; i < videoUrls.length; i++) {
            if (videoUrls[i].trim()) {
                addVideoItem(videoUrls[i].trim());
            }
        }
    }
});

// Hàm thêm một video bổ sung mới
function addVideoItem(videoUrl = '') {
        console.log('Thêm video mới: ' + (videoUrl || 'trống'));
        videoCounter++;
        var videoId = 'video_' + Date.now() + '_' + videoCounter;

        // Tạo container cho video item
        var videoItem = document.createElement('div');
        videoItem.className = 'additional-video-item';
        videoItem.setAttribute('data-video-id', videoId);

        // Tạo số thứ tự
        var videoNumber = document.createElement('div');
        videoNumber.className = 'video-number';
        videoNumber.textContent = videoCounter;

        // Tạo nút xóa
        var removeBtn = document.createElement('div');
        removeBtn.className = 'remove-video-btn';
        removeBtn.innerHTML = '<span>&times;</span>';

        // URL Video
        var urlGroup = document.createElement('div');
        urlGroup.className = 'form-group';

        var urlLabel = document.createElement('label');
        urlLabel.setAttribute('for', videoId + '_url');
        urlLabel.textContent = 'URL Video';

        var urlInput = document.createElement('input');
        urlInput.type = 'text';
        urlInput.className = 'form-control video-url';
        urlInput.id = videoId + '_url';
        urlInput.placeholder = 'Ví dụ: https://res.cloudinary.com/demo/video/upload/v1/example.mp4';
        urlInput.value = videoUrl;

        var urlHelp = document.createElement('small');
        urlHelp.className = 'form-text text-muted';
        urlHelp.textContent = 'Nhập URL video từ Cloudinary hoặc dịch vụ lưu trữ khác';

        urlGroup.appendChild(urlLabel);
        urlGroup.appendChild(urlInput);
        urlGroup.appendChild(urlHelp);

        // Ảnh đại diện
        var thumbnailGroup = document.createElement('div');
        thumbnailGroup.className = 'form-group';

        var thumbnailLabel = document.createElement('label');
        thumbnailLabel.setAttribute('for', videoId + '_thumbnail');
        thumbnailLabel.textContent = 'Ảnh đại diện';

        var thumbnailInputGroup = document.createElement('div');
        thumbnailInputGroup.className = 'input-group';

        var thumbnailCustomFile = document.createElement('div');
        thumbnailCustomFile.className = 'custom-file';

        var thumbnailInput = document.createElement('input');
        thumbnailInput.type = 'file';
        thumbnailInput.className = 'custom-file-input video-thumbnail';
        thumbnailInput.id = videoId + '_thumbnail';
        thumbnailInput.accept = 'image/*';

        var thumbnailFileLabel = document.createElement('label');
        thumbnailFileLabel.className = 'custom-file-label';
        thumbnailFileLabel.setAttribute('for', videoId + '_thumbnail');
        thumbnailFileLabel.textContent = 'Chọn ảnh đại diện';

        thumbnailCustomFile.appendChild(thumbnailInput);
        thumbnailCustomFile.appendChild(thumbnailFileLabel);
        thumbnailInputGroup.appendChild(thumbnailCustomFile);

        var thumbnailPreview = document.createElement('div');
        thumbnailPreview.id = videoId + '_preview';
        thumbnailPreview.className = 'mt-2';
        thumbnailPreview.style.display = 'none';

        var thumbnailImg = document.createElement('img');
        thumbnailImg.src = '';
        thumbnailImg.alt = 'Preview';
        thumbnailImg.className = 'img-thumbnail';
        thumbnailImg.style.maxHeight = '100px';

        thumbnailPreview.appendChild(thumbnailImg);

        var thumbnailHelp = document.createElement('small');
        thumbnailHelp.className = 'form-text text-muted';
        thumbnailHelp.textContent = 'Ảnh đại diện sẽ hiển thị thay cho video trước khi phát';

        thumbnailGroup.appendChild(thumbnailLabel);
        thumbnailGroup.appendChild(thumbnailInputGroup);
        thumbnailGroup.appendChild(thumbnailPreview);
        thumbnailGroup.appendChild(thumbnailHelp);

        // Thêm vào container
        videoItem.appendChild(videoNumber);
        videoItem.appendChild(removeBtn);
        videoItem.appendChild(urlGroup);
        videoItem.appendChild(thumbnailGroup);

        document.getElementById('additional-videos-container').appendChild(videoItem);
        console.log('Video đã được thêm vào container');

        // Xử lý sự kiện xóa video
        removeBtn.addEventListener('click', function() {
            videoItem.remove();
            updateVideoNumbers();
            updateHiddenInputs();
        });

        // Xử lý sự kiện thay đổi URL
        urlInput.addEventListener('input', function() {
            updateHiddenInputs();
        });

        // Xử lý sự kiện chọn file ảnh đại diện
        thumbnailInput.addEventListener('change', function() {
            var file = this.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    thumbnailPreview.style.display = 'block';
                    thumbnailImg.src = e.target.result;

                    // Lưu dữ liệu ảnh vào đối tượng video
                    var videoData = {
                        id: videoId,
                        url: urlInput.value,
                        thumbnail: e.target.result,
                        fileName: file.name
                    };

                    // Cập nhật hoặc thêm mới vào mảng
                    var found = false;
                    for (var i = 0; i < additionalVideos.length; i++) {
                        if (additionalVideos[i].id === videoId) {
                            additionalVideos[i] = videoData;
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        additionalVideos.push(videoData);
                    }

                    updateHiddenInputs();
                };
                reader.readAsDataURL(file);

                // Hiển thị tên file
                thumbnailFileLabel.textContent = file.name;
            }
        });

        updateVideoNumbers();
    }

// Hàm cập nhật số thứ tự video
function updateVideoNumbers() {
    var items = document.querySelectorAll('.additional-video-item');
    items.forEach(function(item, index) {
        item.querySelector('.video-number').textContent = index + 1;
    });

    videoCounter = items.length;
    console.log('Cập nhật số thứ tự video: ' + videoCounter + ' video');
}

// Hàm cập nhật input ẩn
function updateHiddenInputs() {
    // Cập nhật URLs
    var urls = [];
    document.querySelectorAll('.video-url').forEach(function(input) {
        urls.push(input.value);
    });
    document.getElementById('review_videos').value = urls.join(',');

    // Cập nhật dữ liệu thumbnails
    document.getElementById('review_videos_thumbnails_data').value = JSON.stringify(additionalVideos);
}

document.addEventListener('DOMContentLoaded', function() {
    // Xử lý preview ảnh avatar khi chọn file
    document.getElementById('customer_photo').addEventListener('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('photo_preview').style.display = 'block';
                document.getElementById('photo_preview').querySelector('img').src = e.target.result;
            };
            reader.readAsDataURL(file);

            // Hiển thị tên file
            this.nextElementSibling.textContent = file.name;
        }
    });

    // Xử lý preview ảnh đại diện video chính
    document.getElementById('customer_video_thumbnail').addEventListener('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('video_thumbnail_preview').style.display = 'block';
                document.getElementById('video_thumbnail_preview').querySelector('img').src = e.target.result;
            };
            reader.readAsDataURL(file);

            // Hiển thị tên file
            this.nextElementSibling.textContent = file.name;
        }
    });

    // Xử lý preview nhiều ảnh đánh giá khi chọn file
    document.getElementById('review_photos').addEventListener('change', function() {
        var files = this.files;
        var fileCount = files.length;

        // Xóa preview cũ
        document.getElementById('review_photos_preview').innerHTML = '';

        if (fileCount > 0) {
            // Hiển thị số lượng file đã chọn
            this.nextElementSibling.textContent = fileCount + ' ảnh đã chọn';

            // Tạo preview cho mỗi ảnh
            for (var i = 0; i < fileCount; i++) {
                (function(file, index) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var previewItem = document.createElement('div');
                        previewItem.className = 'preview-item mr-2 mb-2';

                        var img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'img-thumbnail';
                        img.style.height = '80px';
                        img.style.width = 'auto';

                        previewItem.appendChild(img);
                        document.getElementById('review_photos_preview').appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                })(files[i], i);
            }
        } else {
            this.nextElementSibling.textContent = 'Chọn nhiều ảnh';
        }
    });

    // Xử lý submit form
    document.getElementById('testimonialForm').addEventListener('submit', function() {
        // Đảm bảo dữ liệu được cập nhật trước khi submit
        updateHiddenInputs();
        return true;
    });
});
</script>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
