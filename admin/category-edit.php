<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Thiết lập tiêu đề trang
$page_title = 'Chỉnh sửa danh mục';

// Kiểm tra ID danh mục
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('error', 'ID danh mục không hợp lệ.');
    redirect('categories.php');
}

$category_id = intval($_GET['id']);
$category = get_category_by_id($category_id);

if (!$category) {
    set_flash_message('error', '<PERSON><PERSON> mục không tồn tại.');
    redirect('categories.php');
}

// Lấy danh sách danh mục theo cấu trúc cây
$category_tree = get_category_tree();

// Lọc bỏ danh mục hiện tại và các danh mục con của nó khỏi danh sách
function filter_categories(&$categories, $exclude_id) {
    foreach ($categories as $key => &$category) {
        if ($category['id'] == $exclude_id) {
            unset($categories[$key]);
            continue;
        }

        if (!empty($category['children'])) {
            filter_categories($category['children'], $exclude_id);
        }
    }

    // Chuyển lại thành mảng tuần tự
    $categories = array_values($categories);
}

// Tạo bản sao của cây danh mục để không ảnh hưởng đến biến gốc
$filtered_category_tree = $category_tree;

// Lọc bỏ danh mục hiện tại và các danh mục con
filter_categories($filtered_category_tree, $category_id);

// Xử lý form chỉnh sửa danh mục
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $status = isset($_POST['status']) ? 1 : 0;

    // Validate dữ liệu
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Tên danh mục không được để trống';
    }

    // Kiểm tra danh mục cha không phải là danh mục con của danh mục hiện tại
    if (!empty($parent_id)) {
        $current_parent = $parent_id;
        while ($current_parent) {
            if ($current_parent == $category_id) {
                $errors[] = 'Không thể chọn danh mục con làm danh mục cha';
                break;
            }
            $parent = get_category_by_id($current_parent);
            $current_parent = $parent ? $parent['parent_id'] : null;
        }
    }

    // Xử lý upload hình ảnh
    $image = $category['image']; // Giữ nguyên hình ảnh cũ nếu không upload mới

    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/categories/';

        // Tạo thư mục uploads/categories nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_name = $_FILES['image']['name'];
        $file_tmp = $_FILES['image']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Kiểm tra định dạng file
        $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($file_ext, $allowed_exts)) {
            $errors[] = 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
        } else {
            // Tạo tên file mới để tránh trùng lặp
            $new_file_name = 'category_' . time() . '_' . uniqid() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_file_name;

            if (move_uploaded_file($file_tmp, $upload_path)) {
                // Xóa file cũ nếu có
                if (!empty($category['image']) && file_exists($upload_dir . $category['image'])) {
                    unlink($upload_dir . $category['image']);
                }

                $image = $new_file_name;
            } else {
                $errors[] = 'Có lỗi xảy ra khi upload file';
            }
        }
    }

    // Nếu không có lỗi, cập nhật danh mục vào database
    if (empty($errors)) {
        $category_data = [
            'name' => $name,
            'description' => $description,
            'image' => $image,
            'parent_id' => $parent_id,
            'status' => $status
        ];

        $result = update_category($category_id, $category_data);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
            redirect('categories.php');
        } else {
            $errors[] = $result['message'];
        }
    }
}

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Chỉnh sửa danh mục</h1>

    <!-- Thông báo lỗi -->
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <!-- Form chỉnh sửa danh mục -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Thông tin danh mục</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="category-edit.php?id=<?php echo $category_id; ?>" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="name">Tên danh mục <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? $category['name']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="parent_id">Danh mục cha</label>
                    <select class="form-control select2" id="parent_id" name="parent_id">
                        <option value="">-- Không có danh mục cha (Danh mục gốc) --</option>
                        <?php
                        // Hàm đệ quy để hiển thị danh mục theo cấu trúc cây trong dropdown
                        function display_category_options($categories, $selected_id = '', $level = 0) {
                            foreach ($categories as $category) {
                                $prefix = str_repeat('&mdash; ', $level);
                                $selected = ($selected_id == $category['id']) ? 'selected' : '';
                                echo '<option value="' . $category['id'] . '" ' . $selected . '>';
                                echo $prefix . htmlspecialchars($category['name']);
                                echo '</option>';

                                if (!empty($category['children'])) {
                                    display_category_options($category['children'], $selected_id, $level + 1);
                                }
                            }
                        }

                        // Hiển thị danh mục
                        $selected_parent_id = isset($_POST['parent_id']) ? $_POST['parent_id'] : $category['parent_id'];
                        display_category_options($filtered_category_tree, $selected_parent_id);
                        ?>
                    </select>
                    <small class="form-text text-muted">Chọn danh mục cha nếu đây là danh mục con. Để trống nếu đây là danh mục gốc.</small>
                </div>

                <div class="form-group">
                    <label for="description">Mô tả</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($_POST['description'] ?? $category['description']); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="image">Hình ảnh</label>
                    <?php if (!empty($category['image'])): ?>
                    <div class="mb-2">
                        <img src="<?php echo BASE_URL . '/uploads/categories/' . $category['image']; ?>" alt="<?php echo htmlspecialchars($category['name']); ?>" class="img-thumbnail" style="max-width: 200px;">
                    </div>
                    <?php endif; ?>
                    <input type="file" class="form-control-file" id="image" name="image">
                    <small class="form-text text-muted">Để trống nếu không muốn thay đổi hình ảnh</small>
                    <small class="form-text text-muted">Hỗ trợ các định dạng: JPG, JPEG, PNG, GIF, WebP. Khuyến nghị sử dụng WebP để tối ưu hiệu suất.</small>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="status" name="status" <?php echo (isset($_POST['status']) ? $_POST['status'] : $category['status']) == 1 ? 'checked' : ''; ?> value="1">
                        <label class="custom-control-label" for="status">Hiển thị</label>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Cập nhật danh mục</button>
                    <a href="categories.php" class="btn btn-secondary">Hủy</a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
