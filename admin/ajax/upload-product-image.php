<?php
/**
 * <PERSON><PERSON> lý upload ảnh cho Summernote trong phần sản phẩm
 */

// Include các file cần thiết
require_once '../../includes/init.php';

// Ki<PERSON>m tra quyền truy cập
if (!is_admin()) {
    echo json_encode([
        'success' => false,
        'message' => 'Bạn không có quyền thực hiện thao tác này.'
    ]);
    exit;
}

// Kiểm tra request
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_FILES['file'])) {
    echo json_encode([
        'success' => false,
        'message' => '<PERSON><PERSON>u cầu không hợp lệ.'
    ]);
    exit;
}

// Xác định loại nội dung
$content_type = isset($_POST['type']) ? $_POST['type'] : 'product_content';

// Th<PERSON> mục lưu ảnh
$upload_dir = UPLOADS_PATH . 'products/content/';

// T<PERSON><PERSON> thư mục nếu chưa tồn tại
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Xử lý file upload
$file = $_FILES['file'];
$file_name = $file['name'];
$file_tmp = $file['tmp_name'];
$file_error = $file['error'];
$file_size = $file['size'];

// Kiểm tra lỗi upload
if ($file_error !== UPLOAD_ERR_OK) {
    $error_message = 'Có lỗi xảy ra khi tải lên file.';
    switch ($file_error) {
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            $error_message = 'File quá lớn.';
            break;
        case UPLOAD_ERR_PARTIAL:
            $error_message = 'File chỉ được tải lên một phần.';
            break;
        case UPLOAD_ERR_NO_FILE:
            $error_message = 'Không có file nào được tải lên.';
            break;
    }
    
    echo json_encode([
        'success' => false,
        'message' => $error_message
    ]);
    exit;
}

// Kiểm tra kích thước file (giới hạn 5MB)
if ($file_size > 5 * 1024 * 1024) {
    echo json_encode([
        'success' => false,
        'message' => 'Kích thước file không được vượt quá 5MB.'
    ]);
    exit;
}

// Kiểm tra loại file
$allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$file_info = finfo_open(FILEINFO_MIME_TYPE);
$file_mime = finfo_file($file_info, $file_tmp);
finfo_close($file_info);

if (!in_array($file_mime, $allowed_types)) {
    echo json_encode([
        'success' => false,
        'message' => 'Chỉ chấp nhận file ảnh (JPG, PNG, GIF, WEBP).'
    ]);
    exit;
}

// Tạo tên file mới để tránh trùng lặp
$file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
$new_file_name = 'product_content_' . uniqid() . '.' . $file_extension;
$upload_path = $upload_dir . $new_file_name;

// Di chuyển file tạm vào thư mục đích
if (move_uploaded_file($file_tmp, $upload_path)) {
    // Trả về URL của ảnh
    $image_url = BASE_URL . '/uploads/products/content/' . $new_file_name;
    
    echo json_encode([
        'success' => true,
        'url' => $image_url,
        'filename' => $new_file_name
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Không thể lưu file. Vui lòng thử lại sau.'
    ]);
}
