<?php
require_once '../../includes/init.php';

// <PERSON><PERSON>m tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Bạn không có quyền truy cập.']);
    exit;
}

// Chỉ chấp nhận POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON>ơng thức không được hỗ trợ.']);
    exit;
}

// Đọc dữ liệu JSON từ request body
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dữ liệu không hợp lệ.']);
    exit;
}

$product_id = isset($input['product_id']) ? intval($input['product_id']) : 0;
$quantity = isset($input['quantity']) ? intval($input['quantity']) : 0;
$csrf_token = isset($input['csrf_token']) ? trim($input['csrf_token']) : '';

// Kiểm tra CSRF token
if (!check_csrf_token($csrf_token)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'CSRF token không hợp lệ.']);
    exit;
}

// Validate dữ liệu
if ($product_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID sản phẩm không hợp lệ.']);
    exit;
}

if ($quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'Số lượng phải lớn hơn 0.']);
    exit;
}

// Kiểm tra sản phẩm tồn tại
$original_product = get_product_by_id($product_id);
if (!$original_product) {
    echo json_encode(['success' => false, 'message' => 'Sản phẩm không tồn tại.']);
    exit;
}

// Thực hiện nhân bản
$success_count = 0;
$error_messages = [];

for ($i = 1; $i <= $quantity; $i++) {
    $result = duplicate_product_with_suffix($product_id, $i);
    
    if ($result['success']) {
        $success_count++;
    } else {
        $error_messages[] = "Bản sao $i: " . $result['message'];
    }
}

// Trả về kết quả
if ($success_count > 0) {
    $message = "Đã nhân bản thành công $success_count/$quantity sản phẩm.";
    if (!empty($error_messages)) {
        $message .= " Lỗi: " . implode('; ', $error_messages);
    }
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'success_count' => $success_count,
        'total_requested' => $quantity
    ]);
} else {
    echo json_encode([
        'success' => false, 
        'message' => 'Không thể nhân bản sản phẩm nào. Lỗi: ' . implode('; ', $error_messages)
    ]);
}

/**
 * Nhân bản sản phẩm với suffix số thứ tự
 *
 * @param int $product_id ID của sản phẩm cần nhân bản
 * @param int $suffix Số thứ tự bản sao
 * @return array Kết quả nhân bản
 */
function duplicate_product_with_suffix($product_id, $suffix) {
    global $conn;
    $original_product = get_product_by_id($product_id);

    if (!$original_product) {
        return ['success' => false, 'message' => 'Sản phẩm gốc không tồn tại.'];
    }

    $new_product_data = $original_product;

    // Xử lý tên sản phẩm mới với suffix
    $new_product_data['name'] = $original_product['name'] . " (Bản sao $suffix)";
    
    // Slug sẽ được tạo tự động trong hàm add_product
    unset($new_product_data['id']);
    unset($new_product_data['slug']);
    
    // Reset một số trường
    $new_product_data['sold'] = 0;
    $new_product_data['views'] = 0;
    $new_product_data['created_at'] = date('Y-m-d H:i:s');
    $new_product_data['updated_at'] = date('Y-m-d H:i:s');

    // Giữ nguyên trạng thái của sản phẩm gốc
    // $new_product_data['status'] = $original_product['status']; // Đã có sẵn trong $new_product_data

    // Để trống SKU để hàm add_product tự động tạo SKU mới
    unset($new_product_data['sku']);

    $upload_dir = ROOT_PATH . 'uploads/products/';

    // Sao chép ảnh đại diện
    if (!empty($original_product['image'])) {
        $source_image_path = $upload_dir . $original_product['image'];
        $duplicate_image_result = duplicate_file($source_image_path, $upload_dir);
        if ($duplicate_image_result['success']) {
            $new_product_data['image'] = $duplicate_image_result['filename'];
        } else {
            error_log("Lỗi sao chép ảnh đại diện cho sản phẩm nhân bản: " . $duplicate_image_result['message']);
            $new_product_data['image'] = null;
        }
    }

    // Sao chép gallery
    if (!empty($original_product['gallery'])) {
        $original_gallery_images = explode(',', $original_product['gallery']);
        $new_gallery_images = [];
        foreach ($original_gallery_images as $img_name) {
            $source_gallery_image_path = $upload_dir . trim($img_name);
            $duplicate_gallery_result = duplicate_file($source_gallery_image_path, $upload_dir);
            if ($duplicate_gallery_result['success']) {
                $new_gallery_images[] = $duplicate_gallery_result['filename'];
            }
        }
        $new_product_data['gallery'] = !empty($new_gallery_images) ? implode(',', $new_gallery_images) : null;
    }

    // Loại bỏ các trường không cần thiết
    unset($new_product_data['category_name']);

    // Gọi hàm add_product
    $result = add_product($new_product_data);

    return $result;
}
?>
