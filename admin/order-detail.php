<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Xử lý cập nhật trạng thái đơn hàng
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && isset($_POST['order_id'])) {
    // Kiểm tra CSRF token
    if (!isset($_POST['csrf_token']) || !check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'CSRF token không hợp lệ hoặc đã hết hạn');
    } else {
        $update_order_id = (int)$_POST['order_id'];
        $status = $_POST['update_status'];

        // Kiểm tra trạng thái hợp lệ
        $valid_statuses = ['pending', 'processing', 'shipping', 'completed', 'cancelled'];
        if (in_array($status, $valid_statuses)) {
            // Cập nhật trạng thái đơn hàng
            $result = update_order_status($update_order_id, $status);

            if ($result['success']) {
                set_flash_message('success', 'Cập nhật trạng thái đơn hàng thành công');
            } else {
                set_flash_message('error', 'Lỗi: ' . $result['message']);
            }
        } else {
            set_flash_message('error', 'Trạng thái không hợp lệ');
        }
    }
}

// Kiểm tra ID đơn hàng
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('error', 'ID đơn hàng không hợp lệ.');
    redirect('orders.php');
}

$order_id = intval($_GET['id']);
$order = get_order_by_id($order_id);

if (!$order) {
    set_flash_message('error', 'Đơn hàng không tồn tại.');
    redirect('orders.php');
}

// Thiết lập tiêu đề trang
$page_title = 'Chi tiết đơn hàng #' . $order_id;

// Tạo CSRF token
$csrf_token = generate_csrf_token();

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <!-- CSRF Token cho AJAX -->
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    <!-- Base URL cho AJAX -->
    <meta name="base-url" content="<?php echo BASE_URL; ?>">

    <!-- Container cho thông báo AJAX -->
    <div class="notification-container"></div>

    <!-- Modern Order Detail Header -->
    <div class="order-detail-header">
        <div class="order-detail-header-content">
            <div class="order-detail-title-section">
                <div class="order-detail-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="order-detail-title-text">
                    <h1>Chi tiết đơn hàng #<?php echo $order_id; ?></h1>
                    <p class="order-detail-subtitle">Thông tin chi tiết và quản lý đơn hàng</p>
                </div>
            </div>
            <div class="order-detail-actions">
                <a href="orders.php" class="btn-modern-secondary">
                    <i class="fas fa-arrow-left"></i>
                    <span>Quay lại</span>
                </a>
                <a href="orders.php" class="btn-modern-primary">
                    <i class="fas fa-list"></i>
                    <span>Danh sách</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Thông báo -->
    <?php display_flash_message(); ?>

    <!-- Thông tin đơn hàng -->
    <div class="row">
        <div class="col-lg-6">
            <div class="modern-card order-info-card">
                <div class="modern-card-header">
                    <div class="card-header-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="card-header-content">
                        <h6 class="card-title">Thông tin đơn hàng</h6>
                        <p class="card-subtitle">Chi tiết và trạng thái đơn hàng</p>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="order-detail-table">
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-hashtag"></i>
                                <span>Mã đơn hàng</span>
                            </div>
                            <div class="detail-value">
                                <span class="order-id-badge">#<?php echo $order['id']; ?></span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Ngày đặt</span>
                            </div>
                            <div class="detail-value">
                                <div class="date-info">
                                    <div class="date-text"><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></div>
                                    <div class="time-text"><?php echo date('H:i', strtotime($order['created_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-flag"></i>
                                <span>Trạng thái</span>
                            </div>
                            <div class="detail-value">
                                <?php
                                $status_info = get_order_status_info($order['status'], 'admin');
                                $status_class = '';
                                switch($order['status']) {
                                    case 'pending':
                                        $status_class = 'warning';
                                        break;
                                    case 'processing':
                                        $status_class = 'info';
                                        break;
                                    case 'shipping':
                                        $status_class = 'primary';
                                        break;
                                    case 'completed':
                                        $status_class = 'active';
                                        break;
                                    case 'cancelled':
                                        $status_class = 'inactive';
                                        break;
                                    default:
                                        $status_class = 'inactive';
                                }
                                ?>
                                <div class="status-badge <?php echo $status_class; ?> order-status" data-order-id="<?php echo $order['id']; ?>">
                                    <?php
                                    $status_icons = [
                                        'pending' => 'fas fa-clock',
                                        'processing' => 'fas fa-cogs',
                                        'shipping' => 'fas fa-truck',
                                        'completed' => 'fas fa-check-circle',
                                        'cancelled' => 'fas fa-times-circle'
                                    ];
                                    ?>
                                    <i class="<?php echo $status_icons[$order['status']] ?? 'fas fa-question-circle'; ?>"></i>
                                    <span><?php echo $status_info['text']; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>Tổng tiền</span>
                            </div>
                            <div class="detail-value">
                                <span class="price-amount"><?php echo format_currency($order['total']); ?></span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-sticky-note"></i>
                                <span>Ghi chú</span>
                            </div>
                            <div class="detail-value">
                                <span class="note-text"><?php echo !empty($order['note']) ? htmlspecialchars($order['note']) : 'Không có ghi chú'; ?></span>
                            </div>
                        </div>
                    </div>

                    <?php if ($order['status'] !== 'completed' && $order['status'] !== 'cancelled'): ?>
                    <div class="status-actions-section" data-order-id="<?php echo $order['id']; ?>">
                        <div class="section-header">
                            <h6 class="section-title">Cập nhật trạng thái</h6>
                            <p class="section-subtitle">Thay đổi trạng thái đơn hàng</p>
                        </div>
                        <div class="status-buttons">
                            <?php if ($order['status'] !== 'pending'): ?>
                            <form method="post" action="" class="status-form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" name="update_status" value="pending" class="status-btn warning">
                                    <i class="fas fa-clock"></i>
                                    <span>Chờ xử lý</span>
                                </button>
                            </form>
                            <?php endif; ?>

                            <?php if ($order['status'] !== 'processing'): ?>
                            <form method="post" action="" class="status-form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" name="update_status" value="processing" class="status-btn info">
                                    <i class="fas fa-cogs"></i>
                                    <span>Đang xử lý</span>
                                </button>
                            </form>
                            <?php endif; ?>

                            <?php if ($order['status'] !== 'shipping'): ?>
                            <form method="post" action="" class="status-form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" name="update_status" value="shipping" class="status-btn primary">
                                    <i class="fas fa-truck"></i>
                                    <span>Đang giao hàng</span>
                                </button>
                            </form>
                            <?php endif; ?>

                            <?php if ($order['status'] !== 'completed'): ?>
                            <form method="post" action="" class="status-form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" name="update_status" value="completed" class="status-btn success">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Hoàn thành</span>
                                </button>
                            </form>
                            <?php endif; ?>

                            <?php if ($order['status'] !== 'cancelled'): ?>
                            <form method="post" action="" class="status-form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" name="update_status" value="cancelled" class="status-btn danger" onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?');">
                                    <i class="fas fa-times-circle"></i>
                                    <span>Hủy đơn hàng</span>
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="modern-card customer-info-card">
                <div class="modern-card-header">
                    <div class="card-header-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="card-header-content">
                        <h6 class="card-title">Thông tin khách hàng</h6>
                        <p class="card-subtitle">Chi tiết liên hệ và địa chỉ</p>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="customer-detail-table">
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-user-circle"></i>
                                <span>Họ tên</span>
                            </div>
                            <div class="detail-value">
                                <span class="customer-name"><?php echo htmlspecialchars($order['full_name']); ?></span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-envelope"></i>
                                <span>Email</span>
                            </div>
                            <div class="detail-value">
                                <span class="email-text"><?php echo htmlspecialchars($order['email']); ?></span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-phone"></i>
                                <span>Số điện thoại</span>
                            </div>
                            <div class="detail-value">
                                <span class="phone-text"><?php echo htmlspecialchars($order['phone']); ?></span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Địa chỉ</span>
                            </div>
                            <div class="detail-value">
                                <span class="address-text"><?php echo htmlspecialchars($order['address']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chi tiết đơn hàng -->
    <div class="modern-card order-items-card">
        <div class="modern-card-header">
            <div class="card-header-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <div class="card-header-content">
                <h6 class="card-title">Chi tiết đơn hàng</h6>
                <p class="card-subtitle">Danh sách sản phẩm trong đơn hàng</p>
            </div>
        </div>
        <div class="modern-card-body">
            <div class="modern-table-container">
                <table class="modern-table order-items-table" width="100%" cellspacing="0">
                    <thead class="modern-table-header">
                        <tr>
                            <th width="40%">Sản phẩm</th>
                            <th width="15%" class="text-center">Giá</th>
                            <th width="15%" class="text-center">Số lượng</th>
                            <th width="15%" class="text-center">Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($order['items'] as $item): ?>
                        <tr class="order-item-row">
                            <td>
                                <div class="product-info">
                                    <div class="product-image-container">
                                        <?php if (!empty($item['product_image'])): ?>
                                        <img src="<?php echo BASE_URL . '/uploads/products/' . $item['product_image']; ?>" alt="<?php echo htmlspecialchars($item['product_name']); ?>" class="product-image">
                                        <?php else: ?>
                                        <img src="<?php echo BASE_URL; ?>/assets/img/no-image.jpg" alt="No Image" class="product-image">
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-name"><?php echo htmlspecialchars($item['product_name']); ?></div>
                                        <div class="product-meta">Mã SP: #<?php echo $item['product_id']; ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="price-info">
                                    <span class="price-amount"><?php echo format_currency($item['price']); ?></span>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="quantity-info">
                                    <span class="quantity-badge"><?php echo $item['quantity']; ?></span>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="total-info">
                                    <span class="total-amount"><?php echo format_currency($item['price'] * $item['quantity']); ?></span>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Order Total Section -->
            <div class="order-total-section">
                <div class="total-row">
                    <div class="total-label">
                        <i class="fas fa-calculator"></i>
                        <span>Tổng cộng</span>
                    </div>
                    <div class="total-value">
                        <span class="final-total"><?php echo format_currency($order['total']); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Order Detail Page Styles -->
<style>
/* Modern Order Detail Page Styles - Nội Thất Băng Vũ Theme */
:root {
    /* Primary Colors - Cam chính từ brand */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* Secondary Colors - Xanh đậm */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-light: #435868;

    /* Enhanced Status Colors - More Vibrant and Contrasting */
    --success: #22C55E;
    --success-dark: #16A34A;
    --warning: #F59E0B;
    --warning-dark: #D97706;
    --danger: #EF4444;
    --danger-dark: #DC2626;
    --info: #3B82F6;
    --info-dark: #2563EB;
    --processing: #8B5CF6;
    --processing-dark: #7C3AED;

    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* Enhanced Gradients with Better Contrast */
    --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
    --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
    --success-gradient: linear-gradient(135deg, #22C55E 0%, #16A34A 100%);
    --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    --processing-gradient: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);

    /* Shadows */
    --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
    --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --border-radius: 1.25rem;
    --border-radius-sm: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-fast: all 0.2s ease;
}

/* Order Detail Page Header */
.order-detail-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(243, 115, 33, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.08),
        0 4px 6px -2px rgba(0, 0, 0, 0.03),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: var(--transition);
}

.order-detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%),
        linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
}

.order-detail-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.order-detail-header:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.order-detail-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.order-detail-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.order-detail-icon {
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    padding: 1rem;
    border-radius: var(--border-radius);
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    position: relative;
    box-shadow:
        0 6px 10px -2px rgba(243, 115, 33, 0.25),
        0 2px 4px -1px rgba(243, 115, 33, 0.15);
}

.order-detail-icon::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: inherit;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.order-detail-icon::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: calc(var(--border-radius) - 2px);
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
    pointer-events: none;
}

.order-detail-header:hover .order-detail-icon {
    transform: rotate(15deg) scale(1.1);
    box-shadow:
        0 20px 25px -5px rgba(243, 115, 33, 0.4),
        0 10px 10px -5px rgba(243, 115, 33, 0.3);
}

.order-detail-header:hover .order-detail-icon::before {
    opacity: 1;
    inset: -5px;
}

.order-detail-title-text h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.order-detail-title-text h1::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
    transition: width 0.6s ease;
}

.order-detail-header:hover .order-detail-title-text h1::before {
    width: 100%;
}

.order-detail-subtitle {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
    opacity: 0.9;
    transition: var(--transition-fast);
}

.order-detail-header:hover .order-detail-subtitle {
    color: var(--gray-700);
    opacity: 1;
}

.order-detail-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.btn-modern-primary, .btn-modern-secondary {
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.btn-modern-secondary {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--gray-700);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary::before, .btn-modern-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern-primary:hover, .btn-modern-secondary:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.btn-modern-primary:hover {
    box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
    color: white;
}

.btn-modern-secondary:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    color: var(--gray-700);
}

.btn-modern-primary:hover::before, .btn-modern-secondary:hover::before {
    left: 100%;
}

/* Modern Cards */
.modern-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(243, 115, 33, 0.08);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    border-color: rgba(243, 115, 33, 0.12);
}

.modern-card:hover::before {
    opacity: 1;
}

.modern-card-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(243, 115, 33, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.card-header-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.2);
    transition: var(--transition);
}

.modern-card:hover .card-header-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(243, 115, 33, 0.3);
}

.card-header-content {
    flex: 1;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: 400;
}

.modern-card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

/* Detail Tables */
.order-detail-table, .customer-detail-table {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
    border-radius: 0.75rem;
    border: 1px solid rgba(243, 115, 33, 0.05);
    transition: var(--transition-fast);
}

.detail-row:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
    border-color: rgba(243, 115, 33, 0.1);
    transform: translateX(4px);
}

.detail-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    min-width: 140px;
}

.detail-label i {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background: var(--primary-gradient);
    color: white;
    font-size: 0.75rem;
    box-shadow: 0 2px 6px rgba(243, 115, 33, 0.3);
}

.detail-value {
    flex: 1;
    text-align: right;
}

.order-id-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
}

.date-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.125rem;
}

.date-text {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.time-text {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-family: monospace;
}

/* Enhanced Status Badges with Better Contrast */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.active {
    background: var(--success-gradient);
    color: white;
    border: 2px solid var(--success-dark);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.status-badge.inactive {
    background: var(--danger-gradient);
    color: white;
    border: 2px solid var(--danger-dark);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-badge.warning {
    background: var(--warning-gradient);
    color: white;
    border: 2px solid var(--warning-dark);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.status-badge.info {
    background: var(--processing-gradient);
    color: white;
    border: 2px solid var(--processing-dark);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.status-badge.primary {
    background: var(--info-gradient);
    color: white;
    border: 2px solid var(--info-dark);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.status-badge i {
    font-size: 0.75rem;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.price-amount {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(243, 115, 33, 0.2);
}

.note-text {
    color: var(--gray-600);
    font-style: italic;
    font-size: 0.875rem;
    background: var(--gray-50);
    padding: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid var(--primary);
}

.customer-name {
    font-weight: 700;
    color: var(--gray-800);
    font-size: 0.95rem;
}

.email-text {
    color: var(--info);
    font-size: 0.875rem;
    word-break: break-word;
    font-weight: 500;
}

.phone-text {
    color: var(--success);
    font-size: 0.875rem;
    font-family: monospace;
    font-weight: 600;
}

.address-text {
    color: var(--gray-700);
    font-size: 0.875rem;
    line-height: 1.5;
    background: var(--gray-50);
    padding: 0.5rem;
    border-radius: 6px;
}

/* Status Actions Section */
.status-actions-section {
    margin-top: 2rem;
    padding: 2rem;
    border-top: 1px solid rgba(243, 115, 33, 0.08);
    background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    border: 1px solid rgba(243, 115, 33, 0.08);
    position: relative;
    overflow: hidden;
}

.status-actions-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

.section-header {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: 400;
}

/* Status Buttons Container */
.status-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: nowrap;
    align-items: center;
    position: relative;
    z-index: 2;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
}

/* Status Form Inline */
.status-form-inline {
    margin: 0;
    display: inline-block;
    flex-shrink: 0;
}

/* Status Button Styles */
.status-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.875rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 100px;
    justify-content: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.status-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.status-btn:hover::before {
    left: 100%;
}

.status-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.status-btn i {
    font-size: 0.7rem;
}

/* Status Button Variants */
.status-btn.warning {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.status-btn.warning:hover {
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
    color: white;
}

.status-btn.info {
    background: var(--processing-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.status-btn.info:hover {
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
    color: white;
}

.status-btn.primary {
    background: var(--info-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.status-btn.primary:hover {
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.status-btn.success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.status-btn.success:hover {
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
    color: white;
}

.status-btn.danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-btn.danger:hover {
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
    color: white;
}

/* Status Action Buttons */
.status-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    align-items: center;
}

.status-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.8rem;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.status-action-btn:hover::before {
    left: 100%;
}

.status-action-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.status-action-btn.btn-processing {
    background: var(--processing-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.status-action-btn.btn-processing:hover {
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
    color: white;
}

.status-action-btn.btn-completed {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.status-action-btn.btn-completed:hover {
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
    color: white;
}

.status-action-btn.btn-cancelled {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-action-btn.btn-cancelled:hover {
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
    color: white;
}

.status-action-btn i {
    font-size: 0.75rem;
}

/* Modern Table Container */
.modern-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(243, 115, 33, 0.08);
}

/* Modern Table */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    margin: 0;
}

.modern-table-header {
    background: var(--primary-gradient);
    color: white;
}

/* Order Items Section */
.order-items-section {
    margin-top: 2rem;
}

.order-items-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(243, 115, 33, 0.08);
}

.order-items-table thead {
    background: var(--primary-gradient);
    color: white;
}

.order-items-table th {
    padding: 1rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-align: left;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-items-table th:first-child {
    border-top-left-radius: var(--border-radius);
}

.order-items-table th:last-child {
    border-top-right-radius: var(--border-radius);
    text-align: right;
}

.order-items-table tbody tr {
    transition: var(--transition-fast);
    border-bottom: 1px solid rgba(243, 115, 33, 0.05);
}

.order-items-table tbody tr:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
}

.order-items-table tbody tr:last-child {
    border-bottom: none;
}

.order-items-table td {
    padding: 1.25rem 1.5rem;
    border: none;
    vertical-align: middle;
}

.order-items-table td:last-child {
    text-align: right;
}

/* Order Item Row */
.order-item-row {
    transition: var(--transition-fast);
}

.order-item-row:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
}

/* Product Image Container */
.product-image-container {
    width: 60px;
    height: 60px;
    border-radius: 0.75rem;
    overflow: hidden;
    border: 2px solid rgba(243, 115, 33, 0.1);
    transition: var(--transition-fast);
}

.product-image-container:hover {
    border-color: var(--primary);
    transform: scale(1.05);
}

/* Product Meta */
.product-meta {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.25rem;
    font-family: monospace;
}

/* Price Info */
.price-info {
    text-align: center;
}

.price-amount {
    font-weight: 700;
    color: var(--primary);
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(243, 115, 33, 0.2);
}

/* Quantity Info */
.quantity-info {
    text-align: center;
}

/* Total Info */
.total-info {
    text-align: center;
}

.total-amount {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(243, 115, 33, 0.2);
}

/* Order Total Section */
.order-total-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    border: 2px solid rgba(243, 115, 33, 0.1);
    position: relative;
    overflow: hidden;
}

.order-total-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.total-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.total-label i {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: var(--primary-gradient);
    color: white;
    font-size: 1rem;
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.3);
}

.total-value {
    text-align: right;
}

.final-total {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--primary);
    text-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
    display: block;
    line-height: 1;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 0.75rem;
    object-fit: cover;
    border: 2px solid rgba(243, 115, 33, 0.1);
    transition: var(--transition-fast);
}

.product-image:hover {
    border-color: var(--primary);
    transform: scale(1.05);
}

.product-details {
    flex: 1;
}

.product-name {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.95rem;
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
}

.product-category {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

.quantity-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--gray-700);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    min-width: 50px;
    border: 1px solid rgba(243, 115, 33, 0.1);
}

.price-cell {
    font-weight: 700;
    color: var(--primary);
    font-size: 1rem;
}

/* Order Summary */
.order-summary {
    background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-top: 2rem;
    border: 1px solid rgba(243, 115, 33, 0.08);
    position: relative;
    overflow: hidden;
}

.order-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

.summary-content {
    position: relative;
    z-index: 2;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(243, 115, 33, 0.05);
}

.summary-row:last-child {
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 0.5rem;
    border-top: 2px solid var(--primary);
}

.summary-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.summary-row:last-child .summary-label {
    font-size: 1.125rem;
    color: var(--gray-800);
}

.summary-value {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.summary-row:last-child .summary-value {
    font-size: 1.25rem;
    color: var(--primary);
    font-weight: 700;
}

.total-section {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 2px solid rgba(243, 115, 33, 0.1);
}

.total-label {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.total-value {
    text-align: center;
}

.final-total {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary);
    text-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .order-detail-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .order-detail-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .modern-card-header {
        padding: 1rem 1.5rem 0.75rem;
    }

    .modern-card-body {
        padding: 1.5rem;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 1rem;
    }

    .detail-label {
        min-width: auto;
        width: 100%;
    }

    .detail-value {
        text-align: left;
        width: 100%;
    }

    .status-buttons {
        flex-wrap: wrap;
        gap: 0.375rem;
    }

    .status-btn {
        min-width: 80px;
        font-size: 0.7rem;
        padding: 0.375rem 0.625rem;
    }

    .status-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .status-action-btn {
        justify-content: center;
        width: 100%;
    }

    .order-items-table {
        font-size: 0.875rem;
    }

    .order-items-table th,
    .order-items-table td {
        padding: 0.75rem 1rem;
    }

    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .product-image {
        width: 50px;
        height: 50px;
    }

    .order-summary {
        padding: 1.5rem;
    }

    .final-total {
        font-size: 1.75rem;
    }
}

@media (max-width: 576px) {
    .order-detail-header {
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .order-detail-title-text h1 {
        font-size: 1.5rem;
    }

    .modern-card-body {
        padding: 1rem;
    }

    .order-items-table th,
    .order-items-table td {
        padding: 0.5rem 0.75rem;
    }

    .product-info {
        gap: 0.5rem;
    }

    .product-image {
        width: 40px;
        height: 40px;
    }

    .final-total {
        font-size: 1.5rem;
    }
}

/* Animation for container-fluid - Consistent with other admin pages */
.container-fluid {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php
// Include footer (chứa jQuery và các thư viện JavaScript cần thiết)
include_once 'partials/footer.php';

// Thêm script quản lý đơn hàng và chi tiết đơn hàng sau khi đã tải jQuery
?>
<script>
// Đảm bảo jQuery đã được tải
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, jQuery status:', typeof $ !== 'undefined' ? 'loaded' : 'not loaded');

    // Kiểm tra các nút cập nhật trạng thái
    if (typeof $ !== 'undefined') {
        var updateButtons = $('.update-order-status');
        console.log('Found ' + updateButtons.length + ' update buttons');

        // Gắn sự kiện click cho các nút cập nhật trạng thái
        updateButtons.on('click', function(e) {
            e.preventDefault();
            console.log('Button clicked');

            var button = $(this);
            var orderId = button.data('order-id');
            var status = button.data('status');
            var csrfToken = $('meta[name="csrf-token"]').attr('content');
            var baseUrl = $('meta[name="base-url"]').attr('content');

            console.log('Update order:', orderId, 'to status:', status);

            // Hiển thị xác nhận nếu là hủy đơn hàng
            if (status === 'cancelled' && !confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                return;
            }

            // Hiển thị loading
            $('body').append('<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div class="spinner-border text-light" role="status"><span class="sr-only">Đang xử lý...</span></div></div>');

            // Gửi AJAX request
            $.ajax({
                url: baseUrl + '/ajax/update_order_status.php',
                type: 'POST',
                data: {
                    order_id: orderId,
                    status: status,
                    csrf_token: csrfToken
                },
                dataType: 'json',
                success: function(response) {
                    // Ẩn loading
                    $('#loading-overlay').remove();

                    console.log('Response:', response);

                    if (response.success) {
                        // Cập nhật UI
                        var statusBadge = $('.order-status[data-order-id="' + orderId + '"]');
                        if (statusBadge.length) {
                            statusBadge.removeClass().addClass('order-status badge ' + response.status_class).text(response.status_text);
                        }

                        // Hiển thị thông báo thành công
                        alert('Cập nhật trạng thái đơn hàng thành công!');

                        // Ẩn các nút cập nhật trạng thái nếu đơn hàng đã hoàn thành hoặc hủy
                        if (status === 'completed' || status === 'cancelled') {
                            $('.status-actions[data-order-id="' + orderId + '"]').hide();
                        }

                        // Tải lại trang sau 1 giây
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        // Hiển thị thông báo lỗi
                        alert('Lỗi: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    // Ẩn loading
                    $('#loading-overlay').remove();

                    console.error('AJAX Error:', xhr, status, error);

                    // Hiển thị thông báo lỗi
                    alert('Có lỗi xảy ra khi cập nhật trạng thái đơn hàng. Vui lòng thử lại sau.');
                }
            });
        });
    } else {
        console.error('jQuery is not loaded!');
    }
});
</script>

<?php
?>
