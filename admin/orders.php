<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Xử lý cập nhật trạng thái đơn hàng
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && isset($_POST['order_id'])) {
    // Kiểm tra CSRF token
    if (!isset($_POST['csrf_token']) || !check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'CSRF token không hợp lệ hoặc đã hết hạn');
    } else {
        $order_id = (int)$_POST['order_id'];
        $status = $_POST['update_status'];

        // Kiểm tra trạng thái hợp lệ
        $valid_statuses = ['pending', 'processing', 'shipping', 'completed', 'cancelled'];
        if (in_array($status, $valid_statuses)) {
            // Cập nhật trạng thái đơn hàng
            $result = update_order_status($order_id, $status);

            if ($result['success']) {
                set_flash_message('success', 'Cập nhật trạng thái đơn hàng thành công');
            } else {
                set_flash_message('error', 'Lỗi: ' . $result['message']);
            }
        } else {
            set_flash_message('error', 'Trạng thái không hợp lệ');
        }
    }
}

// Thiết lập tiêu đề trang
$page_title = 'Quản lý đơn hàng';

// Xử lý tìm kiếm và lọc
$status = isset($_GET['status']) ? $_GET['status'] : null;

// Phân trang
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Lấy danh sách đơn hàng
$orders = get_orders($limit, $offset, null, $status);
$total_orders = count_orders(null, $status);
$total_pages = ceil($total_orders / $limit);

// Include header
include_once 'partials/header.php';

// Thêm CSRF token cho AJAX
$csrf_token = generate_csrf_token();
?>

<!-- Content -->
<div class="container-fluid">
    <!-- CSRF Token cho AJAX -->
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    <!-- Base URL cho AJAX -->
    <meta name="base-url" content="<?php echo BASE_URL; ?>">

    <!-- Container cho thông báo AJAX -->
    <div class="notification-container"></div>

    <!-- Modern Orders Header -->
    <div class="orders-header">
        <div class="orders-header-content">
            <div class="orders-title-section">
                <div class="orders-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="orders-title-text">
                    <h1>Quản lý đơn hàng</h1>
                    <p class="orders-subtitle">Theo dõi và xử lý đơn hàng khách hàng</p>
                </div>
            </div>
            <div class="orders-actions">
                <a href="<?php echo BASE_URL; ?>/admin/overview.php" class="btn-modern-primary">
                    <i class="fas fa-chart-line"></i>
                    <span>Thống kê</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card orders">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Tổng đơn hàng</h3>
                    <div class="stat-value"><?php echo count_orders(); ?></div>
                    <div class="stat-change neutral">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Tất cả đơn hàng</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>

        <div class="stat-card pending">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Chờ xử lý</h3>
                    <div class="stat-value"><?php echo count_orders_by_status('pending'); ?></div>
                    <div class="stat-change warning">
                        <i class="fas fa-clock"></i>
                        <span>Cần xử lý</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
            </div>
        </div>

        <div class="stat-card processing">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Đang xử lý</h3>
                    <div class="stat-value"><?php echo count_orders_by_status('processing'); ?></div>
                    <div class="stat-change info">
                        <i class="fas fa-cogs"></i>
                        <span>Đang thực hiện</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-spinner"></i>
                </div>
            </div>
        </div>

        <div class="stat-card completed">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Hoàn thành</h3>
                    <div class="stat-value"><?php echo count_orders_by_status('completed'); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-check"></i>
                        <span>Thành công</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Thông báo -->
    <?php display_flash_message(); ?>

    <!-- Bộ lọc -->
    <div class="modern-card filter-card">
        <div class="modern-card-header">
            <div class="card-header-icon">
                <i class="fas fa-filter"></i>
            </div>
            <div class="card-header-content">
                <h6 class="card-title">Bộ lọc đơn hàng</h6>
                <p class="card-subtitle">Lọc đơn hàng theo trạng thái</p>
            </div>
        </div>
        <div class="modern-card-body">
            <div class="filter-buttons">
                <a href="orders.php" class="filter-btn <?php echo !$status ? 'active' : ''; ?>">
                    <i class="fas fa-list"></i>
                    <span>Tất cả</span>
                </a>
                <a href="orders.php?status=pending" class="filter-btn <?php echo $status === 'pending' ? 'active' : ''; ?>">
                    <i class="fas fa-clock"></i>
                    <span>Chờ xử lý</span>
                </a>
                <a href="orders.php?status=processing" class="filter-btn <?php echo $status === 'processing' ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i>
                    <span>Đang xử lý</span>
                </a>
                <a href="orders.php?status=shipping" class="filter-btn <?php echo $status === 'shipping' ? 'active' : ''; ?>">
                    <i class="fas fa-truck"></i>
                    <span>Đang giao hàng</span>
                </a>
                <a href="orders.php?status=completed" class="filter-btn <?php echo $status === 'completed' ? 'active' : ''; ?>">
                    <i class="fas fa-check-circle"></i>
                    <span>Hoàn thành</span>
                </a>
                <a href="orders.php?status=cancelled" class="filter-btn <?php echo $status === 'cancelled' ? 'active' : ''; ?>">
                    <i class="fas fa-times-circle"></i>
                    <span>Đã hủy</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Danh sách đơn hàng -->
    <div class="modern-card orders-list-card">
        <div class="modern-card-header">
            <div class="card-header-icon">
                <i class="fas fa-list"></i>
            </div>
            <div class="card-header-content">
                <h6 class="card-title">Danh sách đơn hàng</h6>
                <p class="card-subtitle">Quản lý tất cả đơn hàng khách hàng</p>
            </div>
            <div class="card-header-actions">
                <div class="table-controls">
                    <div class="toggle-control">
                        <input type="checkbox" id="toggle-id-column" checked>
                        <label for="toggle-id-column" class="toggle-label">
                            <span class="toggle-switch"></span>
                            <span class="toggle-text">Hiện ID</span>
                        </label>
                    </div>
                    <div class="search-box">
                        <input type="text" id="order-search" placeholder="Tìm kiếm đơn hàng..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="modern-card-body">
            <div class="modern-table-container">
                <table class="modern-table" width="100%" cellspacing="0">
                    <thead class="modern-table-header">
                        <tr>
                            <th width="5%" class="text-center id-column">ID</th>
                            <th width="20%">Khách hàng</th>
                            <th width="15%">Email</th>
                            <th width="12%">Số điện thoại</th>
                            <th width="10%" class="text-center">Tổng tiền</th>
                            <th width="12%" class="text-center">Trạng thái</th>
                            <th width="12%" class="text-center">Ngày đặt</th>
                            <th width="14%" class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($orders) > 0): ?>
                        <?php foreach ($orders as $order): ?>
                        <tr class="order-row">
                            <td class="text-center id-column">
                                <span class="order-id-badge"><?php echo $order['id']; ?></span>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name"><?php echo htmlspecialchars($order['full_name']); ?></div>
                                    <div class="customer-meta">ID: <?php echo $order['id']; ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="contact-info">
                                    <div class="email-text"><?php echo htmlspecialchars($order['email']); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="phone-info">
                                    <span class="phone-text"><?php echo htmlspecialchars($order['phone']); ?></span>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="price-info">
                                    <span class="price-amount"><?php echo format_currency($order['total']); ?></span>
                                </div>
                            </td>
                            <td class="text-center status-column">
                                <?php
                                $status_info = get_order_status_info($order['status'], 'admin');
                                $status_class = '';
                                switch($order['status']) {
                                    case 'pending':
                                        $status_class = 'warning';
                                        break;
                                    case 'processing':
                                        $status_class = 'info';
                                        break;
                                    case 'shipping':
                                        $status_class = 'primary';
                                        break;
                                    case 'completed':
                                        $status_class = 'active';
                                        break;
                                    case 'cancelled':
                                        $status_class = 'inactive';
                                        break;
                                    default:
                                        $status_class = 'inactive';
                                }
                                ?>
                                <div class="status-badge <?php echo $status_class; ?>" id="order-status-<?php echo $order['id']; ?>">
                                    <?php
                                    $status_icons = [
                                        'pending' => 'fas fa-clock',
                                        'processing' => 'fas fa-cogs',
                                        'shipping' => 'fas fa-truck',
                                        'completed' => 'fas fa-check-circle',
                                        'cancelled' => 'fas fa-times-circle'
                                    ];
                                    ?>
                                    <i class="<?php echo $status_icons[$order['status']] ?? 'fas fa-question-circle'; ?>"></i>
                                    <span><?php echo $status_info['text']; ?></span>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="date-info">
                                    <div class="date-text" style="font-size: 0.85rem;"><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></div>
                                    <div class="time-text" style="font-size: 0.85rem;"><?php echo date('H:i', strtotime($order['created_at'])); ?></div>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="table-actions">
                                    <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn view-btn" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if ($order['status'] === 'completed'): ?>
                                    <div class="action-btn completed-btn" title="Đơn hàng đã hoàn thành">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <div class="action-btn cancelled-btn" title="Đơn hàng đã hủy">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <?php else: ?>
                                    <div class="btn-group status-dropdown" data-order-id="<?php echo $order['id']; ?>">
                                        <button type="button" class="action-btn edit-btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Cập nhật trạng thái">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <div class="dropdown-menu modern-dropdown">
                                            <?php if ($order['status'] !== 'pending'): ?>
                                            <form method="post" action="" class="dropdown-item-form">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <input type="hidden" name="update_status" value="pending">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <button type="submit" class="dropdown-item modern-dropdown-item" data-status="pending">
                                                    <i class="fas fa-clock"></i>
                                                    <span>Chờ xử lý</span>
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if ($order['status'] !== 'processing'): ?>
                                            <form method="post" action="" class="dropdown-item-form">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <input type="hidden" name="update_status" value="processing">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <button type="submit" class="dropdown-item modern-dropdown-item" data-status="processing">
                                                    <i class="fas fa-cogs"></i>
                                                    <span>Đang xử lý</span>
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if ($order['status'] !== 'shipping'): ?>
                                            <form method="post" action="" class="dropdown-item-form">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <input type="hidden" name="update_status" value="shipping">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <button type="submit" class="dropdown-item modern-dropdown-item" data-status="shipping">
                                                    <i class="fas fa-truck"></i>
                                                    <span>Đang giao hàng</span>
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if ($order['status'] !== 'completed'): ?>
                                            <form method="post" action="" class="dropdown-item-form">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <input type="hidden" name="update_status" value="completed">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <button type="submit" class="dropdown-item modern-dropdown-item" data-status="completed">
                                                    <i class="fas fa-check-circle"></i>
                                                    <span>Hoàn thành</span>
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if ($order['status'] !== 'cancelled'): ?>
                                            <form method="post" action="" class="dropdown-item-form">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <input type="hidden" name="update_status" value="cancelled">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                <button type="submit" class="dropdown-item modern-dropdown-item danger" data-status="cancelled" onclick="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?');">
                                                    <i class="fas fa-times-circle"></i>
                                                    <span>Hủy đơn hàng</span>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center no-data">
                                <div class="no-data-message">
                                    <i class="fas fa-inbox"></i>
                                    <p>Không có đơn hàng nào</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Modern Pagination - Consistent with products.php -->
            <?php if ($total_pages > 1): ?>
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="pagination-info">
                    <small class="text-muted">
                        Hiển thị <?php echo (($page - 1) * $limit) + 1; ?> - <?php echo min($page * $limit, $total_orders); ?>
                        trong tổng số <?php echo $total_orders; ?> đơn hàng
                    </small>
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-modern mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i>
                                <span class="d-none d-sm-inline ms-1">Trước</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php
                        // Tính toán phạm vi trang hiển thị
                        $range = 2; // Số trang hiển thị mỗi bên
                        $start_page = max(1, $page - $range);
                        $end_page = min($total_pages, $page + $range);

                        // Hiển thị trang đầu nếu cần
                        if ($start_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                            </li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"><?php echo $total_pages; ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                <span class="d-none d-sm-inline me-1">Tiếp</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modern Orders Page Styles -->
<style>
/* Modern Orders Page Styles - Nội Thất Băng Vũ Theme */
:root {
    /* Primary Colors - Cam chính từ brand */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* Secondary Colors - Xanh đậm */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-light: #435868;

    /* Status Colors */
    --success: #10B981;
    --warning: #F59E0B;
    --danger: #EF4444;
    --info: #3B82F6;

    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
    --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
    --success-gradient: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);

    /* Shadows */
    --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
    --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --border-radius: 1.25rem;
    --border-radius-sm: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-fast: all 0.2s ease;
}

/* Orders Page Header */
.orders-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(243, 115, 33, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.08),
        0 4px 6px -2px rgba(0, 0, 0, 0.03),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: var(--transition);
}

.orders-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%),
        linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
}

.orders-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.orders-header:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.orders-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.orders-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.orders-icon {
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    padding: 1rem;
    border-radius: var(--border-radius);
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    position: relative;
    box-shadow:
        0 6px 10px -2px rgba(243, 115, 33, 0.25),
        0 2px 4px -1px rgba(243, 115, 33, 0.15);
}

.orders-icon::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: inherit;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.orders-icon::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: calc(var(--border-radius) - 2px);
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
    pointer-events: none;
}

.orders-header:hover .orders-icon {
    transform: rotate(15deg) scale(1.1);
    box-shadow:
        0 20px 25px -5px rgba(243, 115, 33, 0.4),
        0 10px 10px -5px rgba(243, 115, 33, 0.3);
}

.orders-header:hover .orders-icon::before {
    opacity: 1;
    inset: -5px;
}

.orders-title-text h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.orders-title-text h1::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
    transition: width 0.6s ease;
}

.orders-header:hover .orders-title-text h1::before {
    width: 100%;
}

.orders-subtitle {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
    opacity: 0.9;
    transition: var(--transition-fast);
}

.orders-header:hover .orders-subtitle {
    color: var(--gray-700);
    opacity: 1;
}

.orders-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.btn-modern-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.btn-modern-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
    color: white;
    text-decoration: none;
}

.btn-modern-primary:hover::before {
    left: 100%;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(243, 115, 33, 0.08);
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.01);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.12),
        0 10px 10px -5px rgba(0, 0, 0, 0.08);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--primary-gradient);
    transition: all 0.4s ease;
    border-radius: 1.5rem 1.5rem 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.4s ease;
}

.stat-card:hover::after {
    transform: scale(1.2);
    opacity: 0.8;
}

.stat-card.orders::before { background: var(--primary-gradient); }
.stat-card.pending::before { background: var(--warning-gradient); }
.stat-card.processing::before { background: var(--info-gradient); }
.stat-card.completed::before { background: var(--success-gradient); }

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

.stat-change.neutral {
    color: var(--gray-500);
}

.stat-change.info {
    color: var(--info);
}

.stat-change.warning {
    color: var(--warning);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    background: rgba(243, 115, 33, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(243, 115, 33, 0.15);
}

/* Modern Cards */
.modern-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(243, 115, 33, 0.08);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    border-color: rgba(243, 115, 33, 0.12);
}

.modern-card:hover::before {
    opacity: 1;
}

.modern-card-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(243, 115, 33, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.card-header-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 4px 8px rgba(243, 115, 33, 0.2);
    transition: var(--transition);
}

.modern-card:hover .card-header-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(243, 115, 33, 0.3);
}

.card-header-content {
    flex: 1;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: 400;
}

.card-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: var(--transition-fast);
    border: 2px solid var(--gray-200);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    color: var(--gray-700);
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.1), transparent);
    transition: left 0.5s ease;
}

.filter-btn:hover {
    border-color: var(--primary-light);
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
    color: var(--primary-dark);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    border-color: var(--primary);
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
}

.filter-btn.active:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(243, 115, 33, 0.4);
}

/* Table Controls */
.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Toggle Control */
.toggle-control {
    display: flex;
    align-items: center;
}

.toggle-control input[type="checkbox"] {
    display: none;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

.toggle-switch {
    position: relative;
    width: 2.5rem;
    height: 1.25rem;
    background: var(--gray-300);
    border-radius: 1rem;
    transition: var(--transition-fast);
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1rem;
    height: 1rem;
    background: white;
    border-radius: 50%;
    transition: var(--transition-fast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-control input[type="checkbox"]:checked + .toggle-label .toggle-switch {
    background: var(--primary);
}

.toggle-control input[type="checkbox"]:checked + .toggle-label .toggle-switch::before {
    transform: translateX(1.25rem);
}

.toggle-text {
    font-weight: 600;
    white-space: nowrap;
}

/* Search Box */
.search-box {
    position: relative;
    width: 250px;
}

.search-input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 0.875rem;
    pointer-events: none;
}

/* Modern Table */
.modern-table-container {
    border-radius: 0.75rem;
    overflow: visible; /* Changed from hidden to visible for dropdown */
    border: 1px solid rgba(243, 115, 33, 0.08);
    position: relative;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.modern-table-header th {
    padding: 1rem 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid rgba(243, 115, 33, 0.1);
    position: relative;
    white-space: nowrap;
    vertical-align: middle;
}

.modern-table-header th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.modern-table-header th:hover::after {
    width: 100%;
}

/* Order Rows */
.order-row {
    border-bottom: 1px solid rgba(243, 115, 33, 0.05);
    transition: var(--transition-fast);
}

.order-row:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, #ffffff 100%);
}

.order-row td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    white-space: nowrap;
}

.order-id-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
}

/* Customer Info */
.customer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.customer-name {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.customer-meta {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-style: italic;
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
}

.email-text {
    font-size: 0.875rem;
    color: var(--gray-700);
    word-break: break-word;
}

/* Phone Info */
.phone-info {
    display: flex;
    align-items: center;
}

.phone-text {
    font-size: 0.875rem;
    color: var(--gray-700);
    font-family: monospace;
}

/* Price Info */
.price-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.price-amount {
    font-weight: 700;
    color: var(--primary);
    font-size: 0.875rem;
}

/* Date Info */
.date-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
}

.date-text {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.85rem;
}

.time-text {
    font-size: 0.85rem;
    color: var(--gray-500);
    font-family: monospace;
}

/* Status Badges - Consistent with categories.php */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.5rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.status-badge.active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.inactive {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-badge.primary {
    background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
    color: #004085;
    border: 1px solid #b3d9ff;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Modern Action Buttons - Consistent with categories.php */
.table-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.8rem;
}

.action-btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.view-btn:hover {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.edit-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.completed-btn {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-color: #c3e6cb;
    cursor: default;
}

.completed-btn:hover {
    background: linear-gradient(135deg, #c3e6cb 0%, #b8dcc8 100%);
    color: #155724;
    border-color: #b8dcc8;
    transform: none;
    box-shadow: 0 2px 8px rgba(21, 87, 36, 0.15);
}

.cancelled-btn {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-color: #f5c6cb;
    cursor: default;
}

.cancelled-btn:hover {
    background: linear-gradient(135deg, #f5c6cb 0%, #f1b0b7 100%);
    color: #721c24;
    border-color: #f1b0b7;
    transform: none;
    box-shadow: 0 2px 8px rgba(114, 28, 36, 0.15);
}

/* Modern Dropdown - Enhanced Design */
.modern-dropdown {
    border: none;
    border-radius: 1rem;
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(243, 115, 33, 0.05);
    padding: 0.75rem;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    min-width: 220px;
    position: absolute;
    z-index: 1050;
    transform: translateY(0.5rem);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modern-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

.modern-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item-form {
    padding: 0;
    margin: 0;
}

.modern-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.75rem;
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    text-align: left;
    position: relative;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.modern-dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.1), transparent);
    transition: left 0.5s ease;
}

.modern-dropdown-item:hover::before {
    left: 100%;
}

.modern-dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-ultra-light) 0%, rgba(243, 115, 33, 0.05) 100%);
    color: var(--primary-dark);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
}

.modern-dropdown-item i {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: rgba(243, 115, 33, 0.1);
    color: var(--primary);
    font-size: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-dropdown-item:hover i {
    background: var(--primary);
    color: white;
    box-shadow: 0 0 8px rgba(243, 115, 33, 0.4);
}

.modern-dropdown-item i::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.modern-dropdown-item:hover i::after {
    left: 100%;
}

.modern-dropdown-item.danger {
    color: var(--danger);
}

.modern-dropdown-item.danger i {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.modern-dropdown-item.danger:hover {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: var(--danger);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.modern-dropdown-item.danger:hover i {
    background: var(--danger);
    color: white;
}

/* Status-specific dropdown items */
.modern-dropdown-item[data-status="pending"] i {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.modern-dropdown-item[data-status="pending"]:hover i {
    background: var(--warning);
    color: white;
}

.modern-dropdown-item[data-status="processing"] i {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.modern-dropdown-item[data-status="processing"]:hover i {
    background: var(--info);
    color: white;
}

.modern-dropdown-item[data-status="shipping"] i {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.modern-dropdown-item[data-status="shipping"]:hover i {
    background: #6366f1;
    color: white;
}

.modern-dropdown-item[data-status="completed"] i {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.modern-dropdown-item[data-status="completed"]:hover i {
    background: var(--success);
    color: white;
}

/* Dropdown positioning fix */
.btn-group.status-dropdown {
    position: relative;
}

.btn-group.status-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    left: auto;
    margin-top: 0.5rem;
    transform-origin: top right;
}

/* No Data Message */
.no-data {
    padding: 3rem 2rem;
    text-align: center;
}

.no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--gray-500);
}

.no-data-message i {
    font-size: 3rem;
    opacity: 0.5;
}

.no-data-message p {
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
}

/* Modern Pagination - Consistent with products.php */
.pagination-modern {
    gap: 0.5rem;
}

.pagination-modern .page-item .page-link {
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition);
    background: white;
    margin: 0;
    text-decoration: none;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-modern .page-item .page-link:hover {
    background: var(--primary-ultra-light);
    border-color: var(--primary-light);
    color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
}

.pagination-modern .page-item.active .page-link {
    background: var(--primary-gradient);
    border-color: var(--primary);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.pagination-modern .page-item.disabled .page-link {
    background: var(--gray-50);
    border-color: var(--gray-200);
    color: var(--gray-400);
    cursor: not-allowed;
}

.pagination-modern .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background: var(--gray-50);
    border-color: var(--gray-200);
    color: var(--gray-400);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.pagination-info .text-muted {
    font-size: 0.875rem;
}

/* Animation for container-fluid */
.container-fluid {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search functionality */
.order-row.hidden {
    display: none;
}

.no-results {
    text-align: center;
    padding: 2rem;
    color: var(--gray-500);
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .orders-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .table-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        width: 100%;
    }

    .search-box {
        width: 100%;
    }

    .filter-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-btn {
        justify-content: center;
        width: 100%;
    }

    .table-actions {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    /* Ẩn cột ID trên mobile để tiết kiệm không gian */
    .id-column {
        display: none !important;
    }

    .toggle-control {
        display: none;
    }

    /* Responsive table content */
    .customer-info,
    .contact-info,
    .phone-info,
    .price-info,
    .date-info {
        font-size: 0.75rem;
    }

    .customer-name {
        font-size: 0.8rem;
    }

    .price-amount {
        font-size: 0.8rem;
    }

    .pagination-modern {
        gap: 0.25rem;
    }

    .pagination-modern .page-item .page-link {
        padding: 0.375rem 0.5rem;
        min-width: 32px;
        font-size: 0.8rem;
    }

    /* Mobile dropdown adjustments */
    .modern-dropdown {
        min-width: 180px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .modern-dropdown-item {
        padding: 0.5rem 0.75rem;
        gap: 0.5rem;
        font-size: 0.8rem;
    }

    .modern-dropdown-item i {
        width: 14px;
        height: 14px;
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .orders-header {
        padding: 1rem 1.5rem;
    }

    .orders-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .orders-title-text h1 {
        font-size: 1.5rem;
    }

    .modern-card-header {
        padding: 1rem 1.5rem 0.75rem;
    }

    .modern-card-body {
        padding: 1.5rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}
</style>

<!-- JavaScript để xử lý tương tác -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chức năng toggle cột ID với localStorage
    const toggleIdColumn = document.getElementById('toggle-id-column');
    if (toggleIdColumn) {
        // Khôi phục trạng thái từ localStorage
        const savedState = localStorage.getItem('orders-show-id-column');
        const showIdColumn = savedState !== null ? savedState === 'true' : true; // Mặc định hiển thị

        toggleIdColumn.checked = showIdColumn;
        updateIdColumnVisibility(showIdColumn);

        toggleIdColumn.addEventListener('change', function() {
            const isChecked = this.checked;
            updateIdColumnVisibility(isChecked);
            // Lưu trạng thái vào localStorage
            localStorage.setItem('orders-show-id-column', isChecked.toString());
        });
    }

    // Hàm cập nhật hiển thị cột ID
    function updateIdColumnVisibility(show) {
        const idColumns = document.querySelectorAll('.id-column');
        const toggleText = document.querySelector('.toggle-text');

        if (show) {
            idColumns.forEach(col => col.style.display = '');
            if (toggleText) toggleText.textContent = 'Hiện ID';
        } else {
            idColumns.forEach(col => col.style.display = 'none');
            if (toggleText) toggleText.textContent = 'Ẩn ID';
        }
    }

    // Chức năng tìm kiếm đơn hàng
    const searchInput = document.getElementById('order-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            const orderRows = document.querySelectorAll('.order-row');
            let visibleCount = 0;

            orderRows.forEach(function(row) {
                const customerName = row.querySelector('.customer-name');
                const emailText = row.querySelector('.email-text');
                const phoneText = row.querySelector('.phone-text');
                const orderId = row.querySelector('.order-id-badge');

                let textContent = '';
                if (customerName) {
                    textContent += customerName.textContent.toLowerCase();
                }
                if (emailText) {
                    textContent += ' ' + emailText.textContent.toLowerCase();
                }
                if (phoneText) {
                    textContent += ' ' + phoneText.textContent.toLowerCase();
                }
                if (orderId) {
                    textContent += ' ' + orderId.textContent.toLowerCase();
                }

                if (textContent.includes(searchTerm)) {
                    row.classList.remove('hidden');
                    visibleCount++;
                } else {
                    row.classList.add('hidden');
                }
            });

            // Hiển thị thông báo nếu không tìm thấy kết quả
            let noResultsRow = document.querySelector('.no-results-row');
            if (visibleCount === 0 && searchTerm !== '') {
                if (!noResultsRow) {
                    const tbody = document.querySelector('.modern-table tbody');
                    noResultsRow = document.createElement('tr');
                    noResultsRow.className = 'no-results-row';
                    noResultsRow.innerHTML = '<td colspan="8" class="no-results">Không tìm thấy đơn hàng nào phù hợp với từ khóa "' + searchTerm + '"</td>';
                    tbody.appendChild(noResultsRow);
                }
                noResultsRow.style.display = 'table-row';
            } else if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        });
    }

    // Xử lý hiệu ứng hover cho stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-6px) scale(1.01)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Xử lý hiệu ứng cho filter buttons
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
            }
        });
        btn.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
            }
        });
        // Đánh dấu để cuộn sau khi lọc
        btn.addEventListener('click', function() {
            sessionStorage.setItem('ordersFilterClicked', 'true');
        });
    });

    // Xử lý hiệu ứng cho action buttons
    const actionBtns = document.querySelectorAll('.action-btn:not(.completed-btn):not(.cancelled-btn)');
    actionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Xử lý hiệu ứng đặc biệt cho completed và cancelled buttons
    const completedBtns = document.querySelectorAll('.completed-btn');
    completedBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    const cancelledBtns = document.querySelectorAll('.cancelled-btn');
    cancelledBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Xử lý hiệu ứng cho status badges
    const statusBadges = document.querySelectorAll('.status-badge');
    statusBadges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });

        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Xử lý hiệu ứng cho pagination links
    const pageLinks = document.querySelectorAll('.pagination-modern .page-link');
    pageLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            if (!this.closest('.page-item').classList.contains('active') &&
                !this.closest('.page-item').classList.contains('disabled')) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.closest('.page-item').classList.contains('active') &&
                !this.closest('.page-item').classList.contains('disabled')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });

    // Smooth scroll cho các liên kết anchor
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Xử lý dropdown animation và positioning
    const dropdownToggles = document.querySelectorAll('.status-dropdown .dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdown = this.nextElementSibling;
            const isOpen = dropdown.classList.contains('show');

            // Đóng tất cả dropdown khác
            document.querySelectorAll('.modern-dropdown.show').forEach(dd => {
                dd.classList.remove('show');
                // Reset positioning styles
                dd.style.top = '';
                dd.style.bottom = '';
                dd.style.left = '';
                dd.style.right = '';
                dd.style.marginTop = '';
                dd.style.marginBottom = '';
            });

            if (!isOpen) {
                // Reset positioning trước khi mở
                dropdown.style.top = '';
                dropdown.style.bottom = '';
                dropdown.style.left = '';
                dropdown.style.right = '';
                dropdown.style.marginTop = '';
                dropdown.style.marginBottom = '';

                // Mở dropdown hiện tại
                dropdown.classList.add('show');

                // Đợi một frame để dropdown được render
                requestAnimationFrame(() => {
                    const rect = dropdown.getBoundingClientRect();
                    const toggleRect = this.getBoundingClientRect();
                    const viewportHeight = window.innerHeight;
                    const viewportWidth = window.innerWidth;

                    // Mặc định hiển thị bên dưới, bên phải
                    dropdown.style.top = '100%';
                    dropdown.style.marginTop = '0.5rem';
                    dropdown.style.right = '0';
                    dropdown.style.left = 'auto';

                    // Kiểm tra lại sau khi set position
                    const newRect = dropdown.getBoundingClientRect();

                    // Nếu bị cắt phía dưới, chuyển lên trên
                    if (newRect.bottom > viewportHeight - 10) {
                        dropdown.style.top = 'auto';
                        dropdown.style.bottom = '100%';
                        dropdown.style.marginTop = '0';
                        dropdown.style.marginBottom = '0.5rem';
                    }

                    // Nếu bị cắt bên phải, chuyển sang trái
                    if (newRect.left < 10) {
                        dropdown.style.right = 'auto';
                        dropdown.style.left = '0';
                    }
                });
            }
        });
    });

    // Đóng dropdown khi click bên ngoài
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.status-dropdown')) {
            document.querySelectorAll('.modern-dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });

    // Xử lý hiệu ứng hover cho dropdown items
    const dropdownItems = document.querySelectorAll('.modern-dropdown-item');
    dropdownItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // Thêm loading state cho các form submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && submitBtn.classList.contains('modern-dropdown-item')) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Đang xử lý...</span>';
            }
        });
    });

    console.log('Orders page JavaScript loaded successfully');
});
</script>

<?php
// Thêm script quản lý đơn hàng
echo '<script src="' . BASE_URL . '/admin/assets/js/order-management.js"></script>';
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var params = new URLSearchParams(window.location.search);
    // Cuộn khi lọc status hoặc click nút lọc
    var shouldScroll = params.has('status') || sessionStorage.getItem('ordersFilterClicked') === 'true';
    if (shouldScroll) {
        sessionStorage.removeItem('ordersFilterClicked');
        var headerEl = document.querySelector('.orders-list-card .modern-card-header');
        if (headerEl) {
            // Tính toán offset
            var offset = 0;
            var topbar = document.querySelector('.topbar');
            if (topbar) offset += topbar.offsetHeight;
            var headerWrapper = document.querySelector('.orders-header');
            if (headerWrapper) offset += headerWrapper.offsetHeight;
            // Tính toán vị trí và cuộn
            var rect = headerEl.getBoundingClientRect();
            var scrollY = window.pageYOffset + rect.top - offset;
            window.scrollTo({ top: scrollY, behavior: 'smooth' });
        }
    }
});
</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
