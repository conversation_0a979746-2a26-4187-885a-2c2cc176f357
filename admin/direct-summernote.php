<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Kiểm tra Summernote Trực tiếp';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Summernote CSS -->
    <link href="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/summernote.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .editor-container {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><?php echo $page_title; ?></h1>
        
        <div class="alert alert-info">
            <p>Trang này dùng để kiểm tra trình soạn thảo Summernote có hoạt động đúng không, với cấu hình tối giản.</p>
        </div>
        
        <div class="editor-container">
            <div id="direct-summernote">Đây là nội dung mẫu để kiểm tra.</div>
        </div>
        
        <div class="debug-info">
            <h4>Thông tin debug</h4>
            <div id="debug-info">
                <p>Đang tải thông tin debug...</p>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="<?php echo BASE_URL; ?>/admin/index.php" class="btn btn-secondary">Quay lại</a>
        </div>
    </div>
    
    <!-- Summernote JS -->
    <script src="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/summernote.min.js"></script>
    
    <!-- Summernote Language (Tiếng Việt) -->
    <script src="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/lang/summernote-vi-VN.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('Direct test page loaded');
            
            // Hiển thị thông tin debug
            var debugInfo = $('#debug-info');
            debugInfo.html('');
            
            // Kiểm tra jQuery
            debugInfo.append('<p><strong>jQuery:</strong> ' + (typeof $ !== 'undefined' ? 'Đã tải (version ' + $.fn.jquery + ')' : 'Chưa tải') + '</p>');
            
            // Kiểm tra Summernote
            debugInfo.append('<p><strong>Summernote:</strong> ' + (typeof $.summernote !== 'undefined' ? 'Đã tải' : 'Chưa tải') + '</p>');
            
            // Thử khởi tạo Summernote
            try {
                $('#direct-summernote').summernote({
                    height: 300,
                    focus: true,
                    lang: 'vi-VN',
                    placeholder: 'Nhập nội dung để kiểm tra...',
                    toolbar: [
                        ['style', ['style']],
                        ['font', ['bold', 'italic', 'underline', 'clear']],
                        ['fontname', ['fontname']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['table', ['table']],
                        ['insert', ['link', 'picture', 'video']],
                        ['view', ['fullscreen', 'codeview', 'help']]
                    ],
                    callbacks: {
                        onInit: function() {
                            debugInfo.append('<p class="text-success"><strong>Kết quả:</strong> Summernote đã khởi tạo thành công!</p>');
                        }
                    }
                });
            } catch (error) {
                debugInfo.append('<p class="text-danger"><strong>Lỗi:</strong> ' + error.message + '</p>');
            }
        });
    </script>
</body>
</html>
