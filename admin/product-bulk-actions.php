<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Kiểm tra phương thức POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    set_flash_message('error', '<PERSON><PERSON>u cầu không hợp lệ.');
    redirect(BASE_URL . '/admin/products.php');
}

// Kiểm tra CSRF token (giả sử bạn đã có hàm check_csrf_token)
if (!isset($_POST['csrf_token']) || !check_csrf_token($_POST['csrf_token'])) {
    set_flash_message('error', 'CSRF token không hợp lệ hoặc đã hết hạn.');
    redirect(BASE_URL . '/admin/products.php');
}

$action = isset($_POST['action']) ? trim($_POST['action']) : null;
$product_ids = isset($_POST['product_ids']) && is_array($_POST['product_ids']) ? $_POST['product_ids'] : [];

// Lấy category_id từ URL trước đó (nếu có) để redirect lại đúng bộ lọc
$redirect_url = BASE_URL . '/admin/products.php';
$query_params = [];
if (isset($_SESSION['last_products_query'])) {
    parse_str($_SESSION['last_products_query'], $query_params);
    unset($query_params['page']); // Xóa page để về trang đầu sau khi action
}
// Thêm auto_scroll parameter để trigger auto-scroll
$query_params['auto_scroll'] = '1';
$redirect_url .= '?' . http_build_query($query_params);

if (empty($action) || empty($product_ids)) {
    set_flash_message('error', 'Vui lòng chọn hành động và sản phẩm.');
    redirect($redirect_url);
}

$success_count = 0;
$error_count = 0;
$results = [];

switch ($action) {
    case 'delete':
        foreach ($product_ids as $id) {
            $product_id = intval($id);
            $product = get_product_by_id($product_id); // Lấy thông tin sản phẩm để xóa ảnh
            if ($product) {
                $delete_result = delete_product($product_id);
                if ($delete_result['success']) {
                    if (!empty($product['image'])) {
                        $image_path = ROOT_PATH . 'uploads/products/' . $product['image'];
                        if (file_exists($image_path)) {
                            @unlink($image_path);
                        }
                    }
                    // Xóa gallery images nếu có
                    if (!empty($product['gallery'])) {
                        $gallery_images = explode(',', $product['gallery']);
                        foreach ($gallery_images as $gallery_image) {
                            $gallery_image_path = ROOT_PATH . 'uploads/products/' . trim($gallery_image);
                            if (file_exists($gallery_image_path)) {
                                @unlink($gallery_image_path);
                            }
                        }
                    }
                    $success_count++;
                } else {
                    $error_count++;
                    error_log("Lỗi khi xóa sản phẩm ID {$product_id}: " . ($delete_result['message'] ?? 'Unknown error'));
                }
            } else {
                $error_count++;
                error_log("Không tìm thấy sản phẩm ID {$product_id} để xóa.");
            }
        }
        if ($success_count > 0) {
            set_flash_message('success', "Đã xóa thành công {$success_count} sản phẩm." . ($error_count > 0 ? " Gặp lỗi với {$error_count} sản phẩm." : ''));
        } else {
            set_flash_message('error', "Không xóa được sản phẩm nào." . ($error_count > 0 ? " Gặp lỗi với {$error_count} sản phẩm." : ''));
        }
        break;

    case 'publish':
        $update_result = update_products_status($product_ids, 1);
        if ($update_result['success']) {
            set_flash_message('success', $update_result['message']);
        } else {
            set_flash_message('error', $update_result['message']);
        }
        break;

    case 'unpublish':
        $update_result = update_products_status($product_ids, 0);
        if ($update_result['success']) {
            set_flash_message('success', $update_result['message']);
        } else {
            set_flash_message('error', $update_result['message']);
        }
        break;

    default:
        set_flash_message('error', 'Hành động không hợp lệ.');
        break;
}

redirect($redirect_url);
?>