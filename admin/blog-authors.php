<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = '<PERSON>uản lý tác giả blog';

// Include header
include_once 'partials/header.php';

// Include blog functions
include_once '../includes/blog-functions.php';

// Khởi tạo biến
$errors = [];
$success = '';
$author = [
    'id' => '',
    'name' => '',
    'bio' => '',
    'position' => '',
    'experience' => '',
    'education' => '',
    'avatar' => '',
    'facebook' => '',
    'zalo' => '',
    'twitter' => '',
    'linkedin' => '',
    'instagram' => '',
    'website' => ''
];

// Xử lý chỉnh sửa tác giả
$is_edit = false;
if (isset($_GET['id'])) {
    $author_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_authors WHERE id = :id");
        $stmt->bindParam(':id', $author_id, PDO::PARAM_INT);
        $stmt->execute();

        $author_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($author_data) {
            $is_edit = true;
            $author = $author_data;
        } else {
            set_flash_message('error', 'Không tìm thấy tác giả.');
            redirect(BASE_URL . '/admin/blog-authors.php');
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        redirect(BASE_URL . '/admin/blog-authors.php');
    }
}

// Xử lý khi form được submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $author['name'] = $_POST['name'] ?? '';
    $author['bio'] = $_POST['bio'] ?? '';
    $author['position'] = $_POST['position'] ?? '';
    $author['experience'] = $_POST['experience'] ?? '';
    $author['education'] = $_POST['education'] ?? '';
    $author['facebook'] = $_POST['facebook'] ?? '';
    $author['zalo'] = $_POST['zalo'] ?? '';
    $author['twitter'] = $_POST['twitter'] ?? '';
    $author['linkedin'] = $_POST['linkedin'] ?? '';
    $author['instagram'] = $_POST['instagram'] ?? '';
    $author['website'] = $_POST['website'] ?? '';

    // Validate dữ liệu
    if (empty($author['name'])) {
        $errors[] = 'Vui lòng nhập tên tác giả.';
    }

    // Xử lý upload avatar nếu có
    if (!empty($_FILES['avatar']['name'])) {
        $upload_result = upload_image($_FILES['avatar'], 'blog/authors');

        if ($upload_result['success']) {
            $author['avatar'] = $upload_result['filename'];
        } else {
            $errors[] = 'Lỗi upload avatar: ' . $upload_result['message'];
        }
    }

    // Nếu không có lỗi, lưu vào database
    if (empty($errors)) {
        try {
            if ($is_edit) {
                // Cập nhật tác giả
                $sql = "UPDATE blog_authors SET
                        name = :name,
                        bio = :bio,
                        position = :position,
                        experience = :experience,
                        education = :education,
                        facebook = :facebook,
                        zalo = :zalo,
                        twitter = :twitter,
                        linkedin = :linkedin,
                        instagram = :instagram,
                        website = :website";

                // Chỉ cập nhật avatar nếu có upload mới
                if (!empty($author['avatar'])) {
                    $sql .= ", avatar = :avatar";
                }

                $sql .= " WHERE id = :id";

                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':id', $author['id'], PDO::PARAM_INT);
            } else {
                // Thêm tác giả mới
                $sql = "INSERT INTO blog_authors (name, bio, position, experience, education, avatar, facebook, zalo, twitter, linkedin, instagram, website)
                        VALUES (:name, :bio, :position, :experience, :education, :avatar, :facebook, :zalo, :twitter, :linkedin, :instagram, :website)";

                $stmt = $conn->prepare($sql);
            }

            if (empty($errors)) {
                $stmt->bindParam(':name', $author['name']);
                $stmt->bindParam(':bio', $author['bio']);
                $stmt->bindParam(':position', $author['position']);
                $stmt->bindParam(':experience', $author['experience']);
                $stmt->bindParam(':education', $author['education']);
                $stmt->bindParam(':facebook', $author['facebook']);
                $stmt->bindParam(':zalo', $author['zalo']);
                $stmt->bindParam(':twitter', $author['twitter']);
                $stmt->bindParam(':linkedin', $author['linkedin']);
                $stmt->bindParam(':instagram', $author['instagram']);
                $stmt->bindParam(':website', $author['website']);

                // Bind avatar nếu có
                if (!empty($author['avatar'])) {
                    $stmt->bindParam(':avatar', $author['avatar']);
                }

                $stmt->execute();

                $success = $is_edit ? 'Cập nhật thông tin tác giả thành công.' : 'Thêm tác giả mới thành công.';

                if (!$is_edit) {
                    // Reset form sau khi thêm mới
                    $author = [
                        'id' => '',
                        'name' => '',
                        'bio' => '',
                        'position' => '',
                        'experience' => '',
                        'education' => '',
                        'avatar' => '',
                        'facebook' => '',
                        'zalo' => '',
                        'twitter' => '',
                        'linkedin' => '',
                        'instagram' => '',
                        'website' => ''
                    ];
                }
            }
        } catch (PDOException $e) {
            $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
        }
    }
}

// Lấy danh sách tác giả
try {
    $stmt = $conn->prepare("
        SELECT *
        FROM blog_authors
        ORDER BY name ASC
    ");
    $stmt->execute();
    $authors = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $errors[] = 'Có lỗi xảy ra khi lấy danh sách tác giả: ' . $e->getMessage();
    $authors = [];
}
?>

<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý tác giả blog</h1>
    </div>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['flash_type']; ?>">
            <?php echo $_SESSION['flash_message']; ?>
        </div>
        <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <?php echo $is_edit ? 'Chỉnh sửa tác giả' : 'Thêm tác giả mới'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <?php if ($is_edit): ?>
                            <input type="hidden" name="id" value="<?php echo $author['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="name">Tên tác giả</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($author['name'] ?? ''); ?>" required>
                            <small class="form-text text-muted">Tên đầy đủ của tác giả.</small>
                        </div>

                        <div class="form-group">
                            <label for="bio">Tiểu sử</label>
                            <textarea class="form-control" id="bio" name="bio" rows="4"><?php echo htmlspecialchars($author['bio'] ?? ''); ?></textarea>
                            <small class="form-text text-muted">Giới thiệu ngắn gọn về tác giả.</small>
                        </div>

                        <div class="form-group">
                            <label for="position">Chức danh / Vị trí</label>
                            <input type="text" class="form-control" id="position" name="position" value="<?php echo htmlspecialchars($author['position'] ?? ''); ?>">
                            <small class="form-text text-muted">Ví dụ: Kiến trúc sư nội thất, Nhà thiết kế, v.v.</small>
                        </div>

                        <div class="form-group">
                            <label for="experience">Kinh nghiệm</label>
                            <textarea class="form-control" id="experience" name="experience" rows="3"><?php echo htmlspecialchars($author['experience'] ?? ''); ?></textarea>
                            <small class="form-text text-muted">Kinh nghiệm làm việc của tác giả.</small>
                        </div>

                        <div class="form-group">
                            <label for="education">Học vấn / Bằng cấp</label>
                            <textarea class="form-control" id="education" name="education" rows="3"><?php echo htmlspecialchars($author['education'] ?? ''); ?></textarea>
                            <small class="form-text text-muted">Thông tin về học vấn và bằng cấp của tác giả.</small>
                        </div>

                        <div class="form-group">
                            <label for="avatar">Avatar</label>
                            <?php if (!empty($author['avatar'])): ?>
                                <div class="mb-2">
                                    <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $author['avatar']; ?>" alt="Avatar" class="img-thumbnail" style="max-width: 100px;">
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control-file" id="avatar" name="avatar">
                            <small class="form-text text-muted">Để trống nếu không muốn thay đổi avatar.</small>
                        </div>

                        <h5 class="mt-4 mb-3">Mạng xã hội</h5>

                        <div class="form-group">
                            <label for="facebook">Facebook</label>
                            <input type="url" class="form-control" id="facebook" name="facebook" value="<?php echo htmlspecialchars($author['facebook'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="zalo">Zalo</label>
                            <input type="url" class="form-control" id="zalo" name="zalo" value="<?php echo htmlspecialchars($author['zalo'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="instagram">Instagram</label>
                            <input type="url" class="form-control" id="instagram" name="instagram" value="<?php echo htmlspecialchars($author['instagram'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="twitter">Twitter</label>
                            <input type="url" class="form-control" id="twitter" name="twitter" value="<?php echo htmlspecialchars($author['twitter'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="linkedin">LinkedIn</label>
                            <input type="url" class="form-control" id="linkedin" name="linkedin" value="<?php echo htmlspecialchars($author['linkedin'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="website">Website cá nhân</label>
                            <input type="url" class="form-control" id="website" name="website" value="<?php echo htmlspecialchars($author['website'] ?? ''); ?>">
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <?php echo $is_edit ? 'Cập nhật' : 'Thêm mới'; ?>
                        </button>
                        <a href="<?php echo BASE_URL; ?>/admin/blog-authors.php" class="btn btn-secondary">Hủy</a>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách tác giả</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Avatar</th>
                                    <th>Tên</th>
                                    <th>Chức danh</th>
                                    <th>Tiểu sử</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($authors)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">Không có tác giả nào.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($authors as $author_item): ?>
                                        <tr>
                                            <td><?php echo $author_item['id']; ?></td>
                                            <td>
                                                <?php if (!empty($author_item['avatar'])): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $author_item['avatar']; ?>" alt="Avatar" class="img-thumbnail" style="max-width: 50px;">
                                                <?php else: ?>
                                                    <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="Default Avatar" class="img-thumbnail" style="max-width: 50px;">
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($author_item['name']); ?></td>
                                            <td><?php echo htmlspecialchars($author_item['position'] ?? ''); ?></td>
                                            <td><?php echo !empty($author_item['bio']) ? substr(htmlspecialchars($author_item['bio']), 0, 50) . '...' : ''; ?></td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-authors.php?id=<?php echo $author_item['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i> Sửa
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-author-delete.php?id=<?php echo $author_item['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa tác giả này?');">
                                                    <i class="fas fa-trash"></i> Xóa
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /.container-fluid -->

<?php
// Include footer
include_once 'partials/footer.php';
?>
