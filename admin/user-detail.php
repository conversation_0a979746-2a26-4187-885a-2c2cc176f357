<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Chi tiết người dùng';

// Kiểm tra ID người dùng
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('error', 'ID người dùng không hợp lệ.');
    redirect(BASE_URL . '/admin/users.php');
}

$user_id = (int)$_GET['id'];
$user = get_user_by_id($user_id);

// Kiểm tra người dùng tồn tại
if (!$user) {
    set_flash_message('error', 'Người dùng không tồn tại.');
    redirect(BASE_URL . '/admin/users.php');
}

// L<PERSON>y số đơn hàng của người dùng
$order_count = count_user_orders($user_id);

// L<PERSON>y danh sách đơn hàng của người dùng
$orders = get_orders(5, 0, $user_id);

// Include header
include_once 'partials/header.php';

// Tạo CSRF token
$csrf_token = generate_csrf_token();
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Chi tiết người dùng</h1>

    <!-- Hiển thị thông báo -->
    <?php display_flash_message(); ?>

    <!-- Nút quay lại -->
    <div class="mb-4">
        <a href="<?php echo BASE_URL; ?>/admin/users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại danh sách người dùng
        </a>
    </div>

    <!-- Thông tin người dùng -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Thông tin cơ bản</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php if (!empty($user['avatar'])): ?>
                        <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                        <img src="<?php echo BASE_URL; ?>/assets/img/default-avatar.png" alt="Default Avatar" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <h5 class="font-weight-bold"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                        <p class="mb-0">
                            <?php if ($user['role'] === 'admin'): ?>
                            <span class="badge badge-primary">Admin</span>
                            <?php else: ?>
                            <span class="badge badge-secondary">Khách hàng</span>
                            <?php endif; ?>

                            <?php if (isset($user['status']) && $user['status'] === 'locked'): ?>
                            <span class="badge badge-danger">Bị khóa</span>
                            <?php else: ?>
                            <span class="badge badge-success">Hoạt động</span>
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="mb-3">
                        <p class="mb-1"><strong>Tên đăng nhập:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                        <p class="mb-1"><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                        <p class="mb-1"><strong>Số điện thoại:</strong> <?php echo !empty($user['phone']) ? htmlspecialchars($user['phone']) : 'Chưa cập nhật'; ?></p>
                        <p class="mb-1"><strong>Ngày tạo tài khoản:</strong> <?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></p>
                        <p class="mb-1"><strong>Cập nhật lần cuối:</strong> <?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></p>
                    </div>

                    <div class="mb-3">
                        <p class="mb-1"><strong>Địa chỉ:</strong></p>
                        <p class="mb-0"><?php echo !empty($user['address']) ? nl2br(htmlspecialchars($user['address'])) : 'Chưa cập nhật'; ?></p>
                    </div>

                    <div class="mt-4">
                        <?php if ($user['role'] !== 'admin'): ?>
                        <?php if (isset($user['status']) && $user['status'] === 'locked'): ?>
                        <a href="<?php echo BASE_URL; ?>/admin/users.php?action=unlock&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-success btn-block" onclick="return confirm('Bạn có chắc chắn muốn mở khóa tài khoản này?');">
                            <i class="fas fa-unlock"></i> Mở khóa tài khoản
                        </a>
                        <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/admin/users.php?action=lock&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-warning btn-block" onclick="return confirm('Bạn có chắc chắn muốn khóa tài khoản này?');">
                            <i class="fas fa-lock"></i> Khóa tài khoản
                        </a>
                        <?php endif; ?>
                        <?php endif; ?>



                        <?php if ($user['role'] !== 'admin'): ?>
                        <a href="<?php echo BASE_URL; ?>/admin/users.php?action=delete&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-danger btn-block mt-2" onclick="return confirm('CẢNH BÁO: Bạn có chắc chắn muốn xóa tài khoản này? Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu liên quan đến người dùng này.');">
                            <i class="fas fa-trash"></i> Xóa tài khoản
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Đơn hàng gần đây -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Đơn hàng gần đây</h6>
                    <?php if ($order_count > 0): ?>
                    <a href="<?php echo BASE_URL; ?>/admin/orders.php?user_id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                        Xem tất cả (<?php echo $order_count; ?>)
                    </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (count($orders) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ngày đặt</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td><?php echo $order['id']; ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                                    <td><?php echo format_currency($order['total']); ?></td>
                                    <td>
                                        <?php
                                        $status_info = get_order_status_info($order['status'], 'admin');
                                        ?>
                                        <span class="badge <?php echo $status_info['class']; ?>"><?php echo $status_info['text']; ?></span>
                                    </td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/admin/order-detail.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <p class="text-center">Người dùng chưa có đơn hàng nào.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Hoạt động gần đây (có thể bổ sung sau) -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Hoạt động gần đây</h6>
                </div>
                <div class="card-body">
                    <p class="text-center">Chức năng này sẽ được phát triển trong tương lai.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
