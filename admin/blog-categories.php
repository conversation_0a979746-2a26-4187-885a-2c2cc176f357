<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý danh mục blog';

// Include file header
include_once 'partials/header.php';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Khởi tạo biến
$errors = [];
$success = '';
$category = [
    'id' => '',
    'name' => '',
    'slug' => '',
    'description' => '',
    'parent_id' => null,
    'status' => 1
];

// Kiểm tra xem đang thêm mới hay cập nhật
$is_edit = isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id']);

// Nếu là cập nhật, lấy thông tin danh mục
if ($is_edit) {
    $category_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_categories WHERE id = :id");
        $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
        $stmt->execute();

        $category_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($category_data) {
            $category = array_merge($category, $category_data);
        } else {
            set_flash_message('error', 'Không tìm thấy danh mục.');
            redirect(BASE_URL . '/admin/blog-categories.php');
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        redirect(BASE_URL . '/admin/blog-categories.php');
    }
}

// Xử lý xóa danh mục
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $category_id = (int)$_GET['id'];

    try {
        // Kiểm tra xem danh mục có bài viết không
        $stmt = $conn->prepare("SELECT COUNT(*) FROM blog_post_categories WHERE category_id = :category_id");
        $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
        $stmt->execute();
        $post_count = $stmt->fetchColumn();

        if ($post_count > 0) {
            set_flash_message('error', 'Không thể xóa danh mục này vì có ' . $post_count . ' bài viết thuộc danh mục.');
        } else {
            // Kiểm tra xem danh mục có danh mục con không
            $stmt = $conn->prepare("SELECT COUNT(*) FROM blog_categories WHERE parent_id = :category_id");
            $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
            $stmt->execute();
            $child_count = $stmt->fetchColumn();

            if ($child_count > 0) {
                set_flash_message('error', 'Không thể xóa danh mục này vì có ' . $child_count . ' danh mục con.');
            } else {
                // Xóa danh mục
                $stmt = $conn->prepare("DELETE FROM blog_categories WHERE id = :category_id");
                $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
                $stmt->execute();

                set_flash_message('success', 'Xóa danh mục thành công.');
            }
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-categories.php');
}

// Xử lý khi form được submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $category['name'] = $_POST['name'] ?? '';
    $category['slug'] = $_POST['slug'] ?? '';
    $category['description'] = $_POST['description'] ?? '';
    $category['parent_id'] = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
    $category['status'] = isset($_POST['status']) ? 1 : 0;

    // Validate dữ liệu
    if (empty($category['name'])) {
        $errors[] = 'Vui lòng nhập tên danh mục.';
    }

    // Tạo slug nếu không có
    if (empty($category['slug'])) {
        $category['slug'] = create_slug($category['name']);
    } else {
        $category['slug'] = create_slug($category['slug']);
    }

    // Kiểm tra slug đã tồn tại chưa
    try {
        $stmt = $conn->prepare("SELECT id FROM blog_categories WHERE slug = :slug AND id != :id");
        $stmt->bindParam(':slug', $category['slug']);
        $stmt->bindParam(':id', $category['id'], PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $errors[] = 'Slug đã tồn tại. Vui lòng chọn slug khác.';
        }
    } catch (PDOException $e) {
        $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
    }

    // Nếu không có lỗi, lưu vào database
    if (empty($errors)) {
        try {
            if ($is_edit) {
                // Cập nhật danh mục
                $stmt = $conn->prepare("
                    UPDATE blog_categories SET
                    name = :name,
                    slug = :slug,
                    description = :description,
                    parent_id = :parent_id,
                    status = :status,
                    updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->bindParam(':id', $category['id'], PDO::PARAM_INT);
            } else {
                // Thêm danh mục mới
                $stmt = $conn->prepare("
                    INSERT INTO blog_categories (
                    name, slug, description, parent_id, status, created_at, updated_at
                    ) VALUES (
                    :name, :slug, :description, :parent_id, :status, NOW(), NOW()
                    )
                ");
            }

            $stmt->bindParam(':name', $category['name']);
            $stmt->bindParam(':slug', $category['slug']);
            $stmt->bindParam(':description', $category['description']);
            $stmt->bindParam(':parent_id', $category['parent_id'], PDO::PARAM_INT);
            $stmt->bindParam(':status', $category['status'], PDO::PARAM_INT);

            $stmt->execute();

            $success = $is_edit ? 'Cập nhật danh mục thành công.' : 'Thêm danh mục mới thành công.';

            if (!$is_edit) {
                // Reset form sau khi thêm mới
                $category = [
                    'id' => '',
                    'name' => '',
                    'slug' => '',
                    'description' => '',
                    'parent_id' => null,
                    'status' => 1
                ];
            }
        } catch (PDOException $e) {
            $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
        }
    }
}

// Lấy danh sách danh mục
try {
    $stmt = $conn->prepare("
        SELECT c.*, p.name as parent_name,
        (SELECT COUNT(*) FROM blog_post_categories WHERE category_id = c.id) as post_count
        FROM blog_categories c
        LEFT JOIN blog_categories p ON c.parent_id = p.id
        ORDER BY c.name ASC
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
    $errors[] = 'Có lỗi xảy ra khi lấy danh sách danh mục: ' . $e->getMessage();
}
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý danh mục blog</h1>

    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>
        <div class="alert alert-<?php echo $flash_message['type'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash_message['message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success" role="alert">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><?php echo $is_edit ? 'Sửa danh mục' : 'Thêm danh mục mới'; ?></h6>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="form-group">
                            <label for="name">Tên danh mục <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($category['name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="slug">Slug</label>
                            <input type="text" class="form-control" id="slug" name="slug" value="<?php echo htmlspecialchars($category['slug']); ?>">
                            <small class="form-text text-muted">Để trống để tự động tạo từ tên danh mục.</small>
                        </div>

                        <div class="form-group">
                            <label for="description">Mô tả</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($category['description']); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="parent_id">Danh mục cha</label>
                            <select class="form-control" id="parent_id" name="parent_id">
                                <option value="">-- Không có --</option>
                                <?php foreach ($categories as $cat): ?>
                                    <?php if ($is_edit && $cat['id'] == $category['id']) continue; // Không cho phép chọn chính nó làm cha ?>
                                    <option value="<?php echo $cat['id']; ?>" <?php echo $category['parent_id'] == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo $cat['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="status" name="status" <?php echo $category['status'] ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="status">Hiển thị</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $is_edit ? 'Cập nhật' : 'Thêm mới'; ?>
                            </button>

                            <?php if ($is_edit): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/blog-categories.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Hủy
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách danh mục</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Tên danh mục</th>
                                    <th>Slug</th>
                                    <th>Danh mục cha</th>
                                    <th>Số bài viết</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($categories)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">Không có danh mục nào.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($categories as $cat): ?>
                                        <tr>
                                            <td><?php echo $cat['name']; ?></td>
                                            <td><?php echo $cat['slug']; ?></td>
                                            <td><?php echo $cat['parent_name'] ?? 'Không có'; ?></td>
                                            <td><?php echo $cat['post_count']; ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $cat['status'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $cat['status'] ? 'Hiển thị' : 'Ẩn'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-categories.php?action=edit&id=<?php echo $cat['id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-categories.php?action=delete&id=<?php echo $cat['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Tự động tạo slug từ tên danh mục
    document.getElementById('name').addEventListener('blur', function() {
        const slugInput = document.getElementById('slug');
        if (slugInput.value === '') {
            const name = this.value;
            const slug = name
                .toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
        }
    });
</script>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
