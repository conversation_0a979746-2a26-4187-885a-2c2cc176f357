<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once 'includes/functions.php';

// Ki<PERSON>m tra đăng nhập
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Thư mục lưu trữ hình ảnh
$upload_dir = '../uploads/custom-editor/';

// T<PERSON><PERSON> thư mục nếu chưa tồn tại
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Xử lý tải lên hình ảnh
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['upload'])) {
    $file = $_FILES['upload'];

    // Kiểm tra lỗi
    if ($file['error'] !== UPLOAD_ERR_OK) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Upload failed: ' . getUploadErrorMessage($file['error'])]);
        exit;
    }

    // Kiểm tra loại file
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
        exit;
    }

    // Kiểm tra kích thước file (giới hạn 5MB)
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'File is too large. Maximum size is 5MB.']);
        exit;
    }

    // Tạo tên file duy nhất
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $file_name = uniqid('ckeditor_') . '.' . $file_extension;
    $file_path = $upload_dir . $file_name;

    // Di chuyển file tải lên vào thư mục đích
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Tạo URL cho hình ảnh
        $file_url = BASE_URL . '/uploads/custom-editor/' . $file_name;

        // Trả về thông tin hình ảnh cho CKEditor
        header('Content-Type: application/json');
        echo json_encode([
            'url' => $file_url,
            'uploaded' => true
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Failed to save the uploaded file.']);
    }
    exit;
}

// Hàm lấy thông báo lỗi tải lên
function getUploadErrorMessage($error_code) {
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
        case UPLOAD_ERR_FORM_SIZE:
            return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
        case UPLOAD_ERR_PARTIAL:
            return 'The uploaded file was only partially uploaded.';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded.';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing a temporary folder.';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk.';
        case UPLOAD_ERR_EXTENSION:
            return 'A PHP extension stopped the file upload.';
        default:
            return 'Unknown upload error.';
    }
}

// Nếu không phải là POST request hoặc không có file được tải lên
header('Content-Type: application/json');
echo json_encode(['error' => 'Invalid request.']);
exit;
?>