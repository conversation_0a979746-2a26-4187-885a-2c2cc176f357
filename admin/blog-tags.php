<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý tags';

// Include file header
include_once 'partials/header.php';

// Kiểm tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Include blog functions
include_once '../includes/blog-functions.php';

// Khởi tạo biến
$errors = [];
$success = '';
$tag = [
    'id' => '',
    'name' => '',
    'slug' => ''
];

// Kiểm tra xem đang thêm mới hay cập nhật
$is_edit = isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id']);

// Nếu là cập nhật, l<PERSON>y thông tin tag
if ($is_edit) {
    $tag_id = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM blog_tags WHERE id = :id");
        $stmt->bindParam(':id', $tag_id, PDO::PARAM_INT);
        $stmt->execute();

        $tag_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($tag_data) {
            $tag = array_merge($tag, $tag_data);
        } else {
            set_flash_message('error', 'Không tìm thấy tag.');
            redirect(BASE_URL . '/admin/blog-tags.php');
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        redirect(BASE_URL . '/admin/blog-tags.php');
    }
}

// Xử lý xóa tag
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $tag_id = (int)$_GET['id'];

    try {
        // Kiểm tra xem tag có bài viết không
        $stmt = $conn->prepare("SELECT COUNT(*) FROM blog_post_tags WHERE tag_id = :tag_id");
        $stmt->bindParam(':tag_id', $tag_id, PDO::PARAM_INT);
        $stmt->execute();
        $post_count = $stmt->fetchColumn();

        if ($post_count > 0) {
            set_flash_message('error', 'Không thể xóa tag này vì có ' . $post_count . ' bài viết sử dụng tag này.');
        } else {
            // Xóa tag
            $stmt = $conn->prepare("DELETE FROM blog_tags WHERE id = :tag_id");
            $stmt->bindParam(':tag_id', $tag_id, PDO::PARAM_INT);
            $stmt->execute();

            set_flash_message('success', 'Xóa tag thành công.');
        }
    } catch (PDOException $e) {
        set_flash_message('error', 'Có lỗi xảy ra: ' . $e->getMessage());
    }

    redirect(BASE_URL . '/admin/blog-tags.php');
}

// Xử lý khi form được submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $tag['name'] = $_POST['name'] ?? '';
    $tag['slug'] = $_POST['slug'] ?? '';

    // Validate dữ liệu
    if (empty($tag['name'])) {
        $errors[] = 'Vui lòng nhập tên tag.';
    }

    // Tạo slug nếu không có
    if (empty($tag['slug'])) {
        $tag['slug'] = create_slug($tag['name']);
    } else {
        $tag['slug'] = create_slug($tag['slug']);
    }

    // Kiểm tra slug đã tồn tại chưa
    try {
        $stmt = $conn->prepare("SELECT id FROM blog_tags WHERE slug = :slug AND id != :id");
        $stmt->bindParam(':slug', $tag['slug']);
        $stmt->bindParam(':id', $tag['id'], PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $errors[] = 'Slug đã tồn tại. Vui lòng chọn slug khác.';
        }
    } catch (PDOException $e) {
        $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
    }

    // Nếu không có lỗi, lưu vào database
    if (empty($errors)) {
        try {
            if ($is_edit) {
                // Cập nhật tag
                $stmt = $conn->prepare("
                    UPDATE blog_tags SET
                    name = :name,
                    slug = :slug
                    WHERE id = :id
                ");
                $stmt->bindParam(':id', $tag['id'], PDO::PARAM_INT);
            } else {
                // Thêm tag mới
                $stmt = $conn->prepare("
                    INSERT INTO blog_tags (
                    name, slug, created_at
                    ) VALUES (
                    :name, :slug, NOW()
                    )
                ");
            }

            $stmt->bindParam(':name', $tag['name']);
            $stmt->bindParam(':slug', $tag['slug']);

            $stmt->execute();

            $success = $is_edit ? 'Cập nhật tag thành công.' : 'Thêm tag mới thành công.';

            if (!$is_edit) {
                // Reset form sau khi thêm mới
                $tag = [
                    'id' => '',
                    'name' => '',
                    'slug' => ''
                ];
            }
        } catch (PDOException $e) {
            $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
        }
    }
}

// Lấy danh sách tag
try {
    $stmt = $conn->prepare("
        SELECT t.*,
        (SELECT COUNT(*) FROM blog_post_tags WHERE tag_id = t.id) as post_count
        FROM blog_tags t
        ORDER BY t.name ASC
    ");
    $stmt->execute();
    $tags = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $tags = [];
    $errors[] = 'Có lỗi xảy ra khi lấy danh sách tag: ' . $e->getMessage();
}
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý tags</h1>

    <?php $flash_message = get_flash_message(); ?>
    <?php if ($flash_message): ?>
        <div class="alert alert-<?php echo $flash_message['type'] === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash_message['message']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger" role="alert">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success" role="alert">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><?php echo $is_edit ? 'Sửa tag' : 'Thêm tag mới'; ?></h6>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="form-group">
                            <label for="name">Tên tag <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($tag['name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="slug">Slug</label>
                            <input type="text" class="form-control" id="slug" name="slug" value="<?php echo htmlspecialchars($tag['slug']); ?>">
                            <small class="form-text text-muted">Để trống để tự động tạo từ tên tag.</small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $is_edit ? 'Cập nhật' : 'Thêm mới'; ?>
                            </button>

                            <?php if ($is_edit): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/blog-tags.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Hủy
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách tags</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Tên tag</th>
                                    <th>Slug</th>
                                    <th>Số bài viết</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($tags)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Không có tag nào.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($tags as $t): ?>
                                        <tr>
                                            <td><?php echo $t['name']; ?></td>
                                            <td><?php echo $t['slug']; ?></td>
                                            <td><?php echo $t['post_count']; ?></td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-tags.php?action=edit&id=<?php echo $t['id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/blog-tags.php?action=delete&id=<?php echo $t['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc chắn muốn xóa tag này?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Tự động tạo slug từ tên tag
    document.getElementById('name').addEventListener('blur', function() {
        const slugInput = document.getElementById('slug');
        if (slugInput.value === '') {
            const name = this.value;
            const slug = name
                .toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/[\s_-]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
        }
    });
</script>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
