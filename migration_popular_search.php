<?php
/**
 * Migration Script: Add Popular Search Feature to Categories
 * Tự động thêm cột show_in_popular_search và popular_search_order vào bảng categories
 */

// Include database connection
require_once 'includes/init.php';

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='vi'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Migration - Popular Search Feature</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>";

echo "<h1>🚀 Database Migration: Popular Search Feature</h1>";
echo "<p>Script này sẽ tự động thêm tính năng 'Tìm kiếm phổ biến' vào hệ thống.</p>";

$errors = [];
$success_messages = [];
$warnings = [];

try {
    // Kiểm tra kết nối database
    if (!$conn) {
        throw new Exception("Không thể kết nối database!");
    }
    
    echo "<div class='info'>✅ Kết nối database thành công!</div>";
    
    // Bước 1: Kiểm tra xem cột đã tồn tại chưa
    echo "<h2>📋 Bước 1: Kiểm tra cấu trúc bảng hiện tại</h2>";
    
    $check_columns = $conn->prepare("SHOW COLUMNS FROM categories LIKE 'show_in_popular_search'");
    $check_columns->execute();
    $column_exists = $check_columns->rowCount() > 0;
    
    if ($column_exists) {
        echo "<div class='warning'>⚠️ Cột 'show_in_popular_search' đã tồn tại!</div>";
    } else {
        echo "<div class='info'>ℹ️ Cột 'show_in_popular_search' chưa tồn tại, sẽ được tạo.</div>";
    }
    
    $check_order_column = $conn->prepare("SHOW COLUMNS FROM categories LIKE 'popular_search_order'");
    $check_order_column->execute();
    $order_column_exists = $check_order_column->rowCount() > 0;
    
    if ($order_column_exists) {
        echo "<div class='warning'>⚠️ Cột 'popular_search_order' đã tồn tại!</div>";
    } else {
        echo "<div class='info'>ℹ️ Cột 'popular_search_order' chưa tồn tại, sẽ được tạo.</div>";
    }
    
    // Bước 2: Thêm cột mới nếu chưa tồn tại
    echo "<h2>🔧 Bước 2: Cập nhật cấu trúc bảng</h2>";
    
    if (!$column_exists) {
        $sql_add_column = "ALTER TABLE categories 
                          ADD COLUMN show_in_popular_search TINYINT(1) DEFAULT 0 
                          COMMENT 'Hiển thị trong tìm kiếm phổ biến'";
        
        $conn->exec($sql_add_column);
        echo "<div class='success'>✅ Đã thêm cột 'show_in_popular_search'</div>";
        $success_messages[] = "Thêm cột show_in_popular_search";
    }
    
    if (!$order_column_exists) {
        $sql_add_order_column = "ALTER TABLE categories 
                                ADD COLUMN popular_search_order INT DEFAULT 0 
                                COMMENT 'Thứ tự hiển thị trong tìm kiếm phổ biến'";
        
        $conn->exec($sql_add_order_column);
        echo "<div class='success'>✅ Đã thêm cột 'popular_search_order'</div>";
        $success_messages[] = "Thêm cột popular_search_order";
    }
    
    // Bước 3: Thiết lập dữ liệu mặc định
    echo "<h2>📊 Bước 3: Thiết lập dữ liệu mặc định</h2>";
    
    // Lấy danh sách danh mục hiện có
    $stmt = $conn->prepare("SELECT id, name, slug FROM categories WHERE status = 1 ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo "<div class='info'>📋 Tìm thấy " . count($categories) . " danh mục active</div>";
    
    // Thiết lập một số danh mục phổ biến mặc định
    $default_popular = [
        'sofa' => 1,
        'ban-an' => 2, 
        'giuong-ngu' => 3,
        'tu-quan-ao' => 4,
        'ban-lam-viec' => 5,
        'ghe-sofa' => 6,
        'tu-bep' => 7,
        'ban-tra' => 8
    ];
    
    $updated_count = 0;
    foreach ($categories as $category) {
        $slug = $category['slug'];
        $name_lower = strtolower($category['name']);
        
        // Kiểm tra xem có trong danh sách mặc định không
        $is_popular = false;
        $order = 0;
        
        foreach ($default_popular as $default_slug => $default_order) {
            if (strpos($slug, $default_slug) !== false || 
                strpos($name_lower, str_replace('-', ' ', $default_slug)) !== false) {
                $is_popular = true;
                $order = $default_order;
                break;
            }
        }
        
        if ($is_popular) {
            $update_stmt = $conn->prepare("
                UPDATE categories 
                SET show_in_popular_search = 1, popular_search_order = ? 
                WHERE id = ?
            ");
            $update_stmt->execute([$order, $category['id']]);
            
            echo "<div class='success'>✅ Đã thiết lập '{$category['name']}' làm tìm kiếm phổ biến (thứ tự: $order)</div>";
            $updated_count++;
        }
    }
    
    echo "<div class='info'>📈 Đã thiết lập $updated_count danh mục làm tìm kiếm phổ biến</div>";
    
    // Bước 4: Kiểm tra kết quả
    echo "<h2>🔍 Bước 4: Kiểm tra kết quả</h2>";
    
    $result_stmt = $conn->prepare("
        SELECT name, slug, show_in_popular_search, popular_search_order 
        FROM categories 
        WHERE show_in_popular_search = 1 
        ORDER BY popular_search_order ASC, name ASC
    ");
    $result_stmt->execute();
    $popular_categories = $result_stmt->fetchAll();
    
    echo "<div class='success'>🎉 Tìm thấy " . count($popular_categories) . " danh mục được thiết lập làm tìm kiếm phổ biến:</div>";
    
    if (count($popular_categories) > 0) {
        echo "<pre>";
        foreach ($popular_categories as $cat) {
            echo "- {$cat['name']} (slug: {$cat['slug']}, order: {$cat['popular_search_order']})\n";
        }
        echo "</pre>";
    }
    
    // Thông báo hoàn thành
    echo "<h2>🎊 Migration hoàn thành!</h2>";
    echo "<div class='success'>
        <strong>✅ Tất cả các bước đã được thực hiện thành công!</strong><br>
        - Đã thêm cột 'show_in_popular_search' và 'popular_search_order'<br>
        - Đã thiết lập " . count($popular_categories) . " danh mục mặc định<br>
        - Hệ thống đã sẵn sàng sử dụng tính năng mới
    </div>";
    
    echo "<div class='info'>
        <strong>📝 Bước tiếp theo:</strong><br>
        1. Truy cập <a href='admin/categories.php'>Admin Categories</a> để quản lý<br>
        2. Kiểm tra <a href='products.php'>Trang Products</a> để xem kết quả<br>
        3. Có thể xóa file migration này sau khi hoàn thành
    </div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Lỗi: " . $e->getMessage() . "</div>";
    $errors[] = $e->getMessage();
}

// Hiển thị tóm tắt
echo "<h2>📊 Tóm tắt</h2>";
if (count($success_messages) > 0) {
    echo "<div class='success'><strong>Thành công:</strong><ul>";
    foreach ($success_messages as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul></div>";
}

if (count($errors) > 0) {
    echo "<div class='error'><strong>Lỗi:</strong><ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul></div>";
}

echo "<div style='margin-top: 30px; text-align: center;'>
    <a href='admin/categories.php' class='btn'>🔧 Quản lý Categories</a>
    <a href='products.php' class='btn'>👀 Xem Products Page</a>
    <a href='index.php' class='btn'>🏠 Về trang chủ</a>
</div>";

echo "</body></html>";
?>
