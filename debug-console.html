<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console - Products AJAX Filter</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            height: 100vh;
        }
        .iframe-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            max-height: 100vh;
            overflow: hidden;
        }
        .debug-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .debug-section {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            background: #fafafa;
        }
        .debug-section h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        .debug-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 15px;
        }
        .debug-btn {
            background: #f97316;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }
        .debug-btn:hover {
            background: #ea580c;
        }
        .debug-btn.secondary {
            background: #6b7280;
        }
        .debug-btn.secondary:hover {
            background: #4b5563;
        }
        .console-output {
            flex: 1;
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 10px;
            border-radius: 4px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .status-indicator {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .status-success { background: #d1fae5; color: #065f46; }
        .status-error { background: #fee2e2; color: #991b1b; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-info { background: #dbeafe; color: #1e40af; }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="iframe-container">
            <iframe id="productsFrame" src="products.php"></iframe>
        </div>
        
        <div class="debug-panel">
            <div class="debug-header">
                <h2>🔧 AJAX Filter Debug Console</h2>
                <div id="statusIndicator" class="status-indicator status-info">Initializing...</div>
            </div>
            
            <div class="debug-section">
                <h4>🎯 Quick Tests</h4>
                <div class="debug-buttons">
                    <button class="debug-btn" onclick="testAjaxFilterExists()">Test AJAX Filter</button>
                    <button class="debug-btn" onclick="testButtonExists()">Test Button</button>
                    <button class="debug-btn" onclick="testEventListeners()">Test Events</button>
                    <button class="debug-btn" onclick="testAPIConnection()">Test API</button>
                    <button class="debug-btn" onclick="simulateButtonClick()">Simulate Click</button>
                    <button class="debug-btn" onclick="testFormData()">Test Form Data</button>
                    <button class="debug-btn" onclick="inspectButton()">Inspect Button</button>
                    <button class="debug-btn" onclick="testConsoleErrors()">Check Errors</button>
                </div>
            </div>
            
            <div class="debug-section">
                <h4>🛠️ Actions</h4>
                <div class="debug-buttons">
                    <button class="debug-btn secondary" onclick="clearConsole()">Clear Console</button>
                    <button class="debug-btn secondary" onclick="reloadFrame()">Reload Frame</button>
                    <button class="debug-btn secondary" onclick="copyConsole()">Copy Console</button>
                    <button class="debug-btn secondary" onclick="exportDebugInfo()">Export Debug</button>
                </div>
            </div>
            
            <div class="debug-section" style="flex: 1; display: flex; flex-direction: column;">
                <h4>📋 Console Output</h4>
                <div id="consoleOutput" class="console-output"></div>
            </div>
        </div>
    </div>

    <script>
        let consoleBuffer = [];
        let iframe = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00',
                success: '#44ff44'
            };
            
            const coloredMessage = `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>`;
            consoleBuffer.push(coloredMessage);
            
            const output = document.getElementById('consoleOutput');
            output.innerHTML = consoleBuffer.join('\n');
            output.scrollTop = output.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const indicator = document.getElementById('statusIndicator');
            indicator.textContent = message;
            indicator.className = `status-indicator status-${type}`;
        }
        
        function clearConsole() {
            consoleBuffer = [];
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        function reloadFrame() {
            iframe.src = iframe.src;
            log('Frame reloaded', 'info');
        }
        
        function copyConsole() {
            const text = consoleBuffer.map(line => line.replace(/<[^>]*>/g, '')).join('\n');
            navigator.clipboard.writeText(text).then(() => {
                log('Console copied to clipboard', 'success');
            });
        }
        
        function exportDebugInfo() {
            const debugInfo = {
                timestamp: new Date().toISOString(),
                console: consoleBuffer,
                userAgent: navigator.userAgent,
                url: iframe.src
            };
            
            const blob = new Blob([JSON.stringify(debugInfo, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'ajax-filter-debug.json';
            a.click();
            URL.revokeObjectURL(url);
            
            log('Debug info exported', 'success');
        }
        
        function getIframeWindow() {
            try {
                return iframe.contentWindow;
            } catch (e) {
                log('ERROR: Cannot access iframe window: ' + e.message, 'error');
                return null;
            }
        }
        
        function getIframeDocument() {
            try {
                return iframe.contentDocument || iframe.contentWindow.document;
            } catch (e) {
                log('ERROR: Cannot access iframe document: ' + e.message, 'error');
                return null;
            }
        }
        
        function testAjaxFilterExists() {
            log('=== Testing AJAX Filter Existence ===', 'info');
            
            const win = getIframeWindow();
            if (!win) return;
            
            log('✓ Iframe window accessible', 'success');
            log('window.ajaxFilter exists: ' + (!!win.ajaxFilter), win.ajaxFilter ? 'success' : 'error');
            log('window.ajaxFilterActive: ' + (!!win.ajaxFilterActive), win.ajaxFilterActive ? 'success' : 'warning');
            
            if (win.ajaxFilter) {
                log('AJAX Filter type: ' + typeof win.ajaxFilter, 'info');
                log('AJAX Filter constructor: ' + win.ajaxFilter.constructor.name, 'info');
                log('AJAX Filter methods: ' + Object.getOwnPropertyNames(Object.getPrototypeOf(win.ajaxFilter)).join(', '), 'info');
            }
            
            updateStatus('AJAX Filter test completed', win.ajaxFilter ? 'success' : 'error');
        }
        
        function testButtonExists() {
            log('=== Testing Button Existence ===', 'info');
            
            const doc = getIframeDocument();
            if (!doc) return;
            
            const button = doc.getElementById('applyFilters');
            log('Button exists: ' + !!button, button ? 'success' : 'error');
            
            if (button) {
                log('Button tagName: ' + button.tagName, 'info');
                log('Button type: ' + button.type, 'info');
                log('Button className: ' + button.className, 'info');
                log('Button disabled: ' + button.disabled, 'info');
                log('Button onclick: ' + (typeof button.onclick), 'info');
                log('Button data-ajax-filter: ' + button.getAttribute('data-ajax-filter'), 'info');
                log('Button innerHTML length: ' + button.innerHTML.length, 'info');
            }
            
            updateStatus('Button test completed', button ? 'success' : 'error');
        }
        
        function testEventListeners() {
            log('=== Testing Event Listeners ===', 'info');
            
            const doc = getIframeDocument();
            const win = getIframeWindow();
            if (!doc || !win) return;
            
            const button = doc.getElementById('applyFilters');
            if (!button) {
                log('ERROR: Button not found', 'error');
                return;
            }
            
            // Test if we can add a test event listener
            let testEventFired = false;
            const testHandler = () => { testEventFired = true; };
            
            button.addEventListener('click', testHandler, true);
            
            // Simulate click
            const event = new doc.defaultView.MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            
            button.dispatchEvent(event);
            
            log('Test event fired: ' + testEventFired, testEventFired ? 'success' : 'warning');
            
            // Remove test handler
            button.removeEventListener('click', testHandler, true);
            
            updateStatus('Event listeners test completed', 'info');
        }
        
        function testAPIConnection() {
            log('=== Testing API Connection ===', 'info');
            
            const testData = { page: 1, items_per_page: 5 };
            log('Sending test request: ' + JSON.stringify(testData), 'info');
            
            fetch('api/filter-products.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(testData)
            })
            .then(response => {
                log('Response status: ' + response.status, response.ok ? 'success' : 'error');
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    log('✓ API working correctly', 'success');
                    log('Products returned: ' + data.data.products.length, 'info');
                    updateStatus('API test successful', 'success');
                } else {
                    log('✗ API error: ' + data.error, 'error');
                    updateStatus('API test failed', 'error');
                }
            })
            .catch(error => {
                log('✗ Network error: ' + error.message, 'error');
                updateStatus('API test failed', 'error');
            });
        }
        
        function simulateButtonClick() {
            log('=== Simulating Button Click ===', 'info');
            
            const doc = getIframeDocument();
            if (!doc) return;
            
            const button = doc.getElementById('applyFilters');
            if (!button) {
                log('ERROR: Button not found', 'error');
                return;
            }
            
            log('Simulating click on button...', 'info');
            
            // Try multiple ways to trigger click
            try {
                // Method 1: Direct click
                log('Method 1: button.click()', 'info');
                button.click();
                
                // Method 2: Dispatch event
                log('Method 2: dispatchEvent', 'info');
                const event = new doc.defaultView.MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                });
                button.dispatchEvent(event);
                
                // Method 3: Call onclick if exists
                if (button.onclick) {
                    log('Method 3: button.onclick()', 'info');
                    button.onclick();
                }
                
                log('Click simulation completed', 'success');
                
            } catch (error) {
                log('ERROR during click simulation: ' + error.message, 'error');
            }
        }
        
        function testFormData() {
            log('=== Testing Form Data Collection ===', 'info');
            
            const doc = getIframeDocument();
            const win = getIframeWindow();
            if (!doc || !win) return;
            
            // Test if AJAX filter can collect form data
            if (win.ajaxFilter && typeof win.ajaxFilter.collectFilterData === 'function') {
                try {
                    const data = win.ajaxFilter.collectFilterData();
                    log('✓ Form data collected: ' + JSON.stringify(data), 'success');
                } catch (error) {
                    log('✗ Error collecting form data: ' + error.message, 'error');
                }
            } else {
                log('✗ collectFilterData method not available', 'error');
            }
            
            // Test form elements manually
            const categoryInputs = doc.querySelectorAll('input[name="category[]"]');
            const priceMin = doc.getElementById('price-min');
            const priceMax = doc.getElementById('price-max');
            const promotionInputs = doc.querySelectorAll('input[name="promotion[]"]');
            
            log('Category inputs found: ' + categoryInputs.length, 'info');
            log('Price min input exists: ' + !!priceMin, 'info');
            log('Price max input exists: ' + !!priceMax, 'info');
            log('Promotion inputs found: ' + promotionInputs.length, 'info');
            
            updateStatus('Form data test completed', 'info');
        }
        
        function inspectButton() {
            log('=== Detailed Button Inspection ===', 'info');
            
            const doc = getIframeDocument();
            if (!doc) return;
            
            const button = doc.getElementById('applyFilters');
            if (!button) {
                log('ERROR: Button not found', 'error');
                return;
            }
            
            // Get all properties
            log('Button properties:', 'info');
            for (let prop in button) {
                if (typeof button[prop] === 'function') {
                    log('  ' + prop + ': [function]', 'info');
                } else if (button[prop] !== null && button[prop] !== undefined) {
                    log('  ' + prop + ': ' + button[prop], 'info');
                }
            }
            
            // Get all attributes
            log('Button attributes:', 'info');
            for (let attr of button.attributes) {
                log('  ' + attr.name + ': ' + attr.value, 'info');
            }
            
            // Check parent elements
            log('Button parent chain:', 'info');
            let parent = button.parentElement;
            let level = 1;
            while (parent && level <= 5) {
                log('  Level ' + level + ': ' + parent.tagName + (parent.id ? '#' + parent.id : '') + (parent.className ? '.' + parent.className.split(' ').join('.') : ''), 'info');
                parent = parent.parentElement;
                level++;
            }
        }
        
        function testConsoleErrors() {
            log('=== Checking Console Errors ===', 'info');
            
            const win = getIframeWindow();
            if (!win) return;
            
            // Override console methods to capture errors
            const originalError = win.console.error;
            const originalWarn = win.console.warn;
            const originalLog = win.console.log;
            
            let errorCount = 0;
            let warnCount = 0;
            
            win.console.error = function(...args) {
                errorCount++;
                log('IFRAME ERROR: ' + args.join(' '), 'error');
                originalError.apply(this, args);
            };
            
            win.console.warn = function(...args) {
                warnCount++;
                log('IFRAME WARN: ' + args.join(' '), 'warning');
                originalWarn.apply(this, args);
            };
            
            win.console.log = function(...args) {
                log('IFRAME LOG: ' + args.join(' '), 'info');
                originalLog.apply(this, args);
            };
            
            log('Console monitoring enabled', 'success');
            log('Errors captured so far: ' + errorCount, 'info');
            log('Warnings captured so far: ' + warnCount, 'info');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            iframe = document.getElementById('productsFrame');
            
            iframe.addEventListener('load', function() {
                log('🚀 Products page loaded', 'success');
                updateStatus('Products page loaded', 'success');
                
                // Auto-run initial tests
                setTimeout(() => {
                    log('🔍 Running initial diagnostics...', 'info');
                    testAjaxFilterExists();
                    setTimeout(testButtonExists, 500);
                    setTimeout(testConsoleErrors, 1000);
                }, 1000);
            });
            
            log('🎯 Debug console initialized', 'success');
            updateStatus('Debug console ready', 'info');
        });
    </script>
</body>
</html>
