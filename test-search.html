<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Functionality</title>
    <link rel="stylesheet" href="assets/css/search-variables.css">
    <link rel="stylesheet" href="assets/css/search-improved.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-form {
            position: relative;
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        .search-input:focus {
            outline: none;
            border-color: #F37321;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-steps {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-steps h3 {
            margin: 0 0 10px 0;
            color: #7b1fa2;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Tính Năng Tìm Kiếm</h1>
        
        <div class="test-info">
            <h3>Thông tin test:</h3>
            <p>Trang này được tạo để kiểm tra vấn đề hiển thị thông báo "Không tìm thấy sản phẩm nào phù hợp" trong lúc đang loading.</p>
        </div>

        <div class="test-steps">
            <h3>Các bước test:</h3>
            <ol>
                <li>Nhập từ khóa vào ô tìm kiếm bên dưới</li>
                <li>Quan sát trạng thái loading</li>
                <li>Kiểm tra xem thông báo "Không tìm thấy..." có xuất hiện trong lúc loading không</li>
                <li>Thử với các từ khóa khác nhau: "sofa", "xyz123", "bàn"</li>
            </ol>
        </div>

        <form class="search-form">
            <input type="text" class="search-input" placeholder="Nhập từ khóa để test tìm kiếm..." autocomplete="off">
        </form>

        <div id="test-results">
            <h3>Kết quả test sẽ hiển thị ở đây</h3>
        </div>
    </div>

    <script>
        const BASE_URL = '.';
        
        // Test với search-improved.js logic
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            const searchForm = document.querySelector('.search-form');
            const testResults = document.getElementById('test-results');
            
            // Tạo suggestions container
            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions hidden';
            suggestionsContainer.style.position = 'absolute';
            suggestionsContainer.style.top = 'calc(100% + 4px)';
            suggestionsContainer.style.left = '0';
            suggestionsContainer.style.right = '0';
            suggestionsContainer.style.zIndex = '1000';
            searchForm.appendChild(suggestionsContainer);
            
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim();
                
                clearTimeout(searchTimeout);
                
                if (keyword.length < 1) {
                    suggestionsContainer.classList.add('hidden');
                    testResults.innerHTML = '<h3>Nhập từ khóa để bắt đầu test</h3>';
                    return;
                }
                
                // Adaptive debounce
                let debounceTime = keyword.length === 1 ? 600 : 
                                 keyword.length === 2 ? 400 : 200;
                
                searchTimeout = setTimeout(() => {
                    testSearch(keyword, suggestionsContainer);
                }, debounceTime);
            });
            
            function testSearch(keyword, container) {
                testResults.innerHTML = `<h3>🔍 Đang test với từ khóa: "${keyword}"</h3>`;
                
                // Hiển thị loading state
                container.innerHTML = `
                    <div class="search-loading">
                        <div class="search-loading-spinner"></div>
                        <p style="margin: 0; color: #666; font-size: 0.9rem;">Đang tìm kiếm...</p>
                    </div>
                `;
                container.classList.remove('hidden');
                
                const startTime = Date.now();
                const minLoadingTime = 300;
                
                // Gọi API
                fetch(`api/search_suggestions.php?keyword=${encodeURIComponent(keyword)}`)
                    .then(response => response.json())
                    .then(data => {
                        const elapsedTime = Date.now() - startTime;
                        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
                        
                        setTimeout(() => {
                            container.innerHTML = '';
                            
                            if (data.suggestions.length === 0) {
                                container.innerHTML = `
                                    <div class="search-no-results" style="padding: 2rem; text-align: center; color: #666;">
                                        <i class="fas fa-search" style="font-size: 1.5rem; margin-bottom: 0.5rem; color: #999;"></i>
                                        <p style="margin: 0; font-size: 0.9rem;">Không tìm thấy sản phẩm nào phù hợp với từ khóa "<strong>${keyword}</strong>"</p>
                                        <p style="margin: 0.5rem 0 0 0; font-size: 0.8rem; color: #999;">Hãy thử tìm kiếm với từ khóa khác</p>
                                    </div>
                                `;
                                testResults.innerHTML += '<p>✅ Thông báo "Không tìm thấy..." chỉ hiển thị SAU khi loading hoàn tất</p>';
                            } else {
                                // Hiển thị kết quả
                                data.suggestions.forEach(product => {
                                    const productElement = document.createElement('div');
                                    productElement.style.padding = '10px';
                                    productElement.style.borderBottom = '1px solid #eee';
                                    productElement.innerHTML = `
                                        <strong>${product.name}</strong><br>
                                        <small>${product.category} - ${product.price}</small>
                                    `;
                                    container.appendChild(productElement);
                                });
                                testResults.innerHTML += `<p>✅ Tìm thấy ${data.suggestions.length} sản phẩm</p>`;
                            }
                            
                            container.classList.remove('hidden');
                        }, remainingTime);
                    })
                    .catch(error => {
                        testResults.innerHTML += `<p>❌ Lỗi: ${error.message}</p>`;
                        container.innerHTML = '<div style="padding: 20px; color: red;">Có lỗi xảy ra khi tìm kiếm</div>';
                    });
            }
        });
    </script>
</body>
</html>
