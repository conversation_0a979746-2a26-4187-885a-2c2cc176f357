<?php
// Thiết lập tiêu đề trang
$page_title = 'Blog';
$page_description = 'C<PERSON><PERSON> nhật tin tức, xu hướng và ý tưởng thiết kế nội thất mới nhất từ Nội Thất Bàng Vũ';

// Include header
include_once 'partials/header.php';

// Include blog functions
include_once 'includes/blog-functions.php';

// Xử lý phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = 9; // Số bài viết trên mỗi trang
$offset = ($page - 1) * $items_per_page;

// Xử lý lọc theo danh mục
$category_id = null;
$category = null;
if (isset($_GET['category'])) {
    $category = get_blog_category_by_slug($_GET['category']);
    if ($category) {
        $category_id = $category['id'];
    }
}

// Xử lý lọc theo tag
$tag_id = null;
$tag = null;
if (isset($_GET['tag'])) {
    $tag = get_blog_tag_by_slug($_GET['tag']);
    if ($tag) {
        $tag_id = $tag['id'];
    }
}

// Lấy tổng số bài viết
$total_posts = count_blog_posts($category_id, $tag_id);
$total_pages = ceil($total_posts / $items_per_page);

// Đảm bảo trang hiện tại không vượt quá tổng số trang
if ($page > $total_pages && $total_pages > 0) {
    redirect(BASE_URL . '/blog.php?page=' . $total_pages);
}

// Lấy danh sách bài viết
$posts = get_blog_posts($items_per_page, $offset, $category_id, $tag_id);

// Lấy bài viết nổi bật (chỉ hiển thị ở trang đầu tiên)
$featured_post = null;
if ($page == 1 && !$category_id && !$tag_id) {
    $featured_posts = get_blog_posts(1, 0, null, null, 1);
    if (!empty($featured_posts)) {
        $featured_post = $featured_posts[0];
    }
}

// Lấy danh sách danh mục
$categories = get_blog_categories();

// Lấy danh sách tag
$tags = get_blog_tags();
?>

<!-- Link CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/blog.css">

<div class="blog-container">
    <!-- Blog Header -->
    <div class="blog-header">
        <?php if ($category): ?>
            <h1>Danh mục: <?php echo $category['name']; ?></h1>
            <?php if (!empty($category['description'])): ?>
                <p><?php echo $category['description']; ?></p>
            <?php endif; ?>
        <?php elseif ($tag): ?>
            <h1>Tag: <?php echo $tag['name']; ?></h1>
        <?php else: ?>
            <h1>Blog</h1>
            <p>Cập nhật tin tức, xu hướng và ý tưởng thiết kế nội thất mới nhất từ Nội Thất Bàng Vũ</p>
        <?php endif; ?>
    </div>

    <div class="blog-with-sidebar">
        <div class="blog-main">
            <?php if ($featured_post && $page == 1 && !$category_id && !$tag_id): ?>
                <!-- Featured Post -->
                <div class="blog-featured">
                    <div class="blog-featured-image">
                        <?php if (!empty($featured_post['featured_image'])): ?>
                            <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $featured_post['featured_image']; ?>" alt="<?php echo $featured_post['title']; ?>">
                        <?php else: ?>
                            <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg" alt="<?php echo $featured_post['title']; ?>">
                        <?php endif; ?>
                    </div>
                    <div class="blog-featured-content">
                        <span class="blog-featured-label">Bài viết nổi bật</span>
                        <h2 class="blog-featured-title">
                            <a href="<?php echo BASE_URL; ?>/blog/<?php echo $featured_post['slug']; ?>"><?php echo $featured_post['title']; ?></a>
                        </h2>
                        <div class="blog-featured-excerpt">
                            <?php echo !empty($featured_post['excerpt']) ? $featured_post['excerpt'] : substr(strip_tags($featured_post['content']), 0, 200) . '...'; ?>
                        </div>
                        <div class="blog-featured-meta">
                            <div class="blog-card-author">
                                <div class="blog-card-author-avatar">
                                    <?php if (!empty($featured_post['author']['avatar'])): ?>
                                        <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $featured_post['author']['avatar']; ?>" alt="<?php echo $featured_post['author']['name'] ?? $featured_post['author']['full_name'] ?? 'Tác giả'; ?>">
                                    <?php else: ?>
                                        <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $featured_post['author']['name'] ?? $featured_post['author']['full_name'] ?? 'Tác giả'; ?>">
                                    <?php endif; ?>
                                </div>
                                <span><?php echo $featured_post['author']['name'] ?? $featured_post['author']['full_name'] ?? 'Tác giả'; ?></span>
                            </div>
                            <div class="blog-card-date">
                                <i class="far fa-calendar-alt"></i>
                                <span><?php echo $featured_post['formatted_date']; ?></span>
                            </div>
                        </div>
                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $featured_post['slug']; ?>" class="blog-featured-button">Đọc tiếp</a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Blog Grid -->
            <div class="blog-grid">
                <?php if (empty($posts)): ?>
                    <div class="col-span-3 text-center py-8">
                        <p class="text-gray-600">Không có bài viết nào.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($posts as $post): ?>
                        <?php
                        // Bỏ qua bài viết nổi bật nếu đã hiển thị ở trên
                        if ($featured_post && $post['id'] == $featured_post['id']) continue;
                        ?>
                        <div class="blog-card">
                            <div class="blog-card-image">
                                <?php if (!empty($post['featured_image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $post['featured_image']; ?>" alt="<?php echo $post['title']; ?>">
                                <?php else: ?>
                                    <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg" alt="<?php echo $post['title']; ?>">
                                <?php endif; ?>
                            </div>
                            <div class="blog-card-content">
                                <?php if (!empty($post['categories'])): ?>
                                    <a href="<?php echo BASE_URL; ?>/blog.php?category=<?php echo $post['categories'][0]['slug']; ?>" class="blog-card-category">
                                        <?php echo $post['categories'][0]['name']; ?>
                                    </a>
                                <?php endif; ?>
                                <h3 class="blog-card-title">
                                    <a href="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>"><?php echo $post['title']; ?></a>
                                </h3>
                                <div class="blog-card-excerpt">
                                    <?php echo !empty($post['excerpt']) ? $post['excerpt'] : substr(strip_tags($post['content']), 0, 120) . '...'; ?>
                                </div>
                                <div class="blog-card-meta">
                                    <div class="blog-card-author">
                                        <div class="blog-card-author-avatar">
                                            <?php if (!empty($post['author']['avatar'])): ?>
                                                <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $post['author']['avatar']; ?>" alt="<?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?>">
                                            <?php else: ?>
                                                <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?>">
                                            <?php endif; ?>
                                        </div>
                                        <span><?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?></span>
                                    </div>
                                    <div class="blog-card-date">
                                        <i class="far fa-calendar-alt"></i>
                                        <span><?php echo $post['formatted_date']; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="blog-pagination">
                    <?php if ($page > 1): ?>
                        <div class="pagination-item">
                            <a href="<?php echo BASE_URL; ?>/blog.php?page=<?php echo $page - 1; ?><?php echo $category_id ? '&category=' . $category['slug'] : ''; ?><?php echo $tag_id ? '&tag=' . $tag['slug'] : ''; ?>" class="pagination-link pagination-prev">
                                <i class="fas fa-chevron-left"></i> Trước
                            </a>
                        </div>
                    <?php endif; ?>

                    <?php
                    // Hiển thị tối đa 5 trang
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $start_page + 4);
                    if ($end_page - $start_page < 4) {
                        $start_page = max(1, $end_page - 4);
                    }
                    ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <div class="pagination-item">
                            <a href="<?php echo BASE_URL; ?>/blog.php?page=<?php echo $i; ?><?php echo $category_id ? '&category=' . $category['slug'] : ''; ?><?php echo $tag_id ? '&tag=' . $tag['slug'] : ''; ?>" class="pagination-link <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </div>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <div class="pagination-item">
                            <a href="<?php echo BASE_URL; ?>/blog.php?page=<?php echo $page + 1; ?><?php echo $category_id ? '&category=' . $category['slug'] : ''; ?><?php echo $tag_id ? '&tag=' . $tag['slug'] : ''; ?>" class="pagination-link pagination-next">
                                Sau <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="blog-sidebar">
            <!-- Categories Widget -->
            <div class="sidebar-widget">
                <h3 class="sidebar-widget-title">Danh mục</h3>
                <ul class="sidebar-categories">
                    <?php foreach ($categories as $cat): ?>
                        <li>
                            <a href="<?php echo BASE_URL; ?>/blog.php?category=<?php echo $cat['slug']; ?>">
                                <?php echo $cat['name']; ?>
                                <span class="category-count">
                                    <?php echo count_blog_posts($cat['id']); ?>
                                </span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Tags Widget -->
            <div class="sidebar-widget">
                <h3 class="sidebar-widget-title">Tags</h3>
                <div class="sidebar-tags">
                    <?php foreach ($tags as $t): ?>
                        <a href="<?php echo BASE_URL; ?>/blog.php?tag=<?php echo $t['slug']; ?>">
                            <?php echo $t['name']; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
