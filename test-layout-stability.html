<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Stability - Mobile Filter Modal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .test-section {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            margin: 5px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Demo modal footer */
        .demo-modal-footer {
            display: flex;
            gap: 16px;
            padding: 24px;
            background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
            border-radius: 16px;
            border: 1px solid #fed7aa;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        
        /* Layout stability indicators */
        .layout-indicator {
            position: absolute;
            width: 2px;
            height: 100%;
            background: red;
            opacity: 0.5;
            pointer-events: none;
            z-index: 1000;
        }
        
        .left-indicator {
            left: 0;
        }
        
        .right-indicator {
            right: 0;
        }
        
        .stability-info {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #f59e0b;
            margin: 20px 0;
        }
        
        .stability-info h3 {
            color: #92400e;
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        
        .stability-info ul {
            margin: 0;
            padding-left: 20px;
            color: #78350f;
        }
        
        .stability-info li {
            margin-bottom: 5px;
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.2rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 15px;
            border-radius: 12px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
            border: 2px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Layout Stability Test</h1>
        <p>Kiểm tra tính ổn định layout của nút "Đặt lại bộ lọc" khi chuyển trạng thái loading</p>
        
        <div class="test-section">
            <h2>🎯 Demo Buttons với Layout Indicators</h2>
            
            <div class="demo-modal-footer" style="position: relative;">
                <!-- Layout stability indicators -->
                <div class="layout-indicator left-indicator"></div>
                <div class="layout-indicator right-indicator"></div>
                
                <button class="btn btn-secondary" id="demoResetBtn">
                    <i class="fas fa-refresh"></i>
                    <span>Đặt lại</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
                <button class="btn btn-primary" id="demoApplyBtn">
                    <i class="fas fa-search"></i>
                    <span>Áp dụng bộ lọc</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="testResetOnly()">🔄 Test Reset Only</button>
                <button class="control-btn" onclick="testApplyOnly()">🎯 Test Apply Only</button>
                <button class="control-btn" onclick="testBothSlow()">⚡ Test Both (Slow)</button>
                <button class="control-btn" onclick="resetAll()">🔧 Reset All</button>
            </div>
        </div>
        
        <div class="stability-info">
            <h3>🔍 Layout Stability Checks:</h3>
            <ul>
                <li><strong>Width Consistency:</strong> Nút không thay đổi chiều rộng</li>
                <li><strong>Height Stability:</strong> Chiều cao cố định 48px</li>
                <li><strong>Text Position:</strong> Text không nhảy khi loading</li>
                <li><strong>Spinner Space:</strong> Spinner có width/height cố định</li>
                <li><strong>Border Animation:</strong> Không ảnh hưởng layout</li>
                <li><strong>Success Feedback:</strong> Checkmark không làm thay đổi size</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test layout stability
        </div>
    </div>
    
    <script>
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function testResetOnly() {
            const btn = document.getElementById('demoResetBtn');
            updateStatus('🔄 Testing Reset Button Layout Stability...', 'testing');
            
            // Measure initial dimensions
            const initialWidth = btn.offsetWidth;
            const initialHeight = btn.offsetHeight;
            
            console.log('Initial Reset Button:', { width: initialWidth, height: initialHeight });
            
            // Show loading
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang đặt lại...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            
            // Measure loading dimensions
            setTimeout(() => {
                const loadingWidth = btn.offsetWidth;
                const loadingHeight = btn.offsetHeight;
                console.log('Loading Reset Button:', { width: loadingWidth, height: loadingHeight });
                
                if (loadingWidth !== initialWidth || loadingHeight !== initialHeight) {
                    updateStatus('⚠️ Layout shift detected in Reset button!', 'warning');
                    console.warn('Layout shift detected!', {
                        widthDiff: loadingWidth - initialWidth,
                        heightDiff: loadingHeight - initialHeight
                    });
                } else {
                    updateStatus('✅ Reset Button Layout Stable', 'ready');
                }
            }, 100);
            
            setTimeout(() => {
                // Success
                const spinner = btn.querySelector('.btn-spinner');
                spinner.innerHTML = '<i class="fas fa-check"></i>';
                
                // Measure success dimensions
                setTimeout(() => {
                    const successWidth = btn.offsetWidth;
                    const successHeight = btn.offsetHeight;
                    console.log('Success Reset Button:', { width: successWidth, height: successHeight });
                }, 100);
                
                setTimeout(() => {
                    // Reset
                    btn.disabled = false;
                    btn.classList.remove('loading');
                    btn.querySelector('span').textContent = 'Đặt lại';
                    btn.querySelector('.btn-spinner').innerHTML = '';
                    
                    // Final measurement
                    setTimeout(() => {
                        const finalWidth = btn.offsetWidth;
                        const finalHeight = btn.offsetHeight;
                        console.log('Final Reset Button:', { width: finalWidth, height: finalHeight });
                        
                        if (finalWidth === initialWidth && finalHeight === initialHeight) {
                            updateStatus('✅ Reset Button: No layout shifts detected!', 'ready');
                        } else {
                            updateStatus('⚠️ Reset Button: Layout inconsistency detected!', 'warning');
                        }
                    }, 100);
                }, 600);
            }, 1500);
        }
        
        function testApplyOnly() {
            const btn = document.getElementById('demoApplyBtn');
            updateStatus('🎯 Testing Apply Button Layout Stability...', 'testing');
            
            // Similar test for apply button
            const initialWidth = btn.offsetWidth;
            const initialHeight = btn.offsetHeight;
            
            console.log('Initial Apply Button:', { width: initialWidth, height: initialHeight });
            
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang áp dụng...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            
            setTimeout(() => {
                const loadingWidth = btn.offsetWidth;
                const loadingHeight = btn.offsetHeight;
                console.log('Loading Apply Button:', { width: loadingWidth, height: loadingHeight });
                
                if (loadingWidth !== initialWidth || loadingHeight !== initialHeight) {
                    updateStatus('⚠️ Layout shift detected in Apply button!', 'warning');
                } else {
                    updateStatus('✅ Apply Button Layout Stable', 'ready');
                }
            }, 100);
            
            setTimeout(() => {
                btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-check"></i>';
                
                setTimeout(() => {
                    btn.disabled = false;
                    btn.classList.remove('loading');
                    btn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                    btn.querySelector('.btn-spinner').innerHTML = '';
                    updateStatus('✅ Apply Button: Test complete', 'ready');
                }, 600);
            }, 1500);
        }
        
        function testBothSlow() {
            updateStatus('⚡ Testing Both Buttons (Slow Motion)...', 'testing');
            
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            // Test both buttons with slower timing
            [applyBtn, resetBtn].forEach((btn, index) => {
                btn.disabled = true;
                btn.classList.add('loading');
                btn.querySelector('span').textContent = index === 0 ? 'Đang áp dụng...' : 'Đang đặt lại...';
                btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            });
            
            setTimeout(() => {
                [applyBtn, resetBtn].forEach(btn => {
                    btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-check"></i>';
                });
                
                setTimeout(() => {
                    applyBtn.disabled = false;
                    applyBtn.classList.remove('loading');
                    applyBtn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                    applyBtn.querySelector('.btn-spinner').innerHTML = '';
                    
                    resetBtn.disabled = false;
                    resetBtn.classList.remove('loading');
                    resetBtn.querySelector('span').textContent = 'Đặt lại';
                    resetBtn.querySelector('.btn-spinner').innerHTML = '';
                    
                    updateStatus('✅ Both Buttons: Slow test complete', 'ready');
                }, 1000);
            }, 3000);
        }
        
        function resetAll() {
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            [applyBtn, resetBtn].forEach((btn, index) => {
                btn.disabled = false;
                btn.classList.remove('loading');
                btn.querySelector('span').textContent = index === 0 ? 'Áp dụng bộ lọc' : 'Đặt lại';
                btn.querySelector('.btn-spinner').innerHTML = '';
            });
            
            updateStatus('🔧 All buttons reset to original state', 'ready');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('🚀 Layout Stability Test Ready', 'ready');
            console.log('Layout Stability Test initialized');
        });
    </script>
</body>
</html>
