{"version": 3, "file": "lang/summernote-nb-NO.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,qBAAqB;QACjCC,UAAU,EAAE,qBAAqB;QACjCC,aAAa,EAAE,sBAAsB;QACrCC,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE,cAAc;QAC5BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,gBAAgB;QAChCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,kBAAkB;QACjCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,gBAAgB;QACjCC,eAAe,EAAE,kBAAkB;QACnCC,oBAAoB,EAAE,gCAAgC;QACtDC,GAAG,EAAE,WAAW;QAChBC,MAAM,EAAE,aAAa;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,OAAO;QACbtB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,yCAAyC;QAC9CU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,oBAAoB;QACjCC,UAAU,EAAE,kCAAkC;QAC9CC,WAAW,EAAE,gCAAgC;QAC7CC,MAAM,EAAE,WAAW;QACnBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,gBAAgB;QAC5BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,qBAAqB;QAC1CC,aAAa,EAAE;MACjB,CAAC;MACDzB,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE,wBAAwB;QAChC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,qBAAqB;QAC/B,WAAW,EAAE,2BAA2B;QACxC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,kBAAkB;QAClC,aAAa,EAAE,uBAAuB;QACtC,eAAe,EAAE,yBAAyB;QAC1C,cAAc,EAAE,sBAAsB;QACtC,aAAa,EAAE,qBAAqB;QACpC,qBAAqB,EAAE,oBAAoB;QAC3C,mBAAmB,EAAE,oBAAoB;QACzC,SAAS,EAAE,yBAAyB;QACpC,QAAQ,EAAE,0BAA0B;QACpC,YAAY,EAAE,qDAAqD;QACnE,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,sBAAsB,EAAE,2BAA2B;QACnD,iBAAiB,EAAE;MACrB,CAAC;MACD0B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,gBAAgB;QAC7BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-nb-NO.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'nb-NO': {\n      font: {\n        bold: 'Fet',\n        italic: 'Kursiv',\n        underline: 'Understrek',\n        clear: 'Fjern formatering',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: 'Skrifttype',\n        strikethrough: 'Gjennomstrek',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Skriftstørrelse',\n      },\n      image: {\n        image: 'B<PERSON>',\n        insert: 'Sett inn bilde',\n        resizeFull: 'Sett full størrelse',\n        resizeHalf: 'Sett halv størrelse',\n        resizeQuarter: 'Sett kvart størrelse',\n        floatLeft: 'Flyt til venstre',\n        floatRight: 'Flyt til høyre',\n        floatNone: 'Fjern flyt',\n        shapeRounded: 'Form: Rundet',\n        shapeCircle: 'Form: Sirkel',\n        shapeThumbnail: 'Form: Miniatyr',\n        shapeNone: 'Form: Ingen',\n        dragImageHere: 'Dra et bilde hit',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Velg fra filer',\n        maximumFileSize: 'Max filstørrelse',\n        maximumFileSizeError: 'Maks filstørrelse overskredet.',\n        url: 'Bilde-URL',\n        remove: 'Fjern bilde',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Videolenke',\n        insert: 'Sett inn video',\n        url: 'Video-URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Lenke',\n        insert: 'Sett inn lenke',\n        unlink: 'Fjern lenke',\n        edit: 'Rediger',\n        textToDisplay: 'Visningstekst',\n        url: 'Til hvilken URL skal denne lenken peke?',\n        openInNewWindow: 'Åpne i nytt vindu',\n      },\n      table: {\n        table: 'Tabell',\n        addRowAbove: 'Legg til rad over',\n        addRowBelow: 'Legg til rad under',\n        addColLeft: 'Legg til kolonne på venstre side',\n        addColRight: 'Legg til kolonne på høyre side',\n        delRow: 'Slett rad',\n        delCol: 'Slett kolonne',\n        delTable: 'Slett tabell',\n      },\n      hr: {\n        insert: 'Sett inn horisontal linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'Paragraf',\n        blockquote: 'Sitat',\n        pre: 'Kode',\n        h1: 'Overskrift 1',\n        h2: 'Overskrift 2',\n        h3: 'Overskrift 3',\n        h4: 'Overskrift 4',\n        h5: 'Overskrift 5',\n        h6: 'Overskrift 6',\n      },\n      lists: {\n        unordered: 'Punktliste',\n        ordered: 'Nummerert liste',\n      },\n      options: {\n        help: 'Hjelp',\n        fullscreen: 'Fullskjerm',\n        codeview: 'HTML-visning',\n      },\n      paragraph: {\n        paragraph: 'Avsnitt',\n        outdent: 'Tilbakerykk',\n        indent: 'Innrykk',\n        left: 'Venstrejustert',\n        center: 'Midtstilt',\n        right: 'Høyrejustert',\n        justify: 'Blokkjustert',\n      },\n      color: {\n        recent: 'Nylig valgt farge',\n        more: 'Flere farger',\n        background: 'Bakgrunnsfarge',\n        foreground: 'Skriftfarge',\n        transparent: 'Gjennomsiktig',\n        setTransparent: 'Sett gjennomsiktig',\n        reset: 'Nullstill',\n        resetToDefault: 'Nullstill til standard',\n      },\n      shortcut: {\n        shortcuts: 'Hurtigtaster',\n        close: 'Lukk',\n        textFormatting: 'Tekstformatering',\n        action: 'Handling',\n        paragraphFormatting: 'Avsnittsformatering',\n        documentStyle: 'Dokumentstil',\n      },\n      help: {\n        'insertParagraph': 'Sett inn avsnitt',\n        'undo': 'Angre siste handling',\n        'redo': 'Gjør om siste handling',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Angi en fet stil',\n        'italic': 'Angi en kursiv stil',\n        'underline': 'Sett en understreket stil',\n        'strikethrough': 'Sett en gjennomgående sti',\n        'removeFormat': 'Tøm formattering',\n        'justifyLeft': 'Angi venstrejustering',\n        'justifyCenter': 'Angi sentrert justering',\n        'justifyRight': 'Angi høyre justering',\n        'justifyFull': 'Angi full justering',\n        'insertUnorderedList': 'Bytt uordnet liste',\n        'insertOrderedList': 'Bytt sortert liste',\n        'outdent': 'Utrykk på valgt avsnitt',\n        'indent': 'Innrykk på valgt avsnitt',\n        'formatPara': 'Endre gjeldende blokkformat til et avsnitt (P-kode)',\n        'formatH1': 'Endre gjeldende blokkformat til H1',\n        'formatH2': 'Endre gjeldende blokkformat til H2',\n        'formatH3': 'Endre gjeldende blokkformat til H3',\n        'formatH4': 'Endre gjeldende blokkformat til H4',\n        'formatH5': 'Endre gjeldende blokkformat til H5',\n        'formatH6': 'Endre gjeldende blokkformat til H6',\n        'insertHorizontalRule': 'Sett inn horisontal deler',\n        'linkDialog.show': 'Vis koblingsdialog',\n      },\n      history: {\n        undo: 'Angre',\n        redo: 'Gjør om',\n      },\n      specialChar: {\n        specialChar: 'SPESIELLE TEGN',\n        select: 'Velg spesielle tegn',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}