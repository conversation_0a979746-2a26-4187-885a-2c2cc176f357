{"version": 3, "file": "lang/summernote-ro-RO.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,yBAAyB;QAChCC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,oBAAoB;QAC1BC,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE,yBAAyB;QACrCC,UAAU,EAAE,qBAAqB;QACjCC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE,oBAAoB;QAC/BC,UAAU,EAAE,qBAAqB;QACjCC,SAAS,EAAE,eAAe;QAC1BC,YAAY,EAAE,eAAe;QAC7BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,mBAAmB;QACnCC,SAAS,EAAE,iBAAiB;QAC5BC,aAAa,EAAE,kCAAkC;QACjDC,SAAS,EAAE,gCAAgC;QAC3CC,eAAe,EAAE,mBAAmB;QACpCC,eAAe,EAAE,0BAA0B;QAC3CC,oBAAoB,EAAE,oCAAoC;QAC1DC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,iBAAiB;QACzBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,sBAAsB;QACrCT,GAAG,EAAE,iDAAiD;QACtDU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,sBAAsB;QACnCC,WAAW,EAAE,sBAAsB;QACnCC,UAAU,EAAE,uBAAuB;QACnCC,WAAW,EAAE,wBAAwB;QACrCC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,aAAa;QAClBC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE,SAAS;QACbC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,kBAAkB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE,qBAAqB;QACjCC,UAAU,EAAE,mBAAmB;QAC/BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,qBAAqB;QACrCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,sBAAsB;QACjCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,gBAAgB;QAChCC,MAAM,EAAE,SAAS;QACjBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,oBAAoB;QACvC,MAAM,EAAE,6BAA6B;QACrC,MAAM,EAAE,6BAA6B;QACrC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,uBAAuB;QAC/B,QAAQ,EAAE,uBAAuB;QACjC,WAAW,EAAE,wBAAwB;QACrC,eAAe,EAAE,oBAAoB;QACrC,cAAc,EAAE,oBAAoB;QACpC,aAAa,EAAE,yBAAyB;QACxC,eAAe,EAAE,yBAAyB;QAC1C,cAAc,EAAE,0BAA0B;QAC1C,aAAa,EAAE,uBAAuB;QACtC,qBAAqB,EAAE,2BAA2B;QAClD,mBAAmB,EAAE,yBAAyB;QAC9C,SAAS,EAAE,oCAAoC;QAC/C,QAAQ,EAAE,kCAAkC;QAC5C,YAAY,EAAE,0CAA0C;QACxD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,UAAU,EAAE,oCAAoC;QAChD,sBAAsB,EAAE,yBAAyB;QACjD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ro-RO.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ro-RO': {\n      font: {\n        bold: 'Îngroșat',\n        italic: 'Înclinat',\n        underline: 'Subliniat',\n        clear: 'Înlătură formatare font',\n        height: 'Înălțime rând',\n        name: '<PERSON><PERSON><PERSON> de fonturi',\n        strikethrough: 'Tăiat',\n        subscript: 'Indice',\n        superscript: 'Exponent',\n        size: 'Dimensiune font',\n      },\n      image: {\n        image: 'Imagine',\n        insert: 'Inserează imagine',\n        resizeFull: 'Redimensionează complet',\n        resizeHalf: 'Redimensionează 1/2',\n        resizeQuarter: 'Redimensionează 1/4',\n        floatLeft: 'Aliniere la stânga',\n        floatRight: 'Aliniere la dreapta',\n        floatNone: 'Fară aliniere',\n        shapeRounded: 'Formă: Rotund',\n        shapeCircle: 'Formă: Cerc',\n        shapeThumbnail: 'Formă: Pictogramă',\n        shapeNone: 'Formă: Nici una',\n        dragImageHere: 'Trage o imagine sau un text aici',\n        dropImage: 'Elibereaz<PERSON> imaginea sau textul',\n        selectFromFiles: 'Alege din fişiere',\n        maximumFileSize: 'Dimensiune maximă fișier',\n        maximumFileSizeError: 'Dimensiune maximă fișier depășită.',\n        url: 'URL imagine',\n        remove: 'Șterge imagine',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link video',\n        insert: 'Inserează video',\n        url: 'URL video?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion sau Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserează link',\n        unlink: 'Înlătură link',\n        edit: 'Editează',\n        textToDisplay: 'Text ce va fi afişat',\n        url: 'La ce adresă URL trebuie să conducă acest link?',\n        openInNewWindow: 'Deschidere în fereastră nouă',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Adaugă rând deasupra',\n        addRowBelow: 'Adaugă rând dedesubt',\n        addColLeft: 'Adaugă coloană stânga',\n        addColRight: 'Adaugă coloană dreapta',\n        delRow: 'Șterge rând',\n        delCol: 'Șterge coloană',\n        delTable: 'Șterge tabel',\n      },\n      hr: {\n        insert: 'Inserează o linie orizontală',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Preformatat',\n        h1: 'Titlu 1',\n        h2: 'Titlu 2',\n        h3: 'Titlu 3',\n        h4: 'Titlu 4',\n        h5: 'Titlu 5',\n        h6: 'Titlu 6',\n      },\n      lists: {\n        unordered: 'Listă neordonată',\n        ordered: 'Listă ordonată',\n      },\n      options: {\n        help: 'Ajutor',\n        fullscreen: 'Măreşte',\n        codeview: 'Sursă',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Creşte identarea',\n        indent: 'Scade identarea',\n        left: 'Aliniere la stânga',\n        center: 'Aliniere centrală',\n        right: 'Aliniere la dreapta',\n        justify: 'Aliniere în bloc',\n      },\n      color: {\n        recent: 'Culoare recentă',\n        more: 'Mai multe  culori',\n        background: 'Culoarea fundalului',\n        foreground: 'Culoarea textului',\n        transparent: 'Transparent',\n        setTransparent: 'Setează transparent',\n        reset: 'Resetează',\n        resetToDefault: 'Revino la iniţial',\n      },\n      shortcut: {\n        shortcuts: 'Scurtături tastatură',\n        close: 'Închide',\n        textFormatting: 'Formatare text',\n        action: 'Acţiuni',\n        paragraphFormatting: 'Formatare paragraf',\n        documentStyle: 'Stil paragraf',\n        extraKeys: 'Taste extra',\n      },\n      help: {\n        'insertParagraph': 'Inserează paragraf',\n        'undo': 'Revine la starea anterioară',\n        'redo': 'Revine la starea ulterioară',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Setează stil îngroșat',\n        'italic': 'Setează stil înclinat',\n        'underline': 'Setează stil subliniat',\n        'strikethrough': 'Setează stil tăiat',\n        'removeFormat': 'Înlătură formatare',\n        'justifyLeft': 'Setează aliniere stânga',\n        'justifyCenter': 'Setează aliniere centru',\n        'justifyRight': 'Setează aliniere dreapta',\n        'justifyFull': 'Setează aliniere bloc',\n        'insertUnorderedList': 'Comutare listă neordinată',\n        'insertOrderedList': 'Comutare listă ordonată',\n        'outdent': 'Înlătură indentare paragraf curent',\n        'indent': 'Adaugă indentare paragraf curent',\n        'formatPara': 'Schimbă formatarea selecției în paragraf',\n        'formatH1': 'Schimbă formatarea selecției în H1',\n        'formatH2': 'Schimbă formatarea selecției în H2',\n        'formatH3': 'Schimbă formatarea selecției în H3',\n        'formatH4': 'Schimbă formatarea selecției în H4',\n        'formatH5': 'Schimbă formatarea selecției în H5',\n        'formatH6': 'Schimbă formatarea selecției în H6',\n        'insertHorizontalRule': 'Adaugă linie orizontală',\n        'linkDialog.show': 'Inserează link',\n      },\n      history: {\n        undo: 'Starea anterioară',\n        redo: 'Starea ulterioară',\n      },\n      specialChar: {\n        specialChar: 'CARACTERE SPECIALE',\n        select: 'Alege caractere speciale',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}