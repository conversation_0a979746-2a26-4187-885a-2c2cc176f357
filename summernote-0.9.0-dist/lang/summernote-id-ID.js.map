{"version": 3, "file": "lang/summernote-id-ID.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,gBAAgB;QACvBC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,iBAAiB;QACzBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,YAAY;QACxBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,WAAW;QACtBC,UAAU,EAAE,YAAY;QACxBC,SAAS,EAAE,gBAAgB;QAC3BC,YAAY,EAAE,mBAAmB;QACjCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,mBAAmB;QACnCC,SAAS,EAAE,mBAAmB;QAC9BC,aAAa,EAAE,0BAA0B;QACzCC,SAAS,EAAE,2BAA2B;QACtCC,eAAe,EAAE,0BAA0B;QAC3CC,eAAe,EAAE,wBAAwB;QACzCC,oBAAoB,EAAE,oCAAoC;QAC1DC,GAAG,EAAE,YAAY;QACjBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,cAAc;QACnBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,eAAe;QACpBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,yBAAyB;QACtCC,WAAW,EAAE,0BAA0B;QACvCC,UAAU,EAAE,yBAAyB;QACrCC,WAAW,EAAE,0BAA0B;QACvCC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,aAAa;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,SAAS;QAClBC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,aAAa;QACrBC,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,aAAa;QACzBC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,mBAAmB;QACnCC,KAAK,EAAE,YAAY;QACnBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,aAAa;QAC7BC,MAAM,EAAE,MAAM;QACdC,mBAAmB,EAAE,iBAAiB;QACtCC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,oBAAoB;QACvC,MAAM,EAAE,4BAA4B;QACpC,MAAM,EAAE,8BAA8B;QACtC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,yBAAyB;QACjC,QAAQ,EAAE,0BAA0B;QACpC,WAAW,EAAE,6BAA6B;QAC1C,eAAe,EAAE,iCAAiC;QAClD,cAAc,EAAE,kBAAkB;QAClC,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,sBAAsB;QACrC,qBAAqB,EAAE,6BAA6B;QACpD,mBAAmB,EAAE,0BAA0B;QAC/C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,oDAAoD;QAClE,UAAU,EAAE,qDAAqD;QACjE,UAAU,EAAE,qDAAqD;QACjE,UAAU,EAAE,qDAAqD;QACjE,UAAU,EAAE,qDAAqD;QACjE,UAAU,EAAE,qDAAqD;QACjE,UAAU,EAAE,qDAAqD;QACjE,sBAAsB,EAAE,2BAA2B;QACnD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-id-ID.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'id-ID': {\n      font: {\n        bold: '<PERSON>bal',\n        italic: 'Miring',\n        underline: 'Garis bawah',\n        clear: '<PERSON><PERSON><PERSON><PERSON> gaya',\n        height: '<PERSON><PERSON><PERSON> baris',\n        name: '<PERSON><PERSON>',\n        strikethrough: '<PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Ukuran font',\n      },\n      image: {\n        image: 'Gambar',\n        insert: 'Sisipkan gambar',\n        resizeFull: 'Ukuran penuh',\n        resizeHalf: 'Ukuran 50%',\n        resizeQuarter: 'Ukuran 25%',\n        floatLeft: 'Rata kiri',\n        floatRight: 'Rata kanan',\n        floatNone: 'Tanpa perataan',\n        shapeRounded: 'Bentuk: Membundar',\n        shapeCircle: 'Bentuk: Bundar',\n        shapeThumbnail: 'Bentuk: Thumbnail',\n        shapeNone: 'Bentuk: Tidak ada',\n        dragImageHere: 'Tarik gambar ke area ini',\n        dropImage: 'Letakkan gambar atau teks',\n        selectFromFiles: 'Pi<PERSON>h gambar dari berkas',\n        maximumFileSize: 'Ukuran maksimal berkas',\n        maximumFileSizeError: 'Ukuran maksimal berkas terlampaui.',\n        url: 'URL gambar',\n        remove: 'Hapus Gambar',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link video',\n        insert: 'Sisipkan video',\n        url: 'Tautan video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion atau Youku)',\n      },\n      link: {\n        link: 'Tautan',\n        insert: 'Tambah tautan',\n        unlink: 'Hapus tautan',\n        edit: 'Edit',\n        textToDisplay: 'Tampilan teks',\n        url: 'Tautan tujuan',\n        openInNewWindow: 'Buka di jendela baru',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Tambahkan baris ke atas',\n        addRowBelow: 'Tambahkan baris ke bawah',\n        addColLeft: 'Tambahkan kolom ke kiri',\n        addColRight: 'Tambahkan kolom ke kanan',\n        delRow: 'Hapus baris',\n        delCol: 'Hapus kolom',\n        delTable: 'Hapus tabel',\n      },\n      hr: {\n        insert: 'Masukkan garis horizontal',\n      },\n      style: {\n        style: 'Gaya',\n        p: 'p',\n        blockquote: 'Kutipan',\n        pre: 'Kode',\n        h1: 'Heading 1',\n        h2: 'Heading 2',\n        h3: 'Heading 3',\n        h4: 'Heading 4',\n        h5: 'Heading 5',\n        h6: 'Heading 6',\n      },\n      lists: {\n        unordered: 'Pencacahan',\n        ordered: 'Penomoran',\n      },\n      options: {\n        help: 'Bantuan',\n        fullscreen: 'Layar penuh',\n        codeview: 'Kode HTML',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Outdent',\n        indent: 'Indent',\n        left: 'Rata kiri',\n        center: 'Rata tengah',\n        right: 'Rata kanan',\n        justify: 'Rata kanan kiri',\n      },\n      color: {\n        recent: 'Warna sekarang',\n        more: 'Selengkapnya',\n        background: 'Warna latar',\n        foreground: 'Warna font',\n        transparent: 'Transparan',\n        setTransparent: 'Atur transparansi',\n        reset: 'Atur ulang',\n        resetToDefault: 'Kembalikan kesemula',\n      },\n      shortcut: {\n        shortcuts: 'Jalan pintas',\n        close: 'Tutup',\n        textFormatting: 'Format teks',\n        action: 'Aksi',\n        paragraphFormatting: 'Format paragraf',\n        documentStyle: 'Gaya dokumen',\n        extraKeys: 'Shortcut tambahan',\n      },\n      help: {\n        'insertParagraph': 'Tambahkan paragraf',\n        'undo': 'Urungkan perintah terakhir',\n        'redo': 'Kembalikan perintah terakhir',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Mengaktifkan gaya tebal',\n        'italic': 'Mengaktifkan gaya italic',\n        'underline': 'Mengaktifkan gaya underline',\n        'strikethrough': 'Mengaktifkan gaya strikethrough',\n        'removeFormat': 'Hapus semua gaya',\n        'justifyLeft': 'Atur rata kiri',\n        'justifyCenter': 'Atur rata tengah',\n        'justifyRight': 'Atur rata kanan',\n        'justifyFull': 'Atur rata kiri-kanan',\n        'insertUnorderedList': 'Nyalakan urutan tanpa nomor',\n        'insertOrderedList': 'Nyalakan urutan bernomor',\n        'outdent': 'Outdent di paragraf terpilih',\n        'indent': 'Indent di paragraf terpilih',\n        'formatPara': 'Ubah format gaya tulisan terpilih menjadi paragraf',\n        'formatH1': 'Ubah format gaya tulisan terpilih menjadi Heading 1',\n        'formatH2': 'Ubah format gaya tulisan terpilih menjadi Heading 2',\n        'formatH3': 'Ubah format gaya tulisan terpilih menjadi Heading 3',\n        'formatH4': 'Ubah format gaya tulisan terpilih menjadi Heading 4',\n        'formatH5': 'Ubah format gaya tulisan terpilih menjadi Heading 5',\n        'formatH6': 'Ubah format gaya tulisan terpilih menjadi Heading 6',\n        'insertHorizontalRule': 'Masukkan garis horizontal',\n        'linkDialog.show': 'Tampilkan Link Dialog',\n      },\n      history: {\n        undo: 'Kembali',\n        redo: 'Ulang',\n      },\n      specialChar: {\n        specialChar: 'KARAKTER KHUSUS',\n        select: 'Pilih karakter khusus',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}