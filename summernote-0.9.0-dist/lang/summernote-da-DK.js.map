{"version": 3, "file": "lang/summernote-da-DK.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,oBAAoB;QAChCC,UAAU,EAAE,gBAAgB;QAC5BC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,cAAc;QAC1BC,SAAS,EAAE,mBAAmB;QAC9BC,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,iBAAiB;QACjCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE,cAAc;QACzBC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,oBAAoB;QACrCC,oBAAoB,EAAE,kDAAkD;QACxEC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,aAAa;QACrBuB,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,4BAA4B;QACjCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE,oBAAoB;QACjCC,UAAU,EAAE,wBAAwB;QACpCC,WAAW,EAAE,sBAAsB;QACnCC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,oBAAoB;QAC5BC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,UAAU;QACtBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,iBAAiB;QACjCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,KAAK;QACZC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-da-DK.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'da-DK': {\n      font: {\n        bold: 'Fed',\n        italic: 'Kursiv',\n        underline: 'Understreget',\n        clear: 'Fjern formatering',\n        height: '<PERSON><PERSON><PERSON><PERSON>',\n        name: 'Skrifttype',\n        strikethrough: 'Gennemstreget',\n        subscript: 'Sænket skrift',\n        superscript: 'Hævet skrift',\n        size: 'Skriftstørrelse',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Indsæt billede',\n        resizeFull: 'Original størrelse',\n        resizeHalf: 'Halv størrelse',\n        resizeQuarter: '<PERSON><PERSON>t størrelse',\n        floatLeft: 'Venstrestillet',\n        floatRight: 'Højrestillet',\n        floatNone: 'Fjern formatering',\n        shapeRounded: 'Form: Runde kanter',\n        shapeCircle: 'Form: Cirkel',\n        shapeThumbnail: 'Form: Miniature',\n        shapeNone: 'Form: Ingen',\n        dragImageHere: 'Træk billede hertil',\n        dropImage: 'Slip billede',\n        selectFromFiles: 'Vælg billed-fil',\n        maximumFileSize: 'Maks fil størrelse',\n        maximumFileSizeError: 'Filen er større end maks tilladte fil størrelse!',\n        url: 'Billede URL',\n        remove: 'Fjern billede',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video Link',\n        insert: 'Indsæt Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Indsæt link',\n        unlink: 'Fjern link',\n        edit: 'Rediger',\n        textToDisplay: 'Visningstekst',\n        url: 'Hvor skal linket pege hen?',\n        openInNewWindow: 'Åbn i nyt vindue',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Tilføj række over',\n        addRowBelow: 'Tilføj række under',\n        addColLeft: 'Tilføj venstre kolonne',\n        addColRight: 'Tilføj højre kolonne',\n        delRow: 'Slet række',\n        delCol: 'Slet kolonne',\n        delTable: 'Slet tabel',\n      },\n      hr: {\n        insert: 'Indsæt horisontal linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Kode',\n        h1: 'Overskrift 1',\n        h2: 'Overskrift 2',\n        h3: 'Overskrift 3',\n        h4: 'Overskrift 4',\n        h5: 'Overskrift 5',\n        h6: 'Overskrift 6',\n      },\n      lists: {\n        unordered: 'Punktopstillet liste',\n        ordered: 'Nummereret liste',\n      },\n      options: {\n        help: 'Hjælp',\n        fullscreen: 'Fuld skærm',\n        codeview: 'HTML-Visning',\n      },\n      paragraph: {\n        paragraph: 'Afsnit',\n        outdent: 'Formindsk indryk',\n        indent: 'Forøg indryk',\n        left: 'Venstrestillet',\n        center: 'Centreret',\n        right: 'Højrestillet',\n        justify: 'Blokjuster',\n      },\n      color: {\n        recent: 'Nyligt valgt farve',\n        more: 'Flere farver',\n        background: 'Baggrund',\n        foreground: 'Forgrund',\n        transparent: 'Transparent',\n        setTransparent: 'Sæt transparent',\n        reset: 'Nulstil',\n        resetToDefault: 'Gendan standardindstillinger',\n      },\n      shortcut: {\n        shortcuts: 'Genveje',\n        close: 'Luk',\n        textFormatting: 'Tekstformatering',\n        action: 'Handling',\n        paragraphFormatting: 'Afsnitsformatering',\n        documentStyle: 'Dokumentstil',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Indsæt paragraf',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Vis Link Dialog',\n      },\n      history: {\n        undo: 'Fortryd',\n        redo: 'Annuller fortryd',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Vælg special karakterer',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}