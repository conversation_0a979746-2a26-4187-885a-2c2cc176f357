{"version": 3, "file": "lang/summernote-lt-LV.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,YAAY;QAC3BC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,aAAa;QACxBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,iBAAiB;QACzBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,eAAe;QAC3BC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,oBAAoB;QAC/BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE,qBAAqB;QACnCC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,gBAAgB;QAChCC,SAAS,EAAE,iBAAiB;QAC5BC,aAAa,EAAE,uBAAuB;QACtCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,kBAAkB;QACnCC,eAAe,EAAE,0BAA0B;QAC3CC,oBAAoB,EAAE,2BAA2B;QACjDC,GAAG,EAAE,YAAY;QACjBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,aAAa;QACxBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,OAAO;QACbtB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,oCAAoC;QACzCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,sBAAsB;QACnCC,WAAW,EAAE,qBAAqB;QAClCC,UAAU,EAAE,6BAA6B;QACzCC,WAAW,EAAE,2BAA2B;QACxCC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,SAAS;QACZC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,qBAAqB;QAChCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,gBAAgB;QAC5BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,wBAAwB;QACxCC,KAAK,EAAE,UAAU;QACjBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,oBAAoB;QACpCC,MAAM,EAAE,SAAS;QACjBC,mBAAmB,EAAE,uBAAuB;QAC5CC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ2B,eAAe,EAAE,oBAAoB;QACrCC,IAAI,EAAE,4BAA4B;QAClCC,IAAI,EAAE,0BAA0B;QAChCC,GAAG,EAAE,QAAQ;QACbC,KAAK,EAAE,kBAAkB;QACzB5F,IAAI,EAAE,6BAA6B;QACnCC,MAAM,EAAE,sCAAsC;QAC9CC,SAAS,EAAE,kBAAkB;QAC7BI,aAAa,EAAE,kBAAkB;QACjCuF,YAAY,EAAE,yBAAyB;QACvCC,WAAW,EAAE,2BAA2B;QACxCC,aAAa,EAAE,gBAAgB;QAC/BC,YAAY,EAAE,yBAAyB;QACvCC,WAAW,EAAE,kCAAkC;QAC/CC,mBAAmB,EAAE,8BAA8B;QACnDC,iBAAiB,EAAE,4BAA4B;QAC/ClC,OAAO,EAAE,oCAAoC;QAC7CC,MAAM,EAAE,0BAA0B;QAClCkC,UAAU,EAAE,oCAAoC;QAChDC,QAAQ,EAAE,oCAAoC;QAC9CC,QAAQ,EAAE,oCAAoC;QAC9CC,QAAQ,EAAE,oCAAoC;QAC9CC,QAAQ,EAAE,oCAAoC;QAC9CC,QAAQ,EAAE,oCAAoC;QAC9CC,QAAQ,EAAE,oCAAoC;QAC9CC,oBAAoB,EAAE,6BAA6B;QACnD,iBAAiB,EAAE;MACrB,CAAC;MACDC,OAAO,EAAE;QACPnB,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE;MACR,CAAC;MACDmB,WAAW,EAAE;QACXA,WAAW,EAAE,gBAAgB;QAC7BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-lt-LV.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'lv-LV': {\n      font: {\n        bold: 'Treknraksts',\n        italic: 'Kursīvs',\n        underline: 'Pasvītrots',\n        clear: 'Noņemt formatējumu',\n        height: '<PERSON><PERSON><PERSON><PERSON> augstums',\n        name: '<PERSON>ont<PERSON>',\n        strikethrough: 'Nosvītro<PERSON>',\n        superscript: 'Augšraksts',\n        subscript: 'Apakšraksts',\n        size: 'Fonta lielums',\n      },\n      image: {\n        image: 'Attēls',\n        insert: 'Ievietot attēlu',\n        resizeFull: 'Pilns izmērts',\n        resizeHalf: 'Samazināt 50%',\n        resizeQuarter: 'Samazināt 25%',\n        floatLeft: 'Līdzināt pa kreisi',\n        floatRight: 'Līdzināt pa labi',\n        floatNone: 'Nelīdzināt',\n        shapeRounded: 'Forma: apaļām malām',\n        shapeCircle: 'Forma: aplis',\n        shapeThumbnail: 'Forma: rām<PERSON>tis',\n        shapeNone: 'Forma: orģināla',\n        dragImageHere: 'Ievēlciet attēlu šeit',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Izvēlēties failu',\n        maximumFileSize: 'Maksimālais faila izmērs',\n        maximumFileSizeError: 'Faila izmērs pārāk liels!',\n        url: 'Attēla URL',\n        remove: 'Dzēst attēlu',\n        original: 'Oriģināls',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video saite',\n        insert: 'Ievietot Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Saite',\n        insert: 'Ievietot saiti',\n        unlink: 'Noņemt saiti',\n        edit: 'Rediģēt',\n        textToDisplay: 'Saites saturs',\n        url: 'Uz kādu URL šai saitei būtu jāved?',\n        openInNewWindow: 'Atvērt jaunā logā',\n      },\n      table: {\n        table: 'Tabula',\n        addRowAbove: 'Pievienot rindu virs',\n        addRowBelow: 'Pievienot rindu zem',\n        addColLeft: 'Pievienot kolonnu pa kreisi',\n        addColRight: 'Pievienot kolonnu pa labi',\n        delRow: 'Dzēst rindu',\n        delCol: 'Dzēst kolonnu',\n        delTable: 'Dzēst tabulu',\n      },\n      hr: {\n        insert: 'Ievietot līniju',\n      },\n      style: {\n        style: 'Stils',\n        p: 'Parasts',\n        blockquote: 'Citāts',\n        pre: 'Kods',\n        h1: 'Virsraksts h1',\n        h2: 'Virsraksts h2',\n        h3: 'Virsraksts h3',\n        h4: 'Virsraksts h4',\n        h5: 'Virsraksts h5',\n        h6: 'Virsraksts h6',\n      },\n      lists: {\n        unordered: 'Nenumurēts saraksts',\n        ordered: 'Numurēts saraksts',\n      },\n      options: {\n        help: 'Palīdzība',\n        fullscreen: 'Pa visu ekrānu',\n        codeview: 'HTML kods',\n      },\n      paragraph: {\n        paragraph: 'Paragrāfs',\n        outdent: 'Samazināt atkāpi',\n        indent: 'Palielināt atkāpi',\n        left: 'Līdzināt pa kreisi',\n        center: 'Centrēt',\n        right: 'Līdzināt pa labi',\n        justify: 'Līdzināt gar abām malām',\n      },\n      color: {\n        recent: 'Nesen izmantotās',\n        more: 'Citas krāsas',\n        background: 'Fona krāsa',\n        foreground: 'Fonta krāsa',\n        transparent: 'Caurspīdīgs',\n        setTransparent: 'Iestatīt caurspīdīgumu',\n        reset: 'Atjaunot',\n        resetToDefault: 'Atjaunot noklusējumu',\n      },\n      shortcut: {\n        shortcuts: 'Saīsnes',\n        close: 'Aizvērt',\n        textFormatting: 'Teksta formatēšana',\n        action: 'Darbība',\n        paragraphFormatting: 'Paragrāfa formatēšana',\n        documentStyle: 'Dokumenta stils',\n        extraKeys: 'Citas taustiņu kombinācijas',\n      },\n      help: {\n        insertParagraph: 'Ievietot Paragrāfu',\n        undo: 'Atcelt iepriekšējo darbību',\n        redo: 'Atkārtot atcelto darbību',\n        tab: 'Atkāpe',\n        untab: 'Samazināt atkāpi',\n        bold: 'Pārvērst tekstu treknrakstā',\n        italic: 'Pārvērst tekstu slīprakstā (kursīvā)',\n        underline: 'Pasvītrot tekstu',\n        strikethrough: 'Nosvītrot tekstu',\n        removeFormat: 'Notīrīt stilu no teksta',\n        justifyLeft: 'Līdzīnāt saturu pa kreisi',\n        justifyCenter: 'Centrēt saturu',\n        justifyRight: 'Līdzīnāt saturu pa labi',\n        justifyFull: 'Izlīdzināt saturu gar abām malām',\n        insertUnorderedList: 'Ievietot nenumurētu sarakstu',\n        insertOrderedList: 'Ievietot numurētu sarakstu',\n        outdent: 'Samazināt/noņemt atkāpi paragrāfam',\n        indent: 'Uzlikt atkāpi paragrāfam',\n        formatPara: 'Mainīt bloka tipu uz (p) Paragrāfu',\n        formatH1: 'Mainīt bloka tipu uz virsrakstu H1',\n        formatH2: 'Mainīt bloka tipu uz virsrakstu H2',\n        formatH3: 'Mainīt bloka tipu uz virsrakstu H3',\n        formatH4: 'Mainīt bloka tipu uz virsrakstu H4',\n        formatH5: 'Mainīt bloka tipu uz virsrakstu H5',\n        formatH6: 'Mainīt bloka tipu uz virsrakstu H6',\n        insertHorizontalRule: 'Ievietot horizontālu līniju',\n        'linkDialog.show': 'Parādīt saites logu',\n      },\n      history: {\n        undo: 'Atsauks (undo)',\n        redo: 'Atkārtot (redo)',\n      },\n      specialChar: {\n        specialChar: 'ĪPAŠIE SIMBOLI',\n        select: 'Izvēlieties īpašos simbolus',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}