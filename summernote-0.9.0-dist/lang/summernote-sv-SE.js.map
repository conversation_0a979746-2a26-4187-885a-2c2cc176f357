{"version": 3, "file": "lang/summernote-sv-SE.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE,UAAU;QACrBC,WAAW,EAAE,SAAS;QACtBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,aAAa;QACrBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,cAAc;QAC1BC,aAAa,EAAE,wBAAwB;QACvCC,SAAS,EAAE,iBAAiB;QAC5BC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE,iBAAiB;QAC5BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,gBAAgB;QAChCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,oBAAoB;QACrCC,oBAAoB,EAAE,sCAAsC;QAC5DC,GAAG,EAAE,gBAAgB;QACrBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,WAAW;QAClBC,SAAS,EAAE,qBAAqB;QAChCpB,MAAM,EAAE,kBAAkB;QAC1BgB,GAAG,EAAE,qBAAqB;QAC1BK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,aAAa;QACrBuB,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,cAAc;QAC7BT,GAAG,EAAE,sCAAsC;QAC3CU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,uBAAuB;QACpCC,WAAW,EAAE,qBAAqB;QAClCC,UAAU,EAAE,6BAA6B;QACzCC,WAAW,EAAE,2BAA2B;QACxCC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE,eAAe;QACxBC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,kBAAkB;QAClCC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,eAAe;QAC1BC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,iBAAiB;QACjCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,qBAAqB;QAC1CC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,0BAA0B;QAClC,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE,mBAAmB;QAC3B,QAAQ,EAAE,sBAAsB;QAChC,WAAW,EAAE,4BAA4B;QACzC,eAAe,EAAE,4BAA4B;QAC7C,cAAc,EAAE,mBAAmB;QACnC,aAAa,EAAE,2BAA2B;QAC1C,eAAe,EAAE,qBAAqB;QACtC,cAAc,EAAE,yBAAyB;QACzC,aAAa,EAAE,wBAAwB;QACvC,qBAAqB,EAAE,qBAAqB;QAC5C,mBAAmB,EAAE,yBAAyB;QAC9C,SAAS,EAAE,oCAAoC;QAC/C,QAAQ,EAAE,iCAAiC;QAC3C,YAAY,EAAE,6DAA6D;QAC3E,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,sBAAsB,EAAE,0BAA0B;QAClD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,eAAe;QAC5BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sv-SE.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'sv-SE': {\n      font: {\n        bold: 'Fet',\n        italic: 'Kursiv',\n        underline: 'Understruken',\n        clear: 'Radera formatering',\n        height: 'Radavstånd',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Genomstruken',\n        subscript: 'Nedsänkt',\n        superscript: 'Upphöjd',\n        size: 'Teckenstorlek',\n      },\n      image: {\n        image: 'Bild',\n        insert: 'Infoga bild',\n        resizeFull: 'Full storlek',\n        resizeHalf: 'Halv storlek',\n        resizeQuarter: 'En fjärdedel i storlek',\n        floatLeft: 'Vänsterjusterad',\n        floatRight: 'Högerjusterad',\n        floatNone: 'Ingen justering',\n        shapeRounded: 'Form: Avrundad',\n        shapeCircle: 'Form: Cirkel',\n        shapeThumbnail: 'Form: Miniatyr',\n        shapeNone: 'Form: Ingen',\n        dragImageHere: 'Dra en bild hit',\n        dropImage: 'Släpp bild eller text',\n        selectFromFiles: 'Välj från filer',\n        maximumFileSize: 'Maximal filstorlek',\n        maximumFileSizeError: 'Maximal filstorlek har överskridits.',\n        url: 'Länk till bild',\n        remove: 'Ta bort bild',\n        original: 'Original',\n      },\n      video: {\n        video: 'Filmklipp',\n        videoLink: 'Länk till filmklipp',\n        insert: 'Infoga filmklipp',\n        url: 'Länk till filmklipp',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)',\n      },\n      link: {\n        link: 'Länk',\n        insert: 'Infoga länk',\n        unlink: 'Ta bort länk',\n        edit: 'Redigera',\n        textToDisplay: 'Visningstext',\n        url: 'Till vilken URL ska denna länk peka?',\n        openInNewWindow: 'Öppna i ett nytt fönster',\n      },\n      table: {\n        table: 'Tabell',\n        addRowAbove: 'Lägg till rad ovanför',\n        addRowBelow: 'Lägg till rad under',\n        addColLeft: 'Lägg till kolumn åt vänster',\n        addColRight: 'Lägg till kolumn åt höger',\n        delRow: 'Radera rad',\n        delCol: 'Radera kolumn',\n        delTable: 'Radera tabell',\n      },\n      hr: {\n        insert: 'Infoga horisontell linje',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Kod',\n        h1: 'Rubrik 1',\n        h2: 'Rubrik 2',\n        h3: 'Rubrik 3',\n        h4: 'Rubrik 4',\n        h5: 'Rubrik 5',\n        h6: 'Rubrik 6',\n      },\n      lists: {\n        unordered: 'Punktlista',\n        ordered: 'Numrerad lista',\n      },\n      options: {\n        help: 'Hjälp',\n        fullscreen: 'Fullskärm',\n        codeview: 'HTML-visning',\n      },\n      paragraph: {\n        paragraph: 'Justera text',\n        outdent: 'Minska indrag',\n        indent: 'Öka indrag',\n        left: 'Vänsterjusterad',\n        center: 'Centrerad',\n        right: 'Högerjusterad',\n        justify: 'Justera text',\n      },\n      color: {\n        recent: 'Senast använda färg',\n        more: 'Fler färger',\n        background: 'Bakgrundsfärg',\n        foreground: 'Teckenfärg',\n        transparent: 'Genomskinlig',\n        setTransparent: 'Gör genomskinlig',\n        reset: 'Nollställ',\n        resetToDefault: 'Återställ till standard',\n      },\n      shortcut: {\n        shortcuts: 'Kortkommandon',\n        close: 'Stäng',\n        textFormatting: 'Textformatering',\n        action: 'Funktion',\n        paragraphFormatting: 'Avsnittsformatering',\n        documentStyle: 'Dokumentstil',\n        extraKeys: 'Extra tangenter',\n      },\n      help: {\n        'insertParagraph': 'Infoga paragraf',\n        'undo': 'Ångra senaste kommandot',\n        'redo': 'Gör om senaste kommandot',\n        'tab': 'Lägg till indrag',\n        'untab': 'Ta bort indrag',\n        'bold': 'Tillämpa fet stil',\n        'italic': 'Tillämpa kursiv stil',\n        'underline': 'Tillämpa understruken stil',\n        'strikethrough': 'Tillämpa genomstruken stil',\n        'removeFormat': 'Rensa formatering',\n        'justifyLeft': 'Tillämpa vänsterjustering',\n        'justifyCenter': 'Tillämpa centrering',\n        'justifyRight': 'Tillämpa högerjustering',\n        'justifyFull': 'Tillämpa justerad text',\n        'insertUnorderedList': 'Tillämpa punktlista',\n        'insertOrderedList': 'Tillämpa numrerad lista',\n        'outdent': 'Minska indrag för aktuell paragraf',\n        'indent': 'Öka indrag för aktuell paragraf',\n        'formatPara': 'Ändra formatet för aktuellt block till en paragraf (P-tagg)',\n        'formatH1': 'Ändra formatet för aktuellt block till rubrik 1',\n        'formatH2': 'Ändra formatet för aktuellt block till rubrik 2',\n        'formatH3': 'Ändra formatet för aktuellt block till rubrik 3',\n        'formatH4': 'Ändra formatet för aktuellt block till rubrik 4',\n        'formatH5': 'Ändra formatet för aktuellt block till rubrik 5',\n        'formatH6': 'Ändra formatet för aktuellt block till rubrik 6',\n        'insertHorizontalRule': 'Infoga horisontell linje',\n        'linkDialog.show': 'Visa dialogruta för länk',\n      },\n      history: {\n        undo: 'Ångra',\n        redo: 'Gör om',\n      },\n      specialChar: {\n        specialChar: 'SPECIALTECKEN',\n        select: 'Välj specialtecken',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}