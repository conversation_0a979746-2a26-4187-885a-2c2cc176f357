{"version": 3, "file": "lang/summernote-ru-RU.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,qBAAqB;QAC5BC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,aAAa;QAC5BC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE,gBAAgB;QAC7BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE,qBAAqB;QACjCC,UAAU,EAAE,kBAAkB;QAC9BC,aAAa,EAAE,kBAAkB;QACjCC,SAAS,EAAE,mBAAmB;QAC9BC,UAAU,EAAE,oBAAoB;QAChCC,SAAS,EAAE,2BAA2B;QACtCC,YAAY,EAAE,qBAAqB;QACnCC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,0BAA0B;QACzCC,SAAS,EAAE,qBAAqB;QAChCC,eAAe,EAAE,mBAAmB;QACpCC,eAAe,EAAE,2BAA2B;QAC5CC,oBAAoB,EAAE,oCAAoC;QAC1DC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,kBAAkB;QAC1BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,iBAAiB;QAC5BpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,WAAW;QAChBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdtB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,oBAAoB;QACnCT,GAAG,EAAE,kBAAkB;QACvBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,sBAAsB;QACnCC,WAAW,EAAE,sBAAsB;QACnCC,UAAU,EAAE,wBAAwB;QACpCC,WAAW,EAAE,yBAAyB;QACtCC,MAAM,EAAE,gBAAgB;QACxBC,MAAM,EAAE,iBAAiB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,YAAY;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE,aAAa;QACjBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,0BAA0B;QAChCC,MAAM,EAAE,qBAAqB;QAC7BC,KAAK,EAAE,2BAA2B;QAClCC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,WAAW;QACvBC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,uBAAuB;QACvCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,0BAA0B;QAC/CC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,gBAAgB;QACnC,MAAM,EAAE,4BAA4B;QACpC,MAAM,EAAE,6BAA6B;QACrC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,2BAA2B;QACnC,QAAQ,EAAE,8BAA8B;QACxC,WAAW,EAAE,iCAAiC;QAC9C,eAAe,EAAE,gCAAgC;QACjD,cAAc,EAAE,gBAAgB;QAChC,aAAa,EAAE,0BAA0B;QACzC,eAAe,EAAE,qBAAqB;QACtC,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE,yBAAyB;QACxC,qBAAqB,EAAE,yCAAyC;QAChE,mBAAmB,EAAE,wCAAwC;QAC7D,SAAS,EAAE,mCAAmC;QAC9C,QAAQ,EAAE,qCAAqC;QAC/C,YAAY,EAAE,iDAAiD;QAC/D,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,sBAAsB,EAAE,+BAA+B;QACvD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ru-RU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ru-RU': {\n      font: {\n        bold: 'Полужирный',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Подчёркнутый',\n        clear: 'Убрать стили шрифта',\n        height: 'Высота линии',\n        name: 'Шр<PERSON>ф<PERSON>',\n        strikethrough: 'Зачёркнутый',\n        subscript: 'Нижний индекс',\n        superscript: 'Верхний индекс',\n        size: 'Размер шрифта',\n      },\n      image: {\n        image: 'Картинка',\n        insert: 'Вставить картинку',\n        resizeFull: 'Восстановить размер',\n        resizeHalf: 'Уменьшить до 50%',\n        resizeQuarter: 'Уменьшить до 25%',\n        floatLeft: 'Расположить слева',\n        floatRight: 'Расположить справа',\n        floatNone: 'Расположение по-умолчанию',\n        shapeRounded: 'Форма: Закругленная',\n        shapeCircle: 'Форма: Круг',\n        shapeThumbnail: 'Форма: Миниатюра',\n        shapeNone: 'Форма: Нет',\n        dragImageHere: 'Перетащите сюда картинку',\n        dropImage: 'Перетащите картинку',\n        selectFromFiles: 'Выбрать из файлов',\n        maximumFileSize: 'Максимальный размер файла',\n        maximumFileSizeError: 'Превышен максимальный размер файла',\n        url: 'URL картинки',\n        remove: 'Удалить картинку',\n        original: 'Оригинал',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Ссылка на видео',\n        insert: 'Вставить видео',\n        url: 'URL видео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)',\n      },\n      link: {\n        link: 'Ссылка',\n        insert: 'Вставить ссылку',\n        unlink: 'Убрать ссылку',\n        edit: 'Редактировать',\n        textToDisplay: 'Отображаемый текст',\n        url: 'URL для перехода',\n        openInNewWindow: 'Открывать в новом окне',\n      },\n      table: {\n        table: 'Таблица',\n        addRowAbove: 'Добавить строку выше',\n        addRowBelow: 'Добавить строку ниже',\n        addColLeft: 'Добавить столбец слева',\n        addColRight: 'Добавить столбец справа',\n        delRow: 'Удалить строку',\n        delCol: 'Удалить столбец',\n        delTable: 'Удалить таблицу',\n      },\n      hr: {\n        insert: 'Вставить горизонтальную линию',\n      },\n      style: {\n        style: 'Стиль',\n        p: 'Нормальный',\n        blockquote: 'Цитата',\n        pre: 'Код',\n        h1: 'Заголовок 1',\n        h2: 'Заголовок 2',\n        h3: 'Заголовок 3',\n        h4: 'Заголовок 4',\n        h5: 'Заголовок 5',\n        h6: 'Заголовок 6',\n      },\n      lists: {\n        unordered: 'Маркированный список',\n        ordered: 'Нумерованный список',\n      },\n      options: {\n        help: 'Помощь',\n        fullscreen: 'На весь экран',\n        codeview: 'Исходный код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Уменьшить отступ',\n        indent: 'Увеличить отступ',\n        left: 'Выровнять по левому краю',\n        center: 'Выровнять по центру',\n        right: 'Выровнять по правому краю',\n        justify: 'Растянуть по ширине',\n      },\n      color: {\n        recent: 'Последний цвет',\n        more: 'Еще цвета',\n        background: 'Цвет фона',\n        foreground: 'Цвет шрифта',\n        transparent: 'Прозрачный',\n        setTransparent: 'Сделать прозрачным',\n        reset: 'Сброс',\n        resetToDefault: 'Восстановить умолчания',\n      },\n      shortcut: {\n        shortcuts: 'Сочетания клавиш',\n        close: 'Закрыть',\n        textFormatting: 'Форматирование текста',\n        action: 'Действие',\n        paragraphFormatting: 'Форматирование параграфа',\n        documentStyle: 'Стиль документа',\n        extraKeys: 'Дополнительные комбинации',\n      },\n      help: {\n        'insertParagraph': 'Новый параграф',\n        'undo': 'Отменить последнюю команду',\n        'redo': 'Повторить последнюю команду',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Установить стиль \"Жирный\"',\n        'italic': 'Установить стиль \"Наклонный\"',\n        'underline': 'Установить стиль \"Подчеркнутый\"',\n        'strikethrough': 'Установить стиль \"Зачеркнутый\"',\n        'removeFormat': 'Сборсить стили',\n        'justifyLeft': 'Выровнять по левому краю',\n        'justifyCenter': 'Выровнять по центру',\n        'justifyRight': 'Выровнять по правому краю',\n        'justifyFull': 'Растянуть на всю ширину',\n        'insertUnorderedList': 'Включить/отключить маркированный список',\n        'insertOrderedList': 'Включить/отключить нумерованный список',\n        'outdent': 'Убрать отступ в текущем параграфе',\n        'indent': 'Вставить отступ в текущем параграфе',\n        'formatPara': 'Форматировать текущий блок как параграф (тег P)',\n        'formatH1': 'Форматировать текущий блок как H1',\n        'formatH2': 'Форматировать текущий блок как H2',\n        'formatH3': 'Форматировать текущий блок как H3',\n        'formatH4': 'Форматировать текущий блок как H4',\n        'formatH5': 'Форматировать текущий блок как H5',\n        'formatH6': 'Форматировать текущий блок как H6',\n        'insertHorizontalRule': 'Вставить горизонтальную черту',\n        'linkDialog.show': 'Показать диалог \"Ссылка\"',\n      },\n      history: {\n        undo: 'Отменить',\n        redo: 'Повтор',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}