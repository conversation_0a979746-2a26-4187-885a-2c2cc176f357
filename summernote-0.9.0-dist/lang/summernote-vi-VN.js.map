{"version": 3, "file": "lang/summernote-vi-VN.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,YAAY;QACpBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,MAAM;QAClBC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE,cAAc;QACzBC,UAAU,EAAE,cAAc;QAC1BC,SAAS,EAAE,YAAY;QACvBC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,cAAc;QAC/BC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,gBAAgB;QAC3BpB,MAAM,EAAE,YAAY;QACpBgB,GAAG,EAAE,KAAK;QACVK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,WAAW;QACnBuB,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE,KAAK;QACXC,aAAa,EAAE,kBAAkB;QACjCT,GAAG,EAAE,KAAK;QACVU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,WAAW,EAAE,qBAAqB;QAClCC,WAAW,EAAE,qBAAqB;QAClCC,UAAU,EAAE,mBAAmB;QAC/BC,WAAW,EAAE,mBAAmB;QAChCC,MAAM,EAAE,UAAU;QAClBC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,CAAC,EAAE,YAAY;QACfC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,SAAS;QACdC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,mBAAmB;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,gBAAgB;QACzBC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,WAAW;QAClBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,SAAS;QACrBC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,gBAAgB;QAChCC,KAAK,EAAE,eAAe;QACtBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,mBAAmB;QACnCC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE,WAAW;QAChCC,aAAa,EAAE,cAAc;QAC7BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,aAAa;QAChC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,gBAAgB;QAC7BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-vi-VN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'vi-VN': {\n      font: {\n        bold: 'In Đậm',\n        italic: 'In Nghiêng',\n        underline: 'Gạch dưới',\n        clear: 'Bỏ định dạng',\n        height: '<PERSON><PERSON>u cao dòng',\n        name: '<PERSON>ông chữ',\n        strikethrough: 'Gạch ngang',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Cỡ chữ',\n      },\n      image: {\n        image: 'Hình ảnh',\n        insert: 'Chèn',\n        resizeFull: '100%',\n        resizeHalf: '50%',\n        resizeQuarter: '25%',\n        floatLeft: 'Trôi về trái',\n        floatRight: 'Trôi về phải',\n        floatNone: 'Không trôi',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Thả Ảnh ở vùng này',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Chọn từ File',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL',\n        remove: 'Xóa',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link đến Video',\n        insert: 'Chèn Video',\n        url: 'URL',\n        providers: '(Hỗ trợ YouTube, Vimeo, Vine, Instagram, DailyMotion và Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Chèn Link',\n        unlink: 'Gỡ Link',\n        edit: 'Sửa',\n        textToDisplay: 'Văn bản hiển thị',\n        url: 'URL',\n        openInNewWindow: 'Mở ở Cửa sổ mới',\n      },\n      table: {\n        table: 'Bảng',\n        addRowAbove: 'Chèn dòng phía trên',\n        addRowBelow: 'Chèn dòng phía dưới',\n        addColLeft: 'Chèn cột bên trái',\n        addColRight: 'Chèn cột bên phải',\n        delRow: 'Xóa dòng',\n        delCol: 'Xóa cột',\n        delTable: 'Xóa bảng',\n      },\n      hr: {\n        insert: 'Chèn',\n      },\n      style: {\n        style: 'Kiểu chữ',\n        p: 'Chữ thường',\n        blockquote: 'Đoạn trích',\n        pre: 'Mã Code',\n        h1: 'H1',\n        h2: 'H2',\n        h3: 'H3',\n        h4: 'H4',\n        h5: 'H5',\n        h6: 'H6',\n      },\n      lists: {\n        unordered: 'Liệt kê danh sách',\n        ordered: 'Liệt kê theo thứ tự',\n      },\n      options: {\n        help: 'Trợ giúp',\n        fullscreen: 'Toàn Màn hình',\n        codeview: 'Xem Code',\n      },\n      paragraph: {\n        paragraph: 'Canh lề',\n        outdent: 'Dịch sang trái',\n        indent: 'Dịch sang phải',\n        left: 'Canh trái',\n        center: 'Canh giữa',\n        right: 'Canh phải',\n        justify: 'Canh đều',\n      },\n      color: {\n        recent: 'Màu chữ',\n        more: 'Mở rộng',\n        background: 'Màu nền',\n        foreground: 'Màu chữ',\n        transparent: 'trong suốt',\n        setTransparent: 'Nền trong suốt',\n        reset: 'Thiết lập lại',\n        resetToDefault: 'Trở lại ban đầu',\n      },\n      shortcut: {\n        shortcuts: 'Phím tắt',\n        close: 'Đóng',\n        textFormatting: 'Định dạng Văn bản',\n        action: 'Hành động',\n        paragraphFormatting: 'Định dạng',\n        documentStyle: 'Kiểu văn bản',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Chèn đo văn',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Lùi lại',\n        redo: 'Làm lại',\n      },\n      specialChar: {\n        specialChar: 'KÝ TỰ ĐẶC BIỆT',\n        select: 'Chọn ký tự đặc biệt',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}