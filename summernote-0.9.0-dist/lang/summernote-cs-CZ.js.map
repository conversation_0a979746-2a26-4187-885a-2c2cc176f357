{"version": 3, "file": "lang/summernote-cs-CZ.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,sBAAsB;QAC7BC,MAAM,EAAE,aAAa;QACrBC,aAAa,EAAE,aAAa;QAC5BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,oBAAoB;QAChCC,aAAa,EAAE,oBAAoB;QACnCC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,kBAAkB;QAC7BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,cAAc;QAC9BC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,wBAAwB;QACvCC,SAAS,EAAE,8BAA8B;QACzCC,eAAe,EAAE,eAAe;QAChCC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,iBAAiB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,aAAa;QACxBlB,MAAM,EAAE,cAAc;QACtBc,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,OAAO;QACbpB,MAAM,EAAE,gBAAgB;QACxBqB,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,kBAAkB;QACjCT,GAAG,EAAE,kCAAkC;QACvCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,kBAAkB;QAC/BC,UAAU,EAAE,sBAAsB;QAClCC,WAAW,EAAE,uBAAuB;QACpCC,MAAM,EAAE,cAAc;QACtBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFjC,MAAM,EAAE;MACV,CAAC;MACDkC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,kBAAkB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,gBAAgB;QAC5BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,WAAW;QACpBC,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,mBAAmB;QAC3BC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,sBAAsB;QACtCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,iBAAiB;QACjCC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,mBAAmB;QAC9BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,mBAAmB;QACnCC,MAAM,EAAE,MAAM;QACdC,mBAAmB,EAAE,sBAAsB;QAC3CC,aAAa,EAAE;MACjB,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,wBAAwB;QAChC,MAAM,EAAE,0BAA0B;QAClC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,gBAAgB;QACxB,QAAQ,EAAE,kBAAkB;QAC5B,WAAW,EAAE,qBAAqB;QAClC,eAAe,EAAE,sBAAsB;QACvC,cAAc,EAAE,yBAAyB;QACzC,aAAa,EAAE,0BAA0B;QACzC,eAAe,EAAE,6BAA6B;QAC9C,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE,6BAA6B;QAC5C,qBAAqB,EAAE,4BAA4B;QACnD,mBAAmB,EAAE,0BAA0B;QAC/C,SAAS,EAAE,sCAAsC;QACjD,QAAQ,EAAE,2BAA2B;QACrC,YAAY,EAAE,yDAAyD;QACvE,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,UAAU,EAAE,iDAAiD;QAC7D,sBAAsB,EAAE,0BAA0B;QAClD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-cs-CZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'cs-CZ': {\n      font: {\n        bold: 'Tučné',\n        italic: '<PERSON><PERSON><PERSON><PERSON>',\n        underline: 'Podtržené',\n        clear: 'Odstranit styl písma',\n        height: '<PERSON><PERSON>ška řádku',\n        strikethrough: 'P<PERSON>eškrtnuté',\n        size: 'Velikost písma',\n      },\n      image: {\n        image: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        insert: 'Vložit obrázek',\n        resizeFull: 'Původní velikost',\n        resizeHalf: 'Poloviční velikost',\n        resizeQuarter: 'Čtvrteční velikost',\n        floatLeft: 'Umístit doleva',\n        floatRight: 'Umístit doprava',\n        floatNone: 'Neobtékat textem',\n        shapeRounded: 'Tvar: zaoblený',\n        shapeCircle: 'Tvar: kruh',\n        shapeThumbnail: 'Tvar: náhled',\n        shapeNone: 'Tvar: ž<PERSON>dný',\n        dragImageHere: '<PERSON><PERSON><PERSON><PERSON>hnout sem obrázek',\n        dropImage: '<PERSON><PERSON><PERSON><PERSON>hnout obrázek nebo text',\n        selectFromFiles: 'Vy<PERSON>t soubor',\n        url: 'URL obrázku',\n        remove: 'Odebrat obrázek',\n        original: 'Originál',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Odkaz videa',\n        insert: 'Vložit video',\n        url: 'URL videa?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion nebo Youku)',\n      },\n      link: {\n        link: 'Odkaz',\n        insert: 'Vytvořit odkaz',\n        unlink: 'Zrušit odkaz',\n        edit: 'Upravit',\n        textToDisplay: 'Zobrazovaný text',\n        url: 'Na jaké URL má tento odkaz vést?',\n        openInNewWindow: 'Otevřít v novém okně',\n      },\n      table: {\n        table: 'Tabulka',\n        addRowAbove: 'Přidat řádek nad',\n        addRowBelow: 'Přidat řádek pod',\n        addColLeft: 'Přidat sloupec vlevo',\n        addColRight: 'Přidat sloupec vpravo',\n        delRow: 'Smazat řádek',\n        delCol: 'Smazat sloupec',\n        delTable: 'Smazat tabulku',\n      },\n      hr: {\n        insert: 'Vložit vodorovnou čáru',\n      },\n      style: {\n        style: 'Styl',\n        p: 'Normální',\n        blockquote: 'Citace',\n        pre: 'Kód',\n        h1: 'Nadpis 1',\n        h2: 'Nadpis 2',\n        h3: 'Nadpis 3',\n        h4: 'Nadpis 4',\n        h5: 'Nadpis 5',\n        h6: 'Nadpis 6',\n      },\n      lists: {\n        unordered: 'Odrážkový seznam',\n        ordered: 'Číselný seznam',\n      },\n      options: {\n        help: 'Nápověda',\n        fullscreen: 'Celá obrazovka',\n        codeview: 'HTML kód',\n      },\n      paragraph: {\n        paragraph: 'Odstavec',\n        outdent: 'Předsadit',\n        indent: 'Odsadit',\n        left: 'Zarovnat doleva',\n        center: 'Zarovnat na střed',\n        right: 'Zarovnat doprava',\n        justify: 'Zarovnat oboustranně',\n      },\n      color: {\n        recent: 'Aktuální barva',\n        more: 'Další barvy',\n        background: 'Barva pozadí',\n        foreground: 'Barva písma',\n        transparent: 'Průhlednost',\n        setTransparent: 'Nastavit průhlednost',\n        reset: 'Obnovit',\n        resetToDefault: 'Obnovit výchozí',\n        cpSelect: 'Vybrat',\n      },\n      shortcut: {\n        shortcuts: 'Klávesové zkratky',\n        close: 'Zavřít',\n        textFormatting: 'Formátování textu',\n        action: 'Akce',\n        paragraphFormatting: 'Formátování odstavce',\n        documentStyle: 'Styl dokumentu',\n      },\n      help: {\n        'insertParagraph': 'Vložit odstavec',\n        'undo': 'Vrátit poslední příkaz',\n        'redo': 'Opakovat poslední příkaz',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Nastavit tučně',\n        'italic': 'Nastavit kurzívu',\n        'underline': 'Nastavit podtrhnutí',\n        'strikethrough': 'Nastavit přeškrtnutí',\n        'removeFormat': 'Ostranit nastavený styl',\n        'justifyLeft': 'Nastavit zarovnání vlevo',\n        'justifyCenter': 'Nastavit zarovnání na střed',\n        'justifyRight': 'Nastavit zarovnání vpravo',\n        'justifyFull': 'Nastavit zarovnání do bloku',\n        'insertUnorderedList': 'Aplikovat odrážkový seznam',\n        'insertOrderedList': 'Aplikovat číselný seznam',\n        'outdent': 'Zmenšit odsazení aktuálního odstavec',\n        'indent': 'Odsadit aktuální odstavec',\n        'formatPara': 'Změnit formátování aktuálního bloku na odstavec (P tag)',\n        'formatH1': 'Změnit formátování aktuálního bloku na Nadpis 1',\n        'formatH2': 'Změnit formátování aktuálního bloku na Nadpis 2',\n        'formatH3': 'Změnit formátování aktuálního bloku na Nadpis 3',\n        'formatH4': 'Změnit formátování aktuálního bloku na Nadpis 4',\n        'formatH5': 'Změnit formátování aktuálního bloku na Nadpis 5',\n        'formatH6': 'Změnit formátování aktuálního bloku na Nadpis 6',\n        'insertHorizontalRule': 'Vložit horizontální čáru',\n        'linkDialog.show': 'Zobrazit dialog pro odkaz',\n      },\n      history: {\n        undo: 'Krok vzad',\n        redo: 'Krok vpřed',\n      },\n      specialChar: {\n        specialChar: 'SPECIÁLNÍ ZNAKY',\n        select: 'Vyberte speciální znaky',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}