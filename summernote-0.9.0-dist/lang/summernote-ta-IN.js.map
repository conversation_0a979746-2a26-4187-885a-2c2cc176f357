{"version": 3, "file": "lang/summernote-ta-IN.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,aAAa,EAAE,kBAAkB;QACjCC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE;MACb,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,eAAe;QACvBC,UAAU,EAAE,WAAW;QACvBC,UAAU,EAAE,UAAU;QACtBC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,gBAAgB;QAC5BC,SAAS,EAAE,mBAAmB;QAC9BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,aAAa;QAC7BC,SAAS,EAAE,iBAAiB;QAC5BC,aAAa,EAAE,wBAAwB;QACvCC,SAAS,EAAE,aAAa;QACxBC,eAAe,EAAE,wBAAwB;QACzCC,eAAe,EAAE,uBAAuB;QACxCC,oBAAoB,EAAE,mCAAmC;QACzDC,GAAG,EAAE,eAAe;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,iBAAiB;QAC5BpB,MAAM,EAAE,iBAAiB;QACzBgB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,SAAS;QACftB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,eAAe;QAC9BT,GAAG,EAAE,eAAe;QACpBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,CAAC,EAAE,OAAO;QACVC,UAAU,EAAE,WAAW;QACvBC,GAAG,EAAE,UAAU;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE,WAAW;QACfC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,eAAe;QAC3BC,WAAW,EAAE,WAAW;QACxBC,cAAc,EAAE,cAAc;QAC9BC,KAAK,EAAE,YAAY;QACnBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,aAAa;QACxBC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,oBAAoB;QACpCC,MAAM,EAAE,cAAc;QACtBC,mBAAmB,EAAE,kBAAkB;QACvCC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ta-IN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ta-IN': {\n      font: {\n        bold: 'தடித்த',\n        italic: 'சாய்வு',\n        underline: 'அடிக்கோடு',\n        clear: 'நீக்கு',\n        height: 'வரி  உயரம்',\n        name: 'எழுத்துரு பெயர்',\n        strikethrough: 'குறுக்குக் கோடு',\n        size: 'எழுத்துரு அளவு',\n        superscript: 'மேல் ஒட்டு',\n        subscript: 'கீழ் ஒட்டு',\n      },\n      image: {\n        image: 'படம்',\n        insert: 'படத்தை செருகு',\n        resizeFull: 'முழு அளவை',\n        resizeHalf: 'அரை அளவை',\n        resizeQuarter: 'கால் அளவை',\n        floatLeft: 'இடப்பக்கமாக வை',\n        floatRight: 'வலப்பக்கமாக வை',\n        floatNone: 'இயல்புநிலையில் வை',\n        shapeRounded: 'வட்டமான வடிவம்',\n        shapeCircle: 'வட்ட வடிவம்',\n        shapeThumbnail: 'சிறு வடிவம்',\n        shapeNone: 'வடிவத்தை நீக்கு',\n        dragImageHere: 'படத்தை இங்கே இழுத்துவை',\n        dropImage: 'படத்தை விடு',\n        selectFromFiles: 'கோப்புகளை தேர்வு செய்',\n        maximumFileSize: 'அதிகபட்ச கோப்பு அளவு',\n        maximumFileSizeError: 'கோப்பு அதிகபட்ச அளவை மீறிவிட்டது',\n        url: 'இணையதள முகவரி',\n        remove: 'படத்தை நீக்கு',\n        original: 'Original',\n      },\n      video: {\n        video: 'காணொளி',\n        videoLink: 'காணொளி இணைப்பு',\n        insert: 'காணொளியை செருகு',\n        url: 'இணையதள முகவரி',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'இணைப்பு',\n        insert: 'இணைப்பை செருகு',\n        unlink: 'இணைப்பை நீக்கு',\n        edit: 'இணைப்பை தொகு',\n        textToDisplay: 'காட்சி வாசகம்',\n        url: 'இணையதள முகவரி',\n        openInNewWindow: 'புதிய சாளரத்தில் திறக்க',\n      },\n      table: {\n        table: 'அட்டவணை',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'கிடைமட்ட கோடு',\n      },\n      style: {\n        style: 'தொகுப்பு',\n        p: 'பத்தி',\n        blockquote: 'மேற்கோள்',\n        pre: 'குறியீடு',\n        h1: 'தலைப்பு 1',\n        h2: 'தலைப்பு 2',\n        h3: 'தலைப்பு 3',\n        h4: 'தலைப்பு 4',\n        h5: 'தலைப்பு 5',\n        h6: 'தலைப்பு 6',\n      },\n      lists: {\n        unordered: 'வரிசையிடாத',\n        ordered: 'வரிசையிட்ட',\n      },\n      options: {\n        help: 'உதவி',\n        fullscreen: 'முழுத்திரை',\n        codeview: 'நிரலாக்க காட்சி',\n      },\n      paragraph: {\n        paragraph: 'பத்தி',\n        outdent: 'வெளித்தள்ளு',\n        indent: 'உள்ளே தள்ளு',\n        left: 'இடது சீரமைப்பு',\n        center: 'நடு சீரமைப்பு',\n        right: 'வலது சீரமைப்பு',\n        justify: 'இருபுற சீரமைப்பு',\n      },\n      color: {\n        recent: 'அண்மை நிறம்',\n        more: 'மேலும்',\n        background: 'பின்புல நிறம்',\n        foreground: 'முன்புற நிறம்',\n        transparent: 'தெளிமையான',\n        setTransparent: 'தெளிமையாக்கு',\n        reset: 'மீட்டமைக்க',\n        resetToDefault: 'இயல்புநிலைக்கு மீட்டமை',\n      },\n      shortcut: {\n        shortcuts: 'குறுக்குவழி',\n        close: 'மூடு',\n        textFormatting: 'எழுத்து வடிவமைப்பு',\n        action: 'செயல்படுத்து',\n        paragraphFormatting: 'பத்தி வடிவமைப்பு',\n        documentStyle: 'ஆவண பாணி',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'மீளமை',\n        redo: 'மீண்டும்',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "size", "superscript", "subscript", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}