{"version": 3, "file": "lang/summernote-es-ES.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,0BAA0B;QACjCC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE,qBAAqB;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,iBAAiB;QACzBC,UAAU,EAAE,iCAAiC;QAC7CC,UAAU,EAAE,0BAA0B;QACtCC,aAAa,EAAE,2BAA2B;QAC1CC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,uBAAuB;QAClCC,UAAU,EAAE,qBAAqB;QACjCC,SAAS,EAAE,WAAW;QACtBC,YAAY,EAAE,mBAAmB;QACjCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,gBAAgB;QAC3BC,aAAa,EAAE,kCAAkC;QACjDC,SAAS,EAAE,2BAA2B;QACtCC,eAAe,EAAE,uBAAuB;QACxCC,eAAe,EAAE,2BAA2B;QAC5CC,oBAAoB,EAAE,uCAAuC;QAC7DC,GAAG,EAAE,kBAAkB;QACvBC,MAAM,EAAE,oBAAoB;QAC5BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,kBAAkB;QAC7BrB,MAAM,EAAE,mBAAmB;QAC3BiB,GAAG,EAAE,eAAe;QACpBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdvB,MAAM,EAAE,oBAAoB;QAC5BwB,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,iBAAiB;QAChCT,GAAG,EAAE,+BAA+B;QACpCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,wBAAwB;QACrCC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE,mCAAmC;QAC/CC,WAAW,EAAE,iCAAiC;QAC9CC,MAAM,EAAE,gBAAgB;QACxBC,MAAM,EAAE,mBAAmB;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFpC,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,MAAM;QAClBC,GAAG,EAAE,QAAQ;QACbC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,mBAAmB;QAC/BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,oBAAoB;QAC7BC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,wBAAwB;QAC9BC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,gBAAgB;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,yBAAyB;QACzCC,KAAK,EAAE,aAAa;QACpBC,cAAc,EAAE,wCAAwC;QACxDC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,mBAAmB;QAC9BC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE,kBAAkB;QAClCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,oBAAoB;QACzCC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ4B,eAAe,EAAE,qBAAqB;QACtCC,IAAI,EAAE,2BAA2B;QACjCC,IAAI,EAAE,0BAA0B;QAChCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,qBAAqB;QAC5B/F,IAAI,EAAE,2BAA2B;QACjCC,MAAM,EAAE,2BAA2B;QACnCC,SAAS,EAAE,6BAA6B;QACxCI,aAAa,EAAE,2BAA2B;QAC1C0F,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,wBAAwB;QACrCC,aAAa,EAAE,mBAAmB;QAClCC,YAAY,EAAE,sBAAsB;QACpCC,WAAW,EAAE,YAAY;QACzBC,mBAAmB,EAAE,gBAAgB;QACrCC,iBAAiB,EAAE,yBAAyB;QAC5CnC,OAAO,EAAE,6BAA6B;QACtCC,MAAM,EAAE,8BAA8B;QACtCmC,UAAU,EAAE,6DAA6D;QACzEC,QAAQ,EAAE,2CAA2C;QACrDC,QAAQ,EAAE,2CAA2C;QACrDC,QAAQ,EAAE,2CAA2C;QACrDC,QAAQ,EAAE,2CAA2C;QACrDC,QAAQ,EAAE,2CAA2C;QACrDC,QAAQ,EAAE,2CAA2C;QACrDC,oBAAoB,EAAE,+BAA+B;QACrD,iBAAiB,EAAE;MACrB,CAAC;MACDC,OAAO,EAAE;QACPnB,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;MACR,CAAC;MACDmB,WAAW,EAAE;QACXA,WAAW,EAAE,uBAAuB;QACpCC,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-es-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'es-ES': {\n      font: {\n        bold: 'Negrita',\n        italic: 'Curs<PERSON>',\n        underline: 'Subrayado',\n        clear: 'Eliminar estilo de letra',\n        height: 'Altura de línea',\n        name: 'Tipo de letra',\n        strikethrough: '<PERSON><PERSON><PERSON>',\n        subscript: 'Subíndice',\n        superscript: 'Superíndice',\n        size: 'Tamaño de la fuente',\n        sizeunit: 'Unidad del tamaño de letra',\n      },\n      image: {\n        image: 'Imagen',\n        insert: 'Insertar imagen',\n        resizeFull: 'Redimensionar a tamaño completo',\n        resizeHalf: 'Redimensionar a la mitad',\n        resizeQuarter: 'Redimensionar a un cuarto',\n        resizeNone: 'Tamaño original',\n        floatLeft: 'Flotar a la izquierda',\n        floatRight: 'Flotar a la derecha',\n        floatNone: 'No flotar',\n        shapeRounded: 'Forma: Redondeado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Miniatura',\n        shapeNone: 'Forma: Ninguna',\n        dragImageHere: 'Arrastre una imagen o texto aquí',\n        dropImage: 'Suelte una imagen o texto',\n        selectFromFiles: 'Seleccione un fichero',\n        maximumFileSize: 'Tamaño máximo del fichero',\n        maximumFileSizeError: 'Superado el tamaño máximo de fichero.',\n        url: 'URL de la imagen',\n        remove: 'Eliminar la imagen',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Enlace del vídeo',\n        insert: 'Insertar un vídeo',\n        url: 'URL del vídeo',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Enlace',\n        insert: 'Insertar un enlace',\n        unlink: 'Quitar el enlace',\n        edit: 'Editar',\n        textToDisplay: 'Texto a mostrar',\n        url: '¿A qué URL lleva este enlace?',\n        openInNewWindow: 'Abrir en una nueva ventana',\n      },\n      table: {\n        table: 'Tabla',\n        addRowAbove: 'Añadir una fila encima',\n        addRowBelow: 'Añadir una fila debajo',\n        addColLeft: 'Añadir una columna a la izquierda',\n        addColRight: 'Añadir una columna a la derecha',\n        delRow: 'Borrar la fila',\n        delCol: 'Borrar la columna',\n        delTable: 'Borrar la tabla',\n      },\n      hr: {\n        insert: 'Insertar una línea horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Cita',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ayuda',\n        fullscreen: 'Pantalla completa',\n        codeview: 'Ver el código fuente',\n      },\n      paragraph: {\n        paragraph: 'Párrafo',\n        outdent: 'Reducir la sangría',\n        indent: 'Aumentar la sangría',\n        left: 'Alinear a la izquierda',\n        center: 'Centrar',\n        right: 'Alinear a la derecha',\n        justify: 'Justificar',\n      },\n      color: {\n        recent: 'Último color',\n        more: 'Más colores',\n        background: 'Color de fondo',\n        foreground: 'Color del texto',\n        transparent: 'Transparente',\n        setTransparent: 'Establecer transparente',\n        reset: 'Restablecer',\n        resetToDefault: 'Restablecer a los valores predefinidos',\n        cpSelect: 'Seleccionar',\n      },\n      shortcut: {\n        shortcuts: 'Atajos de teclado',\n        close: 'Cerrar',\n        textFormatting: 'Formato de texto',\n        action: 'Acción',\n        paragraphFormatting: 'Formato de párrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Teclas adicionales',\n      },\n      help: {\n        insertParagraph: 'Insertar un párrafo',\n        undo: 'Deshacer la última acción',\n        redo: 'Rehacer la última acción',\n        tab: 'Tabular',\n        untab: 'Eliminar tabulación',\n        bold: 'Establecer estilo negrita',\n        italic: 'Establecer estilo cursiva',\n        underline: 'Establecer estilo subrayado',\n        strikethrough: 'Establecer estilo tachado',\n        removeFormat: 'Limpiar estilo',\n        justifyLeft: 'Alinear a la izquierda',\n        justifyCenter: 'Alinear al centro',\n        justifyRight: 'Alinear a la derecha',\n        justifyFull: 'Justificar',\n        insertUnorderedList: 'Insertar lista',\n        insertOrderedList: 'Insertar lista numerada',\n        outdent: 'Reducir sangría del párrafo',\n        indent: 'Aumentar sangría del párrafo',\n        formatPara: 'Cambiar el formato del bloque actual a párrafo (etiqueta P)',\n        formatH1: 'Cambiar el formato del bloque actual a H1',\n        formatH2: 'Cambiar el formato del bloque actual a H2',\n        formatH3: 'Cambiar el formato del bloque actual a H3',\n        formatH4: 'Cambiar el formato del bloque actual a H4',\n        formatH5: 'Cambiar el formato del bloque actual a H5',\n        formatH6: 'Cambiar el formato del bloque actual a H6',\n        insertHorizontalRule: 'Insertar una línea horizontal',\n        'linkDialog.show': 'Mostrar el panel de enlaces',\n      },\n      history: {\n        undo: 'Deshacer',\n        redo: 'Rehacer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIALES',\n        select: 'Seleccionar caracteres especiales',\n      },\n      output: {\n        noSelection: '¡No ha seleccionado nada!',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "output", "noSelection", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}