{"version": 3, "file": "lang/summernote-bn-BD.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,YAAY;QACzBC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,cAAc;QACtBC,UAAU,EAAE,iBAAiB;QAC7BC,UAAU,EAAE,gBAAgB;QAC5BC,aAAa,EAAE,qBAAqB;QACpCC,UAAU,EAAE,UAAU;QACtBC,SAAS,EAAE,UAAU;QACrBC,UAAU,EAAE,UAAU;QACtBC,SAAS,EAAE,UAAU;QACrBC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,gBAAgB;QAC3BC,aAAa,EAAE,6BAA6B;QAC5CC,SAAS,EAAE,mBAAmB;QAC9BC,eAAe,EAAE,yBAAyB;QAC1CC,eAAe,EAAE,sBAAsB;QACvCC,oBAAoB,EAAE,qCAAqC;QAC3DC,GAAG,EAAE,UAAU;QACfC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,cAAc;QACzBrB,MAAM,EAAE,qBAAqB;QAC7BiB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,OAAO;QACbvB,MAAM,EAAE,qBAAqB;QAC7BwB,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,eAAe;QACrBC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,4BAA4B;QACjCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,oBAAoB;QACjCC,WAAW,EAAE,oBAAoB;QACjCC,UAAU,EAAE,oBAAoB;QAChCC,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFpC,MAAM,EAAE;MACV,CAAC;MACDqC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,QAAQ;QACXC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,kBAAkB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,sBAAsB;QAC/BC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,wBAAwB;QAChCC,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,QAAQ;QACrBC,cAAc,EAAE,sBAAsB;QACtCC,KAAK,EAAE,iBAAiB;QACxBC,cAAc,EAAE,4BAA4B;QAC5CC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,iBAAiB;QAC5BC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE,gBAAgB;QAChCC,MAAM,EAAE,OAAO;QACfC,mBAAmB,EAAE,qBAAqB;QAC1CC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,QAAQ,EAAE,QAAQ;QAClB,iBAAiB,EAAE,mBAAmB;QACtC,MAAM,EAAE,8BAA8B;QACtC,MAAM,EAAE,uBAAuB;QAC/B,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,mBAAmB;QAC3B,QAAQ,EAAE,sBAAsB;QAChC,WAAW,EAAE,0BAA0B;QACvC,eAAe,EAAE,0BAA0B;QAC3C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,0BAA0B;QACzC,eAAe,EAAE,8BAA8B;QAC/C,cAAc,EAAE,0BAA0B;QAC1C,aAAa,EAAE,0BAA0B;QACzC,qBAAqB,EAAE,sBAAsB;QAC7C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,wCAAwC;QACnD,QAAQ,EAAE,gCAAgC;QAC1C,YAAY,EAAE,6DAA6D;QAC3E,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,UAAU,EAAE,6CAA6C;QACzD,sBAAsB,EAAE,sBAAsB;QAC9C,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,aAAa;QAC1BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-bn-BD.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'bn-BD': {\n      font: {\n        bold: 'গাঢ়',\n        italic: 'তির্যক',\n        underline: 'নিন্মরেখা',\n        clear: 'ফন্টের শৈলী সরান',\n        height: 'লাইনের উচ্চতা',\n        name: 'ফন্ট পরিবার',\n        strikethrough: 'অবচ্ছেদন',\n        subscript: 'নিম্নলিপি',\n        superscript: 'উর্ধ্বলিপি',\n        size: 'ফন্টের আকার',\n        sizeunit: 'ফন্টের আকারের একক',\n      },\n      image: {\n        image: 'ছবি',\n        insert: 'ছবি যোগ করুন',\n        resizeFull: 'পূর্ণ আকারে নিন',\n        resizeHalf: 'অর্ধ আকারে নিন',\n        resizeQuarter: 'চতুর্থাংশ আকারে নিন',\n        resizeNone: 'আসল আকার',\n        floatLeft: 'বামে নিন',\n        floatRight: 'ডানে নিন',\n        floatNone: 'দিক সরান',\n        shapeRounded: 'আকৃতি: গোলাকার',\n        shapeCircle: 'আকৃতি: বৃত্ত',\n        shapeThumbnail: 'আকৃতি: থাম্বনেইল',\n        shapeNone: 'আকৃতি: কিছু নয়',\n        dragImageHere: 'এখানে ছবি বা লেখা টেনে আনুন',\n        dropImage: 'ছবি বা লেখা ছাড়ুন',\n        selectFromFiles: 'ফাইল থেকে নির্বাচন করুন',\n        maximumFileSize: 'সর্বোচ্চ ফাইলের আকার',\n        maximumFileSizeError: 'সর্বোচ্চ ফাইলের আকার অতিক্রম করেছে।',\n        url: 'ছবির URL',\n        remove: 'ছবি সরান',\n        original: 'আসল',\n      },\n      video: {\n        video: 'ভিডিও',\n        videoLink: 'ভিডিওর লিঙ্ক',\n        insert: 'ভিডিও সন্নিবেশ করুন',\n        url: 'ভিডিওর URL',\n        providers: '(ইউটিউব, গুগল ড্রাইভ, ভিমিও, ভিন, ইনস্টাগ্রাম, ডেইলিমোশন বা ইউকু)',\n      },\n      link: {\n        link: 'লিঙ্ক',\n        insert: 'লিঙ্ক সন্নিবেশ করুন',\n        unlink: 'লিঙ্কমুক্ত করুন',\n        edit: 'সম্পাদনা করুন',\n        textToDisplay: 'দেখানোর জন্য লেখা',\n        url: 'এই লিঙ্কটি কোন URL-এ যাবে?',\n        openInNewWindow: 'নতুন উইন্ডোতে খুলুন',\n      },\n      table: {\n        table: 'ছক',\n        addRowAbove: 'উপরে সারি যোগ করুন',\n        addRowBelow: 'নিচে সারি যোগ করুন',\n        addColLeft: 'বামে কলাম যোগ করুন',\n        addColRight: 'ডানে কলাম যোগ করুন',\n        delRow: 'সারি মুছুন',\n        delCol: 'কলাম মুছুন',\n        delTable: 'ছক মুছুন',\n      },\n      hr: {\n        insert: 'বিভাজক রেখা সন্নিবেশ করুন',\n      },\n      style: {\n        style: 'শৈলী',\n        p: 'সাধারণ',\n        blockquote: 'উক্তি',\n        pre: 'কোড',\n        h1: 'শীর্ষক ১',\n        h2: 'শীর্ষক ২',\n        h3: 'শীর্ষক ৩',\n        h4: 'শীর্ষক ৪',\n        h5: 'শীর্ষক ৫',\n        h6: 'শীর্ষক ৬',\n      },\n      lists: {\n        unordered: 'অবিন্যস্ত তালিকা',\n        ordered: 'বিন্যস্ত তালিকা',\n      },\n      options: {\n        help: 'সাহায্য',\n        fullscreen: 'পূর্ণ পর্দা',\n        codeview: 'কোড দৃশ্য',\n      },\n      paragraph: {\n        paragraph: 'অনুচ্ছেদ',\n        outdent: 'ঋণাত্মক প্রান্তিককরণ',\n        indent: 'প্রান্তিককরণ',\n        left: 'বামে সারিবদ্ধ করুন',\n        center: 'কেন্দ্রে সারিবদ্ধ করুন',\n        right: 'ডানে সারিবদ্ধ করুন',\n        justify: 'যথাযথ ফাঁক দিয়ে সাজান',\n      },\n      color: {\n        recent: 'সাম্প্রতিক রং',\n        more: 'আরও রং',\n        background: 'পটভূমির রং',\n        foreground: 'লেখার রং',\n        transparent: 'স্বচ্ছ',\n        setTransparent: 'স্বচ্ছ নির্ধারণ করুন',\n        reset: 'পুনঃস্থাপন করুন',\n        resetToDefault: 'পূর্বনির্ধারিত ফিরিয়ে আনুন',\n        cpSelect: 'নির্বাচন করুন',\n      },\n      shortcut: {\n        shortcuts: 'কীবোর্ড শর্টকাট',\n        close: 'বন্ধ করুন',\n        textFormatting: 'লেখার বিন্যাসন',\n        action: 'কার্য',\n        paragraphFormatting: 'অনুচ্ছেদের বিন্যাসন',\n        documentStyle: 'নথির শৈলী',\n        extraKeys: 'অতিরিক্ত কীগুলি',\n      },\n      help: {\n        'escape': 'এস্কেপ',\n        'insertParagraph': 'অনুচ্ছেদ সন্নিবেশ',\n        'undo': 'শেষ কমান্ড পূর্বাবস্থায় ফেরত',\n        'redo': 'শেষ কমান্ড পুনরায় করা',\n        'tab': 'ট্যাব',\n        'untab': 'অ-ট্যাব',\n        'bold': 'গাঢ় শৈলী নির্ধারণ',\n        'italic': 'তির্যক শৈলী নির্ধারণ',\n        'underline': 'নিম্নরেখার শৈলী নির্ধারণ',\n        'strikethrough': 'অবচ্ছেদনের শৈলী নির্ধারণ',\n        'removeFormat': 'শৈলী পরিষ্কার',\n        'justifyLeft': 'বামের সারিবন্ধন নির্ধারণ',\n        'justifyCenter': 'কেন্দ্রের সারিবন্ধন নির্ধারণ',\n        'justifyRight': 'ডানের সারিবন্ধন নির্ধারণ',\n        'justifyFull': 'পূর্ণ সারিবন্ধন নির্ধারণ',\n        'insertUnorderedList': 'অবিন্যস্ত তালিকা টগল',\n        'insertOrderedList': 'বিন্যস্ত তালিকা টগল',\n        'outdent': 'বর্তমান অনুচ্ছেদে ঋণাত্মক প্রান্তিককরণ',\n        'indent': 'বর্তমান অনুচ্ছেদে প্রান্তিককরণ',\n        'formatPara': 'বর্তমান ব্লকের বিন্যাসটি অনুচ্ছেদ হিসেবে পরিবর্তন (P ট্যাগ)',\n        'formatH1': 'বর্তমান ব্লকের বিন্যাসটি H1 হিসেবে পরিবর্তন',\n        'formatH2': 'বর্তমান ব্লকের বিন্যাসটি H2 হিসেবে পরিবর্তন',\n        'formatH3': 'বর্তমান ব্লকের বিন্যাসটি H3 হিসেবে পরিবর্তন',\n        'formatH4': 'বর্তমান ব্লকের বিন্যাসটি H4 হিসেবে পরিবর্তন',\n        'formatH5': 'বর্তমান ব্লকের বিন্যাসটি H5 হিসেবে পরিবর্তন',\n        'formatH6': 'বর্তমান ব্লকের বিন্যাসটি H6 হিসেবে পরিবর্তন',\n        'insertHorizontalRule': 'বিভাজক রেখা সন্নিবেশ',\n        'linkDialog.show': 'লিংক ডায়ালগ প্রদর্শন',\n      },\n      history: {\n        undo: 'পূর্বাবস্থায় আনুন',\n        redo: 'পুনঃকরুন',\n      },\n      specialChar: {\n        specialChar: 'বিশেষ অক্ষর',\n        select: 'বিশেষ অক্ষর নির্বাচন করুন',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}