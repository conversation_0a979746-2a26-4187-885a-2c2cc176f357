{"version": 3, "file": "lang/summernote-mn-MN.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA;;AAEA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,gBAAgB;QAC3BC,KAAK,EAAE,UAAU;QACjBC,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE,cAAc;QACzBC,aAAa,EAAE,OAAO;QACtBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,YAAY;QACxBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,sBAAsB;QACjCC,UAAU,EAAE,wBAAwB;QACpCC,SAAS,EAAE,0BAA0B;QACrCC,YAAY,EAAE,cAAc;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,UAAU;QACrBC,aAAa,EAAE,6BAA6B;QAC5CC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,uBAAuB;QACxCC,eAAe,EAAE,oBAAoB;QACrCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,eAAe;QAC1BpB,MAAM,EAAE,eAAe;QACvBgB,GAAG,EAAE,YAAY;QACjBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,SAAS;QACftB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,iBAAiB;QAChCT,GAAG,EAAE,6BAA6B;QAClCU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,UAAU;QACjBC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,UAAU;QACtBC,GAAG,EAAE,YAAY;QACjBC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,iBAAiB;QAC5BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,mBAAmB;QAC/BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,oBAAoB;QAC1BC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,uBAAuB;QAC/BC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,iBAAiB;QACjCC,KAAK,EAAE,2BAA2B;QAClCC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,gBAAgB;QAC3BC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,wBAAwB;QACxCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,2BAA2B;QAChDC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,gBAAgB;QAC7BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-mn-MN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "// Starsoft Mongolia LLC Temuujin Ariunbold\n\n(function($) {\n  $.extend(true, $.summernote.lang, {\n    'mn-MN': {\n      font: {\n        bold: 'Тод',\n        italic: 'Налуу',\n        underline: 'Доогуур зураас',\n        clear: 'Цэвэрлэх',\n        height: 'Өндөр',\n        name: 'Фонт',\n        superscript: 'Дээд илтгэгч',\n        subscript: 'Доод илтгэгч',\n        strikethrough: 'Дарах',\n        size: 'Хэмжээ',\n      },\n      image: {\n        image: 'Зураг',\n        insert: 'Оруулах',\n        resizeFull: 'Хэмжээ бүтэн',\n        resizeHalf: 'Хэмжээ 1/2',\n        resizeQuarter: 'Хэмжээ 1/4',\n        floatLeft: 'Зүүн талд байрлуулах',\n        floatRight: 'Баруун талд байрлуулах',\n        floatNone: 'Анхдагч байрлалд аваачих',\n        shapeRounded: 'Хүрээ: Дугуй',\n        shapeCircle: 'Хүрээ: Тойрог',\n        shapeThumbnail: 'Хүрээ: Хураангуй',\n        shapeNone: 'Хүрээгүй',\n        dragImageHere: 'Зургийг энд чирч авчирна уу',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Файлуудаас сонгоно уу',\n        maximumFileSize: 'Файлын дээд хэмжээ',\n        maximumFileSizeError: 'Файлын дээд хэмжээ хэтэрсэн',\n        url: 'Зургийн URL',\n        remove: 'Зургийг устгах',\n        original: 'Original',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видео холбоос',\n        insert: 'Видео оруулах',\n        url: 'Видео URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion болон Youku)',\n      },\n      link: {\n        link: 'Холбоос',\n        insert: 'Холбоос оруулах',\n        unlink: 'Холбоос арилгах',\n        edit: 'Засварлах',\n        textToDisplay: 'Харуулах бичвэр',\n        url: 'Энэ холбоос хаашаа очих вэ?',\n        openInNewWindow: 'Шинэ цонхонд нээх',\n      },\n      table: {\n        table: 'Хүснэгт',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Хэвтээ шугам оруулах',\n      },\n      style: {\n        style: 'Хэв маяг',\n        p: 'p',\n        blockquote: 'Иш татах',\n        pre: 'Эх сурвалж',\n        h1: 'Гарчиг 1',\n        h2: 'Гарчиг 2',\n        h3: 'Гарчиг 3',\n        h4: 'Гарчиг 4',\n        h5: 'Гарчиг 5',\n        h6: 'Гарчиг 6',\n      },\n      lists: {\n        unordered: 'Эрэмбэлэгдээгүй',\n        ordered: 'Эрэмбэлэгдсэн',\n      },\n      options: {\n        help: 'Тусламж',\n        fullscreen: 'Дэлгэцийг дүүргэх',\n        codeview: 'HTML-Code харуулах',\n      },\n      paragraph: {\n        paragraph: 'Хэсэг',\n        outdent: 'Догол мөр хасах',\n        indent: 'Догол мөр нэмэх',\n        left: 'Зүүн тийш эгнүүлэх',\n        center: 'Төвд эгнүүлэх',\n        right: 'Баруун тийш эгнүүлэх',\n        justify: 'Мөрийг тэгшлэх',\n      },\n      color: {\n        recent: 'Сүүлд хэрэглэсэн өнгө',\n        more: 'Өөр өнгөнүүд',\n        background: 'Дэвсгэр өнгө',\n        foreground: 'Үсгийн өнгө',\n        transparent: 'Тунгалаг',\n        setTransparent: 'Тунгалаг болгох',\n        reset: 'Анхдагч өнгөөр тохируулах',\n        resetToDefault: 'Хэвд нь оруулах',\n      },\n      shortcut: {\n        shortcuts: 'Богино холбоос',\n        close: 'Хаалт',\n        textFormatting: 'Бичвэрийг хэлбэржүүлэх',\n        action: 'Үйлдэл',\n        paragraphFormatting: 'Догол мөрийг хэлбэржүүлэх',\n        documentStyle: 'Бичиг баримтын хэв загвар',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Буцаах',\n        redo: 'Дахин хийх',\n      },\n      specialChar: {\n        specialChar: 'Тусгай тэмдэгт',\n        select: 'Тусгай тэмдэгт сонгох',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "superscript", "subscript", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}