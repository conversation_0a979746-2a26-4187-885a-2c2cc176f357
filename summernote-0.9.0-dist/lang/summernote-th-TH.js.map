{"version": 3, "file": "lang/summernote-th-TH.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,aAAa;QACnBC,aAAa,EAAE,QAAQ;QACvBC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,OAAO;QACpBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,kBAAkB;QAC9BC,UAAU,EAAE,gBAAgB;QAC5BC,aAAa,EAAE,gBAAgB;QAC/BC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,eAAe;QAC1BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,8BAA8B;QAC7CC,SAAS,EAAE,sBAAsB;QACjCC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,iBAAiB;QAClCC,oBAAoB,EAAE,sBAAsB;QAC5CC,GAAG,EAAE,uBAAuB;QAC5BC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,SAAS,EAAE,gBAAgB;QAC3BpB,MAAM,EAAE,YAAY;QACpBgB,GAAG,EAAE,uBAAuB;QAC5BK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,cAAc;QACpBtB,MAAM,EAAE,kBAAkB;QAC1BuB,MAAM,EAAE,oBAAoB;QAC5BC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,mBAAmB;QAClCT,GAAG,EAAE,6CAA6C;QAClDU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,gBAAgB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,UAAU,EAAE,sBAAsB;QAClCC,WAAW,EAAE,qBAAqB;QAClCC,MAAM,EAAE,OAAO;QACfC,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,CAAC,EAAE,MAAM;QACTC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,qBAAqB;QAChCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,gBAAgB;QAC5BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE,WAAW;QACnBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,iBAAiB;QACzBC,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,UAAU;QACvBC,cAAc,EAAE,qBAAqB;QACrCC,KAAK,EAAE,QAAQ;QACfC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,KAAK;QACZC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE,qBAAqB;QAC1CC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,UAAU;QAClB,QAAQ,EAAE,YAAY;QACtB,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,UAAU,EAAE,mCAAmC;QAC/C,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-th-TH.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'th-TH': {\n      font: {\n        bold: 'ตัวหนา',\n        italic: 'ตัวเอียง',\n        underline: 'ขีดเส้นใต้',\n        clear: 'ล้างรูปแบบตัวอักษร',\n        height: 'ความสูงบรรทัด',\n        name: 'แบบตัวอักษร',\n        strikethrough: 'ขีดฆ่า',\n        subscript: 'ตัวห้อย',\n        superscript: 'ตัวยก',\n        size: 'ขนาดตัวอักษร',\n      },\n      image: {\n        image: 'รูปภาพ',\n        insert: 'แทรกรูปภาพ',\n        resizeFull: 'ปรับขนาดเท่าจริง',\n        resizeHalf: 'ปรับขนาดลง 50%',\n        resizeQuarter: 'ปรับขนาดลง 25%',\n        floatLeft: 'ชิดซ้าย',\n        floatRight: 'ชิดขวา',\n        floatNone: 'ไม่จัดตำแหน่ง',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'ลากรูปภาพที่ต้องการไว้ที่นี่',\n        dropImage: 'วางรูปภาพหรือข้อความ',\n        selectFromFiles: 'เลือกไฟล์รูปภาพ',\n        maximumFileSize: 'ขนาดไฟล์ใหญ่สุด',\n        maximumFileSizeError: 'ไฟล์เกินขนาดที่กำหนด',\n        url: 'ที่อยู่ URL ของรูปภาพ',\n        remove: 'ลบรูปภาพ',\n        original: 'Original',\n      },\n      video: {\n        video: 'วีดีโอ',\n        videoLink: 'ลิงก์ของวีดีโอ',\n        insert: 'แทรกวีดีโอ',\n        url: 'ที่อยู่ URL ของวีดีโอ',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion หรือ Youku)',\n      },\n      link: {\n        link: 'ตัวเชื่อมโยง',\n        insert: 'แทรกตัวเชื่อมโยง',\n        unlink: 'ยกเลิกตัวเชื่อมโยง',\n        edit: 'แก้ไข',\n        textToDisplay: 'ข้อความที่ให้แสดง',\n        url: 'ที่อยู่เว็บไซต์ที่ต้องการให้เชื่อมโยงไปถึง?',\n        openInNewWindow: 'เปิดในหน้าต่างใหม่',\n      },\n      table: {\n        table: 'ตาราง',\n        addRowAbove: 'เพิ่มแถวด้านบน',\n        addRowBelow: 'เพิ่มแถวด้านล่าง',\n        addColLeft: 'เพิ่มคอลัมน์ด้านซ้าย',\n        addColRight: 'เพิ่มคอลัมน์ด้านขวา',\n        delRow: 'ลบแถว',\n        delCol: 'ลบคอลัมน์',\n        delTable: 'ลบตาราง',\n      },\n      hr: {\n        insert: 'แทรกเส้นคั่น',\n      },\n      style: {\n        style: 'รูปแบบ',\n        p: 'ปกติ',\n        blockquote: 'ข้อความ',\n        pre: 'โค้ด',\n        h1: 'หัวข้อ 1',\n        h2: 'หัวข้อ 2',\n        h3: 'หัวข้อ 3',\n        h4: 'หัวข้อ 4',\n        h5: 'หัวข้อ 5',\n        h6: 'หัวข้อ 6',\n      },\n      lists: {\n        unordered: 'รายการแบบไม่มีลำดับ',\n        ordered: 'รายการแบบมีลำดับ',\n      },\n      options: {\n        help: 'ช่วยเหลือ',\n        fullscreen: 'ขยายเต็มหน้าจอ',\n        codeview: 'ซอร์สโค้ด',\n      },\n      paragraph: {\n        paragraph: 'ย่อหน้า',\n        outdent: 'เยื้องซ้าย',\n        indent: 'เยื้องขวา',\n        left: 'จัดหน้าชิดซ้าย',\n        center: 'จัดหน้ากึ่งกลาง',\n        right: 'จัดหน้าชิดขวา',\n        justify: 'จัดบรรทัดเสมอกัน',\n      },\n      color: {\n        recent: 'สีที่ใช้ล่าสุด',\n        more: 'สีอื่นๆ',\n        background: 'สีพื้นหลัง',\n        foreground: 'สีพื้นหน้า',\n        transparent: 'โปร่งแสง',\n        setTransparent: 'ตั้งค่าความโปร่งแสง',\n        reset: 'คืนค่า',\n        resetToDefault: 'คืนค่ามาตรฐาน',\n      },\n      shortcut: {\n        shortcuts: 'แป้นลัด',\n        close: 'ปิด',\n        textFormatting: 'การจัดรูปแบบข้อความ',\n        action: 'การกระทำ',\n        paragraphFormatting: 'การจัดรูปแบบย่อหน้า',\n        documentStyle: 'รูปแบบของเอกสาร',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'ทำตัวหนา',\n        'italic': 'ทำตัวเอียง',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H1',\n        'formatH2': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H2',\n        'formatH3': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H3',\n        'formatH4': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H4',\n        'formatH5': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H5',\n        'formatH6': 'เปลี่ยนรูปแบบบล็อคปัจจุบันเป็น H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'เปิดหน้าแก้ไข Link',\n      },\n      history: {\n        undo: 'ยกเลิกการกระทำ',\n        redo: 'ทำซ้ำการกระทำ',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}