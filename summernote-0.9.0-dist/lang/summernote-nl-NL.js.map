{"version": 3, "file": "lang/summernote-nl-NL.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,YAAY;QACnBC,MAAM,EAAE,qBAAqB;QAC7BC,UAAU,EAAE,mBAAmB;QAC/BC,UAAU,EAAE,eAAe;QAC3BC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,iBAAiB;QAC5BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,iBAAiB;QAC5BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,oCAAoC;QACnDC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,uBAAuB;QACxCC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,uBAAuB;QAC5BC,MAAM,EAAE,sBAAsB;QAC9BC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,YAAY;QACvBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,kBAAkB;QACvBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,gBAAgB;QAC/BT,GAAG,EAAE,0CAA0C;QAC/CU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,wBAAwB;QACrCC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE,uBAAuB;QACnCC,WAAW,EAAE,wBAAwB;QACrCC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE,iBAAiB;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,SAAS;QACZC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,mBAAmB;QAC9BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE,iBAAiB;QAC7BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,mBAAmB;QAC/BC,UAAU,EAAE,aAAa;QACzBC,WAAW,EAAE,aAAa;QAC1BC,cAAc,EAAE,aAAa;QAC7BC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,oBAAoB;QAC/BC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,cAAc;QAC9BC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,kBAAkB;QACvCC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,iBAAiB;QACpC,MAAM,EAAE,kCAAkC;QAC1C,MAAM,EAAE,qCAAqC;QAC7C,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,aAAa;QACtB,MAAM,EAAE,uBAAuB;QAC/B,QAAQ,EAAE,2BAA2B;QACrC,WAAW,EAAE,gCAAgC;QAC7C,eAAe,EAAE,iCAAiC;QAClD,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,+BAA+B;QAC9C,qBAAqB,EAAE,mCAAmC;QAC1D,mBAAmB,EAAE,iCAAiC;QACtD,SAAS,EAAE,sCAAsC;QACjD,QAAQ,EAAE,8BAA8B;QACxC,YAAY,EAAE,kDAAkD;QAChE,UAAU,EAAE,+BAA+B;QAC3C,UAAU,EAAE,+BAA+B;QAC3C,UAAU,EAAE,+BAA+B;QAC3C,UAAU,EAAE,+BAA+B;QAC3C,UAAU,EAAE,+BAA+B;QAC3C,UAAU,EAAE,+BAA+B;QAC3C,sBAAsB,EAAE,2BAA2B;QACnD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-nl-NL.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'nl-NL': {\n      font: {\n        bold: 'Vet',\n        italic: 'Cursief',\n        underline: 'Onderstrepen',\n        clear: 'Stijl verwijderen',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: 'Lettertype',\n        strikethrough: '<PERSON>hale<PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Tekstgrootte',\n      },\n      image: {\n        image: 'Afbeelding',\n        insert: 'Afbeelding invoegen',\n        resizeFull: 'Volledige breedte',\n        resizeHalf: 'Halve breedte',\n        resizeQuarter: 'Kwart breedte',\n        floatLeft: 'Links uitlijnen',\n        floatRight: 'Rechts uitlijnen',\n        floatNone: 'Geen uitlijning',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Sleep hier een afbeelding naar toe',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Selecteer een bestand',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL van de afbeelding',\n        remove: 'Verwijder afbeelding',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video link',\n        insert: 'Video invoegen',\n        url: 'URL van de video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion of Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link invoegen',\n        unlink: 'Link verwijderen',\n        edit: 'Wijzigen',\n        textToDisplay: 'Tekst van link',\n        url: 'Naar welke URL moet deze link verwijzen?',\n        openInNewWindow: 'Open in nieuw venster',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Rij hierboven invoegen',\n        addRowBelow: 'Rij hieronder invoegen',\n        addColLeft: 'Kolom links toevoegen',\n        addColRight: 'Kolom rechts toevoegen',\n        delRow: 'Verwijder rij',\n        delCol: 'Verwijder kolom',\n        delTable: 'Verwijder tabel',\n      },\n      hr: {\n        insert: 'Horizontale lijn invoegen',\n      },\n      style: {\n        style: 'Stijl',\n        p: 'Normaal',\n        blockquote: 'Quote',\n        pre: 'Code',\n        h1: 'Kop 1',\n        h2: 'Kop 2',\n        h3: 'Kop 3',\n        h4: 'Kop 4',\n        h5: 'Kop 5',\n        h6: 'Kop 6',\n      },\n      lists: {\n        unordered: 'Ongeordende lijst',\n        ordered: 'Geordende lijst',\n      },\n      options: {\n        help: 'Help',\n        fullscreen: 'Volledig scherm',\n        codeview: 'Bekijk Code',\n      },\n      paragraph: {\n        paragraph: 'Paragraaf',\n        outdent: 'Inspringen verkleinen',\n        indent: 'Inspringen vergroten',\n        left: 'Links uitlijnen',\n        center: 'Centreren',\n        right: 'Rechts uitlijnen',\n        justify: 'Uitvullen',\n      },\n      color: {\n        recent: 'Recente kleur',\n        more: 'Meer kleuren',\n        background: 'Achtergrond kleur',\n        foreground: 'Tekst kleur',\n        transparent: 'Transparant',\n        setTransparent: 'Transparant',\n        reset: 'Standaard',\n        resetToDefault: 'Standaard kleur',\n      },\n      shortcut: {\n        shortcuts: 'Toetsencombinaties',\n        close: 'sluiten',\n        textFormatting: 'Tekststijlen',\n        action: 'Acties',\n        paragraphFormatting: 'Paragraafstijlen',\n        documentStyle: 'Documentstijlen',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Alinea invoegen',\n        'undo': 'Laatste handeling ongedaan maken',\n        'redo': 'Laatste handeling opnieuw uitvoeren',\n        'tab': 'Tab',\n        'untab': 'Herstel tab',\n        'bold': 'Stel stijl in als vet',\n        'italic': 'Stel stijl in als cursief',\n        'underline': 'Stel stijl in als onderstreept',\n        'strikethrough': 'Stel stijl in als doorgestreept',\n        'removeFormat': 'Verwijder stijl',\n        'justifyLeft': 'Lijn links uit',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Lijn rechts uit',\n        'justifyFull': 'Lijn uit op volledige breedte',\n        'insertUnorderedList': 'Zet ongeordende lijstweergave aan',\n        'insertOrderedList': 'Zet geordende lijstweergave aan',\n        'outdent': 'Verwijder inspringing huidige alinea',\n        'indent': 'Inspringen op huidige alinea',\n        'formatPara': 'Wijzig formattering huidig blok in alinea(P tag)',\n        'formatH1': 'Formatteer huidig blok als H1',\n        'formatH2': 'Formatteer huidig blok als H2',\n        'formatH3': 'Formatteer huidig blok als H3',\n        'formatH4': 'Formatteer huidig blok als H4',\n        'formatH5': 'Formatteer huidig blok als H5',\n        'formatH6': 'Formatteer huidig blok als H6',\n        'insertHorizontalRule': 'Invoegen horizontale lijn',\n        'linkDialog.show': 'Toon Link Dialoogvenster',\n      },\n      history: {\n        undo: 'Ongedaan maken',\n        redo: 'Opnieuw doorvoeren',\n      },\n      specialChar: {\n        specialChar: 'SPECIALE TEKENS',\n        select: 'Selecteer Speciale Tekens',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}