{"version": 3, "file": "lang/summernote-az-AZ.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,kBAAkB;QAC1BC,IAAI,EAAE,WAAW;QACjBC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,YAAY;QACvBC,WAAW,EAAE,YAAY;QACzBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,gBAAgB;QACxBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,UAAU;QACtBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,UAAU;QACrBC,UAAU,EAAE,UAAU;QACtBC,SAAS,EAAE,6BAA6B;QACxCC,YAAY,EAAE,sBAAsB;QACpCC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,YAAY;QACvBC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,2BAA2B;QACtCC,eAAe,EAAE,aAAa;QAC9BC,eAAe,EAAE,uBAAuB;QACxCC,oBAAoB,EAAE,mCAAmC;QACzDC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,aAAa;QACxBpB,MAAM,EAAE,gBAAgB;QACxBgB,GAAG,EAAE,cAAc;QACnBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,eAAe;QACvBuB,MAAM,EAAE,WAAW;QACnBC,IAAI,EAAE,kBAAkB;QACxBC,aAAa,EAAE,+BAA+B;QAC9CT,GAAG,EAAE,cAAc;QACnBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,uBAAuB;QACpCC,WAAW,EAAE,sBAAsB;QACnCC,UAAU,EAAE,qBAAqB;QACjCC,WAAW,EAAE,qBAAqB;QAClCC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,SAAS;QACrBC,GAAG,EAAE,UAAU;QACfC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,eAAe;QAC1BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,UAAU;QAClBC,IAAI,EAAE,eAAe;QACrBC,UAAU,EAAE,gBAAgB;QAC5BC,UAAU,EAAE,YAAY;QACxBC,WAAW,EAAE,WAAW;QACxBC,cAAc,EAAE,oBAAoB;QACpCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,YAAY;QACvBC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,sBAAsB;QACtCC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,0BAA0B;QAC/CC,aAAa,EAAE,aAAa;QAC5BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,sBAAsB;QACzC,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,qBAAqB;QAC7B,KAAK,EAAE,mBAAmB;QAC1B,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE,gCAAgC;QACxC,QAAQ,EAAE,iCAAiC;QAC3C,WAAW,EAAE,qCAAqC;QAClD,eAAe,EAAE,qCAAqC;QACtD,cAAc,EAAE,4BAA4B;QAC5C,aAAa,EAAE,mBAAmB;QAClC,eAAe,EAAE,qBAAqB;QACtC,cAAc,EAAE,mBAAmB;QACnC,aAAa,EAAE,6BAA6B;QAC5C,qBAAqB,EAAE,0BAA0B;QACjD,mBAAmB,EAAE,yBAAyB;QAC9C,SAAS,EAAE,sCAAsC;QACjD,QAAQ,EAAE,sCAAsC;QAChD,YAAY,EAAE,uDAAuD;QACrE,UAAU,EAAE,wDAAwD;QACpE,UAAU,EAAE,wDAAwD;QACpE,UAAU,EAAE,wDAAwD;QACpE,UAAU,EAAE,wDAAwD;QACpE,UAAU,EAAE,wDAAwD;QACpE,UAAU,EAAE,wDAAwD;QACpE,sBAAsB,EAAE,uBAAuB;QAC/C,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,kBAAkB;QACxBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-az-AZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "//Summernote WYSIWYG  editor ucun Azerbaycan dili fayli\n//Tercume etdi: RAMIL ALIYEV\n//Tarix: 20.07.2019\n//Baki Azerbaycan\n//Website: https://ramilaliyev.com\n\n//Azerbaijan language for Summernote WYSIWYG \n//Translated by: RAMIL ALIYEV\n//Date: 20.07.2019\n//Baku Azerbaijan\n//Website: https://ramilaliyev.com\n\n(function($) {\n  $.extend(true, $.summernote.lang, {\n    'az-AZ': {\n      font: {\n        bold: 'Qalın',\n        italic: 'Əyri',\n        underline: 'Altı xətli',\n        clear: 'Təmizlə',\n        height: '<PERSON><PERSON><PERSON><PERSON> hünd<PERSON>rl<PERSON>',\n        name: '<PERSON><PERSON><PERSON> Tipi',\n        strikethrough: '<PERSON>st<PERSON> xətli',\n        subscript: 'Alt simvol',\n        superscript: 'Üst simvol',\n        size: 'Yazı ölçüsü',\n      },\n      image: {\n        image: 'Şəkil',\n        insert: 'Şəkil əlavə et',\n        resizeFull: 'Original ölçü',\n        resizeHalf: '1/2 ölçü',\n        resizeQuarter: '1/4 ölçü',\n        floatLeft: 'Sola çək',\n        floatRight: 'Sağa çək',\n        floatNone: 'Sola-sağa çəkilməni ləğv et',\n        shapeRounded: 'Şəkil: yuvarlaq künç',\n        shapeCircle: 'Şəkil: Dairə',\n        shapeThumbnail: 'Şəkil: Thumbnail',\n        shapeNone: 'Şəkil: Yox',\n        dragImageHere: 'Bura sürüşdür',\n        dropImage: 'Şəkil və ya mətni buraxın',\n        selectFromFiles: 'Sənəd seçin',\n        maximumFileSize: 'Maksimum sənəd ölçüsü',\n        maximumFileSizeError: 'Maksimum sənəd ölçüsünü keçdiniz.',\n        url: 'Şəkil linki',\n        remove: 'Şəkli sil',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video linki',\n        insert: 'Video əlavə et',\n        url: 'Video linki?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion və ya Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Link əlavə et',\n        unlink: 'Linki sil',\n        edit: 'Linkə düzəliş et',\n        textToDisplay: 'Ekranda göstəriləcək link adı',\n        url: 'Link ünvanı?',\n        openInNewWindow: 'Yeni pəncərədə aç',\n      },\n      table: {\n        table: 'Cədvəl',\n        addRowAbove: 'Yuxarı sətir əlavə et',\n        addRowBelow: 'Aşağı sətir əlavə et',\n        addColLeft: 'Sola sütun əlavə et',\n        addColRight: 'Sağa sütun əlavə et',\n        delRow: 'Sətiri sil',\n        delCol: 'Sütunu sil',\n        delTable: 'Cədvəli sil',\n      },\n      hr: {\n        insert: 'Üfuqi xətt əlavə et',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'İstinad',\n        pre: 'Ön baxış',\n        h1: 'Başlıq 1',\n        h2: 'Başlıq 2',\n        h3: 'Başlıq 3',\n        h4: 'Başlıq 4',\n        h5: 'Başlıq 5',\n        h6: 'Başlıq 6',\n      },\n      lists: {\n        unordered: 'Nizamsız sıra',\n        ordered: 'Nizamlı sıra',\n      },\n      options: {\n        help: 'Kömək',\n        fullscreen: 'Tam ekran',\n        codeview: 'HTML Kodu',\n      },\n      paragraph: {\n        paragraph: 'Paraqraf',\n        outdent: 'Girintini artır',\n        indent: 'Girintini azalt',\n        left: 'Sola çək',\n        center: 'Ortaya çək',\n        right: 'Sağa çək',\n        justify: 'Sola və sağa çək',\n      },\n      color: {\n        recent: 'Son rənk',\n        more: 'Daha çox rənk',\n        background: 'Arxa fon rəngi',\n        foreground: 'Yazı rıngi',\n        transparent: 'Şəffaflıq',\n        setTransparent: 'Şəffaflığı nizamla',\n        reset: 'Sıfırla',\n        resetToDefault: 'Susyama görə sıfırla',\n      },\n      shortcut: {\n        shortcuts: 'Qısayollar',\n        close: 'Bağla',\n        textFormatting: 'Yazı formatlandırmaq',\n        action: 'Hadisə',\n        paragraphFormatting: 'Paraqraf formatlandırmaq',\n        documentStyle: 'Sənəd stili',\n        extraKeys: 'Əlavə',\n      },\n      help: {\n        'insertParagraph': 'Paraqraf əlavə etmək',\n        'undo': 'Son əmri geri alır',\n        'redo': 'Son əmri irəli alır',\n        'tab': 'Girintini artırır',\n        'untab': 'Girintini azaltır',\n        'bold': 'Qalın yazma stilini nizamlayır',\n        'italic': 'İtalik yazma stilini nizamlayır',\n        'underline': 'Altı xətli yazma stilini nizamlayır',\n        'strikethrough': 'Üstü xətli yazma stilini nizamlayır',\n        'removeFormat': 'Formatlandırmanı ləğv edir',\n        'justifyLeft': 'Yazını sola çəkir',\n        'justifyCenter': 'Yazını ortaya çəkir',\n        'justifyRight': 'Yazını sağa çəkir',\n        'justifyFull': 'Yazını hər iki tərəfə yazır',\n        'insertUnorderedList': 'Nizamsız sıra əlavə edir',\n        'insertOrderedList': 'Nizamlı sıra əlavə edir',\n        'outdent': 'Aktiv paraqrafın girintisini azaltır',\n        'indent': 'Aktiv paragrafın girintisini artırır',\n        'formatPara': 'Aktiv bloqun formatını paraqraf (p) olaraq dəyişdirir',\n        'formatH1': 'Aktiv bloqun formatını başlıq 1 (h1) olaraq dəyişdirir',\n        'formatH2': 'Aktiv bloqun formatını başlıq 2 (h2) olaraq dəyişdirir',\n        'formatH3': 'Aktiv bloqun formatını başlıq 3 (h3) olaraq dəyişdirir',\n        'formatH4': 'Aktiv bloqun formatını başlıq 4 (h4) olaraq dəyişdirir',\n        'formatH5': 'Aktiv bloqun formatını başlıq 5 (h5) olaraq dəyişdirir',\n        'formatH6': 'Aktiv bloqun formatını başlıq 6 (h6) olaraq dəyişdirir',\n        'insertHorizontalRule': 'Üfuqi xətt əlavə edir',\n        'linkDialog.show': 'Link parametrləri qutusunu göstərir',\n      },\n      history: {\n        undo: 'Əvvəlki vəziyyət',\n        redo: 'Sonrakı vəziyyət',\n      },\n      specialChar: {\n        specialChar: 'Xüsusi simvollar',\n        select: 'Xüsusi simvolları seçin',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}