{"version": 3, "file": "lang/summernote-es-EU.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,eAAe;QACtBC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,YAAY;QAClBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,MAAM,EAAE,oBAAoB;QAC5BC,UAAU,EAAE,4BAA4B;QACxCC,UAAU,EAAE,uBAAuB;QACnCC,aAAa,EAAE,2BAA2B;QAC1CC,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE,qBAAqB;QAChCC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,wBAAwB;QACvCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,6BAA6B;QAC9CC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,wBAAwB;QAC7BC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,SAAS,EAAE,kBAAkB;QAC7BpB,MAAM,EAAE,0BAA0B;QAClCgB,GAAG,EAAE,wBAAwB;QAC7BK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,QAAQ;QACdtB,MAAM,EAAE,qBAAqB;QAC7BuB,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,SAAS;QACfC,aAAa,EAAE,kBAAkB;QACjCT,GAAG,EAAE,wBAAwB;QAC7BU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,CAAC,EAAE,GAAG;QACNC,UAAU,EAAE,UAAU;QACtBC,GAAG,EAAE,OAAO;QACZC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE,cAAc;QAClBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,0BAA0B;QACrCC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,kBAAkB;QACxBC,MAAM,EAAE,eAAe;QACvBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE,SAAS;QACtBC,cAAc,EAAE,UAAU;QAC1BC,KAAK,EAAE,aAAa;QACpBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,oBAAoB;QACpCC,MAAM,EAAE,SAAS;QACjBC,mBAAmB,EAAE,wBAAwB;QAC7CC,aAAa,EAAE;MACjB,CAAC;MACDzB,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD0B,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-es-EU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'es-EU': {\n      font: {\n        bold: 'Lodia',\n        italic: 'Etzana',\n        underline: 'Azpimarrat<PERSON>',\n        clear: 'Estiloa kendu',\n        height: 'Lerro altuera',\n        name: 'Tipogra<PERSON>',\n        strikethrough: '<PERSON><PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Letren neurria',\n      },\n      image: {\n        image: 'I<PERSON><PERSON>',\n        insert: 'Irudi bat txertatu',\n        resizeFull: 'Jatorrizko neurrira aldatu',\n        resizeHalf: 'Neurria erdira aldatu',\n        resizeQuarter: 'Neurria laurdenera aldatu',\n        floatLeft: 'Ezkerrean kokatu',\n        floatRight: 'Eskuinean kokatu',\n        floatNone: 'Kokapenik ez ezarri',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Irudi bat ezarri hemen',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Zure fitxategi bat aukeratu',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Irudiaren URL helbidea',\n        remove: 'Remove Image',\n        original: 'Original',\n      },\n      video: {\n        video: 'Bideoa',\n        videoLink: 'Bideorako esteka',\n        insert: 'Bideo berri bat txertatu',\n        url: 'Bideoaren URL helbidea',\n        providers: '(YouTube, Vimeo, Vine, Instagram edo DailyMotion)',\n      },\n      link: {\n        link: 'Esteka',\n        insert: 'Esteka bat txertatu',\n        unlink: 'Esteka ezabatu',\n        edit: 'Editatu',\n        textToDisplay: 'Estekaren testua',\n        url: 'Estekaren URL helbidea',\n        openInNewWindow: 'Leiho berri batean ireki',\n      },\n      table: {\n        table: 'Taula',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Marra horizontala txertatu',\n      },\n      style: {\n        style: 'Estiloa',\n        p: 'p',\n        blockquote: 'Aipamena',\n        pre: 'Kodea',\n        h1: '1. izenburua',\n        h2: '2. izenburua',\n        h3: '3. izenburua',\n        h4: '4. izenburua',\n        h5: '5. izenburua',\n        h6: '6. izenburua',\n      },\n      lists: {\n        unordered: 'Ordenatu gabeko zerrenda',\n        ordered: 'Zerrenda ordenatua',\n      },\n      options: {\n        help: 'Laguntza',\n        fullscreen: 'Pantaila osoa',\n        codeview: 'Kodea ikusi',\n      },\n      paragraph: {\n        paragraph: 'Paragrafoa',\n        outdent: 'Koska txikiagoa',\n        indent: 'Koska handiagoa',\n        left: 'Ezkerrean kokatu',\n        center: 'Erdian kokatu',\n        right: 'Eskuinean kokatu',\n        justify: 'Justifikatu',\n      },\n      color: {\n        recent: 'Azken kolorea',\n        more: 'Kolore gehiago',\n        background: 'Atzeko planoa',\n        foreground: 'Aurreko planoa',\n        transparent: 'Gardena',\n        setTransparent: 'Gardendu',\n        reset: 'Lehengoratu',\n        resetToDefault: 'Berrezarri lehenetsia',\n      },\n      shortcut: {\n        shortcuts: 'Lasterbideak',\n        close: 'Itxi',\n        textFormatting: 'Testuaren formatua',\n        action: 'Ekintza',\n        paragraphFormatting: 'Paragrafoaren formatua',\n        documentStyle: 'Dokumentuaren estiloa',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Desegin',\n        redo: 'Berregin',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}