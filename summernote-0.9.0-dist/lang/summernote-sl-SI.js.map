{"version": 3, "file": "lang/summernote-sl-SI.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,QAAQ;QACdC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,4BAA4B;QACnCC,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,WAAW;QAC1BC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,WAAW;QACxBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,cAAc;QACtBC,UAAU,EAAE,2BAA2B;QACvCC,UAAU,EAAE,+BAA+B;QAC3CC,aAAa,EAAE,+BAA+B;QAC9CC,SAAS,EAAE,gBAAgB;QAC3BC,UAAU,EAAE,iBAAiB;QAC7BC,SAAS,EAAE,gBAAgB;QAC3BC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,2BAA2B;QAC5CC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,kBAAkB;QACvBC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,gBAAgB;QAC3BpB,MAAM,EAAE,cAAc;QACtBgB,GAAG,EAAE,mBAAmB;QACxBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,UAAU;QAChBtB,MAAM,EAAE,iBAAiB;QACzBuB,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,oBAAoB;QACnCT,GAAG,EAAE,UAAU;QACfU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,eAAe;QAC5BC,WAAW,EAAE,eAAe;QAC5BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,kBAAkB;QAC/BC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,eAAe;QACvBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,kBAAkB;QACrBC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,MAAM;QACXC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,gBAAgB;QAC3BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,qBAAqB;QACjCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,gBAAgB;QAC3BC,OAAO,EAAE,gBAAgB;QACzBC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,iBAAiB;QACzBC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,sBAAsB;QAC9BC,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,cAAc;QAC1BC,UAAU,EAAE,gBAAgB;QAC5BC,WAAW,EAAE,YAAY;QACzBC,cAAc,EAAE,YAAY;QAC5BC,KAAK,EAAE,WAAW;QAClBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,OAAO;QACdC,cAAc,EAAE,sBAAsB;QACtCC,MAAM,EAAE,SAAS;QACjBC,mBAAmB,EAAE,sBAAsB;QAC3CC,aAAa,EAAE,qBAAqB;QACpCC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,kBAAkB;QACrC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,yBAAyB;QACjC,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,kBAAkB;QAC1B,QAAQ,EAAE,oBAAoB;QAC9B,WAAW,EAAE,uBAAuB;QACpC,eAAe,EAAE,2BAA2B;QAC5C,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,gBAAgB;QAC/B,eAAe,EAAE,kBAAkB;QACnC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,gBAAgB;QAC/B,qBAAqB,EAAE,uBAAuB;QAC9C,mBAAmB,EAAE,qBAAqB;QAC1C,SAAS,EAAE,8BAA8B;QACzC,QAAQ,EAAE,6BAA6B;QACvC,YAAY,EAAE,sDAAsD;QACpE,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,UAAU,EAAE,sCAAsC;QAClD,sBAAsB,EAAE,wBAAwB;QAChD,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sl-SI.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'sl-SI': {\n      font: {\n        bold: '<PERSON><PERSON><PERSON><PERSON>',\n        italic: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        underline: 'Podčrtano',\n        clear: 'Počisti oblikovanje izbire',\n        height: '<PERSON><PERSON><PERSON>k med vrsticami',\n        name: '<PERSON><PERSON><PERSON>',\n        strikethrough: 'Preč<PERSON><PERSON>',\n        subscript: 'Podpisano',\n        superscript: 'Nadpisano',\n        size: 'Velikost pisave',\n      },\n      image: {\n        image: 'Slika',\n        insert: 'Vstavi sliko',\n        resizeFull: '<PERSON>zš<PERSON> na polno velikost',\n        resizeHalf: '<PERSON>zš<PERSON> na polovico velikosti',\n        resizeQuarter: '<PERSON><PERSON><PERSON><PERSON> na četrtino velikosti',\n        floatLeft: 'Leva poravnava',\n        floatRight: 'Desna poravnava',\n        floatNone: 'Brez poravnave',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Sem povlecite sliko',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Izberi sliko za nalaganje',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL naslov slike',\n        remove: 'Odstrani sliko',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video povezava',\n        insert: 'Vstavi video',\n        url: 'Povezava do videa',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ali Youku)',\n      },\n      link: {\n        link: 'Povezava',\n        insert: 'Vstavi povezavo',\n        unlink: 'Odstrani povezavo',\n        edit: 'Uredi',\n        textToDisplay: 'Prikazano besedilo',\n        url: 'Povezava',\n        openInNewWindow: 'Odpri v novem oknu',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Vstavi horizontalno črto',\n      },\n      style: {\n        style: 'Slogi',\n        p: 'Navadno besedilo',\n        blockquote: 'Citat',\n        pre: 'Koda',\n        h1: 'Naslov 1',\n        h2: 'Naslov 2',\n        h3: 'Naslov 3',\n        h4: 'Naslov 4',\n        h5: 'Naslov 5',\n        h6: 'Naslov 6',\n      },\n      lists: {\n        unordered: 'Označen seznam',\n        ordered: 'Oštevilčen seznam',\n      },\n      options: {\n        help: 'Pomoč',\n        fullscreen: 'Celozaslonski način',\n        codeview: 'Pregled HTML kode',\n      },\n      paragraph: {\n        paragraph: 'Slogi odstavka',\n        outdent: 'Zmanjšaj odmik',\n        indent: 'Povečaj odmik',\n        left: 'Leva poravnava',\n        center: 'Desna poravnava',\n        right: 'Sredinska poravnava',\n        justify: 'Obojestranska poravnava',\n      },\n      color: {\n        recent: 'Uporabi zadnjo barvo',\n        more: 'Več barv',\n        background: 'Barva ozadja',\n        foreground: 'Barva besedila',\n        transparent: 'Brez barve',\n        setTransparent: 'Brez barve',\n        reset: 'Ponastavi',\n        resetToDefault: 'Ponastavi na privzeto',\n      },\n      shortcut: {\n        shortcuts: 'Bljižnice',\n        close: 'Zapri',\n        textFormatting: 'Oblikovanje besedila',\n        action: 'Dejanja',\n        paragraphFormatting: 'Oblikovanje odstavka',\n        documentStyle: 'Oblikovanje naslova',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Razveljavi',\n        redo: 'Uveljavi',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}