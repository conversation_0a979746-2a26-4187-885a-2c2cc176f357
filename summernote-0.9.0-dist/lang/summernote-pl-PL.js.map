{"version": 3, "file": "lang/summernote-pl-PL.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAE,YAAY;QACpBC,SAAS,EAAE,cAAc;QACzBC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,UAAU;QAChBC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,cAAc;QACzBC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE,eAAe;QACvBC,UAAU,EAAE,uBAAuB;QACnCC,UAAU,EAAE,sBAAsB;QAClCC,aAAa,EAAE,sBAAsB;QACrCC,SAAS,EAAE,UAAU;QACrBC,UAAU,EAAE,WAAW;QACvBC,SAAS,EAAE,iBAAiB;QAC5BC,YAAY,EAAE,sBAAsB;QACpCC,WAAW,EAAE,gBAAgB;QAC7BC,cAAc,EAAE,oBAAoB;QACpCC,SAAS,EAAE,eAAe;QAC1BC,aAAa,EAAE,qCAAqC;QACpDC,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,iBAAiB;QAClCC,eAAe,EAAE,uBAAuB;QACxCC,oBAAoB,EAAE,qCAAqC;QAC3DC,GAAG,EAAE,mBAAmB;QACxBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,aAAa;QACxBpB,MAAM,EAAE,aAAa;QACrBgB,GAAG,EAAE,aAAa;QAClBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,UAAU;QAChBtB,MAAM,EAAE,gBAAgB;QACxBuB,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE,uBAAuB;QACtCT,GAAG,EAAE,oDAAoD;QACzDU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,sBAAsB;QACnCC,WAAW,EAAE,sBAAsB;QACnCC,UAAU,EAAE,wBAAwB;QACpCC,WAAW,EAAE,yBAAyB;QACtCC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,UAAU;QACbC,UAAU,EAAE,OAAO;QACnBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE,YAAY;QAChBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,oBAAoB;QAC/BC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,aAAa;QACzBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,kBAAkB;QAC3BC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,mBAAmB;QACzBC,MAAM,EAAE,oBAAoB;QAC5BC,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,gBAAgB;QACtBC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,eAAe;QAC/BC,KAAK,EAAE,UAAU;QACjBC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,qBAAqB;QAChCC,KAAK,EAAE,SAAS;QAChBC,cAAc,EAAE,qBAAqB;QACrCC,MAAM,EAAE,OAAO;QACfC,mBAAmB,EAAE,sBAAsB;QAC3CC,aAAa,EAAE,gBAAgB;QAC/BC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,gBAAgB;QACnC,MAAM,EAAE,4BAA4B;QACpC,MAAM,EAAE,8BAA8B;QACtC,KAAK,EAAE,WAAW;QAClB,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE,aAAa;QACrB,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE,cAAc;QAC3B,eAAe,EAAE,eAAe;QAChC,cAAc,EAAE,mBAAmB;QACnC,aAAa,EAAE,mBAAmB;QAClC,eAAe,EAAE,oBAAoB;QACrC,cAAc,EAAE,oBAAoB;QACpC,aAAa,EAAE,cAAc;QAC7B,qBAAqB,EAAE,qBAAqB;QAC5C,mBAAmB,EAAE,oBAAoB;QACzC,SAAS,EAAE,4BAA4B;QACvC,QAAQ,EAAE,2BAA2B;QACrC,YAAY,EAAE,yCAAyC;QACvD,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,UAAU,EAAE,2BAA2B;QACvC,sBAAsB,EAAE,qBAAqB;QAC7C,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pl-PL.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'pl-PL': {\n      font: {\n        bold: 'Pogrubienie',\n        italic: 'Pochy<PERSON>ie',\n        underline: 'Podkreślenie',\n        clear: '<PERSON><PERSON><PERSON> formatowanie',\n        height: '<PERSON>linia',\n        name: '<PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Prz<PERSON>reślenie',\n        subscript: 'Indeks dolny',\n        superscript: 'Indeks górny',\n        size: 'Rozmiar',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Wstaw grafikę',\n        resizeFull: '<PERSON>mie<PERSON> rozmiar na 100%',\n        resizeHalf: 'Zmień rozmiar na 50%',\n        resizeQuarter: 'Zmień rozmiar na 25%',\n        floatLeft: 'Do lewej',\n        floatRight: 'Do prawej',\n        floatNone: 'Równo z tekstem',\n        shapeRounded: 'Kształt: zaokrąglone',\n        shapeCircle: 'Kształt: okrąg',\n        shapeThumbnail: 'Kształt: miniatura',\n        shapeNone: 'Kształt: brak',\n        dragImageHere: 'Przec<PERSON><PERSON><PERSON>j grafikę lub tekst tutaj',\n        dropImage: 'Przeciągnij grafikę lub tekst',\n        selectFromFiles: 'Wybierz z dysku',\n        maximumFileSize: 'Limit wielkości pliku',\n        maximumFileSizeError: 'Przekroczono limit wielkości pliku.',\n        url: 'Adres URL grafiki',\n        remove: 'Usuń grafikę',\n        original: 'Oryginał',\n      },\n      video: {\n        video: 'Wideo',\n        videoLink: 'Adres wideo',\n        insert: 'Wstaw wideo',\n        url: 'Adres wideo',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion lub Youku)',\n      },\n      link: {\n        link: 'Odnośnik',\n        insert: 'Wstaw odnośnik',\n        unlink: 'Usuń odnośnik',\n        edit: 'Edytuj',\n        textToDisplay: 'Tekst do wyświetlenia',\n        url: 'Na jaki adres URL powinien przenosić ten odnośnik?',\n        openInNewWindow: 'Otwórz w nowym oknie',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Dodaj wiersz powyżej',\n        addRowBelow: 'Dodaj wiersz poniżej',\n        addColLeft: 'Dodaj kolumnę po lewej',\n        addColRight: 'Dodaj kolumnę po prawej',\n        delRow: 'Usuń wiersz',\n        delCol: 'Usuń kolumnę',\n        delTable: 'Usuń tabelę',\n      },\n      hr: {\n        insert: 'Wstaw poziomą linię',\n      },\n      style: {\n        style: 'Styl',\n        p: 'Paragraf',\n        blockquote: 'Cytat',\n        pre: 'Kod',\n        h1: 'Nagłówek 1',\n        h2: 'Nagłówek 2',\n        h3: 'Nagłówek 3',\n        h4: 'Nagłówek 4',\n        h5: 'Nagłówek 5',\n        h6: 'Nagłówek 6',\n      },\n      lists: {\n        unordered: 'Lista wypunktowana',\n        ordered: 'Lista numerowana',\n      },\n      options: {\n        help: 'Pomoc',\n        fullscreen: 'Pełny ekran',\n        codeview: 'Źródło',\n      },\n      paragraph: {\n        paragraph: 'Akapit',\n        outdent: 'Zmniejsz wcięcie',\n        indent: 'Zwiększ wcięcie',\n        left: 'Wyrównaj do lewej',\n        center: 'Wyrównaj do środka',\n        right: 'Wyrównaj do prawej',\n        justify: 'Wyrównaj do lewej i prawej',\n      },\n      color: {\n        recent: 'Ostani kolor',\n        more: 'Więcej kolorów',\n        background: 'Tło',\n        foreground: 'Czcionka',\n        transparent: 'Przeźroczysty',\n        setTransparent: 'Przeźroczyste',\n        reset: 'Zresetuj',\n        resetToDefault: 'Domyślne',\n      },\n      shortcut: {\n        shortcuts: 'Skróty klawiaturowe',\n        close: 'Zamknij',\n        textFormatting: 'Formatowanie tekstu',\n        action: 'Akcja',\n        paragraphFormatting: 'Formatowanie akapitu',\n        documentStyle: 'Styl dokumentu',\n        extraKeys: 'Dodatkowe klawisze',\n      },\n      help: {\n        'insertParagraph': 'Wstaw paragraf',\n        'undo': 'Cofnij poprzednią operację',\n        'redo': 'Przywróć poprzednią operację',\n        'tab': 'Tabulacja',\n        'untab': 'Usuń tabulację',\n        'bold': 'Pogrubienie',\n        'italic': 'Kursywa',\n        'underline': 'Podkreślenie',\n        'strikethrough': 'Przekreślenie',\n        'removeFormat': 'Usuń formatowanie',\n        'justifyLeft': 'Wyrównaj do lewej',\n        'justifyCenter': 'Wyrównaj do środka',\n        'justifyRight': 'Wyrównaj do prawej',\n        'justifyFull': 'Justyfikacja',\n        'insertUnorderedList': 'Nienumerowana lista',\n        'insertOrderedList': 'Wypunktowana lista',\n        'outdent': 'Zmniejsz wcięcie paragrafu',\n        'indent': 'Zwiększ wcięcie paragrafu',\n        'formatPara': 'Zamień format bloku na paragraf (tag P)',\n        'formatH1': 'Zamień format bloku na H1',\n        'formatH2': 'Zamień format bloku na H2',\n        'formatH3': 'Zamień format bloku na H3',\n        'formatH4': 'Zamień format bloku na H4',\n        'formatH5': 'Zamień format bloku na H5',\n        'formatH6': 'Zamień format bloku na H6',\n        'insertHorizontalRule': 'Wstaw poziomą linię',\n        'linkDialog.show': 'Pokaż okno linkowania',\n      },\n      history: {\n        undo: 'Cofnij',\n        redo: 'Ponów',\n      },\n      specialChar: {\n        specialChar: 'ZNAKI SPECJALNE',\n        select: 'Wybierz Znak specjalny',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}