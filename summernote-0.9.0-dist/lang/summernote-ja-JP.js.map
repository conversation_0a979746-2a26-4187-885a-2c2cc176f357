{"version": 3, "file": "lang/summernote-ja-JP.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE,WAAW;QACtBC,WAAW,EAAE,aAAa;QAC1BC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,gBAAgB;QAC9BC,WAAW,EAAE,eAAe;QAC5BC,cAAc,EAAE,kBAAkB;QAClCC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,kBAAkB;QACjCC,SAAS,EAAE,oBAAoB;QAC/BC,eAAe,EAAE,WAAW;QAC5BC,eAAe,EAAE,mBAAmB;QACpCC,oBAAoB,EAAE,6BAA6B;QACnDC,GAAG,EAAE,cAAc;QACnBC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,OAAO;QAClBpB,MAAM,EAAE,MAAM;QACdgB,GAAG,EAAE,QAAQ;QACbK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,KAAK;QACXtB,MAAM,EAAE,OAAO;QACfuB,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAE,QAAQ;QACvBT,GAAG,EAAE,cAAc;QACnBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,WAAW,EAAE,QAAQ;QACrBC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,QAAQ;QACrBC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,CAAC,EAAE,IAAI;QACPC,UAAU,EAAE,IAAI;QAChBC,GAAG,EAAE,KAAK;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,KAAK;QACXC,UAAU,EAAE,SAAS;QACrBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,KAAK;QACbC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,OAAO;QACvBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,KAAK;QACZC,cAAc,EAAE,UAAU;QAC1BC,MAAM,EAAE,OAAO;QACfC,mBAAmB,EAAE,UAAU;QAC/BC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ,iBAAiB,EAAE,MAAM;QACzB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,cAAc;QACtB,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,IAAI;QACjB,eAAe,EAAE,OAAO;QACxB,cAAc,EAAE,OAAO;QACvB,aAAa,EAAE,KAAK;QACpB,eAAe,EAAE,OAAO;QACxB,cAAc,EAAE,KAAK;QACrB,aAAa,EAAE,QAAQ;QACvB,qBAAqB,EAAE,SAAS;QAChC,mBAAmB,EAAE,UAAU;QAC/B,SAAS,EAAE,gBAAgB;QAC3B,QAAQ,EAAE,cAAc;QACxB,YAAY,EAAE,aAAa;QAC3B,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,UAAU,EAAE,MAAM;QAClB,sBAAsB,EAAE,iBAAiB;QACzC,iBAAiB,EAAE;MACrB,CAAC;MACD2B,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,oBAAoB;QACjCC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ja-JP.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ja-JP': {\n      font: {\n        bold: '太字',\n        italic: '斜体',\n        underline: '下線',\n        clear: 'クリア',\n        height: '文字高',\n        name: 'フォント',\n        strikethrough: '取り消し線',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: '大きさ',\n      },\n      image: {\n        image: '画像',\n        insert: '画像挿入',\n        resizeFull: '最大化',\n        resizeHalf: '1/2',\n        resizeQuarter: '1/4',\n        floatLeft: '左寄せ',\n        floatRight: '右寄せ',\n        floatNone: '寄せ解除',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'ここに画像をドラッグしてください',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: '画像ファイルを選ぶ',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URLから画像を挿入する',\n        remove: '画像を削除する',\n        original: 'Original',\n      },\n      video: {\n        video: '動画',\n        videoLink: '動画リンク',\n        insert: '動画挿入',\n        url: '動画のURL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, Youku)',\n      },\n      link: {\n        link: 'リンク',\n        insert: 'リンク挿入',\n        unlink: 'リンク解除',\n        edit: '編集',\n        textToDisplay: 'リンク文字列',\n        url: 'URLを入力してください',\n        openInNewWindow: '新しいウィンドウで開く',\n      },\n      table: {\n        table: 'テーブル',\n        addRowAbove: '行を上に追加',\n        addRowBelow: '行を下に追加',\n        addColLeft: '列を左に追加',\n        addColRight: '列を右に追加',\n        delRow: '行を削除',\n        delCol: '列を削除',\n        delTable: 'テーブルを削除',\n      },\n      hr: {\n        insert: '水平線の挿入',\n      },\n      style: {\n        style: 'スタイル',\n        p: '標準',\n        blockquote: '引用',\n        pre: 'コード',\n        h1: '見出し1',\n        h2: '見出し2',\n        h3: '見出し3',\n        h4: '見出し4',\n        h5: '見出し5',\n        h6: '見出し6',\n      },\n      lists: {\n        unordered: '通常リスト',\n        ordered: '番号リスト',\n      },\n      options: {\n        help: 'ヘルプ',\n        fullscreen: 'フルスクリーン',\n        codeview: 'コード表示',\n      },\n      paragraph: {\n        paragraph: '文章',\n        outdent: '字上げ',\n        indent: '字下げ',\n        left: '左寄せ',\n        center: '中央寄せ',\n        right: '右寄せ',\n        justify: '均等割付',\n      },\n      color: {\n        recent: '現在の色',\n        more: 'もっと見る',\n        background: '背景色',\n        foreground: '文字色',\n        transparent: '透明',\n        setTransparent: '透明にする',\n        reset: '標準',\n        resetToDefault: '標準に戻す',\n      },\n      shortcut: {\n        shortcuts: 'ショートカット',\n        close: '閉じる',\n        textFormatting: '文字フォーマット',\n        action: 'アクション',\n        paragraphFormatting: '文章フォーマット',\n        documentStyle: 'ドキュメント形式',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': '改行挿入',\n        'undo': '一旦、行った操作を戻す',\n        'redo': '最後のコマンドをやり直す',\n        'tab': 'Tab',\n        'untab': 'タブ戻し',\n        'bold': '太文字',\n        'italic': '斜体',\n        'underline': '下線',\n        'strikethrough': '取り消し線',\n        'removeFormat': '装飾を戻す',\n        'justifyLeft': '左寄せ',\n        'justifyCenter': '真ん中寄せ',\n        'justifyRight': '右寄せ',\n        'justifyFull': 'すべてを整列',\n        'insertUnorderedList': '行頭に●を挿入',\n        'insertOrderedList': '行頭に番号を挿入',\n        'outdent': '字下げを戻す（アウトデント）',\n        'indent': '字下げする（インデント）',\n        'formatPara': '段落(P tag)指定',\n        'formatH1': 'H1指定',\n        'formatH2': 'H2指定',\n        'formatH3': 'H3指定',\n        'formatH4': 'H4指定',\n        'formatH5': 'H5指定',\n        'formatH6': 'H6指定',\n        'insertHorizontalRule': '&lt;hr /&gt;を挿入',\n        'linkDialog.show': 'リンク挿入',\n      },\n      history: {\n        undo: '元に戻す',\n        redo: 'やり直す',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}