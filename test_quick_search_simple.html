<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quick Search Simple Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .search-container {
            margin: 20px 0;
        }
        
        #main-search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .typing-effect {
            border-color: #fb923c !important;
            box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.15) !important;
        }
        
        .quick-search-tag {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-search-tag:hover {
            background: #e0e0e0;
            transform: translateY(-1px);
        }
        
        #search-suggestions {
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            margin-top: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        #search-suggestions.hidden {
            display: none;
        }
        
        .search-suggestion-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .search-suggestion-item:hover {
            background: #f5f5f5;
        }
        
        .test-log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .test-case {
            background: #f0f8ff;
            border: 1px solid #cce7ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .status {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 3px;
            margin-left: 10px;
        }
        
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Quick Search Simple Fix</h1>
    
    <div class="instructions">
        <h3>Test Cases:</h3>
        <div class="test-case">
            <strong>Test 1:</strong> Click Quick Search Tag → Suggestions không hiển thị
            <span class="status" id="test1-status">Chưa test</span>
        </div>
        <div class="test-case">
            <strong>Test 2:</strong> Click Quick Search Tag → Ấn Enter → Suggestions không hiển thị
            <span class="status" id="test2-status">Chưa test</span>
        </div>
        <div class="test-case">
            <strong>Test 3:</strong> Click Quick Search Tag → Gõ thêm ký tự → Suggestions hiển thị
            <span class="status" id="test3-status">Chưa test</span>
        </div>
    </div>
    
    <div class="search-container">
        <input type="text" id="main-search-input" placeholder="Nhập từ khóa tìm kiếm...">
        <div id="search-suggestions" class="hidden"></div>
    </div>
    
    <div>
        <span>Tìm kiếm phổ biến:</span>
        <span class="quick-search-tag" data-keyword="Sofa">Sofa</span>
        <span class="quick-search-tag" data-keyword="Bàn ăn">Bàn ăn</span>
        <span class="quick-search-tag" data-keyword="Giường ngủ">Giường ngủ</span>
    </div>
    
    <div class="test-log" id="test-log">
        <strong>Test Log:</strong><br>
    </div>

    <script>
        // Mock BASE_URL
        window.BASE_URL = '';
        
        // Test logging
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Mock suggestions data
        const mockSuggestions = [
            { name: 'Sofa da thật', category: 'Sofa', id: 1 },
            { name: 'Sofa vải', category: 'Sofa', id: 2 },
            { name: 'Bàn ăn gỗ', category: 'Bàn ăn', id: 3 }
        ];
        
        // Mock fetch function
        window.fetch = function(url) {
            log(`🔍 Fetch suggestions called: ${url}`);
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        json: () => Promise.resolve({
                            suggestions: mockSuggestions
                        })
                    });
                }, 300);
            });
        };
        
        // Mock AJAX functions
        window.ajaxFilterActive = false;
        
        log('✅ Test page loaded');
    </script>
    
    <!-- Include the enhanced search script -->
    <script src="assets/js/enhanced-search.js"></script>
    
    <script>
        // Test monitoring
        let testState = {
            quickSearchClicked: false,
            enterPressed: false,
            manualTyping: false
        };
        
        // Monitor suggestions visibility
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.id === 'search-suggestions') {
                        const isHidden = target.classList.contains('hidden');
                        if (!isHidden) {
                            log('⚠️ Suggestions box hiển thị!');
                            
                            // Check test cases
                            if (testState.quickSearchClicked && !testState.manualTyping) {
                                document.getElementById('test1-status').textContent = 'FAIL';
                                document.getElementById('test1-status').className = 'status fail';
                            }
                            if (testState.quickSearchClicked && testState.enterPressed && !testState.manualTyping) {
                                document.getElementById('test2-status').textContent = 'FAIL';
                                document.getElementById('test2-status').className = 'status fail';
                            }
                        } else {
                            log('✅ Suggestions box ẩn');
                        }
                    }
                }
            });
        });
        
        observer.observe(document.getElementById('search-suggestions'), {
            attributes: true,
            attributeFilter: ['class']
        });
        
        log('🔍 Test monitoring initialized');
    </script>
</body>
</html>
