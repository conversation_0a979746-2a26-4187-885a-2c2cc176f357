# Nội Thất <PERSON> - Website B<PERSON> Đồ Nội Thất

Website bán đồ nội thất với các tính năng cơ bản như đăng ký, đ<PERSON><PERSON> nh<PERSON>, quản lý sản phẩm, giỏ hàng và thanh toán.

## Yêu cầu hệ thống

- PHP 7.4 trở lên
- MySQL 5.7 trở lên
- XAMPP, WAMP, LAMP hoặc MAMP

## Cài đặt

1. <PERSON><PERSON> hoặc tải xuống mã nguồn và đặt vào thư mục `htdocs` của XAMPP (hoặc thư mục tương ứng của web server bạn đang sử dụng).

2. Tạ<PERSON> cơ sở dữ liệu MySQL với tên `noithatbangvu`.

3. Import file `database.sql` vào cơ sở dữ liệu vừa tạo.

4. Cấu hình kết nối cơ sở dữ liệu trong file `config/database.php`.

5. <PERSON><PERSON><PERSON> hình URL cơ sở trong file `config/config.php`.

6. Truy cập website thông qua URL: `http://localhost/noithatbangvu`.

## Cấu trúc thư mục

- `admin/` - Phần quản trị
- `ajax/` - Xử lý các yêu cầu AJAX
- `assets/` - Chứa CSS, JS, hình ảnh
- `config/` - Cấu hình ứng dụng
- `includes/` - Chứa các file PHP được include
- `partials/` - Chứa các thành phần giao diện tái sử dụng
- `uploads/` - Chứa hình ảnh sản phẩm được tải lên

## Tài khoản mặc định

- **Admin**
  - Username: admin
  - Password: password

## Tính năng

### Người dùng
- Đăng ký, đăng nhập, đăng xuất
- Xem danh sách sản phẩm
- Xem chi tiết sản phẩm
- Tìm kiếm sản phẩm
- Thêm sản phẩm vào giỏ hàng
- Quản lý giỏ hàng
- Đặt hàng
- Xem lịch sử đơn hàng

### Quản trị viên
- Quản lý danh mục
- Quản lý sản phẩm
- Quản lý đơn hàng
- Quản lý người dùng

## Công nghệ sử dụng

- PHP
- MySQL
- HTML, CSS, JavaScript
- Tailwind CSS
- jQuery
- Font Awesome

## Tác giả

- Nội Thất Băng Vũ

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết.
