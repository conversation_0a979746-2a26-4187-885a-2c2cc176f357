<?php
// Test file để debug giỏ hàng
require_once 'includes/init.php';

echo "<h1>Debug Giỏ Hàng</h1>";

// L<PERSON>y dữ liệu giỏ hàng
$cart_items = get_cart_items();

echo "<h2>Thông tin giỏ hàng:</h2>";
echo "<p>Số lượng items: " . count($cart_items) . "</p>";

if (!empty($cart_items)) {
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<th>Product ID</th>";
    echo "<th>Tên sản phẩm</th>";
    echo "<th>Slug</th>";
    echo "<th>URL được tạo</th>";
    echo "<th>Test Link</th>";
    echo "</tr>";
    
    foreach ($cart_items as $item) {
        $slug = isset($item['slug']) ? $item['slug'] : '';
        $url = !empty($slug) ? get_product_url($slug) : BASE_URL . '/product.php?id=' . $item['product_id'];
        
        echo "<tr>";
        echo "<td>" . $item['product_id'] . "</td>";
        echo "<td>" . htmlspecialchars($item['name']) . "</td>";
        echo "<td>" . htmlspecialchars($slug) . "</td>";
        echo "<td><code>" . $url . "</code></td>";
        echo "<td><a href='" . $url . "' target='_blank'>Test →</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Raw Cart Data:</h3>";
    echo "<pre>";
    print_r($cart_items);
    echo "</pre>";
} else {
    echo "<p>Giỏ hàng trống.</p>";
    echo "<p><a href='" . BASE_URL . "/products.php'>Thêm sản phẩm vào giỏ hàng</a></p>";
}

echo "<h2>Test Functions:</h2>";
echo "<p>BASE_URL: <code>" . BASE_URL . "</code></p>";
echo "<p>get_product_url('test-slug'): <code>" . get_product_url('test-slug') . "</code></p>";

// Test với một sản phẩm thật
$products = get_products(1);
if (!empty($products)) {
    $test_product = $products[0];
    echo "<p>Test với sản phẩm thật:</p>";
    echo "<p>Product: " . htmlspecialchars($test_product['name']) . "</p>";
    echo "<p>Slug: " . htmlspecialchars($test_product['slug']) . "</p>";
    echo "<p>get_product_url('" . $test_product['slug'] . "'): <code>" . get_product_url($test_product['slug']) . "</code></p>";
}
?>
