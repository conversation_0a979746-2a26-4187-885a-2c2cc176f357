-- <PERSON><PERSON><PERSON> c<PERSON> sở dữ liệu
CREATE DATABASE IF NOT EXISTS noithatbangvu;
USE noithatbangvu;

-- Tạo bảng users
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('admin', 'customer') DEFAULT 'customer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tạo bảng categories
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image VARCHAR(255),
    parent_id INT DEFAULT NULL,
    status TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Tạo bảng products
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    sku VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    content LONGTEXT,
    price DECIMAL(15, 2) NOT NULL,
    sale_price DECIMAL(15, 2),
    image VARCHAR(255),
    gallery TEXT,
    quantity INT DEFAULT 0,
    status TINYINT(1) DEFAULT 1,
    featured TINYINT(1) DEFAULT 0,
    rating TINYINT(1) DEFAULT 5,
    sold INT DEFAULT 0,
    views INT DEFAULT 0,
    flash_sale TINYINT(1) DEFAULT 0,
    material VARCHAR(255),
    dimensions VARCHAR(255),
    color VARCHAR(255),
    price_type ENUM('fixed', 'contact') DEFAULT 'fixed',
    size_options JSON,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Tạo bảng orders
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    note TEXT,
    total DECIMAL(15, 2) NOT NULL,
    status ENUM('pending', 'processing', 'shipping', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tạo bảng order_items
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    price DECIMAL(15, 2) NOT NULL,
    quantity INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Tạo dữ liệu mẫu cho bảng users
INSERT INTO users (username, email, password, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'admin');

-- Tạo dữ liệu mẫu cho bảng categories
INSERT INTO categories (name, slug, description) VALUES
('Phòng khách', 'phong-khach', 'Sản phẩm nội thất phòng khách'),
('Phòng ngủ', 'phong-ngu', 'Sản phẩm nội thất phòng ngủ'),
('Phòng bếp', 'phong-bep', 'Sản phẩm nội thất phòng bếp'),
('Phòng làm việc', 'phong-lam-viec', 'Sản phẩm nội thất phòng làm việc');

-- Tạo dữ liệu mẫu cho bảng products
INSERT INTO products (category_id, name, slug, description, price, image, quantity) VALUES
(1, 'Sofa da cao cấp', 'sofa-da-cao-cap', 'Sofa da cao cấp nhập khẩu từ Ý', 15000000, 'sofa-da.jpg', 10),
(1, 'Bàn trà gỗ sồi', 'ban-tra-go-soi', 'Bàn trà gỗ sồi tự nhiên', 5000000, 'ban-tra.jpg', 15),
(2, 'Giường ngủ gỗ óc chó', 'giuong-ngu-go-oc-cho', 'Giường ngủ gỗ óc chó cao cấp', 20000000, 'giuong-ngu.jpg', 5),
(3, 'Tủ bếp hiện đại', 'tu-bep-hien-dai', 'Tủ bếp thiết kế hiện đại', 25000000, 'tu-bep.jpg', 3);
