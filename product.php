<?php
// Include header
include_once 'partials/header.php';

// Lấy slug hoặc id từ URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Nếu không có slug và không có id, chuyển hướng về trang sản phẩm
if (empty($slug) && empty($id)) {
    redirect(BASE_URL . '/products.php');
}

// Lấy thông tin sản phẩm
if (!empty($slug)) {
    // Ưu tiên sử dụng slug nếu có
    $product = get_product_by_slug($slug);
} else {
    // Sử dụng id nếu không có slug
    $product = get_product_by_id($id);
}

// Nếu không tìm thấy sản phẩm, chuyển hướng về trang sản phẩm
if (!$product) {
    set_flash_message('error', 'Sản phẩm không tồn tại.');
    redirect(BASE_URL . '/products.php');
}

// Cập nhật lượt xem sản phẩm
update_product_views($product['id']);

// Thiết lập tiêu đề trang
$page_title = $product['name'];
$page_description = $product['meta_description'] ? $product['meta_description'] : $product['description'];

// Lấy sản phẩm liên quan
$related_products = get_products(4, 0, $product['category_id']);

// Lấy tham số sắp xếp và trang từ URL
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';

// Xử lý tham số phân trang riêng biệt cho tab đánh giá và bình luận
$ratings_page = isset($_GET['ratings_page']) ? intval($_GET['ratings_page']) : 1;
$comments_page = isset($_GET['comments_page']) ? intval($_GET['comments_page']) : 1;

// Sử dụng tham số page cũ nếu có (để tương thích ngược)
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
if ($page > 1) {
    // Nếu có tham số page cũ, sử dụng nó cho cả hai tab
    if (!isset($_GET['ratings_page'])) $ratings_page = $page;
    if (!isset($_GET['comments_page'])) $comments_page = $page;
}

// Xác định tab hiện tại
$current_tab = isset($_GET['tab']) && $_GET['tab'] === 'comments' ? 'comments' : 'ratings';

// Lấy đánh giá của sản phẩm dựa vào tab hiện tại
$page_to_use = $current_tab === 'comments' ? $comments_page : $ratings_page;
$filter_type = $current_tab === 'comments' ? 'comments' : 'ratings';
$reviews_data = get_product_reviews($product['id'], $page_to_use, 5, $sort, $filter_type);
$reviews = $reviews_data['reviews'];
$total_reviews = $reviews_data['total_reviews'];
$average_rating = $reviews_data['average_rating'];
$rating_stats = $reviews_data['rating_stats'];
$rated_reviews_count = $reviews_data['rated_reviews_count'];

// Kiểm tra người dùng đã mua sản phẩm chưa và có thể đánh giá không
$can_rate = is_logged_in() ? has_purchased_product($_SESSION['user_id'], $product['id']) : false;

// Kiểm tra người dùng đã mua sản phẩm chưa (không quan tâm đã đánh giá hay chưa)
$has_purchased = is_logged_in() ? has_purchased_product_simple($_SESSION['user_id'], $product['id']) : false;
?>

<!-- Breadcrumb -->
<div class="bg-gray-100 py-3">
    <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-gray-600">
            <a href="<?php echo BASE_URL; ?>" class="hover:text-blue-500">Trang chủ</a>
            <span class="mx-2">/</span>
            <?php
            // Lấy thông tin danh mục để có slug chính xác
            $category = get_category_by_id($product['category_id']);
            $category_slug = $category ? $category['slug'] : '';
            ?>
            <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category_slug; ?>"
                class="hover:text-blue-500"><?php echo $product['category_name']; ?></a>
            <span class="mx-2">/</span>
            <span class="text-gray-800"><?php echo $product['name']; ?></span>
        </div>
    </div>
</div>

<!-- Product Detail -->
<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Product Images and Info -->
        <div class="product-detail-container">
            <!-- Product Images - Left Column -->
            <div class="product-images-container">
                <div class="sticky-product-images">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="product-gallery-container">
                            <!-- Main Image with Slider -->
                            <div class="product-main-image-container relative mb-4 overflow-hidden rounded">
                                <div class="product-main-slider relative" style="padding-bottom: 100%;">
                                    <div
                                        class="main-slider-track absolute top-0 left-0 w-full h-full flex transition-transform duration-300 ease-out">
                                        <?php if (!empty($product['gallery'])): ?>
                                        <?php
                                            $all_images = explode(',', $product['gallery']);
                                            array_unshift($all_images, $product['image']);
                                            foreach ($all_images as $index => $img):
                                            ?>
                                        <div class="main-slide flex-shrink-0 w-full h-full">
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $img; ?>"
                                                alt="<?php echo $product['name']; ?> - Image <?php echo $index + 1; ?>"
                                                class="w-full h-full object-cover">
                                        </div>
                                        <?php endforeach; ?>
                                        <?php else: ?>
                                        <div class="main-slide flex-shrink-0 w-full h-full">
                                            <?php if ($product['image']): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                                alt="<?php echo $product['name']; ?>"
                                                class="w-full h-full object-cover">
                                            <?php else: ?>
                                            <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-image text-gray-500 text-4xl"></i>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Navigation Buttons -->
                                <?php
                                $has_multiple_images = !empty($product['gallery']) && count(explode(',', $product['gallery'])) > 0;
                                if ($has_multiple_images):
                                ?>
                                <button type="button"
                                    class="gallery-nav-btn gallery-prev absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 hover:bg-opacity-100 rounded-full w-10 h-10 flex items-center justify-center shadow-md transition-all duration-200 focus:outline-none"
                                    aria-label="Previous image">
                                    <i class="fas fa-chevron-left text-gray-700"></i>
                                </button>
                                <button type="button"
                                    class="gallery-nav-btn gallery-next absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 hover:bg-opacity-100 rounded-full w-10 h-10 flex items-center justify-center shadow-md transition-all duration-200 focus:outline-none"
                                    aria-label="Next image">
                                    <i class="fas fa-chevron-right text-gray-700"></i>
                                </button>
                                <?php endif; ?>
                            </div>

                            <!-- Thumbnails -->
                            <?php if (!empty($product['gallery'])): ?>
                            <?php
                            $gallery_images = explode(',', $product['gallery']);
                            // Add main image to the beginning of gallery for navigation
                            array_unshift($gallery_images, $product['image']);
                            $total_images = count($gallery_images);
                            $display_count = min(4, $total_images);
                            $remaining_count = $total_images - 4;
                            ?>
                            <div class="product-thumbnails-container">
                                <div class="grid grid-cols-4 gap-2">
                                    <?php for ($i = 0; $i < $display_count; $i++): ?>
                                    <?php if ($i < 3 || $total_images <= 4): ?>
                                    <!-- Regular thumbnail -->
                                    <div class="product-thumbnail cursor-pointer relative"
                                        data-image="<?php echo BASE_URL; ?>/uploads/products/<?php echo $gallery_images[$i]; ?>"
                                        data-index="<?php echo $i; ?>">
                                        <div class="relative pb-[100%]">
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $gallery_images[$i]; ?>"
                                                alt="<?php echo $product['name']; ?> - Image <?php echo $i + 1; ?>"
                                                class="absolute top-0 left-0 w-full h-full object-cover rounded border-2 transition-all duration-200 border-transparent hover:border-gray-300">
                                        </div>
                                    </div>
                                    <?php elseif ($i === 3 && $total_images > 4): ?>
                                    <!-- "View more" thumbnail -->
                                    <div class="view-more-thumbnail cursor-pointer relative"
                                        data-total="<?php echo $total_images; ?>">
                                        <div class="relative pb-[100%]">
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $gallery_images[3]; ?>"
                                                alt="<?php echo $product['name']; ?> - More images"
                                                class="absolute top-0 left-0 w-full h-full object-cover rounded border-2 border-transparent hover:border-gray-300 transition-all duration-200 opacity-50">
                                            <div
                                                class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 rounded">
                                                <div class="text-white text-center">
                                                    <i class="fas fa-images text-xl mb-1"></i>
                                                    <p class="text-sm font-medium">+<?php echo $remaining_count; ?> ảnh
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                            </div>

                            <!-- Full-screen Gallery Viewer (Hidden by default) -->
                            <div id="fullscreen-gallery"
                                class="fullscreen-gallery fixed inset-0 z-[9999] bg-black bg-opacity-90 hidden">
                                <div class="absolute top-4 right-4 z-10">
                                    <button type="button" id="close-gallery"
                                        class="text-white hover:text-gray-300 focus:outline-none">
                                        <i class="fas fa-times text-2xl"></i>
                                    </button>
                                </div>

                                <div class="gallery-slider h-full w-full relative">
                                    <div
                                        class="gallery-slider-track flex h-full transition-transform duration-300 ease-out">
                                        <?php foreach ($gallery_images as $index => $image): ?>
                                        <div
                                            class="gallery-slide flex-shrink-0 w-full h-full flex items-center justify-center p-4">
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $image; ?>"
                                                alt="<?php echo $product['name']; ?> - Image <?php echo $index + 1; ?>"
                                                class="max-h-full max-w-full object-contain">
                                        </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Gallery Navigation -->
                                    <button type="button"
                                        class="gallery-fullscreen-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 hover:bg-opacity-100 rounded-full w-12 h-12 flex items-center justify-center shadow-md transition-all duration-200 focus:outline-none"
                                        aria-label="Previous image">
                                        <i class="fas fa-chevron-left text-gray-800 text-xl"></i>
                                    </button>
                                    <button type="button"
                                        class="gallery-fullscreen-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-75 hover:bg-opacity-100 rounded-full w-12 h-12 flex items-center justify-center shadow-md transition-all duration-200 focus:outline-none"
                                        aria-label="Next image">
                                        <i class="fas fa-chevron-right text-gray-800 text-xl"></i>
                                    </button>

                                    <!-- Counter -->
                                    <div
                                        class="gallery-counter absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-sm">
                                        <span id="current-slide">1</span> / <span
                                            id="total-slides"><?php echo $total_images; ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Info - Right Column -->
            <div class="product-info-container">
                <div class="bg-white rounded-lg shadow-md p-6 product-info-section">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2"><?php echo $product['name']; ?></h1>

                    <div class="flex items-center mb-4">
                        <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category_slug; ?>"
                            class="text-blue-500 hover:underline">
                            <?php echo $product['category_name']; ?>
                        </a>
                        <span class="mx-2 text-gray-400">|</span>
                        <?php if ($product['quantity'] > 0): ?>
                        <span class="text-green-500">Còn hàng</span>
                        <?php else: ?>
                        <span class="text-red-500">Hết hàng</span>
                        <?php endif; ?>
                    </div>

                    <!-- Đánh giá sao -->
                    <div class="flex items-center mb-4">
                        <div class="flex text-yellow-400 mr-2">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <?php if ($i <= round($average_rating)): ?>
                            <i class="fas fa-star"></i>
                            <?php else: ?>
                            <i class="far fa-star"></i>
                            <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                        <a href="#reviews-section"
                            class="text-gray-600 text-sm hover:text-blue-500 transition duration-200">
                            <?php echo number_format($average_rating, 1); ?> (<?php echo $rated_reviews_count; ?> đánh
                            giá)
                        </a>
                        <span class="mx-2 text-gray-400">|</span>
                        <span class="text-gray-600 text-sm flex items-center">
                            <i class="fas fa-shopping-cart text-green-500 mr-1"></i>
                            <?php echo $product['sold']; ?> đã bán
                        </span>
                        <span class="mx-2 text-gray-400">|</span>
                        <span class="text-gray-600 text-sm flex items-center">
                            <i class="fas fa-eye text-blue-500 mr-1"></i>
                            <?php echo $product['views']; ?> lượt xem
                        </span>
                    </div>

                    <?php if ($product['flash_sale']): ?>
                    <div class="mb-4">
                        <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-bolt mr-1"></i> Flash Sale
                        </span>
                    </div>
                    <?php endif; ?>

                    <div class="mb-6">
                        <?php if ($product['price_type'] === 'contact'): ?>
                        <div class="text-blue-600 font-bold text-2xl flex items-center">
                            <i class="fas fa-phone-alt mr-2"></i>
                            Liên hệ báo giá
                        </div>
                        <p class="text-gray-600 text-sm mt-1">Vui lòng gọi để nhận báo giá tốt nhất</p>
                        <?php elseif ($product['sale_price'] > 0): ?>
                        <div class="flex items-center">
                            <span
                                class="text-gray-500 line-through text-lg mr-2"><?php echo format_currency($product['price']); ?></span>
                            <span
                                class="product-price text-red-500 font-bold text-3xl"><?php echo format_currency($product['sale_price']); ?></span>
                            <?php
                            $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100);
                            ?>
                            <span
                                class="ml-2 bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded">-<?php echo $discount_percent; ?>%</span>
                        </div>
                        <p class="text-green-600 text-sm mt-1">
                            <i class="fas fa-tags mr-1"></i>
                            Tiết kiệm: <?php echo format_currency($product['price'] - $product['sale_price']); ?>
                        </p>
                        <?php else: ?>
                        <span
                            class="product-price text-gray-800 font-bold text-3xl"><?php echo format_currency($product['price']); ?></span>
                        <p class="text-gray-600 text-sm mt-1">Giá đã bao gồm VAT</p>
                        <?php endif; ?>
                    </div>

                    <div class="mb-6">
                        <p class="text-gray-600 text-justify"><?php echo $product['description']; ?></p>
                    </div>



                    <?php if ($product['quantity'] > 0): ?>
                    <form action="<?php echo BASE_URL; ?>/ajax/add_to_cart.php" method="POST" class="mb-6">
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">

                        <?php if ($product['price_type'] === 'contact'): ?>
                        <!-- Hiển thị thông báo liên hệ báo giá -->
                        <div class="mb-4 p-3 bg-blue-50 text-blue-700 rounded">
                            <i class="fas fa-info-circle mr-2"></i>
                            Vui lòng liên hệ với chúng tôi để được báo giá chính xác nhất.
                        </div>
                        <?php else: ?>

                        <?php
                        // Kiểm tra xem có tùy chọn kích thước không
                        $size_options = !empty($product['size_options']) ? json_decode($product['size_options'], true) : null;
                        if (!empty($size_options) && isset($size_options['options']) && count($size_options['options']) > 0):
                        ?>
                        <div class="mb-4">
                            <label for="size_option" class="block text-gray-700 mb-2">Chọn kích thước:</label>
                            <select id="size_option" name="size_option"
                                class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500">
                                <?php foreach ($size_options['options'] as $index => $option): ?>
                                <option value="<?php echo $index; ?>" data-price="<?php echo $option['price']; ?>">
                                    <?php echo $option['size']; ?> - <?php echo format_currency($option['price']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>

                        <div class="flex items-center mb-4">
                            <label for="quantity" class="mr-4 text-gray-700">Số lượng:</label>
                            <div class="flex items-center">
                                <button type="button"
                                    class="quantity-control quantity-decrease bg-gray-200 text-gray-700 w-8 h-8 flex items-center justify-center rounded-l">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" name="quantity" id="quantity" value="1" min="1"
                                    max="<?php echo $product['quantity']; ?>"
                                    class="quantity-input w-16 h-8 border-t border-b border-gray-300 text-center">
                                <button type="button"
                                    class="quantity-control quantity-increase bg-gray-200 text-gray-700 w-8 h-8 flex items-center justify-center rounded-r">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex space-x-2">
                            <button type="button"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded flex items-center justify-center transition duration-200 add-to-cart-btn"
                                data-product-id="<?php echo $product['id']; ?>">
                                <i class="fas fa-shopping-cart mr-2"></i> Thêm vào giỏ hàng
                            </button>
                            <button type="button"
                                class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded flex items-center justify-center transition duration-200">
                                <i class="fas fa-bolt mr-2"></i> Mua ngay
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="mb-6">
                        <p class="text-red-500 font-semibold">Sản phẩm hiện đang hết hàng.</p>
                    </div>
                    <?php endif; ?>

                    <div class="border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                            <i class="fas fa-certificate text-blue-500 mr-2"></i>
                            Chính sách & Dịch vụ
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <i class="fas fa-shield-alt text-blue-500 text-xl mr-3"></i>
                                <div>
                                    <span class="text-gray-800 font-medium block">Bảo hành chính hãng</span>
                                    <span class="text-gray-600 text-sm">12 tháng tại nhà</span>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <i class="fas fa-truck text-green-500 text-xl mr-3"></i>
                                <div>
                                    <span class="text-gray-800 font-medium block">Giao hàng tận nơi</span>
                                    <span class="text-gray-600 text-sm">Miễn phí trong nội thành</span>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                <i class="fas fa-sync-alt text-purple-500 text-xl mr-3"></i>
                                <div>
                                    <span class="text-gray-800 font-medium block">Đổi trả dễ dàng</span>
                                    <span class="text-gray-600 text-sm">Trong vòng 7 ngày</span>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                <i class="fas fa-tools text-yellow-500 text-xl mr-3"></i>
                                <div>
                                    <span class="text-gray-800 font-medium block">Lắp đặt miễn phí</span>
                                    <span class="text-gray-600 text-sm">Đội ngũ kỹ thuật chuyên nghiệp</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mô tả chi tiết sản phẩm -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                Thông tin chi tiết sản phẩm
            </h2>

            <!-- Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <ul class="flex flex-wrap -mb-px" id="product-tabs" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="inline-block p-4 border-b-2 border-blue-500 rounded-t-lg text-blue-600 font-medium"
                                id="overview-tab"
                                data-tabs-target="#overview"
                                type="button"
                                role="tab"
                                aria-controls="overview"
                                aria-selected="true">
                            <i class="fas fa-info-circle mr-2"></i>Tổng quan
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="specifications-tab"
                                data-tabs-target="#specifications"
                                type="button"
                                role="tab"
                                aria-controls="specifications"
                                aria-selected="false">
                            <i class="fas fa-clipboard-list mr-2"></i>Thông số kỹ thuật
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="usage-guide-tab"
                                data-tabs-target="#usage-guide"
                                type="button"
                                role="tab"
                                aria-controls="usage-guide"
                                aria-selected="false">
                            <i class="fas fa-book mr-2"></i>Hướng dẫn sử dụng
                        </button>
                    </li>
                    <li role="presentation">
                        <button class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300"
                                id="warranty-tab"
                                data-tabs-target="#warranty"
                                type="button"
                                role="tab"
                                aria-controls="warranty"
                                aria-selected="false">
                            <i class="fas fa-shield-alt mr-2"></i>Bảo hành
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Tab Contents -->
            <div id="product-tab-content">
                <!-- Tab Tổng quan -->
                <div class="block" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                    <?php if (!empty($product['overview']) || !empty($product['content'])): ?>
                        <?php if (!empty($product['overview'])): ?>
                            <div class="prose product-content max-w-none product-description mb-6">
                                <?php
                                // Hiển thị nội dung tổng quan với hỗ trợ hình ảnh và caption
                                echo $product['overview'];
                                ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($product['features'])): ?>
                            <div class="mb-6">
                                <h3 class="text-xl font-semibold text-gray-800 mb-4">Đặc điểm nổi bật</h3>
                                <ul class="space-y-2">
                                    <?php
                                    $features = json_decode($product['features'], true);
                                    if (is_array($features)):
                                        foreach ($features as $feature):
                                    ?>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                            <span><?php echo htmlspecialchars($feature); ?></span>
                                        </li>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($product['content'])): ?>
                            <div class="prose product-content max-w-none product-description">
                                <?php echo $product['content']; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-file-alt text-gray-300 text-5xl mb-4"></i>
                            <p>Chưa có thông tin tổng quan cho sản phẩm này.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Tab Thông số kỹ thuật -->
                <div class="hidden" id="specifications" role="tabpanel" aria-labelledby="specifications-tab">
                    <?php if (!empty($product['specifications_json']) || !empty($product['specifications'])): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200">
                                <tbody>
                                    <?php
                                    // Ưu tiên sử dụng specifications_json nếu có
                                    if (!empty($product['specifications_json'])) {
                                        $specs = json_decode($product['specifications_json'], true);
                                        if (is_array($specs)) {
                                            $i = 0;
                                            foreach ($specs as $key => $value):
                                                $bg_class = $i % 2 === 0 ? 'bg-gray-50' : 'bg-white';
                                                $i++;
                                    ?>
                                        <tr class="<?php echo $bg_class; ?>">
                                            <td class="px-4 py-3 border-b border-gray-200 font-medium text-gray-700 w-1/3"><?php echo htmlspecialchars($key); ?></td>
                                            <td class="px-4 py-3 border-b border-gray-200 text-gray-800"><?php echo htmlspecialchars($value); ?></td>
                                        </tr>
                                    <?php
                                            endforeach;
                                        }
                                    } elseif (!empty($product['specifications'])) {
                                        // Sử dụng trường specifications cũ nếu không có specifications_json
                                        echo $product['specifications'];
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-clipboard-list text-gray-300 text-5xl mb-4"></i>
                            <p>Chưa có thông số kỹ thuật cho sản phẩm này.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Tab Hướng dẫn sử dụng -->
                <div class="hidden" id="usage-guide" role="tabpanel" aria-labelledby="usage-guide-tab">
                    <?php if (!empty($product['usage_guide_json']) || !empty($product['usage_guide'])): ?>
                        <?php if (!empty($product['usage_guide_json'])): ?>
                            <?php
                            $usage_guide = json_decode($product['usage_guide_json'], true);
                            if (is_array($usage_guide)):
                                if (!empty($usage_guide['steps'])):
                            ?>
                                <div class="mb-6">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Các bước sử dụng</h3>
                                    <ol class="list-decimal pl-6 space-y-2">
                                        <?php foreach ($usage_guide['steps'] as $step): ?>
                                            <li class="pl-2"><?php echo htmlspecialchars($step); ?></li>
                                        <?php endforeach; ?>
                                    </ol>
                                </div>
                            <?php
                                endif;
                                if (!empty($usage_guide['notes'])):
                            ?>
                                <div class="mb-6">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Lưu ý khi sử dụng</h3>
                                    <ul class="list-disc pl-6 space-y-2">
                                        <?php foreach ($usage_guide['notes'] as $note): ?>
                                            <li class="pl-2"><?php echo htmlspecialchars($note); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php
                                endif;
                            endif;
                            ?>
                        <?php elseif (!empty($product['usage_guide'])): ?>
                            <div class="prose product-content max-w-none product-description">
                                <?php echo $product['usage_guide']; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-book text-gray-300 text-5xl mb-4"></i>
                            <p>Chưa có hướng dẫn sử dụng cho sản phẩm này.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Tab Bảo hành -->
                <div class="hidden" id="warranty" role="tabpanel" aria-labelledby="warranty-tab">
                    <?php if (!empty($product['warranty_info'])): ?>
                        <?php
                        $warranty = json_decode($product['warranty_info'], true);
                        if (is_array($warranty)):
                        ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                                        <i class="fas fa-clock text-blue-500 mr-2"></i>
                                        Thời gian bảo hành
                                    </h3>
                                    <p class="text-gray-700"><?php echo htmlspecialchars($warranty['time'] ?? '12 tháng'); ?></p>
                                </div>

                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        Phạm vi bảo hành
                                    </h3>
                                    <p class="text-gray-700"><?php echo htmlspecialchars($warranty['scope'] ?? 'Bảo hành toàn bộ sản phẩm'); ?></p>
                                </div>
                            </div>

                            <?php if (!empty($warranty['conditions'])): ?>
                                <div class="mt-6">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                                        <i class="fas fa-list-ul text-purple-500 mr-2"></i>
                                        Điều kiện bảo hành
                                    </h3>
                                    <ul class="list-disc pl-6 space-y-2">
                                        <?php foreach ($warranty['conditions'] as $condition): ?>
                                            <li class="text-gray-700"><?php echo htmlspecialchars($condition); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($warranty['contact'])): ?>
                                <div class="mt-6 bg-yellow-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                                        <i class="fas fa-phone-alt text-yellow-500 mr-2"></i>
                                        Thông tin liên hệ bảo hành
                                    </h3>
                                    <p class="text-gray-700"><?php echo htmlspecialchars($warranty['contact']); ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-shield-alt text-gray-300 text-5xl mb-4"></i>
                            <p>Chưa có thông tin bảo hành cho sản phẩm này.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <style>
        /* CSS cho phần mô tả chi tiết sản phẩm */
        .product-description {
            color: #333;
            line-height: 1.6;
        }

        .product-description h1,
        .product-description h2,
        .product-description h3,
        .product-description h4,
        .product-description h5,
        .product-description h6 {
            margin-top: 1.5em;
            margin-bottom: 0.75em;
            font-weight: 600;
            color: #1a202c;
        }

        .product-description h1 {
            font-size: 1.875rem;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 0.5rem;
        }

        .product-description h2 {
            font-size: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 0.5rem;
        }

        .product-description h3 {
            font-size: 1.25rem;
        }

        .product-description p {
            margin-bottom: 1rem;
        }

        .product-description ul,
        .product-description ol {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .product-description ul {
            list-style-type: disc;
        }

        .product-description ol {
            list-style-type: decimal;
        }

        .product-description li {
            margin-bottom: 0.5rem;
        }

        .product-description img {
            max-width: 100%;
            height: auto;
            margin: 1.5rem 0;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* CSS cho hình ảnh và caption từ SimpleEditor */
        .product-description .simple-editor-figure {
            display: table;
            margin: 1.5rem auto;
            max-width: 100%;
            text-align: center;
        }

        .product-description .simple-editor-image {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .product-description .simple-editor-caption {
            display: table-caption;
            caption-side: bottom;
            padding: 8px;
            font-size: 0.875rem;
            color: #4a5568;
            text-align: center;
            font-style: italic;
            margin-top: 0.5rem;
        }

        .product-description table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }

        .product-description table th,
        .product-description table td {
            border: 1px solid #e2e8f0;
            padding: 0.75rem;
            text-align: left;
        }

        .product-description table th {
            background-color: #f7fafc;
            font-weight: 600;
        }

        .product-description blockquote {
            border-left: 4px solid #4299e1;
            padding-left: 1rem;
            margin-left: 0;
            margin-right: 0;
            font-style: italic;
            color: #4a5568;
            margin: 1.5rem 0;
        }

        .product-description a {
            color: #3182ce;
            text-decoration: none;
        }

        .product-description a:hover {
            text-decoration: underline;
        }

        .product-description pre,
        .product-description code {
            background-color: #f7fafc;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-family: monospace;
            overflow-x: auto;
            margin: 1.5rem 0;
        }
        </style>

        <!-- Đánh giá và bình luận -->

        <div id="reviews-section" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-star text-yellow-400 mr-2"></i>
                Đánh giá & Bình luận
            </h2>

            <!-- Tổng quan đánh giá -->
            <div class="flex flex-col md:flex-row mb-8">
                <div class="md:w-1/3 mb-6 md:mb-0 flex flex-col items-center justify-center">
                    <div class="text-5xl font-bold text-gray-800 mb-2">
                        <?php echo number_format($average_rating, 1); ?></div>
                    <div class="flex items-center mb-2">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <?php if ($i <= round($average_rating)): ?>
                        <i class="fas fa-star text-yellow-400 text-xl"></i>
                        <?php else: ?>
                        <i class="far fa-star text-yellow-400 text-xl"></i>
                        <?php endif; ?>
                        <?php endfor; ?>
                    </div>
                    <div class="text-gray-600"><?php echo $reviews_data['rated_reviews_count']; ?> đánh giá</div>
                </div>

                <div class="md:w-2/3">
                    <?php for ($i = 5; $i >= 1; $i--): ?>
                    <?php
                            $count = isset($rating_stats[$i]) ? $rating_stats[$i] : 0;
                            $percent = $reviews_data['rated_reviews_count'] > 0 ? ($count / $reviews_data['rated_reviews_count'] * 100) : 0;
                            ?>
                    <div class="flex items-center mb-2">
                        <div class="w-12 text-sm text-gray-600"><?php echo $i; ?> sao</div>
                        <div class="flex-grow mx-2">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-yellow-400 h-2.5 rounded-full" style="width: <?php echo $percent; ?>%">
                                </div>
                            </div>
                        </div>
                        <div class="w-10 text-sm text-gray-600 text-right"><?php echo $count; ?></div>
                    </div>
                    <?php endfor; ?>
                </div>
            </div>

            <!-- Form đánh giá -->
            <div class="border-t border-gray-200 pt-6 mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Viết đánh giá của bạn</h3>

                <!-- Hướng dẫn đánh giá -->
                <div class="review-form-guide mb-6">
                    <h4><i class="fas fa-lightbulb text-blue-600 mr-2"></i>Hướng dẫn đánh giá</h4>
                    <ul>
                        <li>Chia sẻ trải nghiệm thực tế của bạn với sản phẩm</li>
                        <li>Mô tả chi tiết về chất lượng, thiết kế và tính năng của sản phẩm</li>
                        <li>Đánh giá sao chỉ dành cho khách hàng đã mua sản phẩm</li>
                        <li>Bạn có thể đính kèm hình ảnh hoặc video để minh họa</li>
                    </ul>
                </div>

                <form id="review-form" class="review-form" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">

                    <?php if (is_logged_in()): ?>
                    <?php if ($has_purchased): ?>
                    <?php if ($can_rate): ?>
                    <!-- Rating Stars (chỉ hiển thị nếu đã mua sản phẩm và chưa đánh giá) -->
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-3">Đánh giá sao</label>
                        <div class="elegant-rating-container">
                            <div class="rating">
                                <input type="radio" name="rating" value="5" id="rating-5" required>
                                <label for="rating-5"><i class="fas fa-star"></i></label>

                                <input type="radio" name="rating" value="4" id="rating-4">
                                <label for="rating-4"><i class="fas fa-star"></i></label>

                                <input type="radio" name="rating" value="3" id="rating-3">
                                <label for="rating-3"><i class="fas fa-star"></i></label>

                                <input type="radio" name="rating" value="2" id="rating-2">
                                <label for="rating-2"><i class="fas fa-star"></i></label>

                                <input type="radio" name="rating" value="1" id="rating-1">
                                <label for="rating-1"><i class="fas fa-star"></i></label>
                            </div>
                            <div class="rating-feedback">
                                <span class="rating-label">Chưa đánh giá</span>
                            </div>
                        </div>
                    </div>

                    <!-- Tùy chọn ẩn danh (chỉ hiển thị nếu đã mua sản phẩm) -->
                    <div class="mb-4 flex items-center">
                        <input type="checkbox" name="is_anonymous" id="is_anonymous" class="mr-2">
                        <label for="is_anonymous" class="text-gray-700 text-sm">Đánh giá ẩn danh</label>
                    </div>
                    <?php else: ?>
                    <div class="mb-4 p-4 bg-yellow-50 text-yellow-700 rounded-lg border-l-4 border-yellow-400">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-yellow-500 text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Bạn đã đánh giá sản phẩm này</p>
                                <p class="text-sm mt-1">Bạn đã đánh giá đủ số lần tương ứng với số lần mua sản phẩm.
                                    Bạn vẫn có thể thêm bình luận mới hoặc mua thêm sản phẩm để đánh giá lại.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="mb-4 p-4 bg-blue-50 text-blue-700 rounded-lg border-l-4 border-blue-400">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Chỉ khách hàng đã mua sản phẩm mới có thể đánh giá
                                    sao</p>
                                <p class="text-sm mt-1">Bạn vẫn có thể để lại bình luận về sản phẩm này.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="mb-4 p-4 bg-blue-50 text-blue-700 rounded-lg border-l-4 border-blue-400">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">Đăng nhập để đánh giá sao</p>
                                <p class="text-sm mt-1">Chỉ khách hàng đã đăng nhập và mua sản phẩm mới có thể đánh
                                    giá sao. Bạn vẫn có thể để lại bình luận với tư cách khách.</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Review Content -->
                    <div class="mb-4">
                        <label for="review_content" class="block text-gray-700 text-sm font-bold mb-2">Nội dung đánh
                            giá <span class="text-red-500">*</span></label>
                        <textarea name="review_content" id="review_content" rows="4"
                            placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..."
                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required></textarea>
                        <p class="text-gray-500 text-xs mt-1">Tối thiểu 10 ký tự, tối đa 1000 ký tự</p>
                    </div>

                    <!-- Media Upload -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Hình ảnh/Video</label>
                        <div class="media-upload-container">
                            <input type="file" name="media[]" multiple accept="image/*,video/*" class="media-upload">
                            <p class="text-gray-600 text-xs mt-1">Hỗ trợ định dạng: JPG, JPEG, PNG, GIF, WEBP, MP4.
                                Kích thước tối đa: 5MB. Tối đa 5 file.</p>
                        </div>
                    </div>

                    <!-- User Info (for guests) -->
                    <?php if (!is_logged_in()): ?>
                    <div class="p-4 bg-gray-50 rounded-lg mb-4 border border-gray-200">
                        <h4 class="text-gray-700 font-medium mb-3">Thông tin của bạn</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="guest_name" class="block text-gray-700 text-sm font-medium mb-2">Tên của
                                    bạn <span class="text-red-500">*</span></label>
                                <input type="text" name="guest_name" id="guest_name"
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required>
                            </div>

                            <div>
                                <label for="guest_email" class="block text-gray-700 text-sm font-medium mb-2">Email
                                    của bạn <span class="text-red-500">*</span></label>
                                <input type="email" name="guest_email" id="guest_email"
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <button type="submit"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200">
                        Gửi đánh giá
                    </button>
                </form>
            </div>

            <!-- Danh sách đánh giá -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-between items-center mb-4">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800">Đánh giá từ khách hàng</h3>

                        <!-- Tabs -->
                        <div class="flex mt-3 border-b">
                            <button id="tab-ratings"
                                class="py-2 px-4 border-b-2 border-blue-500 text-blue-500 font-medium">
                                Đánh giá có sao
                            </button>
                            <button id="tab-comments"
                                class="py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium">
                                Bình luận
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <label for="sort-reviews" class="text-gray-700 text-sm mr-2">Sắp xếp:</label>
                        <select id="sort-reviews"
                            class="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>Mới nhất
                            </option>
                            <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>Cũ nhất
                            </option>
                            <option value="highest" <?php echo $sort === 'highest' ? 'selected' : ''; ?>>Đánh giá
                                cao nhất</option>
                            <option value="lowest" <?php echo $sort === 'lowest' ? 'selected' : ''; ?>>Đánh giá thấp
                                nhất</option>
                            <option value="helpful" <?php echo $sort === 'helpful' ? 'selected' : ''; ?>>Hữu ích
                                nhất</option>
                        </select>
                    </div>
                </div>

                <!-- Bộ lọc đánh giá -->
                <div class="review-filter mb-4">
                    <button class="filter-btn active" data-filter="all">Tất cả</button>
                    <button class="filter-btn" data-filter="5">5 sao</button>
                    <button class="filter-btn" data-filter="4">4 sao</button>
                    <button class="filter-btn" data-filter="3">3 sao</button>
                    <button class="filter-btn" data-filter="2">2 sao</button>
                    <button class="filter-btn" data-filter="1">1 sao</button>
                    <button class="filter-btn" data-filter="has_media">Có hình ảnh/video</button>
                </div>

                <!-- Tab content for ratings -->
                <div id="tab-content-ratings" class="tab-content block">
                    <?php
                        $has_ratings = false;
                        foreach ($reviews as $review) {
                            if ($review['rating']) {
                                $has_ratings = true;
                                break;
                            }
                        }
                        ?>

                    <!-- Thông báo khi không có đánh giá nào sau khi lọc -->
                    <div id="empty-filtered-reviews" class="hidden text-center py-8 text-gray-600">
                        <i class="fas fa-filter text-4xl mb-3 text-gray-400"></i>
                        <p>Không tìm thấy đánh giá nào phù hợp với bộ lọc.</p>
                        <p class="mt-2">Vui lòng thử bộ lọc khác.</p>
                    </div>

                    <?php if ($has_ratings): ?>
                    <?php foreach ($reviews as $review): ?>
                    <?php if ($review['rating']): ?>
                    <div class="review-item border-b border-gray-200 py-4 mb-4"
                        data-review-id="<?php echo $review['id']; ?>" data-has-rating="1"
                        data-rating="<?php echo $review['rating']; ?>"
                        data-has-media="<?php echo !empty($review['media']) ? '1' : '0'; ?>">
                        <div class="flex justify-between items-start">
                            <div class="flex">
                                <!-- Avatar -->
                                <div class="mr-3">
                                    <?php if ($review['is_anonymous']): ?>
                                    <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user-secret text-gray-500"></i>
                                    </div>
                                    <?php elseif ($review['user_id'] && !empty($review['avatar'])): ?>
                                    <div class="w-10 h-10 rounded-full overflow-hidden">
                                        <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $review['avatar']; ?>"
                                            alt="Avatar" class="w-full h-full object-cover">
                                    </div>
                                    <?php else: ?>
                                    <div
                                        class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div>
                                    <div class="flex items-center text-sm mb-2">
                                        <!-- Rating stars -->
                                        <?php if ($review['rating']): ?>
                                        <div class="flex text-yellow-400 mr-2">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <?php if ($i <= $review['rating']): ?>
                                            <i class="fas fa-star"></i>
                                            <?php else: ?>
                                            <i class="far fa-star"></i>
                                            <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <?php endif; ?>

                                        <span class="review-time text-gray-600"
                                            data-timestamp="<?php echo strtotime($review['created_at']); ?>"><?php echo date('H:i d/m/Y', strtotime($review['created_at'])); ?></span>
                                    </div>
                                    <div class="text-sm mb-2">
                                        <?php if ($review['is_anonymous']): ?>
                                        <span class="font-medium">Người dùng ẩn danh</span>
                                        <?php else: ?>
                                        <span class="font-medium">
                                            <?php echo htmlspecialchars($review['user_id'] ? $review['full_name'] : $review['guest_name']); ?>
                                        </span>
                                        <?php endif; ?>

                                        <?php if ($review['is_verified_buyer']): ?>
                                        <span class="user-badge badge-verified-buyer">
                                            <i class="fas fa-check-circle mr-1"></i> Đã mua hàng
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <button class="helpful-btn text-gray-500 hover:text-blue-500 flex items-center mr-3"
                                    data-review-id="<?php echo $review['id']; ?>" title="Đánh dấu hữu ích">
                                    <i class="far fa-thumbs-up mr-1"></i>
                                    <span class="helpful-count"><?php echo $review['helpful_count']; ?></span>
                                </button>

                                <button class="reply-btn text-gray-500 hover:text-blue-500 mr-3"
                                    title="Trả lời đánh giá này">
                                    <i class="far fa-comment-dots mr-1"></i> Trả lời
                                </button>

                                <button class="report-btn text-gray-500 hover:text-red-500 mr-3"
                                    data-review-id="<?php echo $review['id']; ?>" title="Báo cáo đánh giá này">
                                    <i class="far fa-flag"></i>
                                </button>

                                <?php if (is_logged_in()): ?>
                                <?php if ($review['user_id'] == $_SESSION['user_id']): ?>
                                <button class="delete-review-btn text-gray-500 hover:text-red-500"
                                    data-review-id="<?php echo $review['id']; ?>" title="Xóa đánh giá của bạn">
                                    <i class="far fa-trash-alt"></i>
                                </button>
                                <?php elseif (is_admin()): ?>
                                <button class="delete-review-btn text-red-500 hover:text-red-600"
                                    data-review-id="<?php echo $review['id']; ?>" title="Xóa đánh giá (quyền Admin)">
                                    <i class="far fa-trash-alt"></i>
                                    <span class="text-xs ml-1">Admin</span>
                                </button>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mt-2 text-gray-700 review-content">
                            <?php echo nl2br(htmlspecialchars($review['review_content'])); ?>
                        </div>

                        <!-- Review Media -->
                        <?php if (!empty($review['media'])): ?>
                        <div class="mt-3 review-media-grid">
                            <?php
                                            $mediaCount = count($review['media']);
                                            foreach ($review['media'] as $index => $media):
                                            ?>
                            <?php if ($index < 4): ?>
                            <div class="review-media-item size-standard cursor-pointer">
                                <?php if ($media['media_type'] === 'image' || $media['media_type'] === 'gif'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy">
                                <?php elseif ($media['media_type'] === 'video'): ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                                    <i class="fas fa-play text-white text-xl"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php elseif ($index == 4): ?>
                            <div class="review-media-item size-standard cursor-pointer relative">
                                <?php if ($media['media_type'] === 'image' || $media['media_type'] === 'gif'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy" class="opacity-70">
                                <?php elseif ($media['media_type'] === 'video'): ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <?php endif; ?>

                                <?php $remaining = $mediaCount - 5; ?>
                                <?php if ($remaining > 0): ?>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded text-white font-medium">
                                    <span>Xem thêm <?php echo $remaining; ?> ảnh</span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php else: ?>
                            <div class="review-media-item size-standard cursor-pointer hidden">
                                <?php if ($media['media_type'] === 'image' || $media['media_type'] === 'gif'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy">
                                <?php elseif ($media['media_type'] === 'video'): ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                                    <i class="fas fa-play text-white text-xl"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Reply Form (hidden by default) -->
                        <div class="reply-form hidden mt-4">
                            <form class="reply-submit-form" data-review-id="<?php echo $review['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">

                                <div class="mb-3">
                                    <textarea name="reply_content" rows="2"
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Viết phản hồi của bạn..." required></textarea>
                                </div>

                                <!-- Media Upload for Reply -->
                                <div class="mb-3">
                                    <input type="file" name="media[]" multiple accept="image/*,video/*"
                                        class="media-upload">
                                    <p class="text-gray-600 text-xs mt-1">Hỗ trợ định dạng: JPG, JPEG, PNG, GIF,
                                        WEBP, MP4.</p>
                                </div>

                                <!-- User Info (for guests) -->
                                <?php if (!is_logged_in()): ?>
                                <div class="grid grid-cols-2 gap-3 mb-3">
                                    <div>
                                        <input type="text" name="guest_name"
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Tên của bạn *" required>
                                    </div>
                                    <div>
                                        <input type="email" name="guest_email"
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Email của bạn *" required>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="flex justify-end">
                                    <button type="button"
                                        class="cancel-reply-btn mr-2 text-gray-500 hover:text-gray-700">Hủy</button>
                                    <button type="submit"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm">Gửi
                                        phản hồi</button>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        <?php if (!empty($review['replies'])): ?>
                        <div class="review-replies mt-4 pl-6 border-l-2 border-gray-200">
                            <?php foreach ($review['replies'] as $reply): ?>
                            <div class="review-reply py-3 <?php echo $reply !== end($review['replies']) ? 'border-b border-gray-100' : ''; ?>"
                                data-reply-id="<?php echo $reply['id']; ?>">
                                <div class="flex justify-between items-start">
                                    <div class="flex">
                                        <!-- Avatar -->
                                        <div class="mr-3">
                                            <?php if ($reply['user_id'] && !empty($reply['avatar'])): ?>
                                            <div class="w-8 h-8 rounded-full overflow-hidden">
                                                <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $reply['avatar']; ?>"
                                                    alt="Avatar" class="w-full h-full object-cover">
                                            </div>
                                            <?php else: ?>
                                            <div
                                                class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <div>
                                            <div class="text-sm mb-1">
                                                <span class="font-medium">
                                                    <?php echo htmlspecialchars($reply['user_id'] ? $reply['full_name'] : $reply['guest_name']); ?>
                                                </span>

                                                <?php if ($reply['role'] === 'admin'): ?>
                                                <span
                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded ml-2">Nội
                                                    Thất Bàng Vũ</span>
                                                <?php endif; ?>

                                                <span
                                                    class="text-gray-500 text-xs ml-2"><?php echo date('H:i d/m/Y', strtotime($reply['created_at'])); ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (is_logged_in()): ?>
                                    <div>
                                        <?php if ($reply['user_id'] == $_SESSION['user_id']): ?>
                                        <button class="delete-reply-btn text-gray-500 hover:text-red-500"
                                            data-reply-id="<?php echo $reply['id']; ?>" title="Xóa phản hồi của bạn">
                                            <i class="far fa-trash-alt"></i>
                                        </button>
                                        <?php elseif (is_admin()): ?>
                                        <button class="delete-reply-btn text-red-500 hover:text-red-600"
                                            data-reply-id="<?php echo $reply['id']; ?>"
                                            title="Xóa phản hồi (quyền Admin)">
                                            <i class="far fa-trash-alt"></i>
                                            <span class="text-xs ml-1">Admin</span>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="text-gray-700 text-sm ml-11">
                                    <div class="bg-gray-50 p-2 rounded-lg relative">
                                        <div
                                            class="absolute -top-2 -left-2 w-0 h-0 border-8 border-transparent border-b-gray-50 transform rotate-45">
                                        </div>
                                        <?php echo nl2br(htmlspecialchars($reply['reply_content'])); ?>
                                    </div>
                                </div>

                                <!-- Reply Media -->
                                <?php if (!empty($reply['media'])): ?>
                                <div class="review-media mt-2 flex flex-wrap gap-2">
                                    <?php foreach ($reply['media'] as $media): ?>
                                    <?php if ($media['media_type'] === 'image' || $media['media_type'] === 'gif'): ?>
                                    <div class="review-media-item w-16 h-16 cursor-pointer">
                                        <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                            alt="Reply Image" class="w-full h-full object-cover rounded">
                                    </div>
                                    <?php elseif ($media['media_type'] === 'video'): ?>
                                    <div class="review-media-item w-16 h-16 cursor-pointer relative">
                                        <video class="w-full h-full object-cover rounded">
                                            <source
                                                src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                                type="video/mp4">
                                        </video>
                                        <div
                                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded">
                                            <i class="fas fa-play text-white"></i>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($reviews_data['total_pages'] > 1): ?>
                    <div class="pagination flex justify-center mt-6">
                        <div class="flex items-center">
                            <!-- Previous page button -->
                            <button
                                class="pagination-nav pagination-nav-prev <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div class="flex space-x-1 mx-2">
                                <?php
                                            // Hiển thị tối đa 5 nút phân trang
                                            $maxButtons = 5;
                                            $startPage = max(1, min($reviews_data['current_page'] - floor($maxButtons / 2), $reviews_data['total_pages'] - $maxButtons + 1));
                                            $endPage = min($startPage + $maxButtons - 1, $reviews_data['total_pages']);

                                            // Hiển thị nút trang đầu nếu cần
                                            if ($startPage > 1):
                                            ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="1">1</button>
                                <?php if ($startPage > 2): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded <?php echo $i === $reviews_data['current_page'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>"
                                    data-page="<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </button>
                                <?php endfor; ?>

                                <!-- Hiển thị nút trang cuối nếu cần -->
                                <?php if ($endPage < $reviews_data['total_pages']): ?>
                                <?php if ($endPage < $reviews_data['total_pages'] - 1): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="<?php echo $reviews_data['total_pages']; ?>">
                                    <?php echo $reviews_data['total_pages']; ?>
                                </button>
                                <?php endif; ?>
                            </div>

                            <!-- Next page button -->
                            <button
                                class="pagination-nav pagination-nav-next <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="text-center py-8 text-gray-600">
                        <i class="far fa-star text-4xl mb-3 text-gray-400"></i>
                        <p>Chưa có đánh giá sao nào cho sản phẩm này.</p>
                        <p class="mt-2">Hãy là người đầu tiên đánh giá!</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Tab content for comments -->
                <div id="tab-content-comments" class="tab-content hidden">
                    <?php
                        $has_comments = false;
                        foreach ($reviews as $review) {
                            if (!$review['rating']) {
                                $has_comments = true;
                                break;
                            }
                        }
                        ?>

                    <?php if ($has_comments): ?>
                    <?php foreach ($reviews as $review): ?>
                    <?php if (!$review['rating']): ?>
                    <div class="review-item border-b border-gray-200 py-4 mb-4"
                        data-review-id="<?php echo $review['id']; ?>" data-has-rating="0">
                        <div class="flex justify-between items-start">
                            <div class="flex">
                                <!-- Avatar -->
                                <div class="mr-3">
                                    <?php if ($review['is_anonymous']): ?>
                                    <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-user-secret text-gray-500"></i>
                                    </div>
                                    <?php elseif ($review['user_id'] && !empty($review['avatar'])): ?>
                                    <div class="w-10 h-10 rounded-full overflow-hidden">
                                        <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $review['avatar']; ?>"
                                            alt="Avatar" class="w-full h-full object-cover">
                                    </div>
                                    <?php else: ?>
                                    <div
                                        class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div>
                                    <div class="flex items-center text-sm text-gray-600 mb-2">
                                        <span
                                            class="mr-2"><?php echo date('H:i d/m/Y', strtotime($review['created_at'])); ?></span>
                                    </div>
                                    <div class="text-sm mb-2">
                                        <?php if ($review['is_anonymous']): ?>
                                        <span class="font-medium">Người dùng ẩn danh</span>
                                        <?php else: ?>
                                        <span class="font-medium">
                                            <?php echo htmlspecialchars($review['user_id'] ? $review['full_name'] : $review['guest_name']); ?>
                                        </span>
                                        <?php endif; ?>

                                        <?php if ($review['is_verified_buyer']): ?>
                                        <span
                                            class="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded ml-2">Đã
                                            mua hàng</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <button class="helpful-btn text-gray-500 hover:text-blue-500 flex items-center mr-4"
                                    data-review-id="<?php echo $review['id']; ?>">
                                    <i class="far fa-thumbs-up mr-1"></i>
                                    <span class="helpful-count"><?php echo $review['helpful_count']; ?></span>
                                </button>

                                <button class="reply-btn text-gray-500 hover:text-blue-500 mr-4">
                                    <i class="far fa-comment-dots mr-1"></i> Trả lời
                                </button>

                                <?php if (is_logged_in()): ?>
                                <?php if ($review['user_id'] == $_SESSION['user_id']): ?>
                                <button class="delete-review-btn text-gray-500 hover:text-red-500"
                                    data-review-id="<?php echo $review['id']; ?>" title="Xóa đánh giá của bạn">
                                    <i class="far fa-trash-alt"></i>
                                </button>
                                <?php elseif (is_admin()): ?>
                                <button class="delete-review-btn text-red-500 hover:text-red-600"
                                    data-review-id="<?php echo $review['id']; ?>" title="Xóa đánh giá (quyền Admin)">
                                    <i class="far fa-trash-alt"></i>
                                    <span class="text-xs ml-1">Admin</span>
                                </button>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mt-2 text-gray-700 review-content">
                            <?php echo nl2br(htmlspecialchars($review['review_content'])); ?>
                        </div>

                        <!-- Review Media -->
                        <?php if (!empty($review['media'])): ?>
                        <div class="mt-3 review-media-grid">
                            <?php
                                            $mediaCount = count($review['media']);
                                            foreach ($review['media'] as $index => $media):
                                            ?>
                            <?php if ($index < 4): ?>
                            <div class="review-media-item size-small cursor-pointer">
                                <?php if ($media['media_type'] === 'image'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy">
                                <?php else: ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                                    <i class="fas fa-play text-white text-xl"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php elseif ($index == 4): ?>
                            <div class="review-media-item size-small cursor-pointer relative">
                                <?php if ($media['media_type'] === 'image'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy" class="opacity-70">
                                <?php else: ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <?php endif; ?>

                                <?php $remaining = $mediaCount - 5; ?>
                                <?php if ($remaining > 0): ?>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded text-white font-medium">
                                    <span>+<?php echo $remaining; ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php else: ?>
                            <div class="review-media-item size-small cursor-pointer hidden">
                                <?php if ($media['media_type'] === 'image'): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/reviews/images/<?php echo $media['file_name']; ?>"
                                    alt="Review media" loading="lazy">
                                <?php else: ?>
                                <video>
                                    <source
                                        src="<?php echo BASE_URL; ?>/uploads/reviews/videos/<?php echo $media['file_name']; ?>"
                                        type="video/mp4">
                                </video>
                                <div
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                                    <i class="fas fa-play text-white text-xl"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Reply Form -->
                        <div class="reply-form mt-3 hidden">
                            <form class="reply-submit-form" data-review-id="<?php echo $review['id']; ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">

                                <div class="mb-3">
                                    <textarea name="reply_content" rows="2"
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Viết phản hồi của bạn..." required></textarea>
                                </div>

                                <!-- Media Upload for Reply -->
                                <div class="mb-3">
                                    <input type="file" name="media[]" multiple accept="image/*,video/*"
                                        class="media-upload">
                                    <p class="text-gray-600 text-xs mt-1">Hỗ trợ định dạng: JPG, JPEG, PNG, GIF,
                                        WEBP, MP4.</p>
                                </div>

                                <!-- User Info (for guests) -->
                                <?php if (!is_logged_in()): ?>
                                <div class="grid grid-cols-2 gap-3 mb-3">
                                    <div>
                                        <input type="text" name="guest_name"
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Tên của bạn *" required>
                                    </div>
                                    <div>
                                        <input type="email" name="guest_email"
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Email của bạn *" required>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="flex justify-end">
                                    <button type="button"
                                        class="cancel-reply-btn mr-2 text-gray-500 hover:text-gray-700">Hủy</button>
                                    <button type="submit"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm">Gửi
                                        phản hồi</button>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        <?php if (!empty($review['replies'])): ?>
                        <div class="mt-4 pl-5 border-l-2 border-gray-200">
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Phản hồi
                                (<?php echo count($review['replies']); ?>)</h5>

                            <?php foreach ($review['replies'] as $reply): ?>
                            <div class="review-reply py-3 <?php echo $reply !== end($review['replies']) ? 'border-b border-gray-100' : ''; ?>"
                                data-reply-id="<?php echo $reply['id']; ?>">
                                <div class="flex justify-between items-start">
                                    <div class="flex">
                                        <!-- Avatar -->
                                        <div class="mr-3">
                                            <?php if ($reply['user_id'] && !empty($reply['avatar'])): ?>
                                            <div class="w-8 h-8 rounded-full overflow-hidden">
                                                <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $reply['avatar']; ?>"
                                                    alt="Avatar" class="w-full h-full object-cover">
                                            </div>
                                            <?php else: ?>
                                            <div
                                                class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <div>
                                            <div class="text-sm mb-1">
                                                <span class="font-medium">
                                                    <?php echo htmlspecialchars($reply['user_id'] ? $reply['full_name'] : $reply['guest_name']); ?>
                                                </span>

                                                <?php if ($reply['role'] === 'admin'): ?>
                                                <span
                                                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded ml-2">Nội
                                                    Thất Bàng Vũ</span>
                                                <?php endif; ?>

                                                <span
                                                    class="text-gray-500 text-xs ml-2"><?php echo date('H:i d/m/Y', strtotime($reply['created_at'])); ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (is_logged_in()): ?>
                                    <div>
                                        <?php if ($reply['user_id'] == $_SESSION['user_id']): ?>
                                        <button class="delete-reply-btn text-gray-500 hover:text-red-500"
                                            data-reply-id="<?php echo $reply['id']; ?>" title="Xóa phản hồi của bạn">
                                            <i class="far fa-trash-alt"></i>
                                        </button>
                                        <?php elseif (is_admin()): ?>
                                        <button class="delete-reply-btn text-red-500 hover:text-red-600"
                                            data-reply-id="<?php echo $reply['id']; ?>"
                                            title="Xóa phản hồi (quyền Admin)">
                                            <i class="far fa-trash-alt"></i>
                                            <span class="text-xs ml-1">Admin</span>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="text-gray-700 text-sm ml-11">
                                    <div class="bg-gray-50 p-2 rounded-lg relative">
                                        <div
                                            class="absolute -top-2 -left-2 w-0 h-0 border-8 border-transparent border-b-gray-50 transform rotate-45">
                                        </div>
                                        <?php echo nl2br(htmlspecialchars($reply['reply_content'])); ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; ?>

                    <!-- Pagination for Ratings Tab -->
                    <?php if ($reviews_data['total_pages'] > 1 && $current_tab === 'ratings'): ?>
                    <div id="ratings-pagination" class="pagination flex justify-center mt-6">
                        <div class="flex items-center">
                            <!-- Previous page button -->
                            <button
                                class="pagination-nav pagination-nav-prev <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div class="flex space-x-1 mx-2">
                                <?php
                                            // Hiển thị tối đa 5 nút phân trang
                                            $maxButtons = 5;
                                            $startPage = max(1, min($reviews_data['current_page'] - floor($maxButtons / 2), $reviews_data['total_pages'] - $maxButtons + 1));
                                            $endPage = min($startPage + $maxButtons - 1, $reviews_data['total_pages']);

                                            // Hiển thị nút trang đầu nếu cần
                                            if ($startPage > 1):
                                            ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="1">1</button>
                                <?php if ($startPage > 2): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded <?php echo $i === $reviews_data['current_page'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>"
                                    data-page="<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </button>
                                <?php endfor; ?>

                                <!-- Hiển thị nút trang cuối nếu cần -->
                                <?php if ($endPage < $reviews_data['total_pages']): ?>
                                <?php if ($endPage < $reviews_data['total_pages'] - 1): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="<?php echo $reviews_data['total_pages']; ?>">
                                    <?php echo $reviews_data['total_pages']; ?>
                                </button>
                                <?php endif; ?>
                            </div>

                            <!-- Next page button -->
                            <button
                                class="pagination-nav pagination-nav-next <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Pagination for Comments Tab -->
                    <?php if ($reviews_data['total_pages'] > 1 && $current_tab === 'comments'): ?>
                    <div id="comments-pagination" class="pagination flex justify-center mt-6">
                        <div class="flex items-center">
                            <!-- Previous page button -->
                            <button
                                class="pagination-nav pagination-nav-prev <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] <= 1 ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <div class="flex space-x-1 mx-2">
                                <?php
                                            // Hiển thị tối đa 5 nút phân trang
                                            $maxButtons = 5;
                                            $startPage = max(1, min($reviews_data['current_page'] - floor($maxButtons / 2), $reviews_data['total_pages'] - $maxButtons + 1));
                                            $endPage = min($startPage + $maxButtons - 1, $reviews_data['total_pages']);

                                            // Hiển thị nút trang đầu nếu cần
                                            if ($startPage > 1):
                                            ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="1">1</button>
                                <?php if ($startPage > 2): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded <?php echo $i === $reviews_data['current_page'] ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>"
                                    data-page="<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </button>
                                <?php endfor; ?>

                                <!-- Hiển thị nút trang cuối nếu cần -->
                                <?php if ($endPage < $reviews_data['total_pages']): ?>
                                <?php if ($endPage < $reviews_data['total_pages'] - 1): ?>
                                <span class="px-2 py-1 text-gray-500">...</span>
                                <?php endif; ?>
                                <button
                                    class="pagination-btn px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    data-page="<?php echo $reviews_data['total_pages']; ?>">
                                    <?php echo $reviews_data['total_pages']; ?>
                                </button>
                                <?php endif; ?>
                            </div>

                            <!-- Next page button -->
                            <button
                                class="pagination-nav pagination-nav-next <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>"
                                <?php echo $reviews_data['current_page'] >= $reviews_data['total_pages'] ? 'disabled' : ''; ?>>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else: ?>
                    <div class="text-center py-8 text-gray-600">
                        <i class="far fa-comment-dots text-4xl mb-3 text-gray-400"></i>
                        <p>Chưa có bình luận nào cho sản phẩm này.</p>
                        <p class="mt-2">Hãy là người đầu tiên bình luận!</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Related Products -->
<?php if (count($related_products) > 0): ?>
<div class="py-8 bg-gray-100">
    <div class="container mx-auto px-4">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Sản phẩm liên quan</h2>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <?php foreach ($related_products as $related_product): ?>
            <?php if ($related_product['id'] != $product['id']): ?>
            <div class="bg-white rounded-lg overflow-hidden shadow-md product-card">
                <a href="<?php echo get_product_url($related_product['slug']); ?>"
                    class="block product-image">
                    <?php if ($related_product['image']): ?>
                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $related_product['image']; ?>"
                        alt="<?php echo $related_product['name']; ?>">
                    <?php else: ?>
                    <div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                        <i class="fas fa-image text-gray-500 text-4xl"></i>
                    </div>
                    <?php endif; ?>
                </a>
                <div class="p-4">
                    <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $related_product['category_id']; ?>"
                        class="text-blue-500 text-sm hover:underline">
                        <?php echo $related_product['category_name']; ?>
                    </a>
                    <a href="<?php echo get_product_url($related_product['slug']); ?>"
                        class="block">
                        <h3
                            class="text-xl font-semibold text-gray-800 mb-2 hover:text-blue-500 transition duration-200">
                            <?php echo $related_product['name']; ?></h3>
                    </a>
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <?php if ($related_product['sale_price'] > 0): ?>
                            <span
                                class="text-gray-500 line-through mr-2"><?php echo format_currency($related_product['price']); ?></span>
                            <span
                                class="text-red-500 font-bold"><?php echo format_currency($related_product['sale_price']); ?></span>
                            <?php else: ?>
                            <span
                                class="text-gray-800 font-bold"><?php echo format_currency($related_product['price']); ?></span>
                            <?php endif; ?>
                        </div>
                        <?php if ($related_product['quantity'] > 0): ?>
                        <span class="text-green-500 text-sm">Còn hàng</span>
                        <?php else: ?>
                        <span class="text-red-500 text-sm">Hết hàng</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex space-x-2">
                        <button
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex-grow transition duration-200 add-to-cart-btn"
                            data-product-id="<?php echo $related_product['id']; ?>">
                            <i class="fas fa-shopping-cart mr-2"></i> Thêm vào giỏ
                        </button>
                        <a href="<?php echo get_product_url($related_product['slug']); ?>"
                            class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-2 rounded transition duration-200">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include_once 'partials/footer.php';
?>

<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/product-gallery.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/reviews.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/simple-editor-frontend.css">
<script>
// Biến toàn cục cho JavaScript
const BASE_URL = '<?php echo BASE_URL; ?>';
</script>
<script src="<?php echo BASE_URL; ?>/assets/js/product.js"></script>
<script src="<?php echo BASE_URL; ?>/assets/js/product-tabs.js"></script>
<script src="<?php echo BASE_URL; ?>/assets/js/reviews.js"></script>
<script src="<?php echo BASE_URL; ?>/assets/js/cart-realtime.js"></script>