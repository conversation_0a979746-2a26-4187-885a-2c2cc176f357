<?php
// Test file để kiểm tra liên kết sản phẩm
require_once 'includes/init.php';

// Lấy một vài sản phẩm để test
$products = get_products(5);

echo "<h1>Test Product Links</h1>";
echo "<p>Kiểm tra xem các liên kết sản phẩm có hoạt động đúng không:</p>";

if (!empty($products)) {
    echo "<ul>";
    foreach ($products as $product) {
        $slug_url = BASE_URL . "/product.php?slug=" . $product['slug'];
        $id_url = BASE_URL . "/product.php?id=" . $product['id'];
        
        echo "<li>";
        echo "<strong>" . htmlspecialchars($product['name']) . "</strong><br>";
        echo "Slug URL: <a href='" . $slug_url . "' target='_blank'>" . $slug_url . "</a><br>";
        echo "ID URL: <a href='" . $id_url . "' target='_blank'>" . $id_url . "</a><br>";
        echo "Slug: " . htmlspecialchars($product['slug']) . "<br>";
        echo "ID: " . $product['id'] . "<br><br>";
        echo "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>Không có sản phẩm nào để test.</p>";
}

echo "<h2>Test Cart Items</h2>";
$cart_items = get_cart_items();
if (!empty($cart_items)) {
    echo "<ul>";
    foreach ($cart_items as $item) {
        echo "<li>";
        echo "<strong>" . htmlspecialchars($item['name']) . "</strong><br>";
        echo "Product ID: " . $item['product_id'] . "<br>";
        echo "Slug: " . (isset($item['slug']) ? htmlspecialchars($item['slug']) : 'Không có') . "<br>";
        if (isset($item['slug']) && !empty($item['slug'])) {
            $cart_url = BASE_URL . "/product.php?slug=" . $item['slug'];
            echo "Cart URL: <a href='" . $cart_url . "' target='_blank'>" . $cart_url . "</a><br>";
        }
        echo "</li><br>";
    }
    echo "</ul>";
} else {
    echo "<p>Giỏ hàng trống.</p>";
}
?>
