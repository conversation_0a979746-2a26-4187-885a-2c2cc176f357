# Hướng Dẫn Điều Khiển AI - Làm Việc Thông Minh Hơn

câu lệnh triển khai: 

R<PERSON><PERSON> tốt, giờ bạn hãy triển khai code lần lượt từng phần gi<PERSON><PERSON> tôi, chu<PERSON><PERSON> ui ux,không được sửa những gì làm ảnh hưởng đến logic, Không tạo file mới / Chỉ sửa file hiện có

## 🎯 NGUYÊN TẮC CHUNG

### ❌ Tránh:
- Yêu cầu mơ hồ: "Làm đẹp giao diện"
- Không context: "Thêm tính năng X"
- <PERSON><PERSON> <PERSON> tự quyết định: "Tùy bạn thiết kế"

### ✅ Nên:
- <PERSON><PERSON> thể: "Responsive mobile cho header, giữ màu cam hiện tại"
- Có context: "Website nội thất, user 25-45 tuổi"
- <PERSON><PERSON><PERSON> soá<PERSON>: "<PERSON>ân tích trước, tôi approve rồ<PERSON> mới code"

---

## 📋 TEMPLATE LỆNH CHUẨN

### 1. **LỆNH THIẾT KẾ/UI**
```
THIẾT KẾ: [Tên component/trang]

CONTEXT:
- Website: [loại hình, target user]
- Style hiện tại: [màu sắc, font, layout style]
- Vị trí: [đặt ở đâu trong trang]

REQUIREMENTS:
- Chức năng: [user làm gì, hệ thống phản ứng sao]
- Design: [màu sắc, kích thước, animation]
- Responsive: [mobile-first/desktop-first, breakpoints]
- Performance: [loading time, file size limits]

CONSTRAINTS:
- Không tạo file mới / Chỉ sửa file hiện có
- Tương thích: [browsers, devices]
- Không dùng: [libraries/frameworks không muốn]

WORKFLOW:
1. ANALYZE FIRST - mô tả plan chi tiết
2. Tôi review & approve
3. Implementation
4. Test checklist

REFERENCE: [link/mô tả ví dụ tương tự]
```

### 2. **LỆNH FIX BUG/ISSUE**
```
FIX: [Mô tả vấn đề ngắn gọn]

PROBLEM:
- Hiện tượng: [user thấy gì]
- Expected: [should be như thế nào]
- Browser/Device: [nếu specific]

SCOPE:
- Chỉ fix vấn đề này
- Không refactor code khác
- Không tạo file documentation

APPROACH:
- Minimal changes only
- Test sau khi fix
- Giải thích nguyên nhân (ngắn gọn)
```

### 3. **LỆNH RESPONSIVE**
```
RESPONSIVE: [Component/Page name]

CURRENT STATE:
- Desktop: [mô tả hiện tại]
- Mobile issues: [liệt kê vấn đề cụ thể]

TARGET:
- Mobile: [mô tả mong muốn]
- Tablet: [nếu cần]
- Breakpoints: [320px, 768px, 1024px...]

KEEP UNCHANGED:
- Colors: [liệt kê màu không đổi]
- Functionality: [features giữ nguyên]
- Performance: [không làm chậm]

METHOD:
- Mobile-first approach
- Test trên device thực
- Kiểm tra touch targets (44px min)
```

---

## 🧠 KEYWORDS ĐIỀU KHIỂN

### **Bắt AI suy nghĩ trước:**
- `THINK-FIRST-MODE`
- `ANALYZE BEFORE CODE`
- `PLAN FIRST, CODE LATER`

### **Giữ đơn giản:**
- `SIMPLE FIX ONLY`
- `MINIMAL CHANGES`
- `NO OVER-ENGINEERING`
- `NO EXTRA FILES`

### **Focus vào user:**
- `USER-FOCUSED DESIGN`
- `UX-FIRST APPROACH`
- `ACCESSIBILITY AWARE`

### **Kiểm soát chất lượng:**
- `CONTRAST CHECK REQUIRED`
- `MOBILE TEST MANDATORY`
- `PERFORMANCE CONSCIOUS`

---

## 🎨 DESIGN GUIDELINES

### **Màu sắc & Contrast:**
```
"Kiểm tra contrast ratio:
- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- Interactive elements: rõ ràng all states
- Hover: không được same color background/text"
```

### **Responsive Rules:**
```
"Mobile-first approach:
- Touch targets: 44px minimum
- Text: 16px+ (không zoom)
- Spacing: đủ rộng cho fat fingers
- Navigation: thumb-friendly"
```

### **Animation Standards:**
```
"Animation guidelines:
- Duration: 0.2-0.5s (không quá chậm)
- Easing: ease-out cho entrance, ease-in cho exit
- Reduce motion: respect prefers-reduced-motion
- Performance: 60fps, avoid layout thrashing"
```

---

## 🔧 WORKFLOW CONTROL

### **Bước 1: Planning Phase**
```
"PLANNING PHASE:
1. Phân tích requirements
2. Identify affected files
3. Mô tả approach & structure
4. List potential issues
5. Estimate complexity

→ WAIT FOR APPROVAL trước khi code"
```

### **Bước 2: Implementation**
```
"IMPLEMENTATION RULES:
- Code từng file một, test từng bước
- Explain changes made
- Highlight important decisions
- Note any compromises/trade-offs"
```

### **Bước 3: Quality Check**
```
"QUALITY CHECKLIST:
□ Cross-browser compatibility
□ Mobile responsive
□ Accessibility (keyboard, screen reader)
□ Performance impact
□ Code maintainability"
```

---

## 🚨 EMERGENCY COMMANDS

### **Khi AI làm phức tạp:**
```
"STOP - SIMPLIFY MODE:
Quay lại approach đơn giản nhất.
Chỉ fix vấn đề được nêu.
Không tạo thêm file/system."
```

### **Khi AI không hiểu context:**
```
"CONTEXT RESET:
Website: [type]
Current colors: [list]
User behavior: [describe]
Technical constraints: [list]
Now redo the task with this context."
```

### **Khi cần restart:**
```
"FRESH START:
Ignore previous approach.
Re-read requirements.
Think step-by-step.
Ask clarifying questions if needed."
```

---

## 💡 TIPS NÂNG CAO

### **Để AI tự check logic:**
```
"Before coding, explain:
1. User journey step-by-step
2. All interaction states
3. Edge cases handling
4. Mobile vs desktop differences"
```

### **Để AI optimize performance:**
```
"Performance requirements:
- Page load: < 3s
- Interaction: < 100ms
- Animation: 60fps
- Bundle size: minimize
Optimize accordingly."
```

### **Để AI focus accessibility:**
```
"Accessibility checklist:
- Keyboard navigation
- Screen reader friendly
- Color blind safe
- High contrast mode
- Focus indicators"
```

---

## 🎯 KẾT LUẬN

**Công thức thành công:**
`Context rõ ràng + Requirements cụ thể + Workflow kiểm soát = AI thông minh`

**Remember:**
- AI cần được "dạy" từng project
- Càng cụ thể → kết quả càng tốt
- Kiểm soát workflow → tránh over-engineering
- Test & feedback → AI học và cải thiện
