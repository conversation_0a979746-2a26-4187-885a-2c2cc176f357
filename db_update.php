<?php
// Kết nối đến cơ sở dữ liệu
require_once 'includes/init.php';

// Tiêu đề trang
$page_title = 'Cập nhật cơ sở dữ liệu';

// Khởi tạo biến
$error = '';
$success = '';
$info = '';
$redirect_url = '';

// Lấy tên file SQL từ tham số URL
$sql_file = isset($_GET['file']) ? $_GET['file'] : '';

// Danh sách các file SQL được phép chạy
$allowed_files = [
    'add_blog_homepage_field' => [
        'title' => 'Thêm trường hiển thị bài viết trên trang chủ',
        'description' => 'Thêm trường show_on_homepage vào bảng blog_posts để cho phép hiển thị bài viết trong phần Cẩm nang nội thất trên trang chủ.',
        'path' => 'sql_updates/add_blog_homepage_field.sql',
        'redirect' => 'admin/blog-posts.php'
    ],
    'add_product_homepage_field' => [
        'title' => 'Thêm trường hiển thị sản phẩm trên trang chủ',
        'description' => 'Thêm trường show_on_homepage vào bảng products để cho phép hiển thị sản phẩm trong phần Sản phẩm nội thất bằng vũ trên trang chủ.',
        'path' => 'sql_updates/add_product_homepage_field.sql',
        'redirect' => 'admin/products.php'
    ],
    'update_blog_authors_fields' => [
        'title' => 'Cập nhật bảng blog_authors',
        'description' => 'Thêm các trường mới vào bảng blog_authors và cập nhật liên kết với bảng blog_posts.',
        'path' => 'sql_updates/update_blog_authors_fields.sql',
        'redirect' => 'admin/blog-authors.php'
    ],
    'add_user_status_field' => [
        'title' => 'Cập nhật bảng users',
        'description' => 'Thêm trường status vào bảng users để quản lý trạng thái tài khoản (hoạt động/bị khóa) và các trường liên quan đến reset mật khẩu.',
        'path' => 'sql_updates/add_user_status_field.sql',
        'redirect' => 'admin/users.php'
    ],
    'add_address_fields' => [
        'title' => 'Thêm trường địa chỉ chi tiết',
        'description' => 'Thêm các trường province_code, district_code, ward_code và address_detail vào bảng users để hỗ trợ chọn địa chỉ từ API.',
        'path' => 'sql_updates/add_address_fields.sql',
        'redirect' => 'account/profile.php'
    ]
];

// Nếu không có file nào được chỉ định, hiển thị danh sách các file có sẵn
if (empty($sql_file)) {
    $info = 'Vui lòng chọn một file SQL để cập nhật cơ sở dữ liệu.';
} else {
    // Kiểm tra xem file có trong danh sách được phép không
    if (!isset($allowed_files[$sql_file])) {
        $error = 'File SQL không hợp lệ.';
    } else {
        $file_info = $allowed_files[$sql_file];
        $sql_path = $file_info['path'];

        // Kiểm tra xem file có tồn tại không
        if (!file_exists($sql_path)) {
            $error = 'File SQL không tồn tại.';
        } else {
            try {
                // Đọc nội dung file SQL
                $sql_content = file_get_contents($sql_path);

                // Tách các câu lệnh SQL
                $sql_statements = explode(';', $sql_content);

                // Biến để theo dõi trạng thái transaction
                $transaction_started = false;

                try {
                    // Thiết lập PDO để sử dụng buffered queries
                    $conn->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);

                    // Không sử dụng transaction để tránh lỗi
                    $transaction_started = false;

                    // Thực thi từng câu lệnh SQL
                    foreach ($sql_statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement)) {
                            // Sử dụng try-catch cho từng câu lệnh để tránh lỗi unbuffered query
                            try {
                                $stmt = $conn->prepare($statement);
                                $stmt->execute();
                                // Đảm bảo đọc hết kết quả nếu là câu lệnh SELECT
                                if (stripos($statement, 'SELECT') === 0) {
                                    $stmt->fetchAll(PDO::FETCH_ASSOC);
                                }
                                $info .= "<p>Đã thực thi: " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>";
                            } catch (PDOException $e) {
                                // Ghi log lỗi và tiếp tục nếu lỗi không nghiêm trọng
                                $info .= "<p class='text-yellow-600'>Cảnh báo: " . htmlspecialchars($e->getMessage()) . " khi thực thi: " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>";
                            }
                        }
                    }

                    // Tạo thư mục uploads cho avatar người dùng nếu chưa tồn tại
                    $upload_dir = UPLOADS_PATH . 'avatars';
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                        $info .= "<p>Đã tạo thư mục uploads cho avatar người dùng</p>";
                    }

                    $success = "Cập nhật cơ sở dữ liệu thành công!";

                    // Thiết lập URL chuyển hướng
                    if (!empty($file_info['redirect'])) {
                        $redirect_url = BASE_URL . '/' . $file_info['redirect'];
                    }
                } catch (Exception $e) {
                    // Không cần rollback vì không sử dụng transaction
                    throw $e; // Ném lại ngoại lệ để xử lý ở catch bên ngoài
                }
            } catch (Exception $e) {
                $error = "Lỗi: " . $e->getMessage();
            }
        }
    }
}

// Tự động chuyển hướng sau 3 giây nếu thành công và có URL chuyển hướng
$auto_redirect = !empty($success) && !empty($redirect_url);
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php if ($auto_redirect): ?>
    <meta http-equiv="refresh" content="3;url=<?php echo $redirect_url; ?>">
    <?php endif; ?>
    <style>
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
        }
        .success-icon {
            font-size: 4rem;
            color: #10B981;
        }
        .error-icon {
            font-size: 4rem;
            color: #EF4444;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-4"><?php echo $page_title; ?></h1>

                <?php if (!empty($error)): ?>
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle error-icon"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-bold">Lỗi</p>
                            <p><?php echo $error; ?></p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle success-icon"></i>
                        </div>
                        <div class="ml-4">
                            <p class="font-bold">Thành công</p>
                            <p><?php echo $success; ?></p>
                            <?php if ($auto_redirect): ?>
                            <p class="mt-2">Bạn sẽ được chuyển hướng đến trang quản lý tác giả trong 3 giây...</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($info)): ?>
                <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
                    <div class="flex">
                        <div class="ml-3">
                            <p class="font-bold">Thông tin</p>
                            <?php echo $info; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (empty($sql_file)): ?>
                <div class="mt-6">
                    <h2 class="text-xl font-semibold mb-4">Chọn file SQL để cập nhật</h2>
                    <div class="space-y-4">
                        <?php foreach ($allowed_files as $key => $file): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <h3 class="font-bold"><?php echo $file['title']; ?></h3>
                            <p class="text-gray-600 mb-3"><?php echo $file['description']; ?></p>
                            <a href="<?php echo BASE_URL; ?>/db_update.php?file=<?php echo $key; ?>" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline inline-block">
                                Cập nhật
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="mt-6">
                    <?php if (!empty($redirect_url)): ?>
                    <a href="<?php echo $redirect_url; ?>" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Đi đến trang quản lý tác giả
                    </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/db_update.php" class="ml-2 inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Quay lại danh sách cập nhật
                    </a>
                    <a href="<?php echo BASE_URL; ?>/admin" class="ml-2 inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Quay lại trang quản trị
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
