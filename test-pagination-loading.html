<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagination Loading Effect</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-pagination {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .pagination-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .page-item {
            margin: 0;
        }
        .page-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            height: 44px;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #ffffff;
            color: #6b7280;
            font-weight: 500;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            cursor: pointer;
        }
        .page-link:hover {
            background: rgba(243, 115, 33, 0.05);
            border-color: rgba(243, 115, 33, 0.3);
            color: #F37321;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(243, 115, 33, 0.15);
        }
        .page-link.loading {
            pointer-events: none !important;
            cursor: not-allowed !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
        }

        /* Pagination Loading Content */
        .pagination-loading-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Enhanced Loading Dots Animation */
        .loading-dots {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-dots .dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #F37321;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots .dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dots .dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        .loading-dots .dot:nth-child(3) {
            animation-delay: 0s;
        }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        /* Loading Text Animation */
        .loading-text {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6c757d;
            animation: loadingTextPulse 2s ease-in-out infinite;
        }

        @keyframes loadingTextPulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        .page-item.active .page-link {
            background: #F37321;
            border-color: #F37321;
            color: white;
        }
        .page-item.disabled .page-link {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        .page-text {
            margin: 0 0.25rem;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .fa-spin {
            animation: spin 1s linear infinite;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <h1>🔄 Test Pagination Loading Effect</h1>
        <p>Demo hiệu ứng loading cho pagination buttons</p>
        
        <div class="test-section">
            <h3>Demo Pagination với Loading Effect</h3>
            <p>Click vào các nút để xem hiệu ứng loading</p>
            
            <nav class="demo-pagination">
                <ul class="pagination-list">
                    <!-- Previous Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">
                            <i class="fas fa-chevron-left"></i>
                            <span class="page-text">Trước</span>
                        </a>
                    </li>
                    
                    <!-- Page Numbers -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">1</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link demo-pagination-link" href="#" data-page="2">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="4">4</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="5">5</a>
                    </li>
                    
                    <!-- Next Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">
                            <span class="page-text">Sau</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div id="demo-result"></div>
        </div>

        <div class="test-section">
            <h3>Test với Products Page Thực Tế</h3>
            <p>Mở products page và test pagination loading</p>
            <button class="test-button" onclick="openProductsPage()">Mở Products Page</button>
            <button class="test-button" onclick="testRealPagination()">Hướng Dẫn Test</button>
            <div id="real-test-result"></div>
        </div>
    </div>

    <script>
        // Demo pagination loading effect
        document.addEventListener('click', function(e) {
            if (e.target.closest('.demo-pagination-link')) {
                e.preventDefault();
                const link = e.target.closest('.demo-pagination-link');
                
                // Add loading state
                addDemoLoadingState(link);
                
                // Simulate loading time
                setTimeout(() => {
                    removeDemoLoadingState(link);
                    showResult('demo-result', `✅ Clicked page ${link.dataset.page} - Loading effect completed!`, 'success');
                }, 1500);
            }
        });

        function addDemoLoadingState(clickedLink) {
            // Save original content
            if (!clickedLink.dataset.originalContent) {
                clickedLink.dataset.originalContent = clickedLink.innerHTML;
            }
            
            // Add loading state
            clickedLink.style.opacity = '0.6';
            clickedLink.style.pointerEvents = 'none';
            clickedLink.classList.add('loading');
            
            // Change content based on button type
            const originalContent = clickedLink.dataset.originalContent;
            
            if (originalContent.includes('page-text')) {
                // Previous/Next buttons with text
                if (originalContent.includes('Trước')) {
                    clickedLink.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span class="page-text">Đang tải...</span>';
                } else if (originalContent.includes('Sau')) {
                    clickedLink.innerHTML = '<span class="page-text">Đang tải...</span><i class="fas fa-spinner fa-spin"></i>';
                }
            } else {
                // Number buttons
                clickedLink.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }
        }

        function removeDemoLoadingState(link) {
            // Restore original content
            if (link.dataset.originalContent) {
                link.innerHTML = link.dataset.originalContent;
                delete link.dataset.originalContent;
            }
            
            // Remove loading state
            link.style.opacity = '';
            link.style.pointerEvents = '';
            link.classList.remove('loading');
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function openProductsPage() {
            window.open('http://localhost/noithatbangvu/products.php', '_blank');
            showResult('real-test-result', '✅ Products page opened in new tab', 'success');
        }

        function testRealPagination() {
            showResult('real-test-result', `
                📋 <strong>Hướng Dẫn Test Loading Effect:</strong><br><br>
                1. <strong>Mở products page</strong> trong tab mới<br>
                2. <strong>Scroll xuống</strong> phần pagination<br>
                3. <strong>Click vào số trang</strong> (2, 3, 4...)<br>
                4. <strong>Quan sát:</strong><br>
                   &nbsp;&nbsp;• Nút sẽ hiển thị spinner loading<br>
                   &nbsp;&nbsp;• Opacity giảm xuống 0.6<br>
                   &nbsp;&nbsp;• Không thể click được (pointer-events: none)<br>
                   &nbsp;&nbsp;• Loading kéo dài ~800ms<br>
                   &nbsp;&nbsp;• Sau đó nút trở về bình thường<br>
                   &nbsp;&nbsp;• Sản phẩm thay đổi không reload trang<br><br>
                5. <strong>Test các loại nút:</strong><br>
                   &nbsp;&nbsp;• Nút số: Hiển thị spinner<br>
                   &nbsp;&nbsp;• Nút "Trước": Spinner + "Đang tải..."<br>
                   &nbsp;&nbsp;• Nút "Sau": "Đang tải..." + Spinner
            `, 'info');
        }

        // Show initial demo message
        showResult('demo-result', '👆 Click vào các nút pagination ở trên để xem demo loading effect', 'info');
    </script>
</body>
</html>
