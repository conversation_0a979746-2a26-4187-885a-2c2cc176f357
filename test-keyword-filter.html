<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Keyword Filter Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        .expected {
            background: #e8f5e8;
            border-left-color: #28a745;
        }
        .issue {
            background: #ffeaea;
            border-left-color: #dc3545;
        }
        .fixed {
            background: #e8f4fd;
            border-left-color: #17a2b8;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Case: Keyword Filter Tag Bug Fix</h1>
    
    <div class="highlight">
        <strong>Vấn đề:</strong> Khi xóa tag từ khóa, sau đó chọn filter khác thì tag từ khóa lại hiển thị.
    </div>

    <div class="test-section">
        <h2>📋 Các bước test:</h2>
        
        <div class="step">
            <h3>Bước 1: Tìm kiếm từ khóa</h3>
            <p>1. Mở trang <code>http://localhost/noithatbangvu/products.php</code></p>
            <p>2. Nhập từ khóa "tủ" vào ô tìm kiếm</p>
            <p>3. Nhấn Enter hoặc click nút tìm kiếm</p>
            <div class="expected">
                <strong>Kết quả mong đợi:</strong> Tag từ khóa "tủ" hiển thị trong Filter Results Header
            </div>
        </div>

        <div class="step">
            <h3>Bước 2: Xóa tag từ khóa</h3>
            <p>1. Click vào nút X trên tag từ khóa "tủ"</p>
            <div class="expected">
                <strong>Kết quả mong đợi:</strong> Tag từ khóa biến mất, danh sách sản phẩm cập nhật
            </div>
        </div>

        <div class="step">
            <h3>Bước 3: Chọn filter khác</h3>
            <p>1. Trong sidebar, chọn một khoảng giá (ví dụ: "Dưới 1 triệu")</p>
            <p>2. Hoặc chọn một danh mục sản phẩm</p>
            <p>3. Nhấn nút "Áp dụng bộ lọc"</p>
            <div class="issue">
                <strong>Lỗi cũ:</strong> Tag từ khóa "tủ" lại hiển thị
            </div>
            <div class="fixed">
                <strong>Sau khi fix:</strong> Tag từ khóa KHÔNG hiển thị, chỉ có filter mới được chọn
            </div>
        </div>

        <div class="step">
            <h3>Bước 4: Test thêm trường hợp khác</h3>
            <p>1. Nhập lại từ khóa "tủ" và tìm kiếm</p>
            <p>2. Xóa tag từ khóa</p>
            <p>3. Gõ từ khóa khác vào ô tìm kiếm (ví dụ: "ghế") nhưng KHÔNG nhấn Enter</p>
            <p>4. Chọn filter khác và áp dụng</p>
            <div class="fixed">
                <strong>Sau khi fix:</strong> Tag từ khóa vẫn KHÔNG hiển thị (không lấy từ ô tìm kiếm)
            </div>
        </div>

        <div class="step">
            <h3>Bước 5: Test tìm kiếm mới</h3>
            <p>1. Nhập từ khóa "ghế" và nhấn Enter (thực sự submit)</p>
            <div class="expected">
                <strong>Kết quả mong đợi:</strong> Tag từ khóa "ghế" hiển thị bình thường
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Giải pháp đã implement:</h2>
        
        <div class="step">
            <h3>1. Thêm flag tracking</h3>
            <p>Thêm <code>keywordRemoved</code> flag để track khi keyword bị xóa manually</p>
        </div>

        <div class="step">
            <h3>2. Cập nhật collectFilterData()</h3>
            <p>Không lấy keyword từ search input nếu <code>keywordRemoved = true</code></p>
        </div>

        <div class="step">
            <h3>3. Cập nhật removeKeywordFilter()</h3>
            <p>Set <code>keywordRemoved = true</code> khi user xóa tag keyword</p>
        </div>

        <div class="step">
            <h3>4. Reset flag khi cần</h3>
            <p>Reset <code>keywordRemoved = false</code> khi:</p>
            <ul>
                <li>User thực sự submit search mới</li>
                <li>Load all products</li>
                <li>Clear all UI elements</li>
                <li>Sync với keyword từ server</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Kết quả mong đợi:</h2>
        <div class="expected">
            <p><strong>✅ Trước fix:</strong> Tag từ khóa xuất hiện lại sau khi xóa</p>
            <p><strong>✅ Sau fix:</strong> Tag từ khóa KHÔNG xuất hiện lại sau khi xóa</p>
            <p><strong>✅ Tìm kiếm mới:</strong> Vẫn hoạt động bình thường</p>
        </div>
    </div>

    <div class="highlight">
        <strong>Lưu ý:</strong> Mở Developer Tools (F12) để xem console logs và theo dõi hoạt động của <code>keywordRemoved</code> flag.
    </div>
</body>
</html>
