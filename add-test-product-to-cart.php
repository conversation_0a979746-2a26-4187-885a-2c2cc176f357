<?php
// <PERSON>ript để thêm sản phẩm test vào giỏ hàng
require_once 'includes/init.php';

echo "<h1>Thêm sản phẩm test vào giỏ hàng</h1>";

// L<PERSON>y một sản phẩm để test
$products = get_products(1);

if (!empty($products)) {
    $test_product = $products[0];
    
    echo "<h2>Sản phẩm sẽ được thêm:</h2>";
    echo "<p><strong>ID:</strong> " . $test_product['id'] . "</p>";
    echo "<p><strong>Tên:</strong> " . htmlspecialchars($test_product['name']) . "</p>";
    echo "<p><strong>Slug:</strong> " . htmlspecialchars($test_product['slug']) . "</p>";
    echo "<p><strong>Giá:</strong> " . format_currency($test_product['price']) . "</p>";
    
    // Thêm vào giỏ hàng
    $result = add_to_cart($test_product['id'], 1);
    
    if ($result['success']) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; background: #f0fff0; margin: 10px 0;'>";
        echo "✅ " . $result['message'];
        echo "</div>";
        
        echo "<p><a href='cart.php'>Xem giỏ hàng</a></p>";
        echo "<p><a href='test-cart-debug.php'>Debug giỏ hàng</a></p>";
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; background: #fff0f0; margin: 10px 0;'>";
        echo "❌ " . $result['message'];
        echo "</div>";
    }
} else {
    echo "<p>Không có sản phẩm nào trong database để test.</p>";
}

echo "<h2>Thông tin giỏ hàng hiện tại:</h2>";
$cart_items = get_cart_items();
echo "<p>Số lượng items: " . count($cart_items) . "</p>";

if (!empty($cart_items)) {
    echo "<ul>";
    foreach ($cart_items as $item) {
        echo "<li>";
        echo htmlspecialchars($item['name']) . " (ID: " . $item['product_id'] . ")";
        if (isset($item['slug'])) {
            echo " - Slug: " . htmlspecialchars($item['slug']);
        } else {
            echo " - <strong style='color: red;'>Không có slug!</strong>";
        }
        echo "</li>";
    }
    echo "</ul>";
}
?>
