<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto Scroll - Nội Thất Băng <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .test-header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .test-content {
            padding: 40px;
        }
        
        .feature-banner {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .feature-banner h2 {
            margin: 0 0 10px 0;
            color: #1e40af;
            font-size: 1.5rem;
        }
        
        .feature-banner p {
            margin: 0;
            color: #1d4ed8;
            font-size: 1.1rem;
        }
        
        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #3b82f6;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 1.3rem;
        }
        
        .test-link {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .test-link:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .test-link.category {
            background: #8b5cf6;
        }
        
        .test-link.category:hover {
            background: #7c3aed;
        }
        
        .test-link.combined {
            background: #f59e0b;
        }
        
        .test-link.combined:hover {
            background: #d97706;
        }
        
        .test-link.manual {
            background: #10b981;
        }
        
        .test-link.manual:hover {
            background: #059669;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 5px 0;
        }
        
        .status-auto {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-manual {
            background: #dcfce7;
            color: #166534;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #1a202c;
            font-size: 1.1rem;
        }
        
        .feature-card p {
            margin: 0;
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .manual-test {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .manual-test h4 {
            margin: 0 0 10px 0;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-arrows-alt-v"></i> Test Auto Scroll to Products</h1>
            <p>Kiểm tra tính năng tự động cuộn xuống phần Products khi filter/search</p>
        </div>
        
        <div class="test-content">
            <div class="feature-banner">
                <h2><i class="fas fa-magic"></i> Tính Năng Auto Scroll!</h2>
                <p>Tự động cuộn xuống phần Products với tính toán chính xác chiều cao header</p>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search"></i> Test Auto Scroll - Tìm Kiếm</h3>
                <p>Các link này sẽ tự động cuộn xuống phần Products sau khi load:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=sofa" class="test-link" target="_blank">
                    <i class="fas fa-search"></i> Tìm "sofa"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=bàn" class="test-link" target="_blank">
                    <i class="fas fa-search"></i> Tìm "bàn"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=ghế" class="test-link" target="_blank">
                    <i class="fas fa-search"></i> Tìm "ghế"
                </a>
                
                <div class="status-auto">
                    <i class="fas fa-robot"></i> 🤖 Auto scroll sau 300ms khi page load
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-filter"></i> Test Auto Scroll - Filter Danh Mục</h3>
                <p>Các link này sẽ tự động cuộn xuống phần Products:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?category=1" class="test-link category" target="_blank">
                    <i class="fas fa-tag"></i> Danh mục 1
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?category=2" class="test-link category" target="_blank">
                    <i class="fas fa-tag"></i> Danh mục 2
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?category=3" class="test-link category" target="_blank">
                    <i class="fas fa-tag"></i> Danh mục 3
                </a>
                
                <div class="status-auto">
                    <i class="fas fa-robot"></i> 🤖 Auto scroll với tính toán header height
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-layer-group"></i> Test Auto Scroll - Kết Hợp</h3>
                <p>Test với nhiều filter kết hợp:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=sofa&category=1" class="test-link combined" target="_blank">
                    <i class="fas fa-search"></i><i class="fas fa-tag"></i> Sofa + Danh mục
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=bàn&sort=price_asc" class="test-link combined" target="_blank">
                    <i class="fas fa-search"></i><i class="fas fa-sort"></i> Bàn + Sort
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?category=1&sort=price_desc" class="test-link combined" target="_blank">
                    <i class="fas fa-tag"></i><i class="fas fa-sort"></i> Category + Sort
                </a>
                
                <div class="status-auto">
                    <i class="fas fa-robot"></i> 🤖 Auto scroll khi có bất kỳ filter nào
                </div>
            </div>
            
            <div class="manual-test">
                <h4><i class="fas fa-hand-pointer"></i> Test Manual Scroll</h4>
                <p>Mở trang products.php trong tab mới và test manual:</p>
                
                <a href="http://localhost/noithatbangvu/products.php" class="test-link manual" target="_blank">
                    <i class="fas fa-external-link-alt"></i> Mở Products Page
                </a>
                
                <div class="code-block">
// Trong console của browser, chạy:
autoScrollToProducts();

// Hoặc kiểm tra thông tin header:
console.log('Header info:', {
    isMobile: window.innerWidth <= 768,
    premiumHeader: document.querySelector('.premium-header'),
    mobileHeader: document.querySelector('.mobile-header'),
    topBar: document.querySelector('.top-bar')
});
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> Cách Hoạt Động</h3>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-mobile-alt text-blue-500"></i> Mobile</h4>
                        <p>Tính chiều cao của .mobile-header để cuộn chính xác</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-desktop text-green-500"></i> Desktop</h4>
                        <p>Tính chiều cao .premium-header trừ đi .top-bar (vì top-bar ẩn khi scroll)</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-crosshairs text-purple-500"></i> Vị Trí Cuộn</h4>
                        <p>productsSection.offsetTop - finalHeaderHeight = vị trí cuộn chính xác</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-clock text-red-500"></i> Timing</h4>
                        <p>Auto scroll sau 300ms để đảm bảo page đã render xong</p>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-list-check"></i> Trigger Conditions</h3>
                
                <div class="status-auto">
                    <i class="fas fa-check"></i> <strong>URL có keyword:</strong> ?keyword=something
                </div>
                
                <div class="status-auto">
                    <i class="fas fa-check"></i> <strong>URL có category:</strong> ?category=1
                </div>
                
                <div class="status-auto">
                    <i class="fas fa-check"></i> <strong>URL có price filter:</strong> ?price_min=1000
                </div>
                
                <div class="status-auto">
                    <i class="fas fa-check"></i> <strong>URL có sort:</strong> ?sort=price_asc
                </div>
                
                <div class="status-manual">
                    <i class="fas fa-times"></i> <strong>Trang thông thường:</strong> Không auto scroll
                </div>
            </div>
            
            <div class="feature-banner">
                <h2><i class="fas fa-rocket"></i> Ready to Test!</h2>
                <p>Click vào các link trên để test tính năng auto scroll</p>
            </div>
        </div>
    </div>
</body>
</html>
