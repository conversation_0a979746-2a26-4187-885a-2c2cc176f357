<?php


// Include init để có các hàm cần thiết
require_once 'includes/init.php';

// Lấy từ khóa tìm kiếm và các tham số filter
$keyword = isset($_GET['keyword']) ? sanitize($_GET['keyword']) : '';

// Xử lý multiple categories
$category_ids = [];

// Kiểm tra cả category[] (từ form) và category (từ URL trực tiếp)
if (isset($_GET['category']) && is_array($_GET['category'])) {
    // Từ form với category[]
    $category_ids = array_map('intval', $_GET['category']);
    $category_ids = array_filter($category_ids, function ($id) {
        return $id > 0; });
} elseif (isset($_GET['category']) && !is_array($_GET['category'])) {
    // Từ URL trực tiếp với category=X
    $single_id = (int) $_GET['category'];
    if ($single_id > 0) {
        $category_ids = [$single_id];
    }
}

// Debug: Log để kiểm tra (c<PERSON> thể comment out khi production)
// error_log("DEBUG: _GET = " . print_r($_GET, true));
// error_log("DEBUG: category_ids = " . print_r($category_ids, true));

// Để tương thích với code cũ, lấy category_id đầu tiên
$category_id = !empty($category_ids) ? $category_ids[0] : null;

$price_min = isset($_GET['price_min']) && $_GET['price_min'] !== '' ? (float) $_GET['price_min'] : null;
$price_max = isset($_GET['price_max']) && $_GET['price_max'] !== '' ? (float) $_GET['price_max'] : null;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';
// Xử lý multiple promotion filters
$promotion_filters = [];
if (isset($_GET['promotion'])) {
    if (is_array($_GET['promotion'])) {
        $promotion_filters = array_map('sanitize', $_GET['promotion']);
    } else {
        $promotion_filters = [sanitize($_GET['promotion'])];
    }
}

// Để tương thích với code cũ
$promotion_filter = !empty($promotion_filters) ? $promotion_filters[0] : '';

// Lấy thông tin categories nếu có
$current_categories = [];
if (!empty($category_ids)) {
    foreach ($category_ids as $cat_id) {
        $cat_info = get_category_by_id($cat_id);
        if ($cat_info) {
            $current_categories[] = $cat_info;
        }
    }
}

// Để tương thích với code cũ
$current_category = !empty($current_categories) ? $current_categories[0] : null;

// Thiết lập tiêu đề trang dựa trên có tìm kiếm hay không
if (!empty($keyword)) {
    $page_title = 'Tìm kiếm: ' . $keyword;
    $page_description = 'Kết quả tìm kiếm cho từ khóa "' . $keyword . '" tại Nội Thất Băng Vũ';
} else {
    $page_title = 'Tất cả sản phẩm';
    $page_description = 'Danh sách tất cả sản phẩm tại Nội Thất Băng Vũ';
}

// Include header
include_once 'partials/header.php';
?>

<!-- Giải pháp 1: Overflow Visible CSS và JS -->
<link rel="stylesheet" href="assets/css/search-overflow-solution1.css">
<script src="assets/js/search-overflow-solution1.js"></script>

<?php

// Phân trang
$page = isset($_GET['page']) ? (int) $_GET['page'] : 1;

// Xử lý items per page với cookie support
$allowed_limits = [12, 24, 36, 48];

// Ưu tiên: URL parameter > Cookie > Default
if (isset($_GET['items_per_page'])) {
    $items_per_page = (int) $_GET['items_per_page'];
} elseif (isset($_COOKIE['products_items_per_page']) && in_array((int) $_COOKIE['products_items_per_page'], $allowed_limits)) {
    $items_per_page = (int) $_COOKIE['products_items_per_page'];
} else {
    $items_per_page = ITEMS_PER_PAGE;
}

$limit = in_array($items_per_page, $allowed_limits) ? $items_per_page : ITEMS_PER_PAGE;

$offset = ($page - 1) * $limit;

// Thiết lập tùy chọn sắp xếp
$sort_options = [];
$sort_label = '';
switch ($sort) {
    case 'price_asc':
        $sort_options = ['by' => 'price', 'order' => 'ASC'];
        $sort_label = 'Giá tăng dần';
        break;
    case 'price_desc':
        $sort_options = ['by' => 'price', 'order' => 'DESC'];
        $sort_label = 'Giá giảm dần';
        break;
    case 'name_asc':
        $sort_options = ['by' => 'name', 'order' => 'ASC'];
        $sort_label = 'Tên A-Z';
        break;
    case 'name_desc':
        $sort_options = ['by' => 'name', 'order' => 'DESC'];
        $sort_label = 'Tên Z-A';
        break;
    case 'newest':
    default:
        $sort_options = ['by' => 'created_at', 'order' => 'DESC'];
        $sort_label = 'Mới nhất';
        break;
}




// Lấy danh sách sản phẩm với hỗ trợ multiple filters
$products = get_products_with_filters(
    $category_ids,
    $promotion_filters,
    $limit,
    $offset,
    $keyword,
    1, // status
    $price_min,
    $price_max,
    $sort_options
);

$total_products = count_products_with_filters(
    $category_ids,
    $promotion_filters,
    $keyword,
    1, // status
    $price_min,
    $price_max
);

$total_pages = ceil($total_products / $limit);
?>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Professional Pagination CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-pagination.css">

<!-- Link CSS cho search page để sử dụng product card styles -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-page.css">

<!-- Enhanced Search JavaScript -->
<script src="<?php echo BASE_URL; ?>/assets/js/enhanced-search.js" defer></script>

<!-- Custom CSS for Orange Luxury Filter Results Header -->
<style>
    /* Enhanced responsive design for Filter Results Header */
    @media (max-width: 768px) {
        .filter-results-header {
            padding: 1rem !important;
            margin-bottom: 1rem !important;
        }

        .filter-results-header .flex {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 0.75rem !important;
        }

        .filter-results-header h2 {
            font-size: 1.125rem !important;
            line-height: 1.4 !important;
        }

        .filter-results-header .quick-actions {
            width: 100% !important;
            justify-content: stretch !important;
        }

        .filter-results-header .quick-actions>* {
            flex: 1 !important;
            justify-content: center !important;
        }
    }

    @media (max-width: 640px) {
        .filter-results-header .icon-container {
            width: 2rem !important;
            height: 2rem !important;
        }

        .filter-results-header .icon-container i {
            font-size: 0.75rem !important;
        }

        .filter-results-header .quick-actions {
            flex-direction: column !important;
            gap: 0.5rem !important;
        }

        .active-filters-container {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 0.5rem !important;
        }

        .filter-tag {
            width: 100% !important;
            justify-content: space-between !important;
        }
    }

    /* Subtle micro-interactions and animations */
    .filter-results-header {
        animation: fadeInUp 0.4s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .filter-tag {
        transition: all 0.2s ease;
        transform-origin: center;
    }

    .filter-tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(251, 146, 60, 0.08);
    }

    .filter-tag .remove-btn {
        transition: all 0.15s ease;
    }

    .filter-tag .remove-btn:hover {
        transform: scale(1.05);
        background: #fed7aa;
    }

    /* Subtle gradient animations */
    .gradient-bg {
        background-size: 150% 150%;
        animation: subtleGradientShift 12s ease infinite;
    }

    @keyframes subtleGradientShift {
        0% {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0% 50%;
        }
    }

    /* Gentle floating decorative elements animation */
    .decorative-element {
        animation: gentleFloat 8s ease-in-out infinite;
    }

    @keyframes gentleFloat {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-3px);
        }
    }

    /* Subtle button interactions */
    .luxury-button {
        position: relative;
        overflow: hidden;
    }

    .luxury-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.3s;
    }

    .luxury-button:hover::before {
        left: 100%;
    }

    /* Gentle pulse effect for count badge */
    .count-badge {
        animation: gentlePulse 3s infinite;
    }

    @keyframes gentlePulse {
        0% {
            box-shadow: 0 0 0 0 rgba(251, 146, 60, 0.3);
        }

        70% {
            box-shadow: 0 0 0 4px rgba(251, 146, 60, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(251, 146, 60, 0);
        }
    }

    /* Smooth transitions for interactive elements */
    .filter-tag,
    .luxury-button {
        transition: all 0.15s ease;
    }

    /* Focus states for accessibility */
    .filter-tag:focus-within,
    .luxury-button:focus {
        outline: 2px solid #fb923c;
        outline-offset: 2px;
    }

    /* Focus on content - reduce visual noise */
    .filter-results-header {
        background: linear-gradient(135deg, #fefbf3/60, #fef7ed/60);
        border: 1px solid #fed7aa/30;
        backdrop-filter: blur(8px);
    }

    .filter-results-header.empty-state {
        background: linear-gradient(135deg, #fef3c7/60, #fde68a/60);
        border: 1px solid #f59e0b/30;
    }

    /* Subtle quick search tags */
    .quick-search-tag {
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid rgba(251, 146, 60, 0.2);
        color: #6b7280;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .quick-search-tag:hover {
        background: rgba(255, 247, 237, 0.9);
        border-color: rgba(251, 146, 60, 0.4);
        color: #ea580c;
        transform: translateY(-1px);
    }

    /* Reduce sidebar visual weight */
    .sidebar-filters {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    /* Focus on products grid */
    .products-grid {
        position: relative;
        z-index: 1;
    }

    .product-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .product-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(251, 146, 60, 0.2);
    }

    /* Subtle pagination */
    .pagination-section {
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(8px);
        border-radius: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Reduce button prominence except primary actions */
    .luxury-button:not(.primary-action) {
        opacity: 0.8;
        font-weight: 500;
    }

    .luxury-button:not(.primary-action):hover {
        opacity: 1;
    }

    /* Clean empty state */
    .simple-empty-state {
        background: none;
        border: none;
        box-shadow: none;
    }

    /* Animated Background Pattern for Filter Results Header */
    .filter-bg-pattern {
        background-image:
            /* Geometric dots pattern */
            radial-gradient(circle at 20% 20%, #f97316 1.5px, transparent 1.5px),
            radial-gradient(circle at 80% 80%, #f59e0b 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, #fb923c 1px, transparent 1px),
            radial-gradient(circle at 70% 30%, #f97316 0.8px, transparent 0.8px),
            /* Subtle lines */
            linear-gradient(45deg, transparent 48%, rgba(249, 115, 22, 0.1) 49%, rgba(249, 115, 22, 0.1) 51%, transparent 52%),
            linear-gradient(135deg, transparent 48%, rgba(251, 146, 60, 0.08) 49%, rgba(251, 146, 60, 0.08) 51%, transparent 52%);
        background-size:
            60px 60px,
            40px 40px,
            80px 80px,
            30px 30px,
            120px 120px,
            100px 100px;
        background-position:
            0 0,
            20px 20px,
            40px 10px,
            60px 40px,
            0 0,
            50px 50px;
        animation: filterPatternMove 25s linear infinite;
    }

    @keyframes filterPatternMove {
        0% {
            background-position:
                0 0,
                20px 20px,
                40px 10px,
                60px 40px,
                0 0,
                50px 50px;
        }

        100% {
            background-position:
                60px 60px,
                80px 80px,
                120px 70px,
                90px 70px,
                120px 120px,
                150px 150px;
        }
    }

    /* Loading Skeleton for Filter Results Header */
    .filter-results-header.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .filter-results-header.loading .icon-container {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
    }

    .filter-results-header.loading .count-badge {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
    }

    .filter-results-header.loading h2,
    .filter-results-header.loading .text-gray-600 {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
        border-radius: 4px;
    }

    .filter-results-header.loading .luxury-button {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        color: transparent;
        border-color: transparent;
    }

    @keyframes skeleton-loading {
        0% {
            background-position: 200% 0;
        }

        100% {
            background-position: -200% 0;
        }
    }

    /* Smooth transitions for count badge */
    .count-badge {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center;
    }

    .count-badge.updating {
        animation: countUpdate 0.6s ease-in-out;
    }

    @keyframes countUpdate {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }

        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Smooth transitions for header content */
    .filter-results-header h2 {
        transition: all 0.3s ease;
    }

    .filter-results-header .text-gray-600 {
        transition: all 0.3s ease;
    }

    .filter-results-header.empty-state {
        background: linear-gradient(135deg, #fef3c7/80, #fde68a/80);
        border-color: #f59e0b/40;
    }

    .filter-results-header.empty-state .icon-container.empty-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    /* Visual Hierarchy for Empty States */
    .filter-results-header.empty-state {
        margin-bottom: 2rem;
        border-left: 4px solid #f59e0b;
    }

    /* Enhanced Products Grid Empty State */
    .products-grid-empty {
        background: linear-gradient(135deg, #fefbf3, #fef7ed);
        border: 2px dashed #fed7aa;
        border-radius: 1rem;
        margin: 1rem;
        position: relative;
        overflow: hidden;
    }

    .products-grid-empty::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #f97316, #ea580c, #f97316);
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% {
            background-position: -200% 0;
        }

        100% {
            background-position: 200% 0;
        }
    }

    /* Empty state flow animation */
    .filter-results-header.empty-state+.products-content .products-grid-empty {
        animation: slideInFromTop 0.6s ease-out 0.3s both;
    }

    @keyframes slideInFromTop {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Improved empty state icon styling */
    .empty-state-icon-container {
        position: relative;
        display: inline-block;
    }

    .empty-state-icon-container::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 120%;
        height: 120%;
        border: 2px solid #fed7aa;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: pulse-ring 2s ease-in-out infinite;
    }

    @keyframes pulse-ring {
        0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 1;
        }

        100% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0;
        }
    }

    /* Custom Checkbox Styling */
    .custom-checkbox {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid #d1d5db;
        border-radius: 0.375rem;
        background-color: white;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease-in-out;
    }

    .custom-checkbox:hover {
        border-color: #fb923c;
        box-shadow: 0 0 0 3px rgba(251, 146, 60, 0.1);
    }

    .custom-checkbox:focus {
        outline: none;
        border-color: #f97316;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    }

    .custom-checkbox:checked {
        background-color: #f97316;
        border-color: #f97316;
        background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
        background-size: 0.75rem;
        background-position: center;
        background-repeat: no-repeat;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    }

    .custom-checkbox:checked:hover {
        background-color: #ea580c;
        border-color: #ea580c;
    }

    .custom-checkbox:checked:focus {
        background-color: #f97316;
        border-color: #f97316;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.3);
    }

    /* Animation for checkbox */
    .custom-checkbox:checked {
        animation: checkboxPop 0.2s ease-in-out;
    }

    @keyframes checkboxPop {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.1);
        }

        100% {
            transform: scale(1);
        }
    }

    /* Custom Price Input Styling */
    .price-input {
        padding: 8px 16px 8px 16px !important;
    }

    /* Debug CSS cho Recently Viewed và Trending sections */
    .recently-viewed-section,
    .trending-section {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        position: relative !important;
        z-index: 10 !important;
        pointer-events: auto !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        margin-top: 1.5rem !important;
    }

    .recently-viewed-section *,
    .trending-section * {
        pointer-events: auto !important;
    }

    /* Đảm bảo không có element nào che phủ */
    .recently-viewed-section::before,
    .trending-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        z-index: -1;
    }
</style>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="breadcrumb-wrapper">
        <div class="breadcrumb-item">
            <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                <span class="breadcrumb-icon">
                    <i class="fas fa-home"></i>
                </span>
                <span>Trang chủ</span>
            </a>
        </div>
        <div class="breadcrumb-divider">
            <i class="fas fa-chevron-right"></i>
        </div>
        <div class="breadcrumb-item active">
            <span class="breadcrumb-link">
                <span class="breadcrumb-icon">
                    <i class="fas fa-th-large"></i>
                </span>
                <span>Tất cả sản phẩm</span>
            </span>
        </div>
    </div>
</div>

<div class="relative overflow-hidden header-section-with-search">
    <!-- Animated Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-orange-50/30"></div>
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(249,115,22,0.05),transparent_50%)]"></div>

    <!-- Floating Particles Animation -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-300/30 rounded-full animate-bounce"
            style="animation-delay: 0s; animation-duration: 3s;"></div>
        <div class="absolute top-3/4 right-1/3 w-1 h-1 bg-amber-400/40 rounded-full animate-bounce"
            style="animation-delay: 1s; animation-duration: 4s;"></div>
        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-orange-400/25 rounded-full animate-bounce"
            style="animation-delay: 2s; animation-duration: 5s;"></div>
    </div>

    <!-- Content Container -->
    <div class="relative z-10 text-center py-10 px-6">
        <!-- Luxury Badge with Animation -->
        <div
            class="inline-flex items-center bg-gradient-to-r from-orange-500/10 via-orange-400/10 to-amber-500/10 backdrop-blur-sm border border-orange-200/50 text-orange-700 text-sm font-semibold px-6 py-2.5 rounded-full mb-5 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 group">
            <div
                class="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full mr-3 animate-pulse shadow-sm group-hover:animate-spin">
            </div>
            <span class="tracking-wide">LUXURY COLLECTION</span>
            <div class="ml-3 opacity-100 group-hover:animate-bounce transition-all duration-300 flex items-center">
                <i class="fas fa-crown text-amber-500 text-sm"></i>
            </div>
        </div>

        <!-- Main Title with Enhanced Animation -->
        <div class="space-y-3">
            <h1
                class="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 bg-clip-text text-transparent leading-tight hover:from-orange-600 hover:via-orange-500 hover:to-amber-600 transition-all duration-500">
                Shop Nội Thất Bàng Vũ
            </h1>

            <!-- Enhanced Decorative Divider -->
            <div class="flex items-center justify-center space-x-4">
                <div class="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1 max-w-20"></div>
                <div class="flex space-x-1">
                    <div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse"></div>
                    <div class="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full"></div>
                    <div class="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse" style="animation-delay: 0.5s;">
                    </div>
                </div>
                <div class="h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1 max-w-20"></div>
            </div>

            <p class="text-base md:text-lg text-slate-600 font-medium tracking-wide">
                Nội Thất Chất Lượng Cao Cấp
            </p>
        </div>

        <!-- Enhanced Stats Section -->
        <div class="mt-6 flex flex-wrap justify-center gap-6 text-sm">
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-star text-orange-500 text-xs"></i>
                </div>
                <span class="font-medium">Chất lượng chính hãng</span>
            </div>
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shield-alt text-green-500 text-xs"></i>
                </div>
                <span class="font-medium">Bảo hành 10 năm</span>
            </div>
            <div
                class="flex items-center space-x-2 text-slate-600 hover:text-orange-600 transition-colors duration-300">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-truck text-blue-500 text-xs"></i>
                </div>
                <span class="font-medium">Lắp đặt toàn quốc</span>
            </div>
        </div>


    </div>

    <!-- Enhanced Decorative Elements -->
    <div
        class="absolute top-6 left-6 w-24 h-24 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full blur-xl animate-pulse">
    </div>
    <div class="absolute bottom-6 right-6 w-20 h-20 bg-gradient-to-br from-slate-200/20 to-orange-200/20 rounded-full blur-xl animate-pulse"
        style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 left-8 w-16 h-16 bg-gradient-to-br from-amber-200/15 to-orange-200/15 rounded-full blur-2xl animate-pulse"
        style="animation-delay: 2s;"></div>

    <!-- Subtle Border Bottom -->
    <div
        class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-200/50 to-transparent">
    </div>

    <!-- Luxury Search Section - Moved inside overflow-hidden container -->
    <div class="relative" style="z-index: 100;">
        <div class="relative py-8 px-6" style="z-index: 101;">
            <div class="max-w-4xl mx-auto">
                <!-- Enhanced Search Form -->
                <form action="<?php echo BASE_URL; ?>/products.php" method="GET"
                    class="search-container-solution1 relative" style="z-index: 1000;">
                    <!-- Main Search Input -->
                    <div class="relative">
                        <div class="search-input-container">
                            <input type="text" name="keyword" value="<?php echo htmlspecialchars($keyword); ?>"
                                placeholder="Tìm kiếm sản phẩm nội thất..."
                                class="js-search-input-solution1 search-input-enhanced w-full px-4 py-3 pl-12 pr-20 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all"
                                autocomplete="off" id="main-search-input">

                            <!-- Search Icon (Left) -->
                            <div class="search-icon-left">
                                <i class="fas fa-search search-icon"></i>
                                <i class="fas fa-keyboard typing-icon"></i>
                            </div>

                            <!-- Action Buttons (Right) -->
                            <div class="search-actions-right">
                                <!-- Clear Button -->
                                <button type="button" class="search-clear-btn" id="search-clear-btn">
                                    <i class="fas fa-times"></i>
                                </button>

                                <!-- Quick Search Button -->
                                <button type="submit" class="search-submit-btn" id="search-submit-btn">
                                    <i class="fas fa-search"></i>
                                    <span>Tìm kiếm</span>
                                </button>
                            </div>
                        </div>
                        <!-- Search Suggestions Container -->
                        <div id="search-suggestions"
                            class="search-suggestions-solution1 search-suggestions-container hidden absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50">
                            <!-- Suggestions will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Hidden inputs để giữ nguyên các filter hiện tại -->
                    <?php if ($category_id): ?>
                        <input type="hidden" name="category" value="<?php echo $category_id; ?>">
                    <?php endif; ?>
                    <?php if ($price_min): ?>
                        <input type="hidden" name="price_min" value="<?php echo $price_min; ?>">
                    <?php endif; ?>
                    <?php if ($price_max): ?>
                        <input type="hidden" name="price_max" value="<?php echo $price_max; ?>">
                    <?php endif; ?>
                    <?php if ($sort && $sort !== 'newest'): ?>
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($sort); ?>">
                    <?php endif; ?>
                    <?php if ($promotion_filter): ?>
                        <input type="hidden" name="promotion" value="<?php echo htmlspecialchars($promotion_filter); ?>">
                    <?php endif; ?>

                    <!-- Quick Search Tags -->
                    <div class="mt-6 text-center">
                        <div class="flex flex-wrap justify-center items-center gap-3">
                            <span class="text-sm text-slate-500 font-medium">Tìm kiếm phổ biến:</span>
                            <?php
                            // Lấy danh mục được thiết lập hiển thị trong tìm kiếm phổ biến
                            try {
                                $stmt = $conn->prepare("
                                        SELECT c.id, c.name, c.slug, c.popular_search_order, COUNT(p.id) as product_count
                                        FROM categories c
                                        LEFT JOIN products p ON c.id = p.category_id AND p.status = 1
                                        WHERE c.status = 1 AND c.show_in_popular_search = 1
                                        GROUP BY c.id, c.name, c.slug, c.popular_search_order
                                        ORDER BY c.popular_search_order ASC, c.name ASC
                                        LIMIT 5
                                    ");
                                $stmt->execute();
                                $popular_categories = $stmt->fetchAll();
                            } catch (PDOException $e) {
                                $popular_categories = [];
                            }

                            // Nếu không có danh mục nào được thiết lập, sử dụng dữ liệu mặc định
                            if (empty($popular_categories)) {
                                $popular_categories = [
                                    ['name' => 'Sofa', 'slug' => 'sofa', 'product_count' => 0, 'popular_search_order' => 1],
                                    ['name' => 'Bàn ăn', 'slug' => 'ban-an', 'product_count' => 0, 'popular_search_order' => 2],
                                    ['name' => 'Giường ngủ', 'slug' => 'giuong-ngu', 'product_count' => 0, 'popular_search_order' => 3],
                                    ['name' => 'Tủ quần áo', 'slug' => 'tu-quan-ao', 'product_count' => 0, 'popular_search_order' => 4],
                                    ['name' => 'Bàn làm việc', 'slug' => 'ban-lam-viec', 'product_count' => 0, 'popular_search_order' => 5]
                                ];
                            }

                            foreach ($popular_categories as $category): ?>
                                <button type="button"
                                    class="quick-search-tag px-4 py-2 bg-white/80 hover:bg-orange-50 border border-slate-200/80 hover:border-orange-200 text-slate-600 hover:text-orange-600 text-sm font-medium rounded-full transition-all duration-200 hover:shadow-md transform hover:-translate-y-0.5 group"
                                    data-category-id="<?php echo $category['id']; ?>"
                                    data-keyword="<?php echo htmlspecialchars($category['name']); ?>">
                                    <i
                                        class="fas fa-tag text-xs mr-1.5 opacity-60 group-hover:opacity-100 transition-opacity duration-200"></i>
                                    <span><?php echo htmlspecialchars($category['name']); ?></span>
                                    <?php if ($category['product_count'] > 0): ?>
                                        <span
                                            class="ml-1.5 px-1.5 py-0.5 bg-orange-100 text-orange-600 text-xs rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                            <?php echo $category['product_count']; ?>
                                        </span>
                                    <?php endif; ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div
            class="absolute top-4 right-8 w-16 h-16 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-xl animate-pulse">
        </div>
        <div class="absolute bottom-4 left-8 w-12 h-12 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full blur-xl animate-pulse"
            style="animation-delay: 1s;"></div>
    </div>
</div>



<!-- Products -->
<div id="products-section" class="py-10 bg-gradient-to-b from-white to-gray-50 relative" style="z-index: 1;">
    <div class="container mx-auto px-4">

        <div class="flex flex-col lg:flex-row lg:space-x-6">
            <!-- Sidebar Filters (25%) -->
            <aside class="lg:w-1/4 mb-6 lg:mb-0">
                <div class="sidebar-filters bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Professional Elegant Header -->
                    <div class="px-8 py-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-1 h-8 bg-slate-800 rounded-full"></div>
                                <div>
                                    <h2 class="text-xl font-semibold text-slate-800 tracking-tight">Bộ lọc sản phẩm</h2>
                                    <p class="text-sm text-slate-500 mt-0.5">Tùy chỉnh tìm kiếm của bạn</p>
                                </div>
                            </div>
                            <?php
                            // Đếm số lượng filters active
                            $active_filters_count = 0;
                            $filter_details = [];

                            if (!empty($keyword)) {
                                $active_filters_count++;
                                $filter_details[] = 'từ khóa';
                            }
                            if (!empty($category_ids)) {
                                $active_filters_count++;
                                $filter_details[] = 'danh mục';
                            }
                            if ($price_min || $price_max) {
                                $active_filters_count++;
                                $filter_details[] = 'khoảng giá';
                            }
                            if (!empty($promotion_filters)) {
                                $active_filters_count++;
                                $filter_details[] = 'khuyến mãi';
                            }

                            $has_filters = $active_filters_count > 0;
                            $tooltip_text = $has_filters ?
                                "Xóa tất cả bộ lọc ({$active_filters_count} bộ lọc: " . implode(', ', $filter_details) . ")" :
                                "Đặt lại bộ lọc";

                            $button_classes = $has_filters ?
                                "group relative text-orange-500 hover:text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200 hover:border-orange-300" :
                                "group relative text-slate-400 hover:text-orange-500 hover:bg-orange-50 border-transparent hover:border-orange-200";
                            ?>
                            <button
                                class="<?php echo $button_classes; ?> transition-all duration-300 w-12 h-12 rounded-lg border transform hover:scale-105 flex items-center justify-center flex-shrink-0 relative"
                                id="resetFilters" title="<?php echo htmlspecialchars($tooltip_text); ?>">
                                <i
                                    class="fas fa-sync-alt text-base group-hover:rotate-180 transition-transform duration-500"></i>
                                <?php if ($has_filters): ?>
                                    <div
                                        class="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse">
                                        <?php echo $active_filters_count; ?>
                                    </div>
                                <?php endif; ?>
                            </button>
                        </div>
                    </div>

                    <!-- Filter content -->
                    <div class="p-6 space-y-6">
                        <!-- Category Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('category')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-th-large text-orange-500 text-sm"></i>
                                    </div>
                                    Danh mục
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="category-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-3" id="category-content">
                                <?php
                                $parent_categories = get_categories(null, 1);
                                foreach ($parent_categories as $parent_cat):
                                    if ($parent_cat['parent_id'] == null):
                                        $subcategories = get_categories($parent_cat['id'], 1);
                                        ?>
                                        <div class="group">
                                            <!-- Parent Category -->
                                            <div class="flex items-center justify-between p-3 bg-gray-50 hover:bg-orange-50 rounded-lg transition-colors duration-200 cursor-pointer"
                                                <?php if (!empty($subcategories)): ?>onclick="toggleSubcategories(<?php echo $parent_cat['id']; ?>)" <?php endif; ?>>
                                                <div class="flex items-center flex-1">
                                                    <input type="checkbox" id="cat_<?php echo $parent_cat['id']; ?>"
                                                        name="category[]" value="<?php echo $parent_cat['id']; ?>"
                                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                                        <?php echo (in_array($parent_cat['id'], $category_ids)) ? 'checked' : ''; ?> onclick="event.stopPropagation()">
                                                    <span
                                                        class="ml-3 text-sm font-medium text-gray-700 group-hover:text-orange-600 cursor-pointer hover:text-orange-600"
                                                        onclick="event.stopPropagation(); toggleCategoryCheckbox('cat_<?php echo $parent_cat['id']; ?>')">
                                                        <?php echo htmlspecialchars($parent_cat['name']); ?>
                                                    </span>
                                                </div>
                                                <?php if (!empty($subcategories)): ?>
                                                    <div
                                                        class="text-gray-400 hover:text-orange-500 transition-colors duration-200 ml-2">
                                                        <i class="fas fa-chevron-right text-xs transform transition-transform duration-200"
                                                            id="sub-chevron-<?php echo $parent_cat['id']; ?>"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Subcategories -->
                                            <?php if (!empty($subcategories)): ?>
                                                <div class="ml-6 mt-2 space-y-2 hidden"
                                                    id="subcategories-<?php echo $parent_cat['id']; ?>">
                                                    <?php foreach ($subcategories as $subcat): ?>
                                                        <label
                                                            class="flex items-center p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors duration-200">
                                                            <input type="checkbox" id="subcat_<?php echo $subcat['id']; ?>"
                                                                name="category[]" value="<?php echo $subcat['id']; ?>"
                                                                class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                                                <?php echo (in_array($subcat['id'], $category_ids)) ? 'checked' : ''; ?>>
                                                            <span class="ml-3 text-sm text-gray-600 hover:text-orange-600">
                                                                <?php echo htmlspecialchars($subcat['name']); ?>
                                                            </span>
                                                        </label>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('price')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-dollar-sign text-green-500 text-sm"></i>
                                    </div>
                                    Khoảng giá
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="price-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-4" id="price-content">
                                <!-- Price Input Range -->
                                <div class="flex gap-2">
                                    <div class="flex-1 min-w-0">
                                        <label class="block text-xs font-medium text-gray-500 mb-1.5">Từ (VNĐ)</label>
                                        <div class="relative">
                                            <input type="text" id="price-min" name="price_min" placeholder="0"
                                                class="price-input w-full border border-gray-300 rounded-md focus:border-orange-500 focus:ring-1 focus:ring-orange-500/20 transition-all duration-200"
                                                data-price-field="min" style="font-size: 14px;">
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <label class="block text-xs font-medium text-gray-500 mb-1.5">Đến (VNĐ)</label>
                                        <div class="relative">
                                            <input type="text" id="price-max" name="price_max" placeholder="10.000.000"
                                                class="price-input w-full border border-gray-300 rounded-md focus:border-orange-500 focus:ring-1 focus:ring-orange-500/20 transition-all duration-200"
                                                data-price-field="max" style="font-size: 14px;">
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Presets -->
                                <div class="space-y-2">
                                    <label class="block text-xs font-medium text-gray-500 mb-2">Khoảng giá phổ
                                        biến</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <?php
                                        $price_presets = [
                                            ['min' => 0, 'max' => 5000000, 'label' => 'Dưới 5 triệu'],
                                            ['min' => 5000000, 'max' => 10000000, 'label' => '5 - 10 triệu'],
                                            ['min' => 10000000, 'max' => 20000000, 'label' => '10 - 20 triệu'],
                                            ['min' => 20000000, 'max' => 30000000, 'label' => '20 - 30 triệu'],
                                            ['min' => 30000000, 'max' => 50000000, 'label' => '30 - 50 triệu'],
                                            ['min' => 50000000, 'max' => null, 'label' => 'Trên 50 triệu']
                                        ];

                                        foreach ($price_presets as $preset):
                                            // Check if this preset matches current price filter
                                            $is_active = false;
                                            if ($preset['max'] === null) {
                                                // "Trên X triệu" case
                                                $is_active = ($price_min == $preset['min'] && !$price_max);
                                            } else {
                                                // Range case
                                                $is_active = ($price_min == $preset['min'] && $price_max == $preset['max']);
                                            }

                                            $active_classes = $is_active ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white border-orange-500 shadow-md' : 'bg-white text-gray-700 border-gray-200 hover:bg-gradient-to-r hover:from-orange-500 hover:to-orange-600 hover:text-white hover:border-orange-500 hover:shadow-lg';
                                            ?>
                                            <button type="button"
                                                class="price-preset px-3 py-2 text-xs font-medium rounded-lg border transform hover:scale-105 hover:-translate-y-0.5 <?php echo $active_classes; ?>"
                                                data-min="<?php echo $preset['min']; ?>"
                                                data-max="<?php echo $preset['max'] ?? ''; ?>"
                                                style="transition: transform 0.4s ease-in-out;">
                                                <?php echo $preset['label']; ?>
                                            </button>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Promotion Filter -->
                        <div class="border-b border-gray-100 pb-6">
                            <div class="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors duration-200"
                                onclick="toggleFilterSection('promotion')">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-tags text-red-500 text-sm"></i>
                                    </div>
                                    Khuyến mãi
                                </h3>
                                <div class="text-gray-400 hover:text-orange-500 transition-colors duration-200">
                                    <i class="fas fa-chevron-right transform transition-transform duration-200 rotate-90"
                                        id="promotion-chevron"></i>
                                </div>
                            </div>
                            <div class="space-y-3" id="promotion-content">
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="sale"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('sale', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-red-200">
                                            <i class="fas fa-percentage text-red-500 text-xs"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Đang
                                            giảm giá</span>
                                    </div>
                                </label>
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="flash_sale"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('flash_sale', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-purple-200">
                                            <i class="fas fa-bolt text-purple-500 text-xs"></i>
                                        </div>
                                        <span
                                            class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Flash
                                            Sale</span>
                                    </div>
                                </label>
                                <label
                                    class="flex items-center p-3 bg-gray-50 hover:bg-orange-50 rounded-lg cursor-pointer transition-colors duration-200 group">
                                    <input type="checkbox" name="promotion[]" value="featured"
                                        class="custom-checkbox w-4 h-4 text-orange-500 bg-white border-2 border-gray-300 rounded-md focus:ring-orange-500 focus:ring-2 focus:ring-offset-0"
                                        <?php echo (in_array('featured', $promotion_filters)) ? 'checked' : ''; ?>>
                                    <div class="ml-3 flex items-center">
                                        <div
                                            class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-2 group-hover:bg-yellow-200">
                                            <i class="fas fa-star text-yellow-500 text-xs"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 group-hover:text-orange-600">Sản
                                            phẩm nổi bật</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Apply Filters Button -->
                        <div class="pt-3 space-y-2.5">
                            <button type="button" id="applyFilters"
                                class="group w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5 hover:scale-[1.02] relative overflow-hidden">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700">
                                </div>
                                <i
                                    class="fas fa-filter mr-2 text-xs group-hover:scale-110 transition-transform duration-300"></i>
                                <span class="text-sm relative z-10">Áp dụng bộ lọc</span>
                                <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i class="fas fa-arrow-right text-xs"></i>
                                </div>
                            </button>
                            <button type="button" id="resetFiltersBtn"
                                class="group w-full bg-white border border-gray-200 hover:border-orange-300 text-gray-600 hover:text-orange-600 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 transform hover:scale-[1.02] relative overflow-hidden">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-orange-100/0 via-orange-100/50 to-orange-100/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700">
                                </div>
                                <i
                                    class="fas fa-broom mr-2 text-xs group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"></i>
                                <span class="text-sm relative z-10">Đặt lại bộ lọc</span>
                                <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <i
                                        class="fas fa-sync-alt text-xs group-hover:rotate-180 transition-transform duration-500"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <?php
                // Hàm chuyển đổi thời gian thành dạng thân thiện
                function time_ago($datetime) {
                    $time = time() - strtotime($datetime);

                    if ($time < 60) {
                        return 'Vừa xem';
                    } elseif ($time < 3600) {
                        $minutes = floor($time / 60);
                        return $minutes . ' phút trước';
                    } elseif ($time < 86400) {
                        $hours = floor($time / 3600);
                        return $hours . ' giờ trước';
                    } elseif ($time < 172800) { // 2 days
                        return 'Hôm qua lúc ' . date('H:i', strtotime($datetime));
                    } elseif ($time < 604800) { // 7 days
                        $days = floor($time / 86400);
                        return $days . ' ngày trước';
                    } else {
                        return date('d/m/Y', strtotime($datetime));
                    }
                }

                // Lấy sản phẩm xem gần đây (CHỈ từ dữ liệu thật)
                $recently_viewed = get_recently_viewed_products(4);

                // Lấy sản phẩm trending
                $trending_products = get_trending_products(4);

                // Nếu không có trending products thật, tạo dữ liệu test cho trending
                if (empty($trending_products)) {
                    $trending_products = get_products(4, 0, null, 1, null, 1); // Lấy 4 sản phẩm featured
                    // Thêm recent_views và total_views giả
                    foreach ($trending_products as &$product) {
                        $product['recent_views'] = rand(10, 50);
                        $product['total_views'] = $product['views'] ?? rand(100, 500);
                    }
                }
                ?>

                <!-- Recently Viewed Products Section -->
                <?php if (!empty($recently_viewed)): ?>
                <div class="recently-viewed-section bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6" style="display: block !important; opacity: 1 !important; visibility: visible !important;">
                    <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-history text-blue-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Sản phẩm xem gần đây</h3>
                                <p class="text-sm text-gray-500">Những sản phẩm bạn đã xem</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 space-y-3">
                        <?php foreach ($recently_viewed as $product): ?>
                        <div class="group flex items-center space-x-3 p-3 bg-gray-50 hover:bg-blue-50 rounded-lg transition-all duration-200 cursor-pointer">
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo htmlspecialchars($product['image']); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-500 text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="<?php echo BASE_URL; ?>/san-pham/<?php echo htmlspecialchars($product['slug']); ?>"
                                   class="block">
                                    <h4 class="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-200 truncate">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </h4>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <?php echo htmlspecialchars($product['category_name'] ?? 'Chưa phân loại'); ?>
                                    </p>
                                    <div class="flex items-center justify-between mt-1">
                                        <?php if ($product['price_type'] == 'contact'): ?>
                                            <span class="text-sm font-semibold text-blue-600">Liên hệ báo giá</span>
                                        <?php else: ?>
                                            <span class="text-sm font-semibold text-orange-600">
                                                <?php
                                                if (!empty($product['sale_price']) && $product['sale_price'] > 0 && $product['sale_price'] < $product['price']) {
                                                    echo number_format($product['sale_price']) . 'đ';
                                                } else {
                                                    echo number_format($product['price']) . 'đ';
                                                }
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                        <div class="flex items-center text-xs text-gray-400">
                                            <i class="fas fa-clock mr-1"></i>
                                            <span><?php echo time_ago($product['view_date']); ?></span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Trending Products Section -->
                <?php if (!empty($trending_products)): ?>
                <div class="trending-section bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6" style="display: block !important; opacity: 1 !important; visibility: visible !important;">
                    <div class="px-6 py-4 bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-fire text-orange-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Sản phẩm được quan tâm</h3>
                                <p class="text-sm text-gray-500">Trending trong tuần</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 space-y-3">
                        <?php foreach ($trending_products as $index => $product): ?>
                        <div class="group flex items-center space-x-3 p-3 bg-gray-50 hover:bg-orange-50 rounded-lg transition-all duration-200 cursor-pointer">
                            <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-bold"><?php echo $index + 1; ?></span>
                            </div>
                            <div class="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo htmlspecialchars($product['image']); ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                        <i class="fas fa-image text-gray-500 text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="<?php echo BASE_URL; ?>/san-pham/<?php echo htmlspecialchars($product['slug']); ?>"
                                   class="block">
                                    <h4 class="text-sm font-medium text-gray-800 group-hover:text-orange-600 transition-colors duration-200 truncate">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </h4>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <?php echo htmlspecialchars($product['category_name'] ?? 'Chưa phân loại'); ?>
                                    </p>
                                    <div class="flex items-center justify-between mt-1">
                                        <?php if ($product['price_type'] == 'contact'): ?>
                                            <span class="text-sm font-semibold text-blue-600">Liên hệ báo giá</span>
                                        <?php else: ?>
                                            <span class="text-sm font-semibold text-orange-600">
                                                <?php
                                                if (!empty($product['sale_price']) && $product['sale_price'] > 0 && $product['sale_price'] < $product['price']) {
                                                    echo number_format($product['sale_price']) . 'đ';
                                                } else {
                                                    echo number_format($product['price']) . 'đ';
                                                }
                                                ?>
                                            </span>
                                        <?php endif; ?>
                                        <div class="flex items-center space-x-1">
                                            <i class="fas fa-eye text-gray-400 text-xs"></i>
                                            <span class="text-xs text-gray-400">
                                                <?php echo number_format($product['total_views']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </aside>

            <!-- Main Products Area (75%) -->
            <main class="lg:w-3/4">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <!-- Professional Elegant Header -->
                    <div class="px-8 py-6 bg-white border-b border-gray-200">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-1 h-8 bg-slate-800 rounded-full"></div>
                                <div>
                                    <h2 class="text-xl font-semibold text-slate-800 tracking-tight">Danh sách sản phẩm
                                    </h2>
                                    <div class="flex items-center space-x-1 text-sm text-slate-500 mt-0.5">
                                        <span><?php echo number_format(min($limit, $total_products)); ?></span>
                                        <span class="text-slate-400">/</span>
                                        <span><?php echo number_format($total_products); ?></span>
                                        <span class="text-slate-400 ml-1">sản phẩm</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Controls -->
                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                <!-- Sort -->
                                <div class="flex items-center gap-2">
                                    <label for="sort-select"
                                        class="text-sm text-gray-600 whitespace-nowrap font-medium">Sắp xếp:</label>
                                    <select id="sort-select" name="sort"
                                        class="px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all duration-200 text-sm bg-white">
                                        <option value="newest" <?php echo ($sort == 'newest') ? 'selected' : ''; ?>>Mới
                                            nhất</option>
                                        <option value="price_asc" <?php echo ($sort == 'price_asc') ? 'selected' : ''; ?>>
                                            Giá thấp đến cao</option>
                                        <option value="price_desc" <?php echo ($sort == 'price_desc') ? 'selected' : ''; ?>>Giá cao đến thấp</option>
                                        <option value="name_asc" <?php echo ($sort == 'name_asc') ? 'selected' : ''; ?>>
                                            Tên A-Z</option>
                                        <option value="popular" <?php echo ($sort == 'popular') ? 'selected' : ''; ?>>Phổ
                                            biến nhất</option>
                                    </select>
                                </div>

                                <!-- Items per page -->
                                <div class="flex items-center gap-2">
                                    <label for="items-per-page"
                                        class="text-sm text-gray-600 whitespace-nowrap font-medium">Hiển thị:</label>
                                    <select id="items-per-page" name="items_per_page"
                                        class="px-3 py-2 border border-gray-300 rounded-lg focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all duration-200 text-sm bg-white">
                                        <option value="12" <?php echo ($limit == 12) ? 'selected' : ''; ?>>12</option>
                                        <option value="24" <?php echo ($limit == 24) ? 'selected' : ''; ?>>24</option>
                                        <option value="36" <?php echo ($limit == 36) ? 'selected' : ''; ?>>36</option>
                                        <option value="48" <?php echo ($limit == 48) ? 'selected' : ''; ?>>48</option>
                                    </select>
                                </div>


                            </div>
                        </div>
                    </div>

                    <!-- Products Content -->
                    <div class="p-6 products-content">
                        <!-- Filter Results Header (hiển thị khi có tìm kiếm hoặc filter) -->
                        <?php if (!empty($keyword) || !empty($category_ids) || $price_min || $price_max || !empty($promotion_filter)): ?>
                            <div
                                class="filter-results-header mb-6 bg-gradient-to-r from-orange-50/80 to-amber-50/80 border border-orange-200/40 rounded-xl p-5 shadow-sm backdrop-blur-sm relative overflow-hidden gradient-bg <?php echo empty($products) ? 'empty-state' : ''; ?>">
                                <!-- Animated Background Pattern -->
                                <div class="filter-bg-pattern absolute inset-0 opacity-[0.04] pointer-events-none"></div>

                                <!-- Subtle decorative background elements -->
                                <div
                                    class="decorative-element absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-100/20 to-amber-100/20 rounded-full -translate-y-10 translate-x-10">
                                </div>

                                <div class="relative z-10">
                                    <div class="flex items-center justify-between flex-wrap gap-4">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="icon-container w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg flex items-center justify-center shadow-md <?php echo empty($products) ? 'empty-icon' : ''; ?>">
                                                <?php if (empty($products)): ?>
                                                    <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                                                <?php elseif (!empty($keyword)): ?>
                                                    <i class="fas fa-search text-white text-sm"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-filter text-white text-sm"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <h2 class="text-lg md:text-xl font-semibold text-gray-800">
                                                        <?php if (empty($products)): ?>
                                                            Không tìm thấy kết quả
                                                        <?php elseif (!empty($keyword)): ?>
                                                            Kết quả tìm kiếm
                                                        <?php else: ?>
                                                            Kết quả lọc
                                                        <?php endif; ?>
                                                    </h2>
                                                    <?php if (!empty($products)): ?>
                                                        <div class="count-badge px-2 py-1 bg-gradient-to-r from-orange-500 to-amber-500 text-white text-xs font-semibold rounded-full"
                                                            id="results-count">
                                                            <?php echo number_format($total_products); ?> sản phẩm
                                                        </div>
                                                    <?php else: ?>
                                                        <div
                                                            class="count-badge px-2 py-1 bg-gray-400 text-white text-xs font-semibold rounded-full">
                                                            0 sản phẩm
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-gray-600 text-sm leading-relaxed">
                                                    <?php if (empty($products)): ?>
                                                        <div class="text-gray-500 font-medium">
                                                            Không tìm thấy sản phẩm phù hợp với tiêu chí tìm kiếm.
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="flex flex-wrap items-center gap-2">
                                                            <?php if (!empty($keyword)): ?>
                                                                <span>Tìm kiếm cho</span>
                                                                <span
                                                                    class="inline-flex items-center px-2 py-0.5 bg-white/60 border border-orange-100 rounded text-gray-700 font-medium text-xs">
                                                                    "<?php echo htmlspecialchars($keyword); ?>"
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($current_categories)): ?>
                                                                <?php if (!empty($keyword)): ?><span>trong</span><?php endif; ?>
                                                                <?php foreach ($current_categories as $index => $cat): ?>
                                                                    <?php if ($index > 0): ?><span
                                                                            class="text-gray-400">,</span><?php endif; ?>
                                                                    <span
                                                                        class="inline-flex items-center px-2 py-0.5 bg-white/60 border border-orange-100 rounded text-gray-700 font-medium text-xs">
                                                                        <i class="fas fa-tag text-orange-400 text-xs mr-1"></i>
                                                                        <?php echo htmlspecialchars($cat['name']); ?>
                                                                    </span>
                                                                <?php endforeach; ?>
                                                            <?php endif; ?>
                                                            <span class="text-gray-500">•</span>
                                                            <span class="inline-flex items-center text-xs text-gray-500">
                                                                <i class="fas fa-sort text-orange-400 text-xs mr-1"></i>
                                                                Sắp xếp: <span
                                                                    class="font-medium text-gray-600 ml-1"><?php echo $sort_label; ?></span>
                                                            </span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Quick Actions -->
                                        <div class="quick-actions flex items-center space-x-2">
                                            <?php if (!empty($keyword)): ?>
                                                <button onclick="scrollToProductsSearch()"
                                                    class="luxury-button group inline-flex items-center px-3 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg transition-all duration-200 text-xs font-semibold shadow-md hover:shadow-lg">
                                                    <i class="fas fa-search mr-1.5 text-xs"></i>
                                                    Tìm khác
                                                </button>
                                            <?php else: ?>
                                                <!-- Simple "View All" button when only filtering -->
                                                <a href="<?php echo BASE_URL; ?>/products.php"
                                                    class="luxury-button group inline-flex items-center px-3 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg transition-all duration-200 text-xs font-semibold shadow-md hover:shadow-lg">
                                                    <i class="fas fa-th-large mr-1.5 text-xs"></i>
                                                    Xem tất cả
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Active Filters Display -->
                                <div class="mt-4 pt-4 border-t border-orange-100/60 relative">
                                    <div class="active-filters-container flex flex-wrap items-center gap-2">
                                        <p class="text-xs text-gray-600 font-medium mr-2">Bộ lọc:</p>

                                        <?php if (!empty($keyword)): ?>
                                            <div
                                                class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                                                <i class="fas fa-search text-orange-400 text-xs mr-2"></i>
                                                <span class="font-medium">Từ khóa:
                                                    "<?php echo htmlspecialchars($keyword); ?>"</span>
                                                <a href="<?php echo BASE_URL; ?>/products.php<?php echo $category_id ? '?category=' . $category_id : ''; ?>"
                                                    class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200">
                                                    <i class="fas fa-times text-xs"></i>
                                                </a>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (!empty($current_categories)): ?>
                                            <?php foreach ($current_categories as $cat): ?>
                                                <div
                                                    class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                                                    <i class="fas fa-tag text-orange-400 text-xs mr-2"></i>
                                                    <span class="font-medium">Danh mục:
                                                        <?php echo htmlspecialchars($cat['name']); ?></span>
                                                    <a href="<?php
                                                    // Tạo URL để remove category này nhưng giữ lại các category khác
                                                    $remaining_categories = array_filter($category_ids, function ($id) use ($cat) {
                                                        return $id != $cat['id']; });
                                                    $url_params = [];
                                                    if (!empty($keyword))
                                                        $url_params[] = 'keyword=' . urlencode($keyword);
                                                    foreach ($remaining_categories as $remaining_cat_id) {
                                                        $url_params[] = 'category=' . $remaining_cat_id;
                                                    }
                                                    echo BASE_URL . '/products.php' . (!empty($url_params) ? '?' . implode('&', $url_params) : '');
                                                    ?>"
                                                        class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200">
                                                        <i class="fas fa-times text-xs"></i>
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>

                                        <?php if (!empty($promotion_filters)): ?>
                                            <?php
                                            $promotion_labels = [
                                                'sale' => 'Đang giảm giá',
                                                'flash_sale' => 'Flash Sale',
                                                'featured' => 'Sản phẩm nổi bật'
                                            ];
                                            foreach ($promotion_filters as $promo): ?>
                                                <div
                                                    class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                                                    <i class="fas fa-tags text-orange-400 text-xs mr-2"></i>
                                                    <span class="font-medium">Khuyến mãi:
                                                        <?php echo $promotion_labels[$promo] ?? $promo; ?></span>
                                                    <a href="<?php
                                                    // Tạo URL để remove promotion này nhưng giữ lại các promotion khác
                                                    $remaining_promotions = array_filter($promotion_filters, function ($p) use ($promo) {
                                                        return $p != $promo; });
                                                    $url_params = [];
                                                    if (!empty($keyword))
                                                        $url_params[] = 'keyword=' . urlencode($keyword);
                                                    foreach ($category_ids as $cat_id) {
                                                        $url_params[] = 'category[]=' . $cat_id;
                                                    }
                                                    foreach ($remaining_promotions as $remaining_promo) {
                                                        $url_params[] = 'promotion[]=' . $remaining_promo;
                                                    }
                                                    if ($price_min)
                                                        $url_params[] = 'price_min=' . $price_min;
                                                    if ($price_max)
                                                        $url_params[] = 'price_max=' . $price_max;
                                                    echo BASE_URL . '/products.php' . (!empty($url_params) ? '?' . implode('&', $url_params) : '');
                                                    ?>"
                                                        class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200">
                                                        <i class="fas fa-times text-xs"></i>
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>

                                        <?php if ($price_min || $price_max): ?>
                                            <div
                                                class="filter-tag group inline-flex items-center px-3 py-1.5 bg-orange-50/80 border border-orange-200/60 hover:border-orange-300/80 rounded-lg text-xs text-orange-700 hover:text-orange-800 transition-all duration-200">
                                                <i class="fas fa-dollar-sign text-orange-400 text-xs mr-2"></i>
                                                <span class="font-medium">Giá:
                                                    <?php if ($price_min && $price_max): ?>
                                                        <?php echo number_format($price_min); ?>đ -
                                                        <?php echo number_format($price_max); ?>đ
                                                    <?php elseif ($price_min): ?>
                                                        Từ <?php echo number_format($price_min); ?>đ
                                                    <?php else: ?>
                                                        Đến <?php echo number_format($price_max); ?>đ
                                                    <?php endif; ?>
                                                </span>
                                                <a href="<?php
                                                $clear_price_params = [];
                                                if (!empty($keyword))
                                                    $clear_price_params[] = 'keyword=' . urlencode($keyword);
                                                if ($category_id)
                                                    $clear_price_params[] = 'category=' . $category_id;
                                                echo BASE_URL . '/products.php' . (!empty($clear_price_params) ? '?' . implode('&', $clear_price_params) : '');
                                                ?>"
                                                    class="remove-btn ml-2 w-4 h-4 bg-orange-100 hover:bg-orange-200 rounded-full flex items-center justify-center text-orange-500 hover:text-orange-600 transition-all duration-200">
                                                    <i class="fas fa-times text-xs"></i>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Products Grid -->
                        <div class="products-grid grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                            id="productsGrid">
                            <?php if (!empty($products)): ?>
                                <?php foreach ($products as $product): ?>
                                    <div
                                        class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                                        <div class="product-image-wrapper relative">
                                            <a href="<?php echo get_product_url($product['slug']); ?>"
                                                class="block product-image">
                                                <?php if ($product['image']): ?>
                                                    <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                                        alt="<?php echo $product['name']; ?>"
                                                        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                <?php else: ?>
                                                    <div
                                                        class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                                                        <i class="fas fa-image text-gray-500 text-4xl"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </a>

                                            <!-- Premium Sale Badge -->
                                            <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                                                <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                                                <div class="premium-sale-badge">
                                                    <div class="badge-content">
                                                        <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                                                        <span class="sale-text">SALE</span>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="product-info-wrapper flex flex-col flex-grow">
                                            <!-- Product Title - Max 2 lines -->
                                            <div class="product-title mb-3">
                                                <a href="<?php echo get_product_url($product['slug']); ?>" class="block">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                                        <?php echo $product['name']; ?>
                                                    </h3>
                                                </a>
                                            </div>

                                            <!-- Premium Price Section -->
                                            <div class="premium-price-section">
                                                <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                                                    <!-- Liên hệ báo giá - Giống regular price container -->
                                                    <div class="contact-price-container">
                                                        <div class="contact-price-main">
                                                            GỌI NGAY
                                                        </div>
                                                        <div class="contact-price-subtitle">
                                                            Liên hệ báo giá
                                                        </div>
                                                    </div>
                                                <?php elseif ($product['sale_price'] > 0): ?>
                                                    <!-- Sản phẩm có giá sale -->
                                                    <div class="price-container">
                                                        <div class="original-price">
                                                            <?php echo format_currency($product['price']); ?></div>
                                                        <div class="sale-price">
                                                            <?php echo format_currency($product['sale_price']); ?></div>
                                                    </div>
                                                <?php else: ?>
                                                    <!-- Sản phẩm giá thường -->
                                                    <div class="regular-price-container">
                                                        <div class="price-label">Giá bán</div>
                                                        <div class="main-price"><?php echo format_currency($product['price']); ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Rating and Sales Section -->
                                            <div class="product-rating-sales">
                                                <div class="rating-section">
                                                    <div class="stars">
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                    </div>
                                                    <span class="rating-text">5.0</span>
                                                </div>
                                                <div class="sales-section">
                                                    <i class="fas fa-shopping-cart"></i>
                                                    <span>8 đã bán</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-span-full py-16 text-center">
                                    <div class="max-w-2xl mx-auto">
                                        <!-- Enhanced Empty State -->
                                        <div
                                            class="bg-gradient-to-br from-orange-50/50 to-amber-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-sm">
                                            <!-- Animated Icon -->
                                            <div class="relative mb-6">
                                                <div
                                                    class="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-lg">
                                                    <?php if (!empty($keyword)): ?>
                                                        <i class="fas fa-search-minus text-orange-400 text-2xl"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-box-open text-orange-400 text-2xl"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <!-- Pulse animation -->
                                                <div
                                                    class="absolute inset-0 w-20 h-20 mx-auto bg-orange-200/30 rounded-full animate-ping">
                                                </div>
                                            </div>

                                            <!-- Main Message -->
                                            <h3 class="text-xl font-bold text-gray-800 mb-3">
                                                <?php if (!empty($keyword)): ?>
                                                    Không tìm thấy sản phẩm phù hợp
                                                <?php else: ?>
                                                    Không có sản phẩm trong bộ lọc này
                                                <?php endif; ?>
                                            </h3>

                                            <!-- Detailed Description -->
                                            <p class="text-gray-600 mb-6 leading-relaxed">
                                                <?php if (!empty($keyword)): ?>
                                                    Không có sản phẩm nào khớp với từ khóa <span
                                                        class="inline-flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded font-medium">"<?php echo htmlspecialchars($keyword); ?>"</span>
                                                <?php else: ?>
                                                    Không có sản phẩm nào phù hợp với các tiêu chí lọc hiện tại
                                                <?php endif; ?>
                                            </p>

                                            <!-- Helpful Suggestions -->
                                            <div class="bg-white/60 rounded-xl p-6 mb-6 border border-orange-100/50">
                                                <h4 class="text-sm font-semibold text-gray-700 mb-4 flex items-center">
                                                    <i class="fas fa-lightbulb text-amber-500 mr-2"></i>
                                                    Gợi ý để tìm thấy sản phẩm:
                                                </h4>
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                                                    <?php if (!empty($keyword)): ?>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử từ khóa ngắn gọn hơn</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Kiểm tra chính tả từ khóa</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Sử dụng từ đồng nghĩa</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử bỏ bớt bộ lọc</span>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Mở rộng khoảng giá</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Thử danh mục khác</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Bỏ bớt tiêu chí lọc</span>
                                                        </div>
                                                        <div class="flex items-start space-x-2">
                                                            <i class="fas fa-check-circle text-green-500 text-xs mt-0.5"></i>
                                                            <span>Xem tất cả sản phẩm</span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- Popular Categories -->
                                            <div class="mb-6">
                                                <h4 class="text-sm font-semibold text-gray-700 mb-3">Danh mục phổ biến:</h4>
                                                <div class="flex flex-wrap justify-center gap-2">
                                                    <?php
                                                    // Lấy 6 danh mục phổ biến
                                                    try {
                                                        $stmt = $conn->prepare("
                                                            SELECT c.id, c.name, c.slug, COUNT(p.id) as product_count
                                                            FROM categories c
                                                            LEFT JOIN products p ON c.id = p.category_id AND p.status = 1
                                                            WHERE c.status = 1
                                                            GROUP BY c.id, c.name, c.slug
                                                            HAVING product_count > 0
                                                            ORDER BY product_count DESC, c.name ASC
                                                            LIMIT 6
                                                        ");
                                                        $stmt->execute();
                                                        $popular_cats = $stmt->fetchAll();

                                                        foreach ($popular_cats as $cat):
                                                            ?>
                                                            <a href="<?php echo BASE_URL; ?>/products.php?category=<?php echo $cat['id']; ?>"
                                                                class="inline-flex items-center px-3 py-2 bg-white border border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 rounded-lg text-xs font-medium transition-all duration-200 hover:bg-orange-50 hover:shadow-sm">
                                                                <i class="fas fa-tag mr-1.5 text-xs"></i>
                                                                <?php echo htmlspecialchars($cat['name']); ?>
                                                                <span
                                                                    class="ml-1 text-gray-400">(<?php echo $cat['product_count']; ?>)</span>
                                                            </a>
                                                            <?php
                                                        endforeach;
                                                    } catch (Exception $e) {
                                                        // Fallback nếu có lỗi
                                                    }
                                                    ?>
                                                </div>
                                            </div>

                                            <!-- Contact Support -->
                                            <div class="text-center">
                                                <p class="text-sm text-gray-500 mb-3">Không tìm thấy sản phẩm bạn cần?</p>
                                                <div class="flex flex-col sm:flex-row gap-2 justify-center">
                                                    <a href="<?php echo BASE_URL; ?>/contact.php"
                                                        class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                                        <i class="fas fa-headset mr-2"></i>
                                                        Liên hệ tư vấn
                                                    </a>
                                                    <a href="<?php echo BASE_URL; ?>/products.php"
                                                        class="inline-flex items-center px-4 py-2 bg-white border-2 border-orange-200 hover:border-orange-300 text-orange-600 hover:text-orange-700 font-medium rounded-lg transition-all duration-200 hover:bg-orange-50 text-sm">
                                                        <i class="fas fa-th-large mr-2"></i>
                                                        Xem tất cả sản phẩm
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Professional Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination-section mt-12">
                            <!-- Results Summary -->
                            <div class="pagination-summary">
                                <div class="results-info">
                                    <span class="results-text">
                                        Hiển thị <strong><?php echo (($page - 1) * $limit) + 1; ?></strong> -
                                        <strong><?php echo min($page * $limit, $total_products); ?></strong>
                                        trong tổng số <strong><?php echo number_format($total_products); ?></strong> sản
                                        phẩm
                                    </span>
                                </div>
                            </div>

                            <!-- Professional Pagination Navigation -->
                            <nav class="pagination-nav" aria-label="Điều hướng trang">
                                <ul class="pagination-list">
                                    <?php
                                    // Build URL parameters for pagination
                                    $url_params = [];
                                    if ($category_id)
                                        $url_params['category'] = $category_id;
                                    if ($sort && $sort !== 'newest')
                                        $url_params['sort'] = $sort;

                                    function build_products_pagination_url($page_num, $params)
                                    {
                                        $params['page'] = $page_num;
                                        return BASE_URL . '/products.php?' . http_build_query($params);
                                    }
                                    ?>

                                    <!-- Previous Button -->
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($page - 1, $url_params); ?>"
                                                aria-label="Trang trước">
                                                <i class="fas fa-chevron-left"></i>
                                                <span class="page-text">Trước</span>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-disabled="true">
                                                <i class="fas fa-chevron-left"></i>
                                                <span class="page-text">Trước</span>
                                            </span>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    // Smart pagination logic
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);
                                    ?>

                                    <!-- First page + ellipsis if needed -->
                                    <?php if ($start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url(1, $url_params); ?>"
                                                aria-label="Trang 1">1</a>
                                        </li>
                                        <?php if ($start_page > 2): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link ellipsis">...</span>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <!-- Visible page range -->
                                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($i, $url_params); ?>"
                                                aria-label="Trang <?php echo $i; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <!-- Last page + ellipsis if needed -->
                                    <?php if ($end_page < $total_pages): ?>
                                        <?php if ($end_page < $total_pages - 1): ?>
                                            <li class="page-item disabled">
                                                <span class="page-link ellipsis">...</span>
                                            </li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($total_pages, $url_params); ?>"
                                                aria-label="Trang <?php echo $total_pages; ?>"><?php echo $total_pages; ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Next Button -->
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link"
                                                href="<?php echo build_products_pagination_url($page + 1, $url_params); ?>"
                                                aria-label="Trang sau">
                                                <span class="page-text">Sau</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link" aria-disabled="true">
                                                <span class="page-text">Sau</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>

                        </div>
                    <?php endif; ?>
                </div>
        </div>
    </div>



    <!-- JavaScript cho trang sản phẩm -->
    <script>
        // Không sử dụng JavaScript redirect nữa để tránh load 2 lần

        // Quick search tags functionality for products page
        document.addEventListener('DOMContentLoaded', function () {
            // Lưu items_per_page hiện tại vào Local Storage nếu có
            const urlParams = new URLSearchParams(window.location.search);
            const currentItemsPerPage = urlParams.get('items_per_page');
            if (currentItemsPerPage && ['12', '24', '36', '48'].includes(currentItemsPerPage)) {
                localStorage.setItem('products_items_per_page', currentItemsPerPage);
            }
            const quickSearchTags = document.querySelectorAll('.quick-search-tag');
            const searchInput = document.getElementById('main-search-input');

            // Set and format current price values from PHP
            const priceMinInput = document.getElementById('price-min');
            const priceMaxInput = document.getElementById('price-max');

            <?php if ($price_min): ?>
                priceMinInput.value = '<?php echo $price_min; ?>';
                handlePriceInput(priceMinInput);
            <?php endif; ?>

            <?php if ($price_max): ?>
                priceMaxInput.value = '<?php echo $price_max; ?>';
                handlePriceInput(priceMaxInput);
            <?php endif; ?>

            // Quick search tags functionality - Redirect to category filter
            quickSearchTags.forEach(tag => {
                tag.addEventListener('click', function () {
                    const categoryId = this.getAttribute('data-category-id');

                    // Add visual feedback
                    this.style.transform = 'translateY(-2px) scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // Redirect to products page with category filter
                    if (categoryId) {
                        window.location.href = 'products.php?category=' + categoryId;
                    }
                });
            });
        });
    </script>

    <script>
        // Toggle filter sections
        function toggleFilterSection(sectionName) {
            const content = document.getElementById(sectionName + '-content');
            const chevron = document.getElementById(sectionName + '-chevron');

            if (content.classList.contains('hidden')) {
                // Hiển thị content và xoay mũi tên xuống (90 độ)
                content.classList.remove('hidden');
                chevron.classList.add('rotate-90');
            } else {
                // Ẩn content và xoay mũi tên sang phải (0 độ)
                content.classList.add('hidden');
                chevron.classList.remove('rotate-90');
            }
        }

        // Toggle category checkbox
        function toggleCategoryCheckbox(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
            }
        }

        // Format number with dots (Vietnamese currency format)
        function formatNumberWithDots(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }

        // Remove dots from formatted number
        function removeDotsFromNumber(str) {
            return str.replace(/\./g, '');
        }

        // Handle price input formatting
        function handlePriceInput(input) {
            let value = input.value;

            // Remove all non-digit characters except dots
            value = value.replace(/[^\d.]/g, '');

            // Remove existing dots
            value = removeDotsFromNumber(value);

            // Only keep digits
            value = value.replace(/\D/g, '');

            // Format with dots if there's a value
            if (value) {
                value = formatNumberWithDots(value);
            }

            // Update input value
            input.value = value;
        }

        // Get numeric value from formatted price input
        function getPriceNumericValue(input) {
            const value = input.value;
            if (!value) return '';
            return removeDotsFromNumber(value);
        }

        // Toggle subcategories
        function toggleSubcategories(parentId) {
            const subcategories = document.getElementById('subcategories-' + parentId);
            const chevron = document.getElementById('sub-chevron-' + parentId);

            if (subcategories.classList.contains('hidden')) {
                // Hiển thị subcategories và xoay mũi tên xuống (90 độ)
                subcategories.classList.remove('hidden');
                chevron.classList.add('rotate-90');
            } else {
                // Ẩn subcategories và xoay mũi tên sang phải (0 độ)
                subcategories.classList.add('hidden');
                chevron.classList.remove('rotate-90');
            }
        }

        // Price input formatting
        document.querySelectorAll('.price-input').forEach(input => {
            // Format on input
            input.addEventListener('input', function () {
                handlePriceInput(this);
            });

            // Format on paste
            input.addEventListener('paste', function () {
                setTimeout(() => {
                    handlePriceInput(this);
                }, 10);
            });

            // Format existing value on page load
            if (input.value) {
                handlePriceInput(input);
            }
        });

        // Price preset functionality
        document.querySelectorAll('.price-preset').forEach(button => {
            button.addEventListener('click', function () {
                const minPrice = this.getAttribute('data-min');
                const maxPrice = this.getAttribute('data-max');
                const priceMinInput = document.getElementById('price-min');
                const priceMaxInput = document.getElementById('price-max');

                // Check if this button is currently active
                const isCurrentlyActive = this.classList.contains('from-orange-500');

                if (isCurrentlyActive) {
                    // If currently active, deactivate it (clear inputs)
                    priceMinInput.value = '';
                    priceMaxInput.value = '';

                    // Remove active class from this preset
                    this.className = this.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                    this.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                } else {
                    // If not active, activate it (set values)
                    priceMinInput.value = minPrice || '';
                    priceMaxInput.value = maxPrice || '';

                    if (minPrice) handlePriceInput(priceMinInput);
                    if (maxPrice) handlePriceInput(priceMaxInput);

                    // Remove active class from all presets first
                    document.querySelectorAll('.price-preset').forEach(btn => {
                        btn.className = btn.className.replace(/bg-gradient-to-r|from-orange-500|to-orange-600|text-white|border-orange-500|shadow-md/g, '');
                        btn.classList.add('bg-white', 'text-gray-700', 'border-gray-200');
                    });

                    // Add active class to clicked preset
                    this.className = this.className.replace(/bg-white|text-gray-700|border-gray-200/g, '');
                    this.classList.add('bg-gradient-to-r', 'from-orange-500', 'to-orange-600', 'text-white', 'border-orange-500', 'shadow-md');
                }
            });
        });

        // View toggle functionality
        document.addEventListener('DOMContentLoaded', function () {
            const gridViewBtn = document.getElementById('grid-view');
            const listViewBtn = document.getElementById('list-view');
            const productsGrid = document.getElementById('productsGrid');

            if (gridViewBtn && listViewBtn && productsGrid) {
                // Grid view (default)
                gridViewBtn.addEventListener('click', function () {
                    productsGrid.classList.remove('grid-cols-1');
                    productsGrid.classList.add('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');

                    // Update buttons
                    gridViewBtn.classList.remove('bg-white', 'text-gray-600');
                    gridViewBtn.classList.add('bg-orange-500', 'text-white');

                    listViewBtn.classList.remove('bg-orange-500', 'text-white');
                    listViewBtn.classList.add('bg-white', 'text-gray-600');

                    // Save preference
                    localStorage.setItem('products-view', 'grid');
                });

                // List view
                listViewBtn.addEventListener('click', function () {
                    productsGrid.classList.remove('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
                    productsGrid.classList.add('grid-cols-1');

                    // Update buttons
                    listViewBtn.classList.remove('bg-white', 'text-gray-600');
                    listViewBtn.classList.add('bg-orange-500', 'text-white');

                    gridViewBtn.classList.remove('bg-orange-500', 'text-white');
                    gridViewBtn.classList.add('bg-white', 'text-gray-600');

                    // Save preference
                    localStorage.setItem('products-view', 'list');
                });

                // Load saved preference
                const savedView = localStorage.getItem('products-view');
                if (savedView === 'list') {
                    listViewBtn.click();
                }
            }

            // Sort functionality
            const sortSelect = document.getElementById('sort-select');
            const itemsPerPageSelect = document.getElementById('items-per-page');

            if (sortSelect) {
                sortSelect.addEventListener('change', function () {
                    window.location.href = updateUrlParameter(window.location.href, 'sort', this.value);
                });
            }

            if (itemsPerPageSelect) {
                itemsPerPageSelect.addEventListener('change', function () {
                    // Lưu giá trị vào cả Local Storage và Cookie
                    localStorage.setItem('products_items_per_page', this.value);

                    // Lưu vào cookie với thời hạn 30 ngày
                    const expiryDate = new Date();
                    expiryDate.setDate(expiryDate.getDate() + 30);
                    document.cookie = `products_items_per_page=${this.value}; expires=${expiryDate.toUTCString()}; path=/`;

                    // Reset về trang 1 khi thay đổi items per page
                    let url = updateUrlParameter(window.location.href, 'items_per_page', this.value);
                    url = updateUrlParameter(url, 'page', '1');
                    window.location.href = url;
                });
            }

            // Reset filters
            const resetFiltersBtn = document.getElementById('resetFilters');
            const resetFiltersBtnSidebar = document.getElementById('resetFiltersBtn');
            const resetFiltersNoProductsBtn = document.getElementById('resetFiltersNoProducts');

            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', function () {
                    window.location.href = 'products.php?scroll=1';
                });
            }

            if (resetFiltersBtnSidebar) {
                resetFiltersBtnSidebar.addEventListener('click', function () {
                    window.location.href = 'products.php?scroll=1';
                });
            }

            if (resetFiltersNoProductsBtn) {
                resetFiltersNoProductsBtn.addEventListener('click', function () {
                    window.location.href = 'products.php?scroll=1';
                });
            }

            // Pagination Loading Effect with Delay - Giống y hệt trang tìm kiếm
            const paginationLinks = document.querySelectorAll('.page-link[href]');

            paginationLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    // Prevent immediate navigation
                    e.preventDefault();

                    // Store the target URL
                    const targetUrl = this.href;

                    // Apply loading state immediately
                    this.style.opacity = '0.6';
                    this.style.pointerEvents = 'none';

                    // Show loading spinner with text
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang tải...';

                    // Navigate after 1.5 seconds delay
                    setTimeout(() => {
                        window.location.href = targetUrl;
                    }, 1500);

                    // Fallback restore (in case something goes wrong)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.opacity = '';
                        this.style.pointerEvents = '';
                    }, 5000);
                });
            });

            // Apply filters
            const applyFiltersBtn = document.getElementById('applyFilters');
            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function () {
                    // Collect filter data
                    let url = 'products.php?';

                    // Categories
                    const selectedCategories = Array.from(document.querySelectorAll('input[name="category[]"]:checked')).map(input => input.value);
                    if (selectedCategories.length > 0) {
                        selectedCategories.forEach(cat => {
                            url += `category[]=${cat}&`;
                        });
                    }

                    // Price range
                    const priceMinInput = document.getElementById('price-min');
                    const priceMaxInput = document.getElementById('price-max');
                    const minPrice = getPriceNumericValue(priceMinInput);
                    const maxPrice = getPriceNumericValue(priceMaxInput);
                    if (minPrice) url += `price_min=${minPrice}&`;
                    if (maxPrice) url += `price_max=${maxPrice}&`;

                    // Promotions
                    const selectedPromotions = Array.from(document.querySelectorAll('input[name="promotion[]"]:checked')).map(input => input.value);
                    if (selectedPromotions.length > 0) {
                        selectedPromotions.forEach(promo => {
                            url += `promotion[]=${promo}&`;
                        });
                    }

                    // Remove trailing &
                    if (url.endsWith('&')) {
                        url = url.slice(0, -1);
                    }

                    window.location.href = url;
                });
            }
        });

        // Helper function to update URL parameters
        function updateUrlParameter(url, key, value) {
            const re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
            const separator = url.indexOf('?') !== -1 ? "&" : "?";

            if (url.match(re)) {
                return url.replace(re, '$1' + key + "=" + value + '$2');
            } else {
                return url + separator + key + "=" + value;
            }
        }

        // Product interaction functions
        function quickView(productId) {
            console.log('Quick view for product ID:', productId);
            // Implement quick view modal functionality
            alert('Xem nhanh sản phẩm ID: ' + productId);
        }

        function toggleWishlist(productId) {
            console.log('Toggle wishlist for product ID:', productId);
            // Implement wishlist toggle functionality
            alert('Đã thêm/xóa sản phẩm ID: ' + productId + ' khỏi danh sách yêu thích');
        }

        function toggleCompare(productId) {
            console.log('Toggle compare for product ID:', productId);
            // Implement compare toggle functionality
            alert('Đã thêm/xóa sản phẩm ID: ' + productId + ' khỏi danh sách so sánh');
        }

        function addToCart(productId) {
            console.log('Add to cart product ID:', productId);
            // Implement add to cart functionality
            alert('Đã thêm sản phẩm ID: ' + productId + ' vào giỏ hàng');
        }

        function buyNow(productId) {
            console.log('Buy now product ID:', productId);
            // Implement buy now functionality
            window.location.href = 'checkout.php?product_id=' + productId;
        }

        function contactProduct(productId) {
            console.log('Contact about product ID:', productId);
            // Implement contact functionality
            alert('Liên hệ về sản phẩm ID: ' + productId);
        }
    </script>



    <!-- Tích hợp Giải pháp 1: Overflow Visible với search logic hiện tại -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Đảm bảo solution1 đã được khởi tạo
            if (typeof SearchOverflowSolution1 !== 'undefined') {
                SearchOverflowSolution1.init();

                // Tích hợp với search input hiện tại
                const searchInput = document.getElementById('main-search-input');
                const searchSuggestions = document.getElementById('search-suggestions');

                if (searchInput && searchSuggestions) {
                    let searchTimeout = null;
                    let lastQuery = '';
                    let isSearching = false;
                    let searchCache = new Map(); // Cache để lưu kết quả tìm kiếm
                    let lastApiCall = 0; // Timestamp của lần gọi API cuối
                    const API_DEBOUNCE_TIME = 500; // 500ms debounce
                    let currentRequestId = 0; // ID để track request hiện tại

                    // Hàm xử lý tìm kiếm thống nhất
                    function handleSearch(query, forceSearch = false) {
                        const now = Date.now();

                        // Tránh tìm kiếm trùng lặp
                        if (query === lastQuery && !forceSearch && (now - lastApiCall) < API_DEBOUNCE_TIME) {
                            return;
                        }

                        // Xóa timeout cũ
                        if (searchTimeout) {
                            clearTimeout(searchTimeout);
                        }

                        if (query.length > 0) {
                            // Clear error state khi user bắt đầu nhập
                            searchSuggestions.classList.remove('error-state');

                            // Kiểm tra cache trước
                            if (searchCache.has(query)) {
                                const cachedResults = searchCache.get(query);
                                displayRealSuggestions(cachedResults);
                                SearchOverflowSolution1.showSuggestions();
                                lastQuery = query;
                                return;
                            }

                            // Nếu đang tìm kiếm cùng query, không thực hiện tìm kiếm mới
                            if (isSearching && query === lastQuery) {
                                return;
                            }

                            // Hiển thị loading state chỉ khi cần thiết
                            if (searchSuggestions.innerHTML.trim() === '' || query !== lastQuery) {
                                showLoadingState();
                                SearchOverflowSolution1.showSuggestions();
                            }

                            // Debounce search để tránh gọi API quá nhiều
                            searchTimeout = setTimeout(() => {
                                const currentQuery = searchInput.value.trim();
                                if (query === currentQuery && (Date.now() - lastApiCall) >= API_DEBOUNCE_TIME) {
                                    fetchRealSuggestions(query);
                                    lastQuery = query;
                                    lastApiCall = Date.now();
                                }
                            }, 300);
                        } else {
                            lastQuery = '';
                            SearchOverflowSolution1.hideSuggestions();
                        }
                    }

                    // Đánh dấu để tránh đăng ký event listener trùng lặp
                    if (!searchInput.hasAttribute('data-search-initialized')) {
                        searchInput.setAttribute('data-search-initialized', 'true');

                        // Lắng nghe sự kiện input để hiển thị suggestions
                        searchInput.addEventListener('input', function () {
                            const query = this.value.trim();
                            handleSearch(query);
                        });

                        // Lắng nghe sự kiện focus - chỉ hiển thị lại nếu đã có kết quả
                        searchInput.addEventListener('focus', function () {
                            const query = this.value.trim();
                            if (query.length > 0 && searchSuggestions.innerHTML.trim() !== '') {
                                // Chỉ hiển thị lại kết quả đã có, không gọi API mới
                                SearchOverflowSolution1.showSuggestions();
                            }
                        });

                        // Lắng nghe sự kiện blur
                        searchInput.addEventListener('blur', function () {
                            // Delay để cho phép click vào suggestions
                            setTimeout(() => {
                                SearchOverflowSolution1.hideSuggestions();
                            }, 200);
                        });
                    }
                }

                // Hàm hiển thị trạng thái loading
                function showLoadingState() {
                    searchSuggestions.innerHTML = `
                <div class="p-4 text-center">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
                        <span class="text-gray-500 text-sm">Đang tìm kiếm...</span>
                    </div>
                </div>
            `;
                }

                // Hàm gọi API tìm kiếm thực tế
                function fetchRealSuggestions(query) {
                    // Tạo request ID duy nhất
                    const requestId = ++currentRequestId;
                    console.log(`🚀 Starting search request #${requestId} for query: "${query}"`);

                    // Đánh dấu đang tìm kiếm
                    isSearching = true;

                    // Tạo AbortController để có thể cancel request
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

                    fetch(`<?php echo BASE_URL; ?>/api/search_suggestions.php?keyword=${encodeURIComponent(query)}`, {
                        signal: controller.signal
                    })
                        .then(response => {
                            // Kiểm tra HTTP status code
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Kiểm tra cấu trúc response
                            if (!data || typeof data !== 'object') {
                                throw new Error('Invalid response format');
                            }

                            // Đảm bảo có field suggestions
                            if (!Array.isArray(data.suggestions)) {
                                throw new Error('Missing or invalid suggestions field');
                            }

                            console.log(`✅ Request #${requestId} completed successfully. Results: ${data.suggestions.length} items`);
                            console.log(`Current request ID: ${currentRequestId}, Current query: "${searchInput.value.trim()}"`);

                            // Chỉ hiển thị kết quả nếu đây là request mới nhất và query vẫn còn hiện tại
                            if (requestId === currentRequestId && query === searchInput.value.trim()) {
                                console.log(`📋 Displaying results for request #${requestId}`);
                                // Lưu vào cache
                                searchCache.set(query, data.suggestions);

                                // Giới hạn cache size (tối đa 20 queries)
                                if (searchCache.size > 20) {
                                    const firstKey = searchCache.keys().next().value;
                                    searchCache.delete(firstKey);
                                }

                                displayRealSuggestions(data.suggestions);
                            } else {
                                console.log(`🚫 Ignoring results for request #${requestId} (outdated)`);
                            }
                        })
                        .catch(error => {
                            console.error(`❌ Request #${requestId} failed:`, error);

                            // Không hiển thị lỗi nếu request bị abort (do user thay đổi query nhanh)
                            if (error.name === 'AbortError') {
                                console.log(`⏹️ Request #${requestId} was aborted`);
                                return;
                            }

                            // Chỉ hiển thị lỗi nếu đây là request mới nhất và query vẫn còn hiện tại
                            if (requestId === currentRequestId && query === searchInput.value.trim()) {
                                console.log(`💥 Showing error state for request #${requestId}`);
                                showErrorState();
                            } else {
                                console.log(`🤐 Suppressing error for outdated request #${requestId}`);
                            }
                        })
                        .finally(() => {
                            // Clear timeout
                            clearTimeout(timeoutId);
                            // Đánh dấu kết thúc tìm kiếm
                            isSearching = false;
                        });
                }

                // Hàm hiển thị kết quả tìm kiếm thực tế
                function displayRealSuggestions(suggestions) {
                    // Clear any previous error states
                    searchSuggestions.classList.remove('error-state');

                    if (!suggestions || suggestions.length === 0) {
                        showNoResultsState();
                        return;
                    }

                    const html = suggestions.map(product => `
                <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-200" onclick="selectProduct('${product.url}')">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            <img src="${product.image}" alt="${product.name}" class="w-full h-full object-cover" onerror="this.src='<?php echo BASE_URL; ?>/assets/img/no-image.jpg'">
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 truncate">${product.name}</div>
                            <div class="text-xs text-gray-500">${product.category}</div>
                        </div>
                        <div class="text-right flex-shrink-0">
                            <div class="text-sm font-semibold text-orange-600">${product.price}</div>
                            <div class="text-xs text-gray-400">⭐ ${product.rating}</div>
                        </div>
                    </div>
                </div>
            `).join('');

                    searchSuggestions.innerHTML = html;
                }

                // Hàm hiển thị trạng thái không có kết quả
                function showNoResultsState() {
                    searchSuggestions.innerHTML = `
                <div class="p-4 text-center">
                    <div class="text-gray-400 mb-2">
                        <i class="fas fa-search text-2xl"></i>
                    </div>
                    <div class="text-gray-500 text-sm">Không tìm thấy sản phẩm phù hợp</div>
                    <div class="text-gray-400 text-xs mt-1">Thử tìm kiếm với từ khóa khác</div>
                </div>
            `;
                }

                // Hàm hiển thị trạng thái lỗi (chỉ hiển thị sau một khoảng thời gian)
                function showErrorState() {
                    // Delay hiển thị lỗi để tránh flash error khi user đang nhập nhanh
                    setTimeout(() => {
                        // Chỉ hiển thị lỗi nếu vẫn không có kết quả và user không đang nhập
                        if (searchSuggestions.innerHTML.includes('Đang tìm kiếm...') ||
                            searchSuggestions.classList.contains('error-state')) {

                            searchSuggestions.classList.add('error-state');
                            searchSuggestions.innerHTML = `
                        <div class="p-4 text-center">
                            <div class="text-red-400 mb-2">
                                <i class="fas fa-exclamation-triangle text-2xl"></i>
                            </div>
                            <div class="text-gray-500 text-sm">Có lỗi xảy ra khi tìm kiếm</div>
                            <div class="text-gray-400 text-xs mt-1">Vui lòng thử lại sau</div>
                        </div>
                    `;
                        }
                    }, 1000); // Delay 1 giây
                }

                // Hàm chọn sản phẩm từ suggestions
                window.selectProduct = function (productUrl) {
                    window.location.href = productUrl;
                };

                console.log('✅ Giải pháp 1: Overflow Visible đã được tích hợp thành công!');
            } else {
                console.warn('⚠️ SearchOverflowSolution1 chưa được load');
            }
        });

        // Auto-scroll to Products section when filtering/searching
        function autoScrollToProducts() {
            // Tìm phần Products
            const productsSection = document.getElementById('products-section');
            if (!productsSection) {
                console.warn('Products section not found');
                return;
            }

            // Tính toán chiều cao header sau khi scroll (khi top bar ẩn)
            let finalHeaderHeight = 0;

            // Kiểm tra xem đang ở desktop hay mobile
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // Mobile: tìm mobile header
                const mobileHeader = document.querySelector('.mobile-header');
                if (mobileHeader && window.getComputedStyle(mobileHeader).display !== 'none') {
                    finalHeaderHeight = mobileHeader.offsetHeight;
                }
            } else {
                // Desktop: tính header height sau khi top bar ẩn
                const premiumHeader = document.querySelector('.premium-header');
                if (premiumHeader && window.getComputedStyle(premiumHeader).display !== 'none') {
                    // Tìm top bar - sẽ ẩn khi scroll
                    const topBar = premiumHeader.querySelector('.top-bar');

                    if (topBar) {
                        // Header height = total height - top bar height (vì top bar sẽ ẩn)
                        const totalHeight = premiumHeader.offsetHeight;
                        const topBarHeight = topBar.offsetHeight;
                        finalHeaderHeight = totalHeight - topBarHeight;

                        console.log('Desktop header calculation:', {
                            totalHeight,
                            topBarHeight,
                            finalHeaderHeight
                        });
                    } else {
                        // Fallback: ước tính
                        const totalHeight = premiumHeader.offsetHeight;
                        const estimatedTopBarHeight = 40; // Ước tính top bar height
                        finalHeaderHeight = Math.max(60, totalHeight - estimatedTopBarHeight);
                    }
                }
            }

            // Fallback nếu không tìm thấy header
            if (finalHeaderHeight === 0) {
                const anyHeader = document.querySelector('header, .header, [class*="header"]');
                if (anyHeader) {
                    finalHeaderHeight = isMobile ? anyHeader.offsetHeight : Math.max(70, anyHeader.offsetHeight - 45);
                } else {
                    finalHeaderHeight = isMobile ? 60 : 70; // Default values
                }
            }

            // Tính vị trí cuộn: mép trên của Products section trừ đi chiều cao header
            // Để mép dưới header chạm vào mép trên của Products
            const targetPosition = productsSection.offsetTop - finalHeaderHeight;

            // Smooth scroll
            window.scrollTo({
                top: Math.max(0, targetPosition),
                behavior: 'smooth'
            });

            console.log('Auto-scroll to Products:', {
                isMobile,
                finalHeaderHeight,
                targetPosition,
                productsOffset: productsSection.offsetTop,
                calculation: `${productsSection.offsetTop} - ${finalHeaderHeight} = ${targetPosition}`
            });
        }

        // Auto-scroll khi trang load với filter/search parameters
        document.addEventListener('DOMContentLoaded', function () {
            // Kiểm tra xem có filter/search parameters không
            const urlParams = new URLSearchParams(window.location.search);
            const hasKeyword = urlParams.has('keyword') && urlParams.get('keyword').trim() !== '';

            // Kiểm tra category (hỗ trợ cả category và category[])
            const hasCategory = urlParams.has('category') || urlParams.has('category[]') ||
                Array.from(urlParams.keys()).some(key => key.startsWith('category['));

            // Kiểm tra promotion filter
            const hasPromotion = urlParams.has('promotion') || urlParams.has('promotion[]') ||
                Array.from(urlParams.keys()).some(key => key.startsWith('promotion['));

            const hasPriceMin = urlParams.has('price_min') && urlParams.get('price_min') !== '';
            const hasPriceMax = urlParams.has('price_max') && urlParams.get('price_max') !== '';
            const hasSort = urlParams.has('sort') && urlParams.get('sort') !== 'newest';

            // Kiểm tra scroll parameter cho reset actions
            const hasScrollParam = urlParams.has('scroll') && urlParams.get('scroll') === '1';

            // Nếu có bất kỳ filter nào hoặc scroll parameter, auto-scroll
            if (hasKeyword || hasCategory || hasPromotion || hasPriceMin || hasPriceMax || hasSort || hasScrollParam) {
                // Delay một chút để đảm bảo page đã render xong
                setTimeout(autoScrollToProducts, 300);
            }
        });

        // Auto-scroll khi submit form tìm kiếm
        const searchForms = document.querySelectorAll('form[action*="products.php"]');
        searchForms.forEach(form => {
            form.addEventListener('submit', function (e) {
                // Không prevent default, để form submit bình thường
                // Sau khi page reload, DOMContentLoaded sẽ handle auto-scroll
            });
        });

        // Auto-scroll khi click vào filter buttons
        document.addEventListener('click', function (e) {
            // Quick search tags
            if (e.target.closest('.quick-search-tag')) {
                setTimeout(autoScrollToProducts, 100);
            }

            // Apply filters button
            if (e.target.id === 'applyFilters' || e.target.closest('#applyFilters')) {
                setTimeout(autoScrollToProducts, 100);
            }

            // Category filter links
            if (e.target.closest('a[href*="category="]')) {
                // Sẽ auto-scroll sau khi page load
            }

            // Search suggestion links
            if (e.target.closest('a[href*="keyword="]')) {
                // Sẽ auto-scroll sau khi page load
            }
        });

        // Auto-scroll khi thay đổi sort
        const sortSelect = document.querySelector('select[name="sort"]');
        if (sortSelect) {
            sortSelect.addEventListener('change', function () {
                setTimeout(autoScrollToProducts, 100);
            });
        }

        // Auto-scroll khi thay đổi items per page
        const limitSelect = document.querySelector('select[name="items_per_page"]');
        if (limitSelect) {
            limitSelect.addEventListener('change', function () {
                setTimeout(autoScrollToProducts, 100);
            });
        }

        // Auto-scroll khi click pagination
        document.addEventListener('click', function (e) {
            if (e.target.closest('.pagination a')) {
                // Sẽ auto-scroll sau khi page load
            }
        });

        // Expose function globally for manual use
        window.autoScrollToProducts = autoScrollToProducts;

        // Function to scroll to products search input
        function scrollToProductsSearch() {
            const searchInput = document.getElementById('main-search-input');
            if (searchInput) {
                // Smooth scroll to search input
                searchInput.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Focus and select text after scroll
                setTimeout(() => {
                    searchInput.focus();
                    searchInput.select();
                }, 500);
            }
        }



        // Loading Skeleton for Filter Results Header
        function showFilterHeaderLoading() {
            const filterHeader = document.querySelector('.filter-results-header');
            if (filterHeader) {
                filterHeader.classList.add('loading');
            }
        }

        function hideFilterHeaderLoading() {
            const filterHeader = document.querySelector('.filter-results-header');
            if (filterHeader) {
                filterHeader.classList.remove('loading');
            }
        }

        // Smooth count badge animation
        function animateCountBadge() {
            const countBadge = document.getElementById('results-count');
            if (countBadge) {
                countBadge.classList.add('updating');
                setTimeout(() => {
                    countBadge.classList.remove('updating');
                }, 600);
            }
        }

        // Observer to detect count changes (for future AJAX updates)
        function observeCountChanges() {
            const countBadge = document.getElementById('results-count');
            if (countBadge && window.MutationObserver) {
                const observer = new MutationObserver(function (mutations) {
                    mutations.forEach(function (mutation) {
                        if (mutation.type === 'childList' || mutation.type === 'characterData') {
                            animateCountBadge();
                        }
                    });
                });

                observer.observe(countBadge, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
            }
        }

        // Add loading state to form submissions and filter changes
        document.addEventListener('DOMContentLoaded', function () {
            // Loading for search form submission
            const searchForms = document.querySelectorAll('form[action*="products.php"]');
            searchForms.forEach(form => {
                form.addEventListener('submit', function () {
                    showFilterHeaderLoading();
                });
            });

            // Loading for filter button clicks
            const applyFiltersBtn = document.getElementById('applyFilters');
            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function () {
                    showFilterHeaderLoading();
                });
            }

            // Loading for quick search tags
            const quickSearchTags = document.querySelectorAll('.quick-search-tag');
            quickSearchTags.forEach(tag => {
                tag.addEventListener('click', function () {
                    showFilterHeaderLoading();
                });
            });

            // Loading for pagination links
            const paginationLinks = document.querySelectorAll('.page-link[href]');
            paginationLinks.forEach(link => {
                link.addEventListener('click', function () {
                    showFilterHeaderLoading();
                });
            });

            // Loading for sort/limit changes
            const sortSelect = document.querySelector('select[name="sort"]');
            const limitSelect = document.querySelector('select[name="limit"]');

            if (sortSelect) {
                sortSelect.addEventListener('change', function () {
                    showFilterHeaderLoading();
                });
            }

            if (limitSelect) {
                limitSelect.addEventListener('change', function () {
                    showFilterHeaderLoading();
                });
            }

            // Initialize count change observer
            observeCountChanges();
        });

    </script>

    <?php
    // Thêm script giỏ hàng thời gian thực
    echo '<script src="' . BASE_URL . '/assets/js/cart-realtime.js"></script>';

    // Thêm script cho recently viewed và trending products
    echo '<script src="' . BASE_URL . '/assets/js/recently-viewed-trending.js"></script>';

    // Include footer
    include_once 'partials/footer.php';
    ?>