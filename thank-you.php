<?php
// Thiết lập tiêu đề trang
$page_title = 'Đặt hàng thành công';
$page_description = 'Cảm ơn bạn đã đặt hàng tại Nội Thất Băng Vũ';

// Include header
include_once 'partials/header.php';

// Lấy ID đơn hàng từ URL
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// Nếu không có ID đơn hàng, chuyển hướng về trang chủ
if ($order_id <= 0) {
    redirect(BASE_URL);
}

// Lấy thông tin đơn hàng
$order = get_order_by_id($order_id);

// Nếu không tìm thấy đơn hàng, chuyển hướng về trang chủ
if (!$order) {
    redirect(BASE_URL);
}
?>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>/cart.php" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </span>
                    <span>Giỏ hàng</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>/checkout.php" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-credit-card"></i>
                    </span>
                    <span>Thanh toán</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-check-circle"></i>
                    </span>
                    <span>Đặt hàng thành công</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">
<!-- Link CSS cho thank you page hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-thankyou.css">

<!-- CSS trực tiếp cho các phần tùy chỉnh -->
<style>
    /* Email Notification Card */
    .email-notification-card {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 30px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        border: 1px solid #bae7ff;
        text-align: left;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .email-notification-card:hover {
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .email-notification-card::before {
        content: '';
        position: absolute;
        top: -50px;
        right: -50px;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%);
        border-radius: 50%;
    }

    .email-notification-card::after {
        content: '';
        position: absolute;
        bottom: -30px;
        left: -30px;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(24, 144, 255, 0.08) 0%, rgba(24, 144, 255, 0) 70%);
        border-radius: 50%;
    }

    .email-notification-icon {
        flex-shrink: 0;
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 26px;
        margin-right: 24px;
        position: relative;
        box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
        }
    }

    .email-notification-content {
        flex-grow: 1;
        position: relative;
        z-index: 1;
    }

    .email-notification-title {
        font-size: 20px;
        font-weight: 600;
        color: #1890ff;
        margin-bottom: 10px;
    }

    .email-notification-text {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.6;
    }

    .email-notification-tip {
        font-size: 14px;
        color: #666;
        font-style: italic;
    }

    .email-highlight {
        color: #1890ff;
        font-weight: 600;
        position: relative;
        display: inline-block;
        padding-bottom: 2px;
    }

    .email-highlight::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: rgba(24, 144, 255, 0.3);
        border-radius: 1px;
    }

    /* Register Form Styling */
    .register-form {
        background-color: white;
        border-radius: 10px;
        padding: 25px;
        border: 1px solid #eaeaea;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
        text-align: left;
        margin-top: 20px;
    }

    .register-card {
        background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
        border: 1px solid #bae7ff;
        border-radius: 10px;
        padding: 20px;
        text-align: left;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    }

    .register-card::before {
        content: '';
        position: absolute;
        top: -20px;
        right: -20px;
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%);
        border-radius: 50%;
    }

    .register-title {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .register-title i {
        margin-right: 10px;
        background-color: #1890ff;
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .register-description {
        font-size: 14px;
        color: #555;
        line-height: 1.6;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
    }

    .input-with-icon {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #bfbfbf;
        font-size: 16px;
    }

    .form-group input[type="text"],
    .form-group input[type="password"] {
        width: 100%;
        padding: 12px 15px 12px 40px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.3s ease;
        background-color: #fafafa;
    }

    .form-group input[type="text"]:focus,
    .form-group input[type="password"]:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        background-color: white;
        outline: none;
    }

    .form-group input[type="text"]:focus + .input-icon,
    .form-group input[type="password"]:focus + .input-icon {
        color: #1890ff;
    }

    .form-help-text {
        font-size: 12px;
        color: #888;
        margin-top: 6px;
        font-style: italic;
    }

    /* Custom Checkbox */
    .custom-checkbox-container {
        margin: 15px 0;
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #eee;
        transition: all 0.3s ease;
    }

    .custom-checkbox-container:hover {
        background-color: #f0f7ff;
        border-color: #d0e8ff;
    }

    .custom-checkbox-wrapper {
        position: relative;
    }

    .custom-checkbox {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .custom-checkbox-label {
        cursor: pointer;
        user-select: none;
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        width: 100%;
        display: block;
        padding-left: 36px;
        position: relative;
    }

    .checkbox-icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 24px;
        height: 24px;
        border: 2px solid #d9d9d9;
        border-radius: 6px;
        transition: all 0.3s ease;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .checkbox-icon i {
        color: white;
        font-size: 14px;
        opacity: 0;
        transform: scale(0) rotate(-10deg);
        transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .custom-checkbox:checked + .custom-checkbox-label .checkbox-icon {
        background-color: #10b981;
        border-color: #10b981;
        transform: scale(1.05);
    }

    .custom-checkbox:checked + .custom-checkbox-label .checkbox-icon i {
        opacity: 1;
        transform: scale(1) rotate(0);
    }

    .custom-checkbox:focus + .custom-checkbox-label .checkbox-icon {
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
    }

    .custom-checkbox-label:hover .checkbox-icon {
        border-color: #10b981;
        transform: scale(1.05);
    }

    .checkbox-text {
        display: inline;
    }

    .terms-link {
        color: #1890ff;
        font-weight: 500;
        text-decoration: none;
        position: relative;
        transition: all 0.3s ease;
        padding: 0 2px;
    }

    .terms-link:hover {
        color: #096dd9;
    }

    .terms-link::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: #1890ff;
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
    }

    .terms-link:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }

    /* Button styling */
    .thankyou-btn {
        padding: 12px 20px;
        font-size: 15px;
        font-weight: 600;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        cursor: pointer;
        border: none;
        outline: none;
    }

    .thankyou-btn i {
        margin-right: 8px;
    }

    .thankyou-btn-success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    }

    .thankyou-btn-success:hover {
        background: linear-gradient(135deg, #34d399, #10b981);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3);
    }

    /* Password toggle */
    .password-field-container {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        padding: 5px;
        transition: all 0.2s ease;
    }

    .password-toggle:hover {
        color: #1890ff;
    }

    @media (max-width: 768px) {
        .email-notification-card {
            flex-direction: column;
            text-align: center;
            padding: 20px 15px;
        }

        .email-notification-icon {
            margin-right: 0;
            margin-bottom: 15px;
        }

        .custom-checkbox-container {
            padding: 12px 10px;
        }

        .custom-checkbox-wrapper {
            flex-direction: row;
            align-items: flex-start;
        }

        .checkbox-icon {
            margin-top: 2px;
        }

        .checkbox-text {
            text-align: left;
        }

        .email-notification-title {
            font-size: 18px;
        }

        .email-notification-text {
            font-size: 15px;
        }

        .register-form {
            padding: 20px 15px;
        }

        .register-title {
            font-size: 16px;
        }

        .register-description {
            font-size: 13px;
        }

        .form-group input[type="text"],
        .form-group input[type="password"] {
            padding: 10px 12px;
        }
    }
</style>

<!-- Thank You -->
<div class="modern-thankyou">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto thankyou-card">
            <!-- Header thành công -->
            <div class="thankyou-header">
                <div class="thankyou-header-content">
                    <div class="thankyou-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1 class="thankyou-title">Đặt hàng thành công!</h1>
                    <p class="thankyou-subtitle">Cảm ơn bạn đã đặt hàng tại Nội Thất Băng Vũ</p>
                </div>
            </div>

            <!-- Nội dung chính -->
            <div class="thankyou-content">
                <!-- Thông tin đơn hàng -->
                <div class="mb-8">
                    <h2 class="thankyou-section-title">Thông tin đơn hàng</h2>

                    <div class="order-info-card">
                        <div class="order-info-item">
                            <div class="order-info-label">Mã đơn hàng:</div>
                            <div class="order-info-value">#<?php echo $order['id']; ?></div>
                        </div>
                        <div class="order-info-item">
                            <div class="order-info-label">Ngày đặt hàng:</div>
                            <div class="order-info-value"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></div>
                        </div>
                        <div class="order-info-item">
                            <div class="order-info-label">Tổng tiền:</div>
                            <div class="order-info-value font-semibold text-primary"><?php echo format_currency($order['total']); ?></div>
                        </div>
                        <div class="order-info-item">
                            <div class="order-info-label">Trạng thái:</div>
                            <div class="order-info-value">
                                <?php
                                $status_info = get_order_status_info($order['status'], 'user');
                                $status_class = strpos($status_info['class'], 'green') !== false ? 'order-status-success' : 'order-status-processing';
                                ?>
                                <span class="order-status <?php echo $status_class; ?>">
                                    <i class="fas fa-circle text-xs mr-1"></i> <?php echo $status_info['text']; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chi tiết đơn hàng -->
                <div class="mb-8">
                    <h2 class="thankyou-section-title">Chi tiết đơn hàng</h2>

                    <div class="overflow-x-auto">
                        <table class="order-details-table">
                            <thead>
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th>Giá</th>
                                    <th>Số lượng</th>
                                    <th>Tổng</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($order['items'] as $item): ?>
                                <tr>
                                    <td>
                                        <div class="flex items-center">
                                            <div class="order-product-image">
                                                <?php if ($item['product_image']): ?>
                                                <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['product_image']; ?>" alt="<?php echo $item['product_name']; ?>">
                                                <?php else: ?>
                                                <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-3">
                                                <div class="order-product-name"><?php echo $item['product_name']; ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo format_currency($item['price']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td class="font-medium"><?php echo format_currency($item['price'] * $item['quantity']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-right">Tạm tính:</td>
                                    <td><?php echo format_currency($order['total']); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-right">Phí vận chuyển:</td>
                                    <td class="text-green-600">Miễn phí</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-right font-bold">Tổng cộng:</td>
                                    <td class="order-total-price"><?php echo format_currency($order['total']); ?></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Thông tin giao hàng -->
                <div class="mb-8">
                    <h2 class="thankyou-section-title">Thông tin giao hàng</h2>

                    <div class="shipping-info-card">
                        <div class="shipping-info-item">
                            <span class="shipping-info-label">Họ và tên:</span>
                            <?php echo $order['full_name']; ?>
                        </div>
                        <div class="shipping-info-item">
                            <span class="shipping-info-label">Email:</span>
                            <?php echo $order['email']; ?>
                        </div>
                        <div class="shipping-info-item">
                            <span class="shipping-info-label">Số điện thoại:</span>
                            <?php echo $order['phone']; ?>
                        </div>
                        <div class="shipping-info-item">
                            <span class="shipping-info-label">Địa chỉ:</span>
                            <?php echo $order['address']; ?>
                        </div>
                        <?php if ($order['note']): ?>
                        <div class="shipping-info-item">
                            <span class="shipping-info-label">Ghi chú:</span>
                            <?php echo $order['note']; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Thông báo và nút -->
                <div class="text-center">
                    <div class="email-notification-card">
                        <div class="email-notification-icon">
                            <i class="fas fa-envelope-open-text"></i>
                        </div>
                        <div class="email-notification-content">
                            <h3 class="email-notification-title">Email xác nhận đã được gửi</h3>
                            <p class="email-notification-text">Chúng tôi đã gửi email xác nhận đơn hàng đến địa chỉ <strong class="email-highlight"><?php echo $order['email']; ?></strong></p>
                            <p class="email-notification-tip">Vui lòng kiểm tra hộp thư đến của bạn. Nếu không tìm thấy, hãy kiểm tra thư mục spam.</p>
                        </div>
                    </div>

                    <?php if (!is_logged_in() && $order['user_id'] === null): ?>
                    <!-- Lưu thông tin đơn hàng vào session để liên kết sau khi đăng ký -->
                    <?php
                    // Lưu thông tin đơn hàng vào session để có thể liên kết sau khi đăng ký
                    $_SESSION['pending_order_id'] = $order_id;
                    $_SESSION['pending_order_email'] = $order['email'];
                    $_SESSION['pending_order_full_name'] = $order['full_name'];
                    ?>

                    <!-- Thẻ thông tin tạo tài khoản -->
                    <div class="mb-8 max-w-md mx-auto">
                        <div class="register-card">
                            <h3 class="register-title">
                                <i class="fas fa-user-plus"></i> Tạo tài khoản để theo dõi đơn hàng
                            </h3>
                            <p class="register-description">
                                Tạo tài khoản để dễ dàng theo dõi đơn hàng và nhận các ưu đãi đặc biệt! Chúng tôi sẽ liên kết đơn hàng này với tài khoản của bạn.
                            </p>

                            <div class="mt-4">
                                <a href="<?php echo BASE_URL; ?>/auth.php?register=1" class="thankyou-btn thankyou-btn-success w-full inline-block text-center">
                                    <i class="fas fa-user-plus"></i> Tạo tài khoản ngay
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="<?php echo BASE_URL; ?>" class="thankyou-btn thankyou-btn-primary">
                            <i class="fas fa-shopping-bag"></i> Tiếp tục mua sắm
                        </a>
                        <?php if (is_logged_in()): ?>
                        <a href="<?php echo BASE_URL; ?>/account/orders.php" class="thankyou-btn thankyou-btn-secondary">
                            <i class="fas fa-clipboard-list"></i> Xem đơn hàng của tôi
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
