<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Loading States - Mobile Filter Modal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .demo-section {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Demo modal footer */
        .demo-modal-footer {
            display: flex;
            gap: 16px;
            padding: 24px;
            background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
            border-radius: 16px;
            border: 1px solid #fed7aa;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.2rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .features {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #0ea5e9;
            margin: 20px 0;
        }
        
        .features h3 {
            color: #0c4a6e;
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        
        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #075985;
        }
        
        .features li {
            margin-bottom: 5px;
        }
        
        .status {
            padding: 15px;
            border-radius: 12px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
            animation: successPulse 0.6s ease;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        /* Custom elegant checkmark animation for demo */
        .demo-checkmark-primary {
            animation: elegantCheckmark 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
            color: white !important; /* White for orange button */
            font-weight: 600 !important;
        }

        .demo-checkmark-secondary {
            animation: elegantCheckmark 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
            color: #475569 !important; /* Gray for secondary button */
            font-weight: 600 !important;
        }

        @keyframes elegantCheckmark {
            0% {
                opacity: 0;
                transform: scale(0.3) rotate(-12deg);
            }
            30% {
                opacity: 0.8;
                transform: scale(1.15) rotate(3deg);
            }
            60% {
                opacity: 1;
                transform: scale(0.95) rotate(-1deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎨 Modern Loading States</h1>
        <p>Thiết kế loading states hiện đại, sạch sẽ và chuyên nghiệp cho Mobile Filter Modal</p>
        
        <div class="demo-section">
            <h2>✨ Demo Buttons</h2>
            
            <div class="demo-modal-footer">
                <button class="btn btn-secondary" id="demoResetBtn">
                    <i class="fas fa-refresh"></i>
                    <span>Đặt lại</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
                <button class="btn btn-primary" id="demoApplyBtn">
                    <i class="fas fa-search"></i>
                    <span>Áp dụng bộ lọc</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
            </div>
            
            <div class="demo-buttons">
                <button class="control-btn" onclick="testApply()">🎯 Test Apply</button>
                <button class="control-btn" onclick="testReset()">🔄 Test Reset</button>
                <button class="control-btn" onclick="testBoth()">⚡ Test Both</button>
                <button class="control-btn" onclick="resetAll()">🔧 Reset All</button>
            </div>
        </div>
        
        <div class="features">
            <h3>🚀 Elegant Design Features:</h3>
            <ul>
                <li><strong>Clean Animation:</strong> Subtle border glow thay vì pulse</li>
                <li><strong>Modern Spinner:</strong> Circle-notch icon với rotation</li>
                <li><strong>Elegant Checkmark:</strong> Cùng màu với text, bounce nhẹ</li>
                <li><strong>Color Consistency:</strong> White cho nút cam, gray cho nút xám</li>
                <li><strong>Smooth Transitions:</strong> Cubic-bezier easing như sidebar</li>
                <li><strong>Minimal Feedback:</strong> Không quá nhiều hiệu ứng</li>
                <li><strong>Professional Feel:</strong> Tham khảo thiết kế sidebar 25%</li>
                <li><strong>Quick Success:</strong> Checkmark elegant (400ms)</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test modern loading states
        </div>
    </div>
    
    <script>
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function testApply() {
            const btn = document.getElementById('demoApplyBtn');
            updateStatus('🎯 Testing Apply Button...', 'testing');
            
            // Show loading
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang áp dụng...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            
            setTimeout(() => {
                // Success with elegant checkmark animation (white for primary button)
                const spinner = btn.querySelector('.btn-spinner');
                spinner.innerHTML = '<i class="fas fa-check demo-checkmark-primary"></i>';
                updateStatus('✅ Apply Button Test Complete', 'success');
                
                setTimeout(() => {
                    // Reset
                    btn.disabled = false;
                    btn.classList.remove('loading');
                    btn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                    btn.querySelector('.btn-spinner').innerHTML = '';
                    updateStatus('✨ Ready for next test', 'ready');
                }, 600);
            }, 1500);
        }
        
        function testReset() {
            const btn = document.getElementById('demoResetBtn');
            updateStatus('🔄 Testing Reset Button...', 'testing');
            
            // Show loading
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang đặt lại...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            
            setTimeout(() => {
                // Success with elegant checkmark animation (gray for secondary button)
                const spinner = btn.querySelector('.btn-spinner');
                spinner.innerHTML = '<i class="fas fa-check demo-checkmark-secondary"></i>';
                updateStatus('✅ Reset Button Test Complete', 'success');
                
                setTimeout(() => {
                    // Reset
                    btn.disabled = false;
                    btn.classList.remove('loading');
                    btn.querySelector('span').textContent = 'Đặt lại';
                    btn.querySelector('.btn-spinner').innerHTML = '';
                    updateStatus('✨ Ready for next test', 'ready');
                }, 600);
            }, 1500);
        }
        
        function testBoth() {
            updateStatus('⚡ Testing Both Buttons...', 'testing');
            
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            // Show loading for both
            [applyBtn, resetBtn].forEach((btn, index) => {
                btn.disabled = true;
                btn.classList.add('loading');
                btn.querySelector('span').textContent = index === 0 ? 'Đang áp dụng...' : 'Đang đặt lại...';
                btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-circle-notch"></i>';
            });
            
            setTimeout(() => {
                // Success for both with elegant checkmark animation
                const applySpinner = applyBtn.querySelector('.btn-spinner');
                const resetSpinner = resetBtn.querySelector('.btn-spinner');

                applySpinner.innerHTML = '<i class="fas fa-check demo-checkmark-primary"></i>';
                resetSpinner.innerHTML = '<i class="fas fa-check demo-checkmark-secondary"></i>';

                updateStatus('✅ Both Buttons Test Complete', 'success');
                
                setTimeout(() => {
                    // Reset both
                    applyBtn.disabled = false;
                    applyBtn.classList.remove('loading');
                    applyBtn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                    applyBtn.querySelector('.btn-spinner').innerHTML = '';
                    
                    resetBtn.disabled = false;
                    resetBtn.classList.remove('loading');
                    resetBtn.querySelector('span').textContent = 'Đặt lại';
                    resetBtn.querySelector('.btn-spinner').innerHTML = '';
                    
                    updateStatus('✨ Ready for next test', 'ready');
                }, 600);
            }, 2000);
        }
        
        function resetAll() {
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            [applyBtn, resetBtn].forEach((btn, index) => {
                btn.disabled = false;
                btn.classList.remove('loading');
                btn.querySelector('span').textContent = index === 0 ? 'Áp dụng bộ lọc' : 'Đặt lại';
                btn.querySelector('.btn-spinner').innerHTML = '';
            });
            
            updateStatus('🔧 All buttons reset to original state', 'ready');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('🚀 Modern Loading Demo Ready', 'ready');
        });
    </script>
</body>
</html>
