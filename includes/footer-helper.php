<?php
/**
 * Footer Helper Functions
 * Các hàm hỗ trợ cho footer hiện đại
 */

/**
 * L<PERSON>y giá trị cài đặt từ bảng site_settings
 *
 * @param string $key Khóa cài đặt
 * @param mixed $default Gi<PERSON> trị mặc định nếu không tìm thấy
 * @return mixed Giá trị cài đặt
 */
function get_setting($key, $default = '') {
    global $conn;

    try {
        // Kiểm tra xem bảng site_settings có tồn tại không
        $stmt = $conn->query("SHOW TABLES LIKE 'site_settings'");
        if ($stmt->rowCount() == 0) {
            return $default;
        }

        // Lấy giá trị cài đặt
        $stmt = $conn->prepare("SELECT setting_value FROM site_settings WHERE setting_key = :key LIMIT 1");
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch();
            return $result['setting_value'];
        }
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Error in get_setting: " . $e->getMessage());
    }

    return $default;
}

/**
 * Cập nhật giá trị cài đặt trong bảng site_settings
 *
 * @param string $key Khóa cài đặt
 * @param mixed $value Giá trị cài đặt
 * @return bool Trạng thái cập nhật
 */
function update_setting($key, $value) {
    global $conn;

    try {
        // Kiểm tra xem bảng site_settings có tồn tại không
        $stmt = $conn->query("SHOW TABLES LIKE 'site_settings'");
        if ($stmt->rowCount() == 0) {
            // Tạo bảng nếu chưa tồn tại
            $conn->exec("CREATE TABLE site_settings (
                id INT(11) NOT NULL AUTO_INCREMENT,
                setting_key VARCHAR(255) NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY (setting_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        }

        // Cập nhật hoặc thêm mới cài đặt
        $stmt = $conn->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES (:key, :value)
                               ON DUPLICATE KEY UPDATE setting_value = :value");
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        $stmt->bindParam(':value', $value, PDO::PARAM_STR);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        // Ghi log lỗi nếu cần
        error_log("Error in update_setting: " . $e->getMessage());
        return false;
    }
}

/**
 * Lấy danh sách danh mục sản phẩm cho footer
 *
 * @param int $limit Số lượng danh mục tối đa
 * @return array Danh sách danh mục
 */
function get_footer_categories($limit = 6) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            SELECT id, name, slug
            FROM categories
            WHERE status = 1 AND parent_id IS NULL
            ORDER BY name ASC
            LIMIT :limit
        ");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error in get_footer_categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách bài viết mới nhất cho footer
 *
 * @param int $limit Số lượng bài viết tối đa
 * @return array Danh sách bài viết
 */
function get_footer_latest_posts($limit = 3) {
    global $conn;

    try {
        // Kiểm tra xem bảng blog_posts có tồn tại không
        $stmt = $conn->query("SHOW TABLES LIKE 'blog_posts'");
        if ($stmt->rowCount() == 0) {
            return [];
        }

        $stmt = $conn->prepare("
            SELECT id, title, slug, published_at
            FROM blog_posts
            WHERE status = 1
            ORDER BY published_at DESC
            LIMIT :limit
        ");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error in get_footer_latest_posts: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy danh sách liên kết nhanh cho footer
 *
 * @return array Danh sách liên kết
 */
function get_footer_quick_links() {
    $links_json = get_setting('footer_col2_links', '[]');
    $links = json_decode($links_json, true);

    if (!is_array($links)) {
        return [];
    }

    return $links;
}

/**
 * Lấy danh sách liên kết hỗ trợ khách hàng cho footer
 *
 * @return array Danh sách liên kết
 */
function get_footer_support_links() {
    $links_json = get_setting('footer_col3_links', '[]');
    $links = json_decode($links_json, true);

    if (!is_array($links)) {
        return [];
    }

    return $links;
}

/**
 * Tạo HTML cho Facebook Page Plugin
 *
 * @return string HTML của Facebook Page Plugin
 */
function get_facebook_page_plugin() {
    // Lấy URL Facebook từ cài đặt
    $facebook_url = get_setting('facebook_url', 'https://web.facebook.com/tunhuachatluong');
    $company_name = get_setting('company_name', 'Nội Thất Chất Lượng Hà Nội');

    // Sử dụng chính xác mã Facebook Page Plugin theo yêu cầu
    $html = '
    <div class="fb-page"
        data-href="' . htmlspecialchars($facebook_url) . '"
        data-tabs=""
        data-width="500"
        data-height="130"
        data-small-header="false"
        data-adapt-container-width="true"
        data-hide-cover="false"
        data-show-facepile="false"
        data-lazy="true">
        <blockquote cite="' . htmlspecialchars($facebook_url) . '" class="fb-xfbml-parse-ignore">
            <a href="' . htmlspecialchars($facebook_url) . '">' . htmlspecialchars($company_name) . '</a>
        </blockquote>
    </div>';

    return $html;
}

/**
 * Tạo HTML cho danh sách mạng xã hội
 *
 * @return string HTML của danh sách mạng xã hội
 */
function get_social_links_html() {
    $social_networks = [
        'facebook' => ['image' => 'facebook.webp', 'title' => 'Facebook'],
        'instagram' => ['image' => 'instagram.webp', 'title' => 'Instagram'],
        'youtube' => ['image' => 'youtube.webp', 'title' => 'YouTube'],
        'tiktok' => ['image' => 'tiktok.webp', 'title' => 'TikTok'],
        'zalo' => ['image' => 'zalo.png', 'title' => 'Zalo']
    ];

    $html = '<div class="footer-social">';

    foreach ($social_networks as $network => $info) {
        $url = get_setting($network . '_url', '');

        if (!empty($url) && file_exists(ROOT_PATH . 'assets/images/logo social/' . $info['image'])) {
            $html .= '
            <a href="' . htmlspecialchars($url) . '" class="footer-social-link" target="_blank" rel="noopener" aria-label="' . $info['title'] . '">
                <img src="' . BASE_URL . '/assets/images/logo social/' . $info['image'] . '" alt="' . $info['title'] . '" class="footer-social-icon">
            </a>';
        }
    }

    $html .= '</div>';

    return $html;
}

/**
 * Tạo HTML cho thông tin liên hệ
 *
 * @return string HTML của thông tin liên hệ
 */
function get_contact_info_html() {
    $company_address = get_setting('company_address', '');
    $company_phone = get_setting('company_phone', '');
    $company_email = get_setting('company_email', '');
    $business_hours = get_setting('business_hours', '');

    $html = '<div class="footer-contact-info">';

    if (!empty($company_address)) {
        $html .= '
        <div class="footer-contact-item">
            <div class="footer-contact-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="footer-contact-text">
                ' . htmlspecialchars($company_address) . '
            </div>
        </div>';
    }

    if (!empty($company_phone)) {
        $html .= '
        <div class="footer-contact-item">
            <div class="footer-contact-icon">
                <i class="fas fa-phone-alt"></i>
            </div>
            <div class="footer-contact-text">
                <a href="tel:' . preg_replace('/[^0-9]/', '', $company_phone) . '">' . htmlspecialchars($company_phone) . '</a>
            </div>
        </div>';
    }

    if (!empty($company_email)) {
        $html .= '
        <div class="footer-contact-item">
            <div class="footer-contact-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="footer-contact-text">
                <a href="mailto:' . htmlspecialchars($company_email) . '">' . htmlspecialchars($company_email) . '</a>
            </div>
        </div>';
    }

    if (!empty($business_hours)) {
        $html .= '
        <div class="footer-contact-item">
            <div class="footer-contact-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="footer-contact-text">
                ' . htmlspecialchars($business_hours) . '
            </div>
        </div>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Tạo HTML cho các chứng nhận (Bộ Công Thương và các chứng nhận khác)
 *
 * @return string HTML của các chứng nhận
 */
function get_government_certification_html() {
    $html = '<div class="footer-certifications">';

    // Lấy danh sách chứng nhận từ cài đặt
    $certifications_json = get_setting('footer_certifications', '[]');
    $certifications = json_decode($certifications_json, true);

    // Nếu không có chứng nhận nào hoặc dữ liệu không hợp lệ, sử dụng chứng nhận mặc định (Bộ Công Thương)
    if (!is_array($certifications) || empty($certifications)) {
        // Lấy URL và đường dẫn hình ảnh từ cài đặt cũ (để tương thích ngược)
        $bct_url = get_setting('bct_url', 'http://online.gov.vn/');
        $bct_image = get_setting('bct_image', '/uploads/footer/bocongthong.png');

        // Đảm bảo đường dẫn hình ảnh bắt đầu bằng BASE_URL
        if (strpos($bct_image, 'http') !== 0 && strpos($bct_image, '//') !== 0) {
            $bct_image = BASE_URL . $bct_image;
        }

        $html .= '
        <div class="footer-certification">
            <a href="' . htmlspecialchars($bct_url) . '" target="_blank" rel="noopener" class="footer-certification-link">
                <img src="' . htmlspecialchars($bct_image) . '" alt="Chứng nhận Bộ Công Thương" class="footer-certification-image">
            </a>
        </div>';
    } else {
        // Hiển thị tất cả các chứng nhận từ danh sách
        foreach ($certifications as $cert) {
            if (empty($cert['url']) || empty($cert['image'])) {
                continue;
            }

            $cert_url = $cert['url'];
            $cert_image = $cert['image'];
            $cert_name = !empty($cert['name']) ? $cert['name'] : 'Chứng nhận';

            // Đảm bảo đường dẫn hình ảnh bắt đầu bằng BASE_URL
            if (strpos($cert_image, 'http') !== 0 && strpos($cert_image, '//') !== 0) {
                $cert_image = BASE_URL . $cert_image;
            }

            $html .= '
            <div class="footer-certification">
                <a href="' . htmlspecialchars($cert_url) . '" target="_blank" rel="noopener" class="footer-certification-link">
                    <img src="' . htmlspecialchars($cert_image) . '" alt="' . htmlspecialchars($cert_name) . '" class="footer-certification-image">
                </a>
            </div>';
        }
    }

    $html .= '</div>';
    return $html;
}