<?php
/**
 * Database connection configuration
 */

try {
    // Database credentials
    $host = 'localhost';
    $dbname = 'noithatbangvu';
    $username = 'root';
    $password = '';
    $charset = 'utf8mb4';

    // DSN (Data Source Name)
    $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

    // PDO options
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    // Create PDO instance
    $db = new PDO($dsn, $username, $password, $options);

} catch (PDOException $e) {
    // Log error
    error_log("Database Connection Error: " . $e->getMessage());
    
    // Set $db to null so functions can handle the error gracefully
    $db = null;
    
    // You might want to show a user-friendly message
    die("<PERSON>h<PERSON>ng thể kết nối đến cơ sở dữ liệu. <PERSON>ui lòng thử lại sau.");
} 