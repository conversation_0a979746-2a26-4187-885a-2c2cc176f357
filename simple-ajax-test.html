<!DOCTYPE html>
<html>
<head>
    <title>Simple AJAX Test</title>
</head>
<body>
    <h1>Simple AJAX Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing...';
            
            try {
                // Test simple API first
                const response1 = await fetch('api/simple-test.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'data' })
                });
                
                const data1 = await response1.json();
                console.log('Simple test result:', data1);
                
                // Test filter API
                const response2 = await fetch('api/filter-products.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ page: 1, items_per_page: 5 })
                });
                
                const data2 = await response2.json();
                console.log('Filter test result:', data2);
                
                result.innerHTML = `
                    <h3>Simple API Test:</h3>
                    <pre>${JSON.stringify(data1, null, 2)}</pre>
                    <h3>Filter API Test:</h3>
                    <pre>${JSON.stringify(data2, null, 2)}</pre>
                `;
                
            } catch (error) {
                result.innerHTML = `Error: ${error.message}`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>
