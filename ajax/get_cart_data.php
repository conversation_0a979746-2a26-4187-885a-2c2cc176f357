<?php
/**
 * AJAX endpoint để lấy dữ liệu giỏ hàng
 */

// <PERSON>o gồm các file cần thiết
require_once '../includes/init.php';

// Đ<PERSON><PERSON> bảo phản hồi là JSON
header('Content-Type: application/json');

// Lấy thông tin giỏ hàng
$cart_count = get_cart_count(); // Tổng số lượng sản phẩm
$cart_items_count = get_cart_items_count(); // Số lượng items (sản phẩm khác nhau)
$cart_total = get_cart_total();
// Lấy tất cả sản phẩm trong giỏ hàng, không giới hạn số lượng
$cart_items = get_cart_items();

// Định dạng tổng tiền
$formatted_total = format_currency($cart_total);

// Trả về dữ liệu giỏ hàng dưới dạng JSON
echo json_encode([
    'success' => true,
    'count' => $cart_count, // Tổng số lượng sản phẩm (cho badge)
    'items_count' => $cart_items_count, // Số lượng items khác nhau (cho hiển thị "X sản phẩm")
    'total' => $formatted_total,
    'raw_total' => $cart_total,
    'items' => $cart_items
]);
