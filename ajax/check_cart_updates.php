<?php
// Include init
require_once '../includes/init.php';

// L<PERSON>y thời gian cập nhật cuối cùng từ request
$last_updated = isset($_GET['last_updated']) ? (int)$_GET['last_updated'] : 0;

// Lấy thời gian hiện tại
$current_time = time() * 1000; // Chuyển đổi sang milliseconds để phù hợp với JavaScript

// Kiểm tra xem có cập nhật giỏ hàng không
$updated = false;

// Nếu có session cart_updated, sử dụng nó để kiểm tra
if (isset($_SESSION['cart_updated']) && $_SESSION['cart_updated'] > $last_updated) {
    $updated = true;
    $timestamp = $_SESSION['cart_updated'];
} else {
    // Nếu không có session cart_updated, sử dụng thời gian hiện tại
    $timestamp = $current_time;
}

// Tr<PERSON> về kết quả
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'updated' => $updated,
    'timestamp' => $timestamp,
    'count' => get_cart_count()
]);
?>
