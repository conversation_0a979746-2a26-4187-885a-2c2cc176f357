<?php
// Thiết lập tiêu đề trang
$page_title = 'Giỏ hàng';
$page_description = 'Giỏ hàng của bạn tại Nội Thất Băng Vũ';

// Include header
include_once 'partials/header.php';

// L<PERSON>y danh sách sản phẩm trong giỏ hàng
$cart_items = get_cart_items();
$cart_total = get_cart_total();
$cart_count = get_cart_count(); // Tổng số lượng sản phẩm (quantity)

// Kiểm tra thông báo cập nhật giỏ hàng từ session
$cart_update_message = '';
$cart_update_detail = '';
$cart_update_type = 'success';
$show_cart_notification = false;

if (isset($_SESSION['cart_update_result'])) {
    $cart_update_result = $_SESSION['cart_update_result'];
    $cart_update_message = $cart_update_result['message'] ?? 'Cập nhật thành công';
    $cart_update_detail = $cart_update_result['detail'] ?? '';
    $cart_update_type = $cart_update_result['success'] ? 'success' : 'error';
    $show_cart_notification = true;

    // Xóa thông báo sau khi đã lấy
    unset($_SESSION['cart_update_result']);
}
?>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </span>
                    <span>Giỏ hàng</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">
<!-- Link CSS cho notifications -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/notifications.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/realtime-updates.css">

<style>
    /* Hiệu ứng cho thông báo giỏ hàng */
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
        }
    }

    #cart-inline-notification {
        transform-origin: top center;
    }

    #cart-inline-notification.hiding {
        animation: slideUp 0.3s ease-out forwards;
    }

    /* Skeleton Loading Styles - Enhanced */
    .skeleton-container {
        position: relative;
        overflow: hidden;
        z-index: 10;
    }

    .skeleton-cart-item {
        height: 180px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        padding: 24px;
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .skeleton-cart-image {
        width: 80px;
        height: 80px;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 8px;
        flex-shrink: 0;
    }

    .skeleton-cart-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding-top: 4px;
    }

    .skeleton-cart-title {
        height: 20px;
        width: 75%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
    }

    .skeleton-cart-price {
        height: 18px;
        width: 45%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
        margin-top: 8px;
    }

    .skeleton-cart-controls {
        height: 40px;
        width: 220px;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 8px;
        margin-top: 16px;
    }

    .skeleton-order-summary {
        background: white;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        padding: 24px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 96px;
    }

    .skeleton-summary-title {
        height: 28px;
        width: 65%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
        margin-bottom: 24px;
    }

    .skeleton-summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .skeleton-summary-label {
        height: 16px;
        width: 35%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
    }

    .skeleton-summary-value {
        height: 16px;
        width: 28%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
    }

    .skeleton-checkout-btn {
        height: 56px;
        width: 100%;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 8px;
        margin-top: 24px;
    }

    @keyframes skeleton-shimmer {
        0% {
            background-position: -200% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    /* Mobile responsive skeleton */
    @media (max-width: 768px) {
        .skeleton-cart-item {
            height: 160px;
            padding: 16px;
        }

        .skeleton-cart-image {
            width: 60px;
            height: 60px;
        }

        .skeleton-cart-controls {
            width: 180px;
            height: 36px;
        }

        .skeleton-order-summary {
            padding: 20px;
            position: static;
        }
    }

    /* Simple Select All Control - Minimal Design */
    .premium-select-all-control {
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        padding: 6px 12px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        transition: all 0.2s ease;
        user-select: none;
    }

    .premium-select-all-control:hover {
        border-color: #d1d5db;
        background: #f9fafb;
    }

    /* Simple Checkbox Styling */
    .premium-select-all-checkbox {
        width: 16px;
        height: 16px;
        border: 1px solid #d1d5db;
        border-radius: 3px;
        background: #ffffff;
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
        appearance: none;
        -webkit-appearance: none;
        margin: 0;
        flex-shrink: 0;
    }

    .premium-select-all-checkbox:hover {
        border-color: #9ca3af;
    }

    .premium-select-all-checkbox:checked {
        background: #3b82f6;
        border-color: #3b82f6;
    }

    .premium-select-all-checkbox:checked::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 5px;
        width: 4px;
        height: 7px;
        border: 2px solid white;
        border-top: none;
        border-left: none;
        transform: rotate(45deg);
    }

    /* Simple Text Styling */
    .premium-select-all-text {
        margin-left: 8px;
        font-size: 14px;
        font-weight: 400;
        color: #6b7280;
        transition: color 0.2s ease;
    }

    .premium-select-all-control:hover .premium-select-all-text {
        color: #374151;
    }

    /* Focus for accessibility */
    .premium-select-all-checkbox:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

</style>

<!-- Cart -->
<div class="py-8 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-start gap-8">
            <!-- Main Content -->
            <div class="w-full lg:w-2/3">
                <!-- Modern Cart Header -->
                <div class="cart-header-container mb-6">
                    <!-- Desktop Layout -->
                    <div class="hidden md:flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <h1 class="text-3xl font-bold text-gray-800">Giỏ hàng của bạn</h1>
                            <?php if ($cart_count > 0): ?>
                            <div class="flex items-center">
                                <label class="premium-select-all-control">
                                    <input type="checkbox" id="select-all-checkbox"
                                           class="premium-select-all-checkbox"
                                           checked>
                                    <span class="premium-select-all-text">Chọn tất cả</span>
                                </label>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="text-sm text-gray-500 bg-white px-3 py-1 rounded-full shadow-sm border border-gray-200">
                            <span class="font-medium text-primary"><?php echo $cart_count; ?></span> sản phẩm
                        </div>
                    </div>

                    <!-- Mobile Layout - Professional & Detailed Design -->
                    <div class="md:hidden">
                        <!-- Professional Header Card -->
                        <div class="bg-white rounded-2xl border border-gray-200 shadow-lg mb-6 overflow-hidden">
                            <!-- Header Top Section -->
                            <div class="header-top-section px-5 py-5 border-b border-gray-200 relative overflow-hidden">
                                <!-- Decorative Elements -->
                                <div class="absolute top-2 left-4 w-2 h-2 bg-blue-400 rounded-full opacity-30"></div>
                                <div class="absolute top-6 right-8 w-1 h-1 bg-purple-400 rounded-full opacity-40"></div>
                                <div class="absolute bottom-3 left-12 w-1.5 h-1.5 bg-cyan-400 rounded-full opacity-25"></div>

                                <div class="relative z-10">
                                    <!-- Top Row: Title -->
                                    <div class="text-center mb-3">
                                        <h1 class="text-2xl font-bold text-gray-900 mb-1">Giỏ hàng</h1>
                                        <div class="w-16 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
                                    </div>


                                </div>
                            </div>

                            <!-- Header Bottom Section - Controls -->
                            <?php if ($cart_count > 0): ?>
                            <div class="px-5 py-4">
                                <div class="flex items-center justify-between">
                                    <!-- Left: Selection Info -->
                                    <div class="flex items-center gap-3">
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-tasks text-gray-500 text-sm"></i>
                                            <span class="text-sm text-gray-600">Quản lý:</span>
                                        </div>
                                    </div>

                                    <!-- Right: Select All Button -->
                                    <button type="button" class="mobile-select-all-btn bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded-xl text-sm font-semibold flex items-center gap-2 transition-all duration-300 border border-blue-200 shadow-sm">
                                        <i class="fas fa-check-square text-sm"></i>
                                        <span>Chọn tất cả</span>
                                    </button>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mt-3">
                                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                        <span>Tiến độ mua sắm</span>
                                        <span class="selected-progress-text">100%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="selected-progress-bar bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: 100%"></div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Mobile Select All Checkbox (Hidden but functional) -->
                        <?php if ($cart_count > 0): ?>
                        <input type="checkbox" id="select-all-checkbox-mobile" class="hidden" checked>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($cart_count > 0): ?>
                <!-- Cart Items -->
                <div class="space-y-4" id="cart-items-section" style="display: none;">
                    <!-- Thông báo trong giỏ hàng -->
                    <div id="cart-inline-notification" class="<?php echo $show_cart_notification ? '' : 'hidden'; ?> bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 transition-all duration-300 mb-4">
                        <div class="p-4 flex items-center gap-3">
                            <div id="cart-notification-icon" class="w-10 h-10 rounded-full flex items-center justify-center text-white <?php
                                if ($cart_update_type === 'success') echo 'bg-primary';
                                elseif ($cart_update_type === 'error') echo 'bg-red-500';
                                elseif ($cart_update_type === 'warning') echo 'bg-yellow-500';
                                else echo 'bg-blue-500';
                            ?>">
                                <i class="fas <?php
                                    if ($cart_update_type === 'success') echo 'fa-check';
                                    elseif ($cart_update_type === 'error') echo 'fa-exclamation-circle';
                                    elseif ($cart_update_type === 'warning') echo 'fa-exclamation-triangle';
                                    else echo 'fa-info-circle';
                                ?>"></i>
                            </div>
                            <div class="flex-1">
                                <div id="cart-notification-message" class="font-medium text-gray-800"><?php echo htmlspecialchars($cart_update_message); ?></div>
                                <div id="cart-notification-detail" class="text-sm text-gray-500"><?php echo htmlspecialchars($cart_update_detail); ?></div>
                            </div>
                            <button type="button" onclick="hideCartNotification()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <?php foreach ($cart_items as $item): ?>
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300 border border-gray-100">
                        <div class="cart-item-padding">
                            <!-- Mobile Layout (unchanged) -->
                            <div class="mobile-cart-layout md:hidden">
                                <!-- Top Row: Checkbox + Image + Product Name -->
                                <div class="flex items-start gap-4 mb-3">
                                    <!-- Checkbox -->
                                    <div class="flex items-center justify-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox"
                                                   class="cart-item-checkbox form-checkbox h-5 w-5 text-primary rounded border-gray-300 focus:ring-primary focus:ring-offset-0"
                                                   id="checkbox-mobile-<?php echo $item['product_id']; ?>"
                                                   data-product-id="<?php echo $item['product_id']; ?>"
                                                   data-price="<?php echo $item['price']; ?>"
                                                   data-quantity="<?php echo $item['quantity']; ?>"
                                                   checked>
                                            <span class="sr-only">Chọn sản phẩm</span>
                                        </label>
                                    </div>

                                    <!-- Product Image -->
                                    <div class="w-20 h-20 flex-shrink-0">
                                        <div class="w-full h-full rounded-lg overflow-hidden bg-gray-100 relative">
                                            <?php if ($item['image']): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['image']; ?>"
                                                 alt="<?php echo $item['name']; ?>"
                                                 class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                                            <?php else: ?>
                                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400 text-xl"></i>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Product Name + Price -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-800 hover:text-primary transition-colors duration-200 line-clamp-2 mb-1">
                                            <a href="<?php echo !empty($item['slug']) ? get_product_url($item['slug']) : BASE_URL . '/product.php?id=' . $item['product_id']; ?>">
                                                <?php echo $item['name']; ?>
                                            </a>
                                        </h3>
                                        <div class="text-primary font-medium text-lg">
                                            <?php echo format_currency($item['price']); ?>
                                        </div>
                                    </div>

                                    <!-- Delete Button (Mobile Only) -->
                                    <div class="mobile-delete-btn">
                                        <button type="button" class="remove-cart-btn text-red-500 hover:text-red-700 transition-colors duration-200 text-sm flex items-center gap-1"
                                                data-product-id="<?php echo $item['product_id']; ?>">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Second Row: Quantity Controls + Update Button -->
                                <div class="flex flex-col md:flex-row justify-end items-start md:items-center gap-4 mt-3">
                                    <div class="flex items-center gap-4">
                                        <!-- Quantity Controls -->
                                        <div class="flex items-center border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                                            <button type="button" class="quantity-control quantity-decrease bg-white text-gray-700 w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors duration-200">
                                                <i class="fas fa-minus text-xs"></i>
                                            </button>
                                            <input type="number" class="quantity-input w-14 h-10 border-x border-gray-300 text-center bg-white"
                                                   value="<?php echo $item['quantity']; ?>" min="1"
                                                   data-product-id="<?php echo $item['product_id']; ?>"
                                                   data-max-quantity="<?php
                                                       $product = get_product_by_id($item['product_id']);
                                                       echo $product ? $product['quantity'] : 0;
                                                   ?>">
                                            <button type="button" class="quantity-control quantity-increase bg-white text-gray-700 w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors duration-200">
                                                <i class="fas fa-plus text-xs"></i>
                                            </button>
                                        </div>

                                        <!-- Update Button -->
                                        <button type="button" class="text-primary hover:text-primary-dark transition-colors duration-200 text-sm flex items-center gap-1"
                                                onclick="updateCartItemManual(<?php echo $item['product_id']; ?>, this); return false;"
                                                data-product-id="<?php echo $item['product_id']; ?>">
                                            <i class="fas fa-sync-alt"></i> Cập nhật
                                        </button>

                                        <!-- Delete Button (Desktop Only) -->
                                        <div class="desktop-delete-btn">
                                            <button type="button" class="remove-cart-btn text-red-500 hover:text-red-700 transition-colors duration-200 text-sm flex items-center gap-1"
                                                    data-product-id="<?php echo $item['product_id']; ?>">
                                                <i class="fas fa-trash-alt"></i> Xóa
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bottom Row: Discount Info + Savings + Total Price -->
                                <div class="mt-4 pt-3 border-t border-gray-100">
                                    <?php
                                    // Giả sử có giá gốc và giá khuyến mãi
                                    $original_price = $item['price'] * 1.2; // Giả sử giá gốc cao hơn 20%
                                    $discount_percent = round((($original_price - $item['price']) / $original_price) * 100);
                                    $savings_amount = ($original_price - $item['price']) * $item['quantity'];

                                    if ($discount_percent > 0): ?>
                                        <!-- Discount and Savings Row -->
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center">
                                                <i class="fas fa-tag mr-1"></i>Giảm <?php echo $discount_percent; ?>%
                                            </span>
                                            <span class="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center">
                                                <i class="fas fa-piggy-bank mr-1"></i>Tiết kiệm <?php echo format_currency($savings_amount); ?>
                                            </span>
                                        </div>
                                        <!-- Total Price Row -->
                                        <div class="flex justify-end">
                                            <div class="text-lg font-semibold text-primary-dark">
                                                <?php echo format_currency($item['price'] * $item['quantity']); ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- No Discount Layout -->
                                        <div class="flex justify-between items-center">
                                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center">
                                                <i class="fas fa-check-circle mr-1"></i>Giá tốt nhất
                                            </span>
                                            <div class="text-lg font-semibold text-primary-dark">
                                                <?php echo format_currency($item['price'] * $item['quantity']); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Desktop Layout (new cart-test style) -->
                            <div class="desktop-cart-layout hidden md:block">
                                <div class="cart-item-layout-professional">
                                    <!-- Checkbox Column -->
                                    <div class="checkbox-column-professional">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox"
                                                   class="cart-item-checkbox professional-checkbox-style"
                                                   id="checkbox-desktop-<?php echo $item['product_id']; ?>"
                                                   data-product-id="<?php echo $item['product_id']; ?>"
                                                   data-price="<?php echo $item['price']; ?>"
                                                   data-quantity="<?php echo $item['quantity']; ?>"
                                                   checked>
                                            <span class="sr-only">Chọn sản phẩm</span>
                                        </label>
                                    </div>

                                    <!-- Image Column -->
                                    <div class="image-column-professional">
                                        <div class="product-image-container-professional">
                                            <?php if ($item['image']): ?>
                                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $item['image']; ?>"
                                                 alt="<?php echo $item['name']; ?>"
                                                 class="product-image-professional">
                                            <?php else: ?>
                                            <div class="product-image-placeholder-professional">
                                                <i class="fas fa-image text-gray-400 text-2xl"></i>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Content Column -->
                                    <div class="content-column-professional">
                                        <!-- Product Info Row -->
                                        <div class="product-info-row-professional">
                                            <div class="product-main-info-professional">
                                                <div class="product-details-professional">
                                                    <h3 class="product-title-professional">
                                                        <a href="<?php echo !empty($item['slug']) ? get_product_url($item['slug']) : BASE_URL . '/product.php?id=' . $item['product_id']; ?>">
                                                            <?php echo $item['name']; ?>
                                                        </a>
                                                    </h3>
                                                    <div class="product-price-professional">
                                                        <?php echo format_currency($item['price']); ?>
                                                        <?php if ($discount_percent > 0): ?>
                                                            <span class="original-price-professional"><?php echo format_currency($original_price); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="discount-total-section-professional">
                                                <div class="discount-section-professional">
                                                    <?php if ($discount_percent > 0): ?>
                                                        <div class="discount-badge-professional">
                                                            <i class="fas fa-tag"></i>
                                                            Giảm <?php echo $discount_percent; ?>%
                                                        </div>
                                                        <div class="savings-badge-professional">
                                                            <i class="fas fa-piggy-bank"></i>
                                                            Tiết kiệm <?php echo format_currency($savings_amount); ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="best-price-badge-professional">
                                                            <i class="fas fa-star"></i>
                                                            Giá tốt nhất
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="total-section-professional">
                                                    <div class="total-label-professional">Tổng tiền sản phẩm</div>
                                                    <div class="total-amount-professional"><?php echo format_currency($item['price'] * $item['quantity']); ?></div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Controls Row -->
                                        <div class="controls-row-professional">
                                            <div class="quantity-section-professional">
                                                <span class="quantity-label-professional">Số lượng:</span>
                                                <div class="quantity-controls-professional">
                                                    <button type="button" class="qty-btn-professional quantity-control quantity-decrease">−</button>
                                                    <input type="number" class="qty-input-professional quantity-input"
                                                           value="<?php echo $item['quantity']; ?>" min="1"
                                                           data-product-id="<?php echo $item['product_id']; ?>"
                                                           data-max-quantity="<?php
                                                               $product = get_product_by_id($item['product_id']);
                                                               echo $product ? $product['quantity'] : 0;
                                                           ?>">
                                                    <button type="button" class="qty-btn-professional quantity-control quantity-increase">+</button>
                                                </div>
                                            </div>

                                            <div class="spacer-professional"></div>

                                            <div class="action-buttons-professional">
                                                <button type="button" class="action-btn-professional update-btn-professional"
                                                        onclick="updateCartItemManual(<?php echo $item['product_id']; ?>, this); return false;"
                                                        data-product-id="<?php echo $item['product_id']; ?>">
                                                    <i class="fas fa-sync-alt"></i>
                                                    Cập nhật
                                                </button>
                                                <button type="button" class="action-btn-professional delete-btn-professional remove-cart-btn"
                                                        data-product-id="<?php echo $item['product_id']; ?>">
                                                    <i class="fas fa-trash-alt"></i>
                                                    Xóa
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Continue Shopping -->
                <div class="mt-6">
                    <a href="<?php echo BASE_URL; ?>/products.php" class="inline-flex items-center text-primary hover:text-primary-dark transition-colors duration-200 group">
                        <i class="fas fa-arrow-left mr-2 transform group-hover:-translate-x-1 transition-transform duration-200"></i>
                        <span class="font-medium">Tiếp tục mua sắm</span>
                    </a>
                </div>

                <!-- Clear Cart (Mobile Only) -->
                <div class="mt-6 block md:hidden">
                    <button type="button" id="clear-cart-btn-mobile" class="w-full bg-white border border-red-500 text-red-500 hover:bg-red-50 px-4 py-3 rounded-lg transition duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-trash-alt"></i> Xóa giỏ hàng
                    </button>
                </div>
                <?php else: ?>
                <!-- Empty Cart -->
                <div class="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-100">
                    <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-primary-ultra-light text-primary mb-6">
                        <i class="fas fa-shopping-cart text-3xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Giỏ hàng của bạn đang trống</h2>
                    <p class="text-gray-600 mb-6 max-w-md mx-auto">Hãy thêm sản phẩm vào giỏ hàng để tiến hành mua sắm và khám phá các ưu đãi hấp dẫn.</p>
                    <a href="<?php echo BASE_URL; ?>/products.php" class="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-lg inline-flex items-center justify-center gap-2 transition duration-200 shadow-sm hover:shadow">
                        <i class="fas fa-shopping-bag"></i> Mua sắm ngay
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Skeleton Loading Script - Inline for immediate execution -->
            <script>
            (function() {
                // Tạo skeleton cho Cart Items ngay lập tức
                const cartSection = document.getElementById('cart-items-section');
                if (cartSection) {
                    // Tạo skeleton container
                    const skeletonDiv = document.createElement('div');
                    skeletonDiv.id = 'cart-skeleton-temp';
                    skeletonDiv.className = 'space-y-4';
                    skeletonDiv.innerHTML = `
                        <div class="skeleton-cart-item">
                            <div class="skeleton-cart-image"></div>
                            <div class="skeleton-cart-content">
                                <div class="skeleton-cart-title"></div>
                                <div class="skeleton-cart-price"></div>
                                <div class="skeleton-cart-controls"></div>
                            </div>
                        </div>
                        <div class="skeleton-cart-item">
                            <div class="skeleton-cart-image"></div>
                            <div class="skeleton-cart-content">
                                <div class="skeleton-cart-title"></div>
                                <div class="skeleton-cart-price"></div>
                                <div class="skeleton-cart-controls"></div>
                            </div>
                        </div>
                        <div class="skeleton-cart-item">
                            <div class="skeleton-cart-image"></div>
                            <div class="skeleton-cart-content">
                                <div class="skeleton-cart-title"></div>
                                <div class="skeleton-cart-price"></div>
                                <div class="skeleton-cart-controls"></div>
                            </div>
                        </div>
                    `;
                    cartSection.parentNode.insertBefore(skeletonDiv, cartSection);
                }
            })();
            </script>

            <!-- Order Summary -->
            <?php if ($cart_count > 0): ?>
            <div class="w-full lg:w-1/3 mt-8 lg:mt-0" id="order-summary-section">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden sticky top-24" style="display: none;">
                    <div class="p-6 border-b border-gray-100">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Tổng đơn hàng</h2>

                        <div class="space-y-3">
                            <div class="flex justify-between items-center text-gray-700">
                                <span>Tạm tính</span>
                                <span class="font-medium cart-subtotal"><?php echo format_currency($cart_total); ?></span>
                            </div>

                            <div class="flex justify-between items-center text-gray-700">
                                <span>Phí vận chuyển</span>
                                <span class="text-green-600 font-medium">Miễn phí</span>
                            </div>

                            <div class="flex justify-between items-center text-gray-700 selected-items-info">
                                <span>Đã chọn <span class="selected-items-count font-medium"><?php echo $cart_count; ?></span> sản phẩm</span>
                                <span class="text-primary font-medium">(<span class="selected-items-percentage">100</span>%)</span>
                            </div>

                            <div class="pt-3 mt-3 border-t border-gray-100">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-semibold text-gray-800">Tổng cộng</span>
                                    <span class="text-xl font-bold text-primary cart-total"><?php echo format_currency($cart_total); ?></span>
                                </div>
                                <div class="text-xs text-gray-500 mt-1 text-right">
                                    (Đã bao gồm VAT)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <a href="<?php echo BASE_URL; ?>/checkout.php" id="checkout-btn" class="premium-checkout-btn w-full flex items-center justify-center gap-2 mb-4">
                            <i class="fas fa-credit-card"></i> Tiến hành thanh toán
                        </a>



                        <button type="button" id="clear-cart-btn-desktop" class="w-full bg-white border border-red-500 text-red-500 hover:bg-red-50 px-4 py-3 rounded-lg transition duration-200 hidden md:inline-flex items-center justify-center gap-2">
                            <i class="fas fa-trash-alt"></i> Xóa giỏ hàng
                        </button>

                        <div class="mt-6 flex items-center justify-center gap-4 text-sm text-gray-500">
                            <div class="flex items-center gap-1">
                                <i class="fas fa-shield-alt text-primary"></i>
                                <span>Thanh toán an toàn</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <i class="fas fa-truck text-primary"></i>
                                <span>Giao hàng nhanh chóng</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skeleton Loading Script for Order Summary -->
            <script>
            (function() {
                // Tạo skeleton cho Order Summary ngay lập tức
                const orderSection = document.getElementById('order-summary-section');
                if (orderSection) {
                    // Tạo skeleton container
                    const skeletonDiv = document.createElement('div');
                    skeletonDiv.id = 'order-skeleton-temp';
                    skeletonDiv.className = 'skeleton-order-summary';
                    skeletonDiv.innerHTML = `
                        <div class="skeleton-summary-title"></div>
                        <div class="space-y-4">
                            <div class="skeleton-summary-row">
                                <div class="skeleton-summary-label"></div>
                                <div class="skeleton-summary-value"></div>
                            </div>
                            <div class="skeleton-summary-row">
                                <div class="skeleton-summary-label"></div>
                                <div class="skeleton-summary-value"></div>
                            </div>
                            <div class="skeleton-summary-row">
                                <div class="skeleton-summary-label"></div>
                                <div class="skeleton-summary-value"></div>
                            </div>
                            <div style="border-top: 1px solid #e5e7eb; padding-top: 16px; margin-top: 16px;">
                                <div class="skeleton-summary-row">
                                    <div class="skeleton-summary-label" style="width: 40%;"></div>
                                    <div class="skeleton-summary-value" style="width: 35%; height: 20px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="skeleton-checkout-btn"></div>
                    `;
                    orderSection.appendChild(skeletonDiv);
                }
            })();
            </script>

            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Thêm CSS cho trang giỏ hàng
?>
<style>
    /* Thiết lập chung */
    :root {
        --cart-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        --cart-hover-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        --cart-border-radius: 1rem;
        --cart-transition: all 0.3s ease;
    }

    /* Hiệu ứng hover cho card sản phẩm */
    .cart-item {
        transition: var(--cart-transition);
    }

    /* Ẩn mũi tên tăng giảm số lượng */
    .quantity-input {
        -moz-appearance: textfield;
        appearance: textfield;
    }
    .quantity-input::-webkit-outer-spin-button,
    .quantity-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Hiệu ứng cho nút tăng giảm */
    .quantity-control {
        transition: all 0.2s ease;
    }
    .quantity-control:hover {
        background-color: #f3f4f6;
    }
    .quantity-control:active, .quantity-control.active-control {
        transform: scale(0.85);
        background-color: #e5e7eb;
    }

    /* Hiệu ứng cho input số lượng */
    .quantity-input {
        transition: all 0.3s ease;
    }
    .quantity-input:focus {
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        border-color: #6366F1;
    }

    /* Hiệu ứng cho nút cập nhật */
    .update-cart-btn {
        transition: all 0.2s ease;
    }
    .update-cart-btn:hover {
        transform: translateX(2px);
    }

    /* Hiệu ứng cho nút xóa */
    .remove-cart-btn:hover {
        transform: translateX(2px);
    }

    /* Hiệu ứng pulse nhẹ nhàng cho badge */
    .badge-pulse {
        animation: pulse 0.8s ease-in-out;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Hiệu ứng cho thông báo trong giỏ hàng */
    #cart-inline-notification {
        transform-origin: top;
        animation: slideDown 0.3s ease-out forwards;
    }
    #cart-inline-notification.hiding {
        animation: slideUp 0.3s ease-in forwards;
    }
    @keyframes slideDown {
        from { transform: scaleY(0); opacity: 0; }
        to { transform: scaleY(1); opacity: 1; }
    }
    @keyframes slideUp {
        from { transform: scaleY(1); opacity: 1; }
        to { transform: scaleY(0); opacity: 0; }
    }

    /* Hiệu ứng highlight nhẹ nhàng cho giá trị thay đổi */
    .highlight-change {
        animation: highlightChange 1.2s ease-out;
    }
    @keyframes highlightChange {
        0% { background-color: rgba(79, 70, 229, 0.15); }
        50% { background-color: rgba(79, 70, 229, 0.08); }
        100% { background-color: transparent; }
    }

    /* Hiệu ứng highlight nhẹ nhàng cho card sản phẩm */
    .bg-white.rounded-xl.highlight-change {
        animation: cardHighlight 1.2s ease-out;
        position: relative;
        z-index: 1;
    }
    @keyframes cardHighlight {
        0% { box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2); }
        50% { box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.15); }
        100% { box-shadow: 0 0 0 0px rgba(79, 70, 229, 0); }
    }

    /* Hiệu ứng cho card sản phẩm khi cập nhật thành công */
    .bg-white.rounded-xl.card-updated {
        animation: cardUpdated 1s ease-out;
    }
    @keyframes cardUpdated {
        0% { transform: translateY(0); }
        25% { transform: translateY(-5px); }
        50% { transform: translateY(0); }
        75% { transform: translateY(-3px); }
        100% { transform: translateY(0); }
    }

    /* Hiệu ứng cho phần tổng đơn hàng khi cập nhật */
    .order-summary-updated {
        animation: orderSummaryUpdated 2s ease-out;
    }
    @keyframes orderSummaryUpdated {
        0% { box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3); }
        50% { box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2); }
        100% { box-shadow: 0 0 0 0px rgba(79, 70, 229, 0); }
    }

    /* Hiệu ứng pulse cho các phần tử giá trị */
    .pulse-animation {
        animation: pulseAnimation 0.8s ease-out;
    }
    @keyframes pulseAnimation {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Hiệu ứng scale cho các phần tử giá trị */
    .scale-animation {
        animation: scaleAnimation 0.8s ease-out;
    }
    @keyframes scaleAnimation {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Skeleton Loading Animation cho Order Summary - Updated Colors */
    .skeleton-loading {
        position: relative;
        overflow: hidden;
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
        border-radius: 4px;
        color: transparent !important;
    }

    .skeleton-loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
    }



    /* Skeleton loading cho các phần tử cụ thể */
    .cart-subtotal.skeleton-loading,
    .text-green-600.font-medium.skeleton-loading,
    .text-primary.font-medium.skeleton-loading,
    .cart-total.skeleton-loading {
        min-height: 1.25rem; /* Đảm bảo có chiều cao tối thiểu */
        display: inline-block;
        min-width: 80px; /* Đảm bảo có chiều rộng tối thiểu */
    }

    /* Skeleton loading cho tổng cộng lớn hơn */
    .text-xl.font-bold.text-primary.cart-total.skeleton-loading {
        min-height: 1.75rem;
        min-width: 120px;
    }

    /* Skeleton loading cho nút checkout - đồng nhất màu sắc */
    .w-full.bg-primary.skeleton-loading {
        background: linear-gradient(90deg, #e2e8f0 25%, #cbd5e1 50%, #e2e8f0 75%) !important;
        background-size: 200% 100% !important;
        animation: skeleton-shimmer 1.5s infinite !important;
        color: transparent !important;
        min-height: 3.5rem; /* Giữ chiều cao của nút */
        position: relative;
        overflow: hidden;
    }

    .w-full.bg-primary.skeleton-loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 1.5s infinite;
    }

    /* Ẩn nội dung bên trong nút khi skeleton loading */
    .w-full.bg-primary.skeleton-loading * {
        opacity: 0 !important;
    }

    /* Spinner loading màu cam ở giữa nút checkout */
    .w-full.bg-primary.skeleton-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        border: 3px solid rgba(255, 165, 0, 0.3);
        border-top: 3px solid #ff6b35;
        border-radius: 50%;
        animation: checkout-spinner 1s linear infinite;
        z-index: 10;
    }

    @keyframes checkout-spinner {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Loại trừ skeleton loading cho các phần tử không mong muốn */
    .text-sm.text-gray-500.bg-white.skeleton-loading {
        background: none !important;
        color: inherit !important;
        animation: none !important;
    }

    .text-sm.text-gray-500.bg-white.skeleton-loading::before {
        display: none !important;
    }

    /* Hiệu ứng bounce cho phần tiết kiệm */
    .savings-bounce {
        animation: savingsBounce 0.6s ease-out;
    }
    @keyframes savingsBounce {
        0% { transform: scale(1); }
        25% { transform: scale(1.1); }
        50% { transform: scale(0.95); }
        75% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Hiệu ứng cho nút cập nhật khi thành công */
    .update-success {
        animation: updateSuccess 0.5s ease-out;
    }
    @keyframes updateSuccess {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    /* Hiệu ứng cho nút tăng giảm khi active */
    .quantity-control:active {
        transform: scale(0.85);
        background-color: #e5e7eb;
    }

    /* Hiệu ứng cho nút thanh toán */
    .checkout-btn {
        transition: all 0.3s ease;
    }
    .checkout-btn:hover {
        transform: translateY(-2px);
    }

    /* Đảm bảo nút thanh toán luôn có thể click trên desktop */
    @media (min-width: 768px) {
        #checkout-btn {
            pointer-events: auto !important;
            opacity: 1 !important;
        }

        #checkout-btn.pointer-events-none {
            pointer-events: auto !important;
            opacity: 0.8 !important;
        }

        #checkout-btn.opacity-50 {
            opacity: 0.8 !important;
        }

        /* Force cart item padding to match order summary - High specificity */
        .space-y-4 > div.bg-white.rounded-xl.shadow-sm > div.cart-item-padding {
            padding: 1.5rem !important;
        }

        /* Ultra high specificity override for cart item padding */
        body div.container div.space-y-4 > div.bg-white.rounded-xl.shadow-sm > div.cart-item-padding,
        body .space-y-4 > .bg-white.rounded-xl.shadow-sm > .cart-item-padding,
        body [class*="cart-item-padding"] {
            padding: 1.5rem !important;
            padding-top: 1.5rem !important;
            padding-right: 1.5rem !important;
            padding-bottom: 1.5rem !important;
            padding-left: 1.5rem !important;
        }
    }

    /* Hiệu ứng cho hình ảnh sản phẩm */
    .product-image-hover {
        transition: transform 0.5s ease;
    }
    .product-image-hover:hover {
        transform: scale(1.05);
    }

    /* Responsive cho mobile - Thiết kế lại hoàn toàn */
    @media (max-width: 576px) {
        /* Container chính */
        .py-8.bg-gray-50 {
            padding: 1rem 0 !important;
            background: #f8f9fa !important;
        }

        /* Professional & Detailed Cart Header Mobile */
        .cart-header-container {
            margin-bottom: 1.5rem !important;
        }

        /* Professional header card */
        .cart-header-container .bg-white.rounded-2xl {
            background: #ffffff !important;
            border: 1px solid #e5e7eb !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s ease !important;
            overflow: hidden !important;
        }

        .cart-header-container .bg-white.rounded-2xl:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
            transform: translateY(-2px) !important;
        }

        /* Header top section - Modern design */
        .cart-header-container .header-top-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%) !important;
            border-bottom: 1px solid #e2e8f0 !important;
            position: relative !important;
        }

        .cart-header-container .header-top-section::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 2px !important;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%) !important;
        }

        .cart-header-container .header-top-section::after {
            content: '' !important;
            position: absolute !important;
            top: -50% !important;
            right: -20% !important;
            width: 200px !important;
            height: 200px !important;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%) !important;
            border-radius: 50% !important;
            pointer-events: none !important;
        }

        /* Decorative dots animation */
        .cart-header-container .header-top-section .absolute.bg-blue-400 {
            animation: float1 3s ease-in-out infinite !important;
        }

        .cart-header-container .header-top-section .absolute.bg-purple-400 {
            animation: float2 4s ease-in-out infinite !important;
        }

        .cart-header-container .header-top-section .absolute.bg-cyan-400 {
            animation: float3 5s ease-in-out infinite !important;
        }

        @keyframes float1 {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
            50% { transform: translateY(-10px) scale(1.2); opacity: 0.6; }
        }

        @keyframes float2 {
            0%, 100% { transform: translateX(0px) scale(1); opacity: 0.4; }
            50% { transform: translateX(8px) scale(1.1); opacity: 0.7; }
        }

        @keyframes float3 {
            0%, 100% { transform: translate(0px, 0px) scale(1); opacity: 0.25; }
            50% { transform: translate(-5px, -8px) scale(1.3); opacity: 0.5; }
        }

        /* Enhanced header styling */
        .cart-header-container .header-top-section {
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
        }

        .cart-header-container .header-top-section:hover::before {
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 30%, #06b6d4 60%, #10b981 100%) !important;
            animation: shimmer 2s ease-in-out infinite !important;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* New Layout Styling */
        .cart-header-container h1 {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            color: #111827 !important;
            letter-spacing: -0.025em !important;
        }

        /* Gradient underline */
        .cart-header-container .w-16 {
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%) !important;
            animation: shimmerLine 3s ease-in-out infinite !important;
        }

        @keyframes shimmerLine {
            0%, 100% { opacity: 0.8; transform: scaleX(1); }
            50% { opacity: 1; transform: scaleX(1.1); }
        }

        /* Info cards styling */
        .cart-header-container .bg-white.bg-opacity-60 {
            background: rgba(255, 255, 255, 0.7) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.6) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
        }

        .cart-header-container .bg-white.bg-opacity-60:hover {
            background: rgba(255, 255, 255, 0.85) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        /* Icon containers in cards */
        .cart-header-container .w-10.h-10.bg-blue-100 {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
            transition: all 0.3s ease !important;
        }

        .cart-header-container .w-10.h-10.bg-green-100 {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%) !important;
            transition: all 0.3s ease !important;
        }

        .cart-header-container .w-10.h-10 i {
            transition: transform 0.3s ease !important;
        }

        .cart-header-container .bg-white.bg-opacity-60:hover .w-10.h-10 i {
            transform: scale(1.1) !important;
        }



        /* Typography in cards */
        .cart-header-container .text-sm.font-semibold {
            font-weight: 600 !important;
            color: #1f2937 !important;
        }

        .cart-header-container .text-xs.text-gray-600 {
            color: #6b7280 !important;
            font-weight: 500 !important;
        }

        .cart-header-container .text-sm.font-semibold.text-green-700 {
            color: #047857 !important;
        }



        /* Professional select all button */
        .mobile-select-all-btn {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
            border: 1px solid #bfdbfe !important;
            color: #1d4ed8 !important;
            font-weight: 600 !important;
            letter-spacing: 0.025em !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;
        }

        .mobile-select-all-btn:hover {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
            border-color: #93c5fd !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
        }

        .mobile-select-all-btn:active {
            transform: translateY(0) !important;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;
        }

        .mobile-select-all-btn i {
            color: #2563eb !important;
            transition: all 0.2s ease !important;
        }

        /* Progress bar styling */
        .selected-progress-bar {
            background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%) !important;
            box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3) !important;
            transition: width 0.5s ease !important;
        }

        .selected-progress-text {
            font-weight: 600 !important;
            color: #374151 !important;
        }

        /* Dot separator */
        .cart-header-container .w-1.h-1.bg-gray-400 {
            background-color: #9ca3af !important;
            opacity: 0.6 !important;
        }

        /* Professional animation */
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .cart-header-container .bg-white.rounded-2xl {
            animation: slideInDown 0.6s ease-out !important;
        }

        /* Pulse animation for badge */
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        /* Modern checkbox for desktop header */
        .modern-checkbox {
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            width: 20px !important;
            height: 20px !important;
            border: 2px solid #e5e7eb !important;
            border-radius: 6px !important;
            background-color: #ffffff !important;
            cursor: pointer !important;
            position: relative !important;
            transition: all 0.3s ease !important;
        }

        .modern-checkbox:hover {
            border-color: #cbd5e1 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            transform: scale(1.05) !important;
        }

        .modern-checkbox:checked {
            background-color: #3b82f6 !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        }

        .modern-checkbox:checked::after {
            content: '' !important;
            position: absolute !important;
            top: 2px !important;
            left: 6px !important;
            width: 6px !important;
            height: 10px !important;
            border: solid white !important;
            border-width: 0 2px 2px 0 !important;
            transform: rotate(45deg) !important;
            opacity: 1 !important;
        }

        .modern-checkbox:focus {
            outline: none !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        }

        /* Card sản phẩm mobile - Thiết kế app thương mại điện tử */
        .bg-white.rounded-xl.shadow-sm {
            border-radius: 12px !important;
            margin-bottom: 0.75rem !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
            border: 1px solid #f0f0f0 !important;
        }

        .bg-white.rounded-xl.shadow-sm .p-4.md\\:p-6 {
            padding: 1rem !important;
        }

        /* Layout chính của cart item */
        .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\\:flex-row {
            flex-direction: row !important;
            align-items: flex-start !important;
            gap: 0.75rem !important;
        }

        /* Checkbox */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.justify-center.mr-2 {
            margin-right: 0 !important;
            align-self: flex-start !important;
            margin-top: 0.25rem !important;
        }

        /* Hình ảnh sản phẩm */
        .bg-white.rounded-xl.shadow-sm .w-full.md\:w-1\/4 {
            width: 80px !important;
            flex-shrink: 0 !important;
        }

        .bg-white.rounded-xl.shadow-sm .aspect-square {
            width: 80px !important;
            height: 80px !important;
            border-radius: 8px !important;
        }

        /* Thông tin sản phẩm */
        .bg-white.rounded-xl.shadow-sm .flex-1 {
            min-width: 0 !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-between {
            flex-direction: column !important;
            gap: 0.5rem !important;
        }

        /* Tên sản phẩm */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 0.95rem !important;
            line-height: 1.3 !important;
            margin-bottom: 0.25rem !important;
            font-weight: 600 !important;
        }

        /* Giá sản phẩm */
        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium {
            font-size: 1rem !important;
            font-weight: 700 !important;
            color: #e11d48 !important;
            margin-bottom: 0.5rem !important;
        }

        /* Controls container */
        .bg-white.rounded-xl.shadow-sm .flex.md\:flex-col.items-center {
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-top: 0.5rem !important;
        }

        /* Quantity controls */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.border {
            border-radius: 6px !important;
            height: 32px !important;
            border: 1px solid #e5e7eb !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-control {
            width: 32px !important;
            height: 32px !important;
            font-size: 0.75rem !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-input {
            width: 40px !important;
            height: 32px !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
        }

        /* Action buttons */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
            gap: 1rem !important;
            margin-top: 0 !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
            font-size: 0.8rem !important;
            padding: 0.25rem 0.5rem !important;
            border-radius: 4px !important;
        }

        /* Bottom section */
        .bg-white.rounded-xl.shadow-sm .mt-3.pt-3.border-t {
            margin-top: 0.75rem !important;
            padding-top: 0.75rem !important;
            border-top: 1px solid #f3f4f6 !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.justify-between.items-center {
            flex-direction: row !important;
            align-items: center !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-sm.text-gray-500 {
            font-size: 0.8rem !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-lg.font-semibold.text-primary-dark {
            font-size: 1rem !important;
            font-weight: 700 !important;
            color: #dc2626 !important;
        }



        /* Continue shopping link */
        .mt-6 a {
            font-size: 0.9rem !important;
            padding: 0.5rem 0 !important;
        }

        /* Empty cart state */
        .bg-white.rounded-xl.shadow-sm.p-8 {
            padding: 2rem 1rem !important;
            
        }

        .bg-white.rounded-xl.shadow-sm.p-8 h2 {
            font-size: 1.25rem !important;
        }

        .bg-white.rounded-xl.shadow-sm.p-8 p {
            font-size: 0.9rem !important;
        }

        /* Mobile-specific animations and transitions */
        .bg-white.rounded-xl.shadow-sm {
            transition: all 0.2s ease !important;
        }

        .bg-white.rounded-xl.shadow-sm:active {
            transform: scale(0.98) !important;
        }

        /* Improved mobile quantity controls */
        .quantity-control:active {
            background-color: #f3f4f6 !important;
            transform: scale(0.9) !important;
        }

        /* Mobile-optimized button styles */
        .bg-white.rounded-xl.shadow-sm button {
            transition: all 0.15s ease !important;
            touch-action: manipulation !important;
        }

        .bg-white.rounded-xl.shadow-sm button:active {
            transform: scale(0.95) !important;
        }

        /* Improved mobile typography */
        .bg-white.rounded-xl.shadow-sm * {
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Mobile-specific spacing adjustments */
        .container.mx-auto.px-4 {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        /* Notification adjustments for mobile - Khớp chiều rộng với các phần khác */
        #cart-inline-notification {
            margin: 0 0 1rem 0 !important;
            border-radius: 12px !important;
        }

        /* Mobile breadcrumb adjustments */
        .modern-breadcrumb {
            padding: 0.5rem 0 !important;
        }

        .modern-breadcrumb .container {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        .breadcrumb-wrapper {
            font-size: 0.8rem !important;
        }

        /* Mobile loading states */
        .bg-white.rounded-xl.shadow-sm.loading {
            opacity: 0.7 !important;
            pointer-events: none !important;
        }

        /* Mobile-optimized modal */
        #delete-confirmation-modal .bg-white {
            margin: 1rem !important;
            border-radius: 12px !important;
        }

        /* ===== PROFESSIONAL HEADER CARD RESPONSIVE ===== */

        /* Small Mobile (≤319px) - Ultra-compact header */
        @media (max-width: 319px) {
            .cart-header-container {
                margin-bottom: 1rem !important;
            }

            .cart-header-container .bg-white.rounded-2xl {
                border-radius: 12px !important;
                margin-bottom: 1rem !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
            }

            /* Ultra-compact header top section */
            .cart-header-container .header-top-section {
                padding: 0.75rem !important;
            }

            /* Ultra-compact title */
            .cart-header-container h1 {
                font-size: 1.125rem !important;
                margin-bottom: 0.25rem !important;
            }

            /* Ultra-compact gradient line */
            .cart-header-container .w-16 {
                width: 2.5rem !important;
                height: 2px !important;
            }

            /* Ultra-compact decorative elements */
            .cart-header-container .header-top-section .absolute.bg-blue-400 {
                width: 4px !important;
                height: 4px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-purple-400 {
                width: 3px !important;
                height: 3px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-cyan-400 {
                width: 4px !important;
                height: 4px !important;
            }

            /* Ultra-compact info cards */
            .cart-header-container .bg-white.bg-opacity-60 {
                padding: 0.5rem !important;
                border-radius: 6px !important;
            }

            .cart-header-container .w-10.h-10 {
                width: 1.75rem !important;
                height: 1.75rem !important;
            }

            .cart-header-container .text-sm.font-semibold {
                font-size: 0.7rem !important;
            }

            .cart-header-container .text-xs.text-gray-600 {
                font-size: 0.625rem !important;
            }

            /* Ultra-compact select all button */
            .mobile-select-all-btn {
                padding: 0.5rem 0.75rem !important;
                font-size: 0.7rem !important;
                border-radius: 6px !important;
            }

            /* Ultra-compact progress section */
            .selected-progress-text {
                font-size: 0.7rem !important;
            }
        }

        /* Medium Mobile (320px - 374px) - Compact header */
        @media (min-width: 320px) and (max-width: 374px) {
            .cart-header-container {
                margin-bottom: 1.125rem !important;
            }

            .cart-header-container .bg-white.rounded-2xl {
                border-radius: 14px !important;
                margin-bottom: 1.125rem !important;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07) !important;
            }

            /* Compact header top section */
            .cart-header-container .header-top-section {
                padding: 0.875rem !important;
            }

            /* Compact title */
            .cart-header-container h1 {
                font-size: 1.25rem !important;
                margin-bottom: 0.375rem !important;
            }

            /* Compact gradient line */
            .cart-header-container .w-16 {
                width: 3rem !important;
                height: 2px !important;
            }

            /* Compact decorative elements */
            .cart-header-container .header-top-section .absolute.bg-blue-400 {
                width: 5px !important;
                height: 5px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-purple-400 {
                width: 4px !important;
                height: 4px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-cyan-400 {
                width: 5px !important;
                height: 5px !important;
            }

            /* Compact info cards */
            .cart-header-container .bg-white.bg-opacity-60 {
                padding: 0.625rem !important;
                border-radius: 7px !important;
            }

            .cart-header-container .w-10.h-10 {
                width: 2rem !important;
                height: 2rem !important;
            }

            .cart-header-container .text-sm.font-semibold {
                font-size: 0.75rem !important;
            }

            .cart-header-container .text-xs.text-gray-600 {
                font-size: 0.65rem !important;
            }

            /* Compact select all button */
            .mobile-select-all-btn {
                padding: 0.625rem 0.875rem !important;
                font-size: 0.75rem !important;
                border-radius: 7px !important;
            }

            /* Compact progress section */
            .selected-progress-text {
                font-size: 0.75rem !important;
            }

            /* Compact grid layout */
            .cart-header-container .grid.grid-cols-2.gap-3 {
                gap: 0.625rem !important;
            }
        }

        /* Regular Mobile (375px - 413px) - Standard mobile header */
        @media (min-width: 375px) and (max-width: 413px) {
            .cart-header-container {
                margin-bottom: 1.25rem !important;
            }

            .cart-header-container .bg-white.rounded-2xl {
                border-radius: 16px !important;
                margin-bottom: 1.25rem !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
            }

            /* Standard header top section */
            .cart-header-container .header-top-section {
                padding: 1rem !important;
            }

            /* Standard title */
            .cart-header-container h1 {
                font-size: 1.375rem !important;
                margin-bottom: 0.5rem !important;
            }

            /* Standard gradient line */
            .cart-header-container .w-16 {
                width: 3.5rem !important;
                height: 2px !important;
            }

            /* Standard decorative elements */
            .cart-header-container .header-top-section .absolute.bg-blue-400 {
                width: 6px !important;
                height: 6px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-purple-400 {
                width: 4px !important;
                height: 4px !important;
            }

            .cart-header-container .header-top-section .absolute.bg-cyan-400 {
                width: 5px !important;
                height: 5px !important;
            }

            /* Standard info cards */
            .cart-header-container .bg-white.bg-opacity-60 {
                padding: 0.75rem !important;
                border-radius: 8px !important;
            }

            .cart-header-container .w-10.h-10 {
                width: 2.25rem !important;
                height: 2.25rem !important;
            }

            .cart-header-container .text-sm.font-semibold {
                font-size: 0.8rem !important;
            }

            .cart-header-container .text-xs.text-gray-600 {
                font-size: 0.7rem !important;
            }

            /* Standard select all button */
            .mobile-select-all-btn {
                padding: 0.75rem 1rem !important;
                font-size: 0.8rem !important;
                border-radius: 8px !important;
            }

            /* Standard progress section */
            .selected-progress-text {
                font-size: 0.8rem !important;
            }

            /* Standard grid layout */
            .cart-header-container .grid.grid-cols-2.gap-3 {
                gap: 0.75rem !important;
            }

            /* Standard hover effects */
            .cart-header-container .bg-white.rounded-2xl:hover {
                transform: translateY(-1px) !important;
            }
        }

        /* ===== RESPONSIVE GAP OPTIMIZATION FOR ACTION BUTTONS ===== */

        /* Small Mobile (≤319px) - Ultra-compact gaps and buttons */
        @media (max-width: 319px) {
            /* Ultra-compact gap for action buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                gap: 0.375rem !important; /* 6px - ultra-compact */
                flex-wrap: wrap !important;
                justify-content: flex-start !important;
            }

            /* Ultra-compact button sizing */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                font-size: 0.625rem !important;
                padding: 0.25rem 0.4rem !important;
                border-radius: 4px !important;
                min-width: auto !important;
                white-space: nowrap !important;
            }

            /* Ultra-compact gap for quantity controls */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.border {
                gap: 0 !important; /* No gap between quantity controls */
            }

            /* Ultra-compact gap for main cart item layout */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4 {
                gap: 0.5rem !important; /* 8px instead of 16px */
            }

            /* Priority button visibility - Hide less important buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:not(:first-child):not(.btn-primary) {
                display: none !important;
            }

            /* Show only essential buttons with icons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button i {
                margin-right: 0.25rem !important;
                font-size: 0.75rem !important;
            }
        }

        /* Medium Mobile (320px - 374px) - Compact gaps and buttons */
        @media (min-width: 320px) and (max-width: 374px) {
            /* Compact gap for action buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                gap: 0.5rem !important; /* 8px - compact */
                flex-wrap: wrap !important;
                justify-content: flex-start !important;
            }

            /* Compact button sizing */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                font-size: 0.68rem !important;
                padding: 0.28rem 0.5rem !important;
                border-radius: 5px !important;
                min-width: auto !important;
                white-space: nowrap !important;
            }

            /* Compact gap for quantity controls */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.border {
                gap: 0 !important;
            }

            /* Compact gap for main cart item layout */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4 {
                gap: 0.55rem !important; /* 9px */
            }

            /* Button icons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button i {
                margin-right: 0.3rem !important;
                font-size: 0.8rem !important;
            }
        }

        /* Regular Mobile (375px - 413px) - Standard gaps and buttons */
        @media (min-width: 375px) and (max-width: 413px) {
            /* Standard gap for action buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                gap: 0.65rem !important; /* 10px - standard mobile */
                flex-wrap: wrap !important;
                justify-content: flex-start !important;
            }

            /* Standard button sizing */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                font-size: 0.72rem !important;
                padding: 0.32rem 0.6rem !important;
                border-radius: 6px !important;
                min-width: auto !important;
                white-space: nowrap !important;
            }

            /* Standard gap for quantity controls */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.border {
                gap: 0 !important;
            }

            /* Standard gap for main cart item layout */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4 {
                gap: 0.7rem !important; /* 11px */
            }

            /* Button icons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button i {
                margin-right: 0.35rem !important;
                font-size: 0.85rem !important;
            }
        }

        /* Large Mobile (414px - 479px) - Enhanced gaps and buttons */
        @media (min-width: 414px) and (max-width: 479px) {
            /* Enhanced gap for action buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                gap: 0.75rem !important; /* 12px - original gap */
                flex-wrap: nowrap !important;
                justify-content: flex-start !important;
            }

            /* Enhanced button sizing */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                font-size: 0.8rem !important;
                padding: 0.375rem 0.75rem !important;
                border-radius: 6px !important;
                min-width: auto !important;
                transition: all 0.2s ease !important;
            }

            /* Enhanced gap for main cart item layout */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4 {
                gap: 0.875rem !important; /* 14px */
            }

            /* Button hover effects */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            }

            /* Button icons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button i {
                margin-right: 0.4rem !important;
                font-size: 0.9rem !important;
            }
        }

        /* Extra Large Mobile (480px - 576px) - Premium gaps and buttons */
        @media (min-width: 480px) and (max-width: 576px) {
            /* Premium gap for action buttons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                gap: 1rem !important; /* 16px - premium spacing */
                flex-wrap: nowrap !important;
                justify-content: flex-start !important;
            }

            /* Premium button sizing */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                font-size: 0.875rem !important;
                padding: 0.5rem 1rem !important;
                border-radius: 8px !important;
                min-width: auto !important;
                transition: all 0.3s ease !important;
                font-weight: 500 !important;
            }

            /* Premium gap for main cart item layout */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4 {
                gap: 1rem !important; /* 16px - original */
            }

            /* Premium button hover effects */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
            }

            /* Premium button active effects */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:active {
                transform: translateY(0) !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            }

            /* Button icons */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button i {
                margin-right: 0.5rem !important;
                font-size: 1rem !important;
            }
        }

        /* ===== SMART BUTTON MANAGEMENT & OVERFLOW HANDLING ===== */

        /* Universal button improvements */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
            /* Prevent overflow */
            overflow: hidden !important;
            max-width: 100% !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
            /* Ensure buttons don't break layout */
            flex-shrink: 0 !important;
            text-overflow: ellipsis !important;
            overflow: hidden !important;
            max-width: fit-content !important;
        }

        /* Smart text truncation for very long button text */
        @media (max-width: 374px) {
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                max-width: 80px !important;
            }
        }

        /* Priority system for buttons - Hide less important ones on small screens */
        @media (max-width: 319px) {
            /* Keep only the most important button (usually Update) */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:nth-child(n+2) {
                display: none !important;
            }

            /* Show delete button as icon only */
            .mobile-delete-btn {
                display: inline-flex !important;
            }
        }

        /* Responsive flex behavior */
        @media (min-width: 320px) and (max-width: 374px) {
            /* Allow wrapping if needed but prefer single line */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 {
                flex-wrap: wrap !important;
                align-items: flex-start !important;
            }
        }

        /* Enhanced touch targets for mobile */
        @media (max-width: 576px) {
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
                min-height: 32px !important;
                touch-action: manipulation !important;
                -webkit-tap-highlight-color: transparent !important;
            }

            /* Improve tap feedback */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:active {
                opacity: 0.7 !important;
                transition: opacity 0.1s ease !important;
            }
        }

        /* Accessibility improvements */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button {
            /* Ensure good contrast and readability */
            font-weight: 500 !important;
            letter-spacing: 0.025em !important;
        }

        /* Focus states for keyboard navigation */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
            border-radius: 6px !important;
        }

        /* Loading states */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button.loading {
            opacity: 0.6 !important;
            pointer-events: none !important;
            position: relative !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-3 button.loading::after {
            content: '' !important;
            position: absolute !important;
            width: 12px !important;
            height: 12px !important;
            border: 2px solid transparent !important;
            border-top: 2px solid currentColor !important;
            border-radius: 50% !important;
            animation: spin 1s linear infinite !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        #delete-confirmation-modal .p-6 {
            padding: 1.25rem !important;
        }

        /* Smooth scroll behavior for mobile */
        html {
            scroll-behavior: smooth !important;
        }

        /* Mobile-specific focus states */
        .quantity-input:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
            border-color: #3b82f6 !important;
        }

        /* Mobile cart item hover effects */
        .bg-white.rounded-xl.shadow-sm:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
        }

        /* ===== MOBILE ORDER SUMMARY RESPONSIVE DESIGN ===== */

        /* Order Summary Container - Mobile Optimized */
        .w-full.lg\:w-1\/3 {
            margin-top: 1.5rem !important; /* Giảm khoảng cách từ mt-8 */
        }

        /* Order Summary Card - Mobile Styling */
        .w-full.lg\:w-1\/3 .bg-white.rounded-xl.shadow-sm {
            border-radius: 1rem !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid #e5e7eb !important;
            overflow: hidden !important;
            position: static !important; /* Remove sticky on mobile */
        }

        /* Header Section - Reduced Padding */
        .w-full.lg\:w-1\/3 .p-6.border-b {
            padding: 1.25rem !important; /* Giảm từ p-6 (1.5rem) xuống 1.25rem */
            border-bottom: 1px solid #f3f4f6 !important;
        }

        /* Order Summary Title */
        .w-full.lg\:w-1\/3 .text-xl.font-bold.text-gray-800 {
            font-size: 1.125rem !important; /* Giảm từ text-xl xuống text-lg */
            font-weight: 700 !important;
            color: #1f2937 !important;
            margin-bottom: 1rem !important;
            text-align: center !important; /* Center title on mobile */
        }

        /* ===== SPECIFIC BREAKPOINT OPTIMIZATIONS FOR ORDER SUMMARY ===== */

        /* Small Mobile (≤319px) - Ultra-compact Order Summary */
        @media (max-width: 319px) {
            /* Ultra-compact container */
            .w-full.lg\:w-1\/3 {
                margin-top: 1rem !important;
            }

            /* Ultra-compact card styling */
            .w-full.lg\:w-1\/3 .bg-white.rounded-xl.shadow-sm {
                border-radius: 0.75rem !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
                margin-bottom: 1rem !important;
            }

            /* Ultra-compact header padding */
            .w-full.lg\:w-1\/3 .p-6.border-b {
                padding: 0.875rem !important;
                border-bottom: 1px solid #f3f4f6 !important;
            }

            /* Ultra-compact title */
            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-gray-800 {
                font-size: 0.95rem !important;
                font-weight: 700 !important;
                margin-bottom: 0.75rem !important;
                text-align: center !important;
            }

            /* Ultra-compact summary items */
            .w-full.lg\:w-1\/3 .space-y-3 {
                gap: 0.5rem !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex.justify-between {
                padding: 0.25rem 0 !important;
                font-size: 0.75rem !important;
            }

            /* Ultra-compact text sizes */
            .w-full.lg\:w-1\/3 .space-y-3 .flex span:first-child {
                font-size: 0.75rem !important;
                font-weight: 500 !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex span:last-child {
                font-size: 0.75rem !important;
                font-weight: 600 !important;
            }

            /* Ultra-compact total section */
            .w-full.lg\:w-1\/3 .pt-3.mt-3.border-t {
                padding: 0.75rem !important;
                margin-top: 0.75rem !important;
                margin-left: -0.875rem !important;
                margin-right: -0.875rem !important;
                margin-bottom: -0.875rem !important;
            }

            .w-full.lg\:w-1\/3 .flex.justify-between.items-center span:first-child {
                font-size: 0.8rem !important;
                font-weight: 600 !important;
            }

            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-primary.cart-total {
                font-size: 1rem !important;
                font-weight: 700 !important;
            }

            /* Ultra-compact action section */
            .w-full.lg\:w-1\/3 .p-6:not(.border-b) {
                padding: 0.875rem !important;
            }

            /* Ultra-compact checkout button */
            .w-full.lg\:w-1\/3 #checkout-btn {
                padding: 0.75rem 1rem !important;
                font-size: 0.875rem !important;
                font-weight: 600 !important;
                border-radius: 0.5rem !important;
                margin-bottom: 0.75rem !important;
            }

            /* Ultra-compact clear cart button */
            .w-full.lg\:w-1\/3 #clear-cart-btn-desktop {
                padding: 0.625rem 0.75rem !important;
                font-size: 0.75rem !important;
                border-radius: 0.5rem !important;
                margin-bottom: 0.75rem !important;
            }

            /* Ultra-compact security info */
            .w-full.lg\:w-1\/3 .mt-6.flex.items-center {
                margin-top: 0.875rem !important;
                padding-top: 0.75rem !important;
                gap: 0.5rem !important;
                flex-direction: column !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center > div {
                font-size: 0.65rem !important;
                gap: 0.25rem !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center i {
                font-size: 0.7rem !important;
            }
        }

        /* Medium Mobile (320px - 374px) - Compact Order Summary */
        @media (min-width: 320px) and (max-width: 374px) {
            /* Compact container */
            .w-full.lg\:w-1\/3 {
                margin-top: 1.125rem !important;
            }

            /* Compact card styling */
            .w-full.lg\:w-1\/3 .bg-white.rounded-xl.shadow-sm {
                border-radius: 0.875rem !important;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07) !important;
                margin-bottom: 1.125rem !important;
            }

            /* Compact header padding */
            .w-full.lg\:w-1\/3 .p-6.border-b {
                padding: 1rem !important;
                border-bottom: 1px solid #f3f4f6 !important;
            }

            /* Compact title */
            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-gray-800 {
                font-size: 1rem !important;
                font-weight: 700 !important;
                margin-bottom: 0.875rem !important;
                text-align: center !important;
            }

            /* Compact summary items */
            .w-full.lg\:w-1\/3 .space-y-3 {
                gap: 0.625rem !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex.justify-between {
                padding: 0.375rem 0 !important;
                font-size: 0.8rem !important;
            }

            /* Compact text sizes */
            .w-full.lg\:w-1\/3 .space-y-3 .flex span:first-child {
                font-size: 0.8rem !important;
                font-weight: 500 !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex span:last-child {
                font-size: 0.8rem !important;
                font-weight: 600 !important;
            }

            /* Compact total section */
            .w-full.lg\:w-1\/3 .pt-3.mt-3.border-t {
                padding: 0.875rem !important;
                margin-top: 0.875rem !important;
                margin-left: -1rem !important;
                margin-right: -1rem !important;
                margin-bottom: -1rem !important;
            }

            .w-full.lg\:w-1\/3 .flex.justify-between.items-center span:first-child {
                font-size: 0.875rem !important;
                font-weight: 600 !important;
            }

            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-primary.cart-total {
                font-size: 1.125rem !important;
                font-weight: 700 !important;
            }

            /* Compact action section */
            .w-full.lg\:w-1\/3 .p-6:not(.border-b) {
                padding: 1rem !important;
            }

            /* Compact checkout button */
            .w-full.lg\:w-1\/3 #checkout-btn {
                padding: 0.875rem 1.25rem !important;
                font-size: 0.9rem !important;
                font-weight: 600 !important;
                border-radius: 0.625rem !important;
                margin-bottom: 0.875rem !important;
            }

            /* Compact clear cart button */
            .w-full.lg\:w-1\/3 #clear-cart-btn-desktop {
                padding: 0.75rem 1rem !important;
                font-size: 0.8rem !important;
                border-radius: 0.5rem !important;
                margin-bottom: 0.875rem !important;
            }

            /* Compact security info */
            .w-full.lg\:w-1\/3 .mt-6.flex.items-center {
                margin-top: 1rem !important;
                padding-top: 0.875rem !important;
                gap: 0.75rem !important;
                flex-wrap: wrap !important;
                justify-content: center !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center > div {
                font-size: 0.7rem !important;
                gap: 0.3rem !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center i {
                font-size: 0.75rem !important;
            }
        }

        /* Regular Mobile (375px - 413px) - Standard Mobile Order Summary */
        @media (min-width: 375px) and (max-width: 413px) {
            /* Standard mobile container */
            .w-full.lg\:w-1\/3 {
                margin-top: 1.25rem !important;
            }

            /* Standard mobile card styling */
            .w-full.lg\:w-1\/3 .bg-white.rounded-xl.shadow-sm {
                border-radius: 1rem !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
                margin-bottom: 1.25rem !important;
            }

            /* Standard mobile header padding */
            .w-full.lg\:w-1\/3 .p-6.border-b {
                padding: 1.125rem !important;
                border-bottom: 1px solid #f3f4f6 !important;
            }

            /* Standard mobile title */
            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-gray-800 {
                font-size: 1.0625rem !important;
                font-weight: 700 !important;
                margin-bottom: 0.9375rem !important;
                text-align: center !important;
            }

            /* Standard mobile summary items */
            .w-full.lg\:w-1\/3 .space-y-3 {
                gap: 0.75rem !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex.justify-between {
                padding: 0.4375rem 0 !important;
                font-size: 0.85rem !important;
            }

            /* Standard mobile text sizes */
            .w-full.lg\:w-1\/3 .space-y-3 .flex span:first-child {
                font-size: 0.85rem !important;
                font-weight: 500 !important;
            }

            .w-full.lg\:w-1\/3 .space-y-3 .flex span:last-child {
                font-size: 0.85rem !important;
                font-weight: 600 !important;
            }

            /* Standard mobile total section */
            .w-full.lg\:w-1\/3 .pt-3.mt-3.border-t {
                padding: 0.9375rem !important;
                margin-top: 0.9375rem !important;
                margin-left: -1.125rem !important;
                margin-right: -1.125rem !important;
                margin-bottom: -1.125rem !important;
                border-radius: 0.5rem !important;
            }

            .w-full.lg\:w-1\/3 .flex.justify-between.items-center span:first-child {
                font-size: 0.9375rem !important;
                font-weight: 600 !important;
            }

            .w-full.lg\:w-1\/3 .text-xl.font-bold.text-primary.cart-total {
                font-size: 1.25rem !important;
                font-weight: 700 !important;
            }

            /* Standard mobile VAT text */
            .w-full.lg\:w-1\/3 .text-xs.text-gray-500 {
                font-size: 0.7rem !important;
                margin-top: 0.3125rem !important;
            }

            /* Standard mobile action section */
            .w-full.lg\:w-1\/3 .p-6:not(.border-b) {
                padding: 1.125rem !important;
            }

            /* Standard mobile checkout button */
            .w-full.lg\:w-1\/3 #checkout-btn {
                padding: 0.9375rem 1.375rem !important;
                font-size: 0.9375rem !important;
                font-weight: 600 !important;
                border-radius: 0.6875rem !important;
                margin-bottom: 0.9375rem !important;
                box-shadow: 0 4px 12px rgba(243, 115, 33, 0.25) !important;
            }

            .w-full.lg\:w-1\/3 #checkout-btn:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 5px 15px rgba(243, 115, 33, 0.3) !important;
            }

            /* Standard mobile clear cart button */
            .w-full.lg\:w-1\/3 #clear-cart-btn-desktop {
                padding: 0.8125rem 1.125rem !important;
                font-size: 0.8125rem !important;
                border-radius: 0.5625rem !important;
                margin-bottom: 0.9375rem !important;
            }

            /* Standard mobile security info */
            .w-full.lg\:w-1\/3 .mt-6.flex.items-center {
                margin-top: 1.125rem !important;
                padding-top: 0.9375rem !important;
                gap: 0.875rem !important;
                justify-content: space-around !important;
                flex-wrap: wrap !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center > div {
                font-size: 0.75rem !important;
                gap: 0.3125rem !important;
            }

            .w-full.lg\:w-1\/3 .mt-6.flex.items-center i {
                font-size: 0.8125rem !important;
            }
        }

        /* Summary Items Container */
        .w-full.lg\:w-1\/3 .space-y-3 {
            gap: 0.875rem !important; /* Tăng khoảng cách giữa các item */
        }

        /* Summary Items - Better Mobile Layout */
        .w-full.lg\:w-1\/3 .space-y-3 .flex.justify-between {
            padding: 0.5rem 0 !important;
            border-bottom: 1px solid #f9fafb !important;
            align-items: center !important;
        }

        .w-full.lg\:w-1\/3 .space-y-3 .flex.justify-between:last-child {
            border-bottom: none !important;
        }

        /* Summary Text - Mobile Optimized */
        .w-full.lg\:w-1\/3 .space-y-3 .flex span:first-child {
            font-size: 0.9rem !important;
            color: #4b5563 !important;
            font-weight: 500 !important;
        }

        .w-full.lg\:w-1\/3 .space-y-3 .flex span:last-child {
            font-size: 0.9rem !important;
            font-weight: 600 !important;
        }

        /* Selected Items Info - Consistent Styling */
        .w-full.lg\:w-1\/3 .selected-items-info {
            background: transparent !important;
            padding: 0 !important;
            border-radius: 0 !important;
            border: none !important;
            margin: 0 !important;
        }

        .w-full.lg\:w-1\/3 .selected-items-info span:first-child {
            color: #4b5563 !important;
        }

        .w-full.lg\:w-1\/3 .selected-items-info .text-primary {
            color: #F37321 !important;
            font-weight: 600 !important;
        }

        /* Force text-primary color for all cart elements - High Priority */
        .text-primary,
        span.text-primary,
        .text-primary.font-medium,
        .selected-items-info .text-primary,
        .selected-items-info span.text-primary {
            color: #F37321 !important;
        }

        /* Ultra High Priority - Force orange color for percentage text */
        .selected-items-info span.text-primary.font-medium,
        .selected-items-info .text-primary,
        .selected-items-percentage,
        span.selected-items-percentage,
        .selected-items-percentage *,
        span.selected-items-percentage *,
        .selected-items-info .selected-items-percentage,
        .selected-items-info span.selected-items-percentage {
            color: #F37321 !important;
        }

        /* Maximum Priority - Target the exact element structure */
        body .w-full.lg\:w-1\/3 .space-y-3 .selected-items-info span.text-primary.font-medium span.selected-items-percentage,
        body .selected-items-info span.text-primary span.selected-items-percentage,
        body .selected-items-info .selected-items-percentage,
        body span.selected-items-percentage,
        html body .selected-items-percentage,
        html body span.selected-items-percentage {
            color: #F37321 !important;
            font-weight: 600 !important;
        }

        /* Force orange color for the entire percentage span including parentheses */
        .selected-items-info span:last-child,
        .selected-items-info span:last-child *,
        .flex.justify-between.items-center.text-gray-700.selected-items-info span:last-child,
        .flex.justify-between.items-center.text-gray-700.selected-items-info span:last-child * {
            color: #F37321 !important;
        }

        /* Specific override for mobile cart */
        @media (max-width: 576px) {
            .text-primary,
            span.text-primary,
            .text-primary.font-medium,
            .selected-items-info .text-primary,
            .selected-items-info span.text-primary,
            .selected-items-info span.text-primary.font-medium,
            .selected-items-info .text-primary,
            .selected-items-percentage,
            span.selected-items-percentage,
            .selected-items-percentage *,
            span.selected-items-percentage *,
            .selected-items-info .selected-items-percentage,
            .selected-items-info span.selected-items-percentage,
            .selected-items-info span:last-child,
            .selected-items-info span:last-child *,
            .flex.justify-between.items-center.text-gray-700.selected-items-info span:last-child,
            .flex.justify-between.items-center.text-gray-700.selected-items-info span:last-child *,
            body .w-full.lg\:w-1\/3 .space-y-3 .selected-items-info span.text-primary.font-medium span.selected-items-percentage,
            body .selected-items-info span.text-primary span.selected-items-percentage,
            body .selected-items-info .selected-items-percentage,
            body span.selected-items-percentage,
            html body .selected-items-percentage,
            html body span.selected-items-percentage {
                color: #F37321 !important;
                font-weight: 600 !important;
            }
        }

        /* Total Section - Enhanced Mobile Design */
        .w-full.lg\:w-1\/3 .pt-3.mt-3.border-t {
            padding-top: 1rem !important;
            margin-top: 1rem !important;
            border-top: 2px solid #e5e7eb !important;
            background: #fafbfc !important;
            padding: 1rem !important;
            border-radius: 0.5rem !important;
            margin-left: -1.25rem !important;
            margin-right: -1.25rem !important;
            margin-bottom: -1.25rem !important;
        }

        /* Total Amount - Prominent Display */
        .w-full.lg\:w-1\/3 .flex.justify-between.items-center span:first-child {
            font-size: 1rem !important;
            font-weight: 600 !important;
            color: #1f2937 !important;
        }

        .w-full.lg\:w-1\/3 .text-xl.font-bold.text-primary.cart-total {
            font-size: 1.375rem !important; /* Slightly smaller but still prominent */
            font-weight: 800 !important;
            color: #F37321 !important;
            letter-spacing: -0.025em !important;
        }

        /* VAT Text */
        .w-full.lg\:w-1\/3 .text-xs.text-gray-500 {
            font-size: 0.75rem !important;
            color: #6b7280 !important;
            margin-top: 0.375rem !important;
            text-align: right !important;
        }

        /* Action Section - Mobile Optimized */
        .w-full.lg\:w-1\/3 .p-6:not(.border-b) {
            padding: 1.25rem !important;
            background: #ffffff !important;
        }

        /* Checkout Button - Mobile Enhanced */
        .w-full.lg\:w-1\/3 #checkout-btn {
            padding: 1rem 1.5rem !important; /* Increased padding for better touch */
            font-size: 1rem !important;
            font-weight: 600 !important;
            border-radius: 0.75rem !important;
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.25) !important;
            margin-bottom: 1rem !important;
            transition: all 0.3s ease !important;
        }

        .w-full.lg\:w-1\/3 #checkout-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 16px rgba(243, 115, 33, 0.35) !important;
        }

        /* Action Buttons Row - Mobile Layout */
        .w-full.lg\:w-1\/3 .flex.gap-2.mb-4 {
            gap: 0.75rem !important;
            margin-bottom: 1rem !important;
        }

        .w-full.lg\:w-1\/3 .flex.gap-2.mb-4 button {
            padding: 0.875rem 1rem !important;
            font-size: 0.875rem !important;
            border-radius: 0.5rem !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
        }

        /* Clear Cart Button - Mobile */
        .w-full.lg\:w-1\/3 #clear-cart-btn-desktop {
            padding: 0.875rem 1rem !important;
            font-size: 0.875rem !important;
            border-radius: 0.5rem !important;
            margin-bottom: 1rem !important;
        }

        /* Security Info - Mobile Layout */
        .w-full.lg\:w-1\/3 .mt-6.flex.items-center {
            margin-top: 1.25rem !important;
            padding-top: 1rem !important;
            border-top: 1px solid #f3f4f6 !important;
            justify-content: space-around !important;
            flex-wrap: wrap !important;
            gap: 1rem !important;
        }

        .w-full.lg\:w-1\/3 .mt-6.flex.items-center > div {
            font-size: 0.8rem !important;
            color: #6b7280 !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.375rem !important;
        }

        .w-full.lg\:w-1\/3 .mt-6.flex.items-center i {
            font-size: 0.875rem !important;
            color: #F37321 !important;
        }

        /* Mobile-specific utility classes */
        .mobile-only {
            display: block !important;
        }

        .desktop-only {
            display: none !important;
        }
    }

    /* Tablet adjustments */
    @media (min-width: 577px) and (max-width: 768px) {
        .bg-white.rounded-xl.shadow-sm .aspect-square {
            width: 100px !important;
            height: 100px !important;
        }

        .bg-white.rounded-xl.shadow-sm .w-full.md\:w-1\/4 {
            width: 100px !important;
        }

        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 1rem !important;
        }


    }

    /* CSS cho modal xác nhận xóa */
    #delete-confirmation-modal {
        transition: opacity 0.3s ease, visibility 0.3s ease;
        opacity: 0;
        visibility: hidden;
    }

    #delete-confirmation-modal.active {
        opacity: 1;
        visibility: visible;
        display: flex;
    }

    #delete-confirmation-modal .bg-white {
        transition: transform 0.3s ease;
        transform: translateY(20px);
    }

    #delete-confirmation-modal.active .bg-white {
        transform: translateY(0);
    }

    /* Hiệu ứng loading cho nút xác nhận */
    #delete-confirm-btn.loading {
        position: relative;
        color: transparent !important;
        pointer-events: none;
        min-height: 48px; /* Đảm bảo chiều cao cố định */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    #delete-confirm-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 0.8s linear infinite;
        display: block;
    }

    #delete-confirm-btn.loading span {
        opacity: 0 !important;
        visibility: hidden !important;
    }

    #delete-confirm-btn.loading * {
        opacity: 0 !important;
        visibility: hidden !important;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Hiệu ứng shake cho modal khi lỗi */
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    #delete-confirmation-modal.shake .bg-white {
        animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
    }

    /* CSS cho layout mới của cart item */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Professional Cart Layout Styles - Desktop Only */
    @media (min-width: 768px) {
        .cart-item-layout-professional {
            display: grid;
            grid-template-columns: 20px 160px 1fr;
            gap: 1.5rem;
            align-items: flex-start;
        }

        .checkbox-column-professional {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 0.5rem;
        }

        .professional-checkbox-style {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 18px !important;
            height: 18px !important;
            border: 2px solid #3b82f6 !important;
            border-radius: 4px !important;
            background: white !important;
            cursor: pointer !important;
            position: relative !important;
            transition: all 0.2s ease !important;
        }

        .professional-checkbox-style:checked {
            background: #3b82f6 !important;
            border-color: #3b82f6 !important;
        }

        .professional-checkbox-style:checked::after {
            content: '' !important;
            position: absolute !important;
            top: 2px !important;
            left: 5px !important;
            width: 5px !important;
            height: 8px !important;
            border: solid white !important;
            border-width: 0 2px 2px 0 !important;
            transform: rotate(45deg) !important;
        }

        .professional-checkbox-style:hover {
            border-color: #60a5fa !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
        }

        .image-column-professional {
            position: relative;
            display: flex;
            justify-content: center;
        }

        .product-image-container-professional {
            width: 160px;
            height: 160px;
            border-radius: 8px;
            overflow: hidden;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .product-image-professional {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-image-professional:hover {
            transform: scale(1.05);
        }

        .product-image-placeholder-professional {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
        }

        .content-column-professional {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
            min-width: 0;
        }

        .product-info-row-professional {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .product-main-info-professional {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .product-details-professional {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 0;
        }

        .product-title-professional {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.4;
            margin: 0;
            word-wrap: break-word;
        }

        .product-title-professional a {
            color: inherit;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .product-title-professional a:hover {
            color: #F37321;
        }

        .product-price-professional {
            font-size: 1.25rem;
            font-weight: 700;
            color: #F37321;
            margin: 0;
            display: flex;
            align-items: baseline;
            gap: 0.5rem;
        }

        .original-price-professional {
            font-size: 0.875rem;
            color: #9ca3af;
            text-decoration: line-through;
            font-weight: 500;
        }

        .discount-total-section-professional {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 2rem;
            align-items: center;
        }

        .discount-section-professional {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .discount-badge-professional {
            background: #fef2f2;
            color: #dc2626;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #fecaca;
        }

        .savings-badge-professional {
            background: #f0fdf4;
            color: #16a34a;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #bbf7d0;
        }

        .best-price-badge-professional {
            background: #f0f9ff;
            color: #0284c7;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #bae6fd;
        }

        .total-section-professional {
            text-align: right;
            flex-shrink: 0;
        }

        .total-label-professional {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .total-amount-professional {
            font-size: 1.375rem;
            font-weight: 700;
            color: #1f2937;
            letter-spacing: -0.025em;
        }

        .controls-row-professional {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 1.5rem;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .quantity-section-professional {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quantity-label-professional {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
            white-space: nowrap;
        }

        .quantity-controls-professional {
            display: flex;
            align-items: center;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
            background: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .qty-btn-professional {
            width: 32px;
            height: 32px;
            border: none;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .qty-btn-professional:hover {
            background: #FFF4EC;
            color: #F37321;
        }

        .qty-btn-professional:active {
            background: #f3f4f6;
            transform: scale(0.95);
        }

        .qty-input-professional {
            width: 50px !important;
            height: 32px !important;
            border: none !important;
            border-left: 1px solid #d1d5db !important;
            border-right: 1px solid #d1d5db !important;
            text-align: center !important;
            font-weight: 600 !important;
            color: #1f2937 !important;
            font-size: 0.875rem !important;
            background: white !important;
        }

        .qty-input-professional:focus {
            outline: none !important;
            background: #fafafa !important;
            border-left-color: #F37321 !important;
            border-right-color: #F37321 !important;
        }

        .spacer-professional {
            flex: 1;
        }

        .action-buttons-professional {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn-professional {
            padding: 0.5rem 0.875rem;
            border-radius: 6px;
            border: 1px solid;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8rem;
            text-decoration: none;
            min-width: 85px;
            justify-content: center;
            height: 32px;
        }

        .update-btn-professional {
            background: white;
            color: #F37321;
            border-color: #F37321;
        }

        .update-btn-professional:hover {
            background: #FFF4EC;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
        }

        .delete-btn-professional {
            background: white;
            color: #dc2626;
            border-color: #dc2626;
        }

        .delete-btn-professional:hover {
            background: #fef2f2;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
        }
    }

    /* Padding cho cart item */
    .cart-item-padding {
        padding: 0.6rem;
    }

    /* Mobile layout visibility */
    @media (max-width: 767px) {
        .mobile-cart-layout {
            display: block !important;
        }
        .desktop-cart-layout {
            display: none !important;
        }
    }

    /* Tablet layout (577px - 767px) */
    @media (min-width: 577px) and (max-width: 767px) {
        .mobile-cart-layout {
            display: none !important;
        }
        .desktop-cart-layout {
            display: block !important;
        }

        /* Tablet cart item padding - Slightly smaller than desktop */
        .cart-item-padding {
            padding: 1.25rem !important;
        }

        /* Tablet-specific adjustments */
        .bg-white.rounded-xl.shadow-sm {
            margin-bottom: 1rem !important;
        }

        /* Adjust font sizes for tablet */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 1rem !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
            font-size: 1.125rem !important;
        }
    }

    /* Desktop layout (≥768px) */
    @media (min-width: 768px) {
        .mobile-cart-layout {
            display: none !important;
        }
        .desktop-cart-layout {
            display: block !important;
        }

        /* Desktop cart item padding - Full padding for better spacing */
        .cart-item-padding {
            padding: 1.5rem !important;
        }

        /* Desktop-specific enhancements */
        .bg-white.rounded-xl.shadow-sm {
            margin-bottom: 1.25rem !important;
            border-radius: 16px !important;
        }

        /* Enhanced typography for desktop */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 1.125rem !important;
            line-height: 1.4 !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
            font-size: 1.25rem !important;
        }
    }

    /* Large Desktop (≥1024px) */
    @media (min-width: 1024px) {
        /* Increased padding for better spacing on large screens */
        .cart-item-padding {
            padding: 2rem !important;
        }

        /* Enhanced card design for large screens */
        .bg-white.rounded-xl.shadow-sm {
            margin-bottom: 1.5rem !important;
            border-radius: 20px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        }

        .bg-white.rounded-xl.shadow-sm:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
            transform: translateY(-2px) !important;
        }

        /* Typography improvements for large screens */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 1.25rem !important;
            line-height: 1.5 !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
            font-size: 1.375rem !important;
        }
    }

    /* Ultra-wide Desktop (≥1440px) */
    @media (min-width: 1440px) {
        /* Maximum padding for ultra-wide screens */
        .cart-item-padding {
            padding: 2.5rem !important;
        }

        /* Premium card design for ultra-wide screens */
        .bg-white.rounded-xl.shadow-sm {
            margin-bottom: 2rem !important;
            border-radius: 24px !important;
        }

        /* Premium typography for ultra-wide screens */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 1.375rem !important;
            line-height: 1.6 !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
            font-size: 1.5rem !important;
        }
    }

    /* Desktop: Ẩn mobile delete button, hiện desktop delete button */
    @media (min-width: 577px) {
        .mobile-delete-btn {
            display: none;
        }
        .desktop-delete-btn {
            display: block;
        }
    }

    /* Mobile: Hiện mobile delete button, ẩn desktop delete button */
    @media (max-width: 576px) {
        .mobile-delete-btn {
            display: block;
        }
        .desktop-delete-btn {
            display: none;
        }

        /* ===== MOBILE CART ITEM REDESIGN ===== */

        /* Cart item container - Modern card design tối giản */
        .bg-white.rounded-xl.shadow-sm {
            border-radius: 12px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
            border: 1px solid #f3f4f6 !important;
            margin-bottom: 1rem !important;
            overflow: hidden !important;
            transition: all 0.2s ease !important;
            background-color: #ffffff !important;
        }

        .bg-white.rounded-xl.shadow-sm:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
            transform: translateY(-1px) !important;
            border-color: #e5e7eb !important;
        }

        /* Cart item padding - Optimized for mobile (1.2rem) */
        .bg-white.rounded-xl.shadow-sm .cart-item-padding,
        .cart-item-padding {
            padding: 1.2rem !important;
        }

        /* Đảm bảo override Tailwind classes */
        div.cart-item-padding {
            padding: 1.2rem !important;
        }

        /* ===== TOP ROW: Checkbox + Image + Product Info + Delete ===== */

        /* Top row container */
        .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4.mb-3 {
            gap: 0.875rem !important;
            margin-bottom: 1rem !important;
            align-items: flex-start !important;
        }

        /* Modern Soft Checkbox Styling */
        .bg-white.rounded-xl.shadow-sm .cart-item-checkbox {
            width: 20px !important;
            height: 20px !important;
            border-radius: 6px !important;
            border: 2px solid #e5e7eb !important;
            margin-top: 2px !important;
            background-color: #ffffff !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            position: relative !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }

        .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:hover {
            border-color: #cbd5e1 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            transform: scale(1.05) !important;
        }

        .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked {
            background-color: #3b82f6 !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        }

        .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked::after {
            content: '' !important;
            position: absolute !important;
            top: 2px !important;
            left: 6px !important;
            width: 6px !important;
            height: 10px !important;
            border: solid white !important;
            border-width: 0 2px 2px 0 !important;
            transform: rotate(45deg) !important;
            opacity: 1 !important;
            transition: opacity 0.2s ease !important;
        }

        .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:focus {
            outline: none !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
        }

        /* Checkbox container styling */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.justify-center {
            padding: 2px !important;
        }

        .bg-white.rounded-xl.shadow-sm label.inline-flex.items-center.cursor-pointer {
            transition: all 0.2s ease !important;
        }

        .bg-white.rounded-xl.shadow-sm label.inline-flex.items-center.cursor-pointer:hover {
            transform: translateY(-1px) !important;
        }

        /* Product image - Modern rounded design */
        .bg-white.rounded-xl.shadow-sm .w-20.h-20 {
            width: 64px !important;
            height: 64px !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .bg-white.rounded-xl.shadow-sm .w-20.h-20 img {
            border-radius: 12px !important;
        }

        /* Product info container */
        .bg-white.rounded-xl.shadow-sm .flex-1.min-w-0 {
            min-width: 0 !important;
            flex: 1 !important;
        }

        /* Product name - Typography optimization */
        .bg-white.rounded-xl.shadow-sm h3 {
            font-size: 0.925rem !important;
            line-height: 1.35 !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            margin-bottom: 0.375rem !important;
            letter-spacing: -0.01em !important;
        }

        .bg-white.rounded-xl.shadow-sm h3 a {
            color: inherit !important;
            text-decoration: none !important;
        }

        .bg-white.rounded-xl.shadow-sm h3 a:hover {
            color: #3b82f6 !important;
        }

        /* Product price - Enhanced styling với màu cam nhẹ */
        .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
            font-size: 1rem !important;
            font-weight: 600 !important;
            color: #ea580c !important;
            margin-top: 0.125rem !important;
        }

        /* Mobile delete button - Minimalist design */
        .mobile-delete-btn {
            display: flex !important;
            align-items: flex-start !important;
            margin-top: 2px !important;
        }

        .mobile-delete-btn button {
            width: 32px !important;
            height: 32px !important;
            border-radius: 8px !important;
            background-color: #f9fafb !important;
            color: #9ca3af !important;
            border: 1px solid #e5e7eb !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 0.875rem !important;
            transition: all 0.2s ease !important;
        }

        .mobile-delete-btn button:hover {
            background-color: #f3f4f6 !important;
            border-color: #d1d5db !important;
            color: #6b7280 !important;
            transform: scale(1.02) !important;
        }

        /* ===== CONTROLS ROW: Quantity + Update Button ===== */

        /* Controls container - Layout cân đối với nút cập nhật bên phải */
        .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-end {
            display: flex !important;
            flex-direction: row !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-left: 105px !important;
            margin-top: 0.75rem !important;
            flex-wrap: nowrap !important;
        }

        /* Quantity controls - Thiết kế tinh tế và tối giản */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.border {
            border-radius: 8px !important;
            border: 1px solid #e2e8f0 !important;
            background-color: #ffffff !important;
            overflow: hidden !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
            flex-shrink: 0 !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-control {
            width: 30px !important;
            height: 30px !important;
            background-color: #ffffff !important;
            border: none !important;
            color: #64748b !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-control:hover {
            background-color: #f1f5f9 !important;
            color: #475569 !important;
            border: 1px solid #cbd5e1 !important;
            transform: scale(1.02) !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-control:active {
            transform: scale(0.95) !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-input {
            width: 36px !important;
            height: 30px !important;
            border: none !important;
            background: transparent !important;
            text-align: center !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            border-left: 1px solid #f1f5f9 !important;
            border-right: 1px solid #f1f5f9 !important;
        }

        .bg-white.rounded-xl.shadow-sm .quantity-input:focus {
            outline: none !important;
            background-color: #f9fafb !important;
            color: #374151 !important;
            border-left-color: #d1d5db !important;
            border-right-color: #d1d5db !important;
        }

        /* Update button - Căn phải và nhất quán màu sắc */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 {
            flex-shrink: 0 !important;
            margin-left: auto !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button {
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            padding: 0.375rem 0.75rem !important;
            border-radius: 8px !important;
            background-color: #f8fafc !important;
            color: #475569 !important;
            border: 1px solid #e2e8f0 !important;
            transition: all 0.2s ease !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.25rem !important;
            white-space: nowrap !important;
            min-width: fit-content !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button:hover {
            background-color: #f1f5f9 !important;
            color: #334155 !important;
            border-color: #cbd5e1 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button:active {
            transform: translateY(0) !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button i {
            font-size: 0.625rem !important;
        }

        /* Loading state cho nút cập nhật trên mobile */
        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button:disabled {
            opacity: 0.7 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button .fa-spinner {
            animation: spin 1s linear infinite !important;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* CSS loading cho nút cập nhật với class text-primary */
        .bg-white.rounded-xl.shadow-sm .text-primary:disabled {
            opacity: 0.7 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .bg-white.rounded-xl.shadow-sm .text-primary .fa-spinner {
            animation: spin 1s linear infinite !important;
        }

        /* ===== BOTTOM ROW: Discount + Savings + Total ===== */

        /* Bottom section - Enhanced spacing */
        .bg-white.rounded-xl.shadow-sm .mt-4.pt-3.border-t {
            margin-top: 1rem !important;
            padding-top: 0.875rem !important;
            border-top: 1px solid #f1f5f9 !important;
        }

        /* Discount and savings badges - Màu sắc phù hợp UX */
        .bg-white.rounded-xl.shadow-sm .bg-red-100.text-red-600 {
            background-color: #fef2f2 !important;
            color: #dc2626 !important;
            padding: 0.375rem 0.625rem !important;
            border-radius: 6px !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            border: 1px solid #fecaca !important;
        }

        .bg-white.rounded-xl.shadow-sm .bg-green-100.text-green-700 {
            background-color: #f0fdf4 !important;
            color: #16a34a !important;
            padding: 0.375rem 0.625rem !important;
            border-radius: 6px !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            border: 1px solid #bbf7d0 !important;
        }

        /* Total price - Enhanced typography với màu đậm */
        .bg-white.rounded-xl.shadow-sm .text-lg.font-semibold.text-primary-dark {
            font-size: 1.125rem !important;
            font-weight: 700 !important;
            color: #1f2937 !important;
            letter-spacing: -0.01em !important;
        }

        /* ===== RESPONSIVE IMPROVEMENTS ===== */

        /* Small Mobile (≤319px) - Ultra-compact layout */
        @media (max-width: 319px) {
            .cart-item-padding {
                padding: 0.6rem !important;
            }

            /* Ultra-compact image */
            .bg-white.rounded-xl.shadow-sm .w-20.h-20 {
                width: 40px !important;
                height: 40px !important;
                border-radius: 8px !important;
            }

            /* Compact typography */
            .bg-white.rounded-xl.shadow-sm h3 {
                font-size: 0.8rem !important;
                line-height: 1.25 !important;
                margin-bottom: 0.25rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
                font-size: 0.875rem !important;
            }

            /* Ultra-compact controls */
            .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-end {
                margin-left: 50px !important;
                margin-top: 0.5rem !important;
            }

            /* Smaller quantity controls */
            .bg-white.rounded-xl.shadow-sm .quantity-control {
                width: 24px !important;
                height: 24px !important;
                font-size: 0.625rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .quantity-input {
                width: 28px !important;
                height: 24px !important;
                font-size: 0.75rem !important;
            }

            /* Compact update button */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button {
                font-size: 0.625rem !important;
                padding: 0.25rem 0.5rem !important;
            }

            /* Reduce gaps */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4.mb-3 {
                gap: 0.5rem !important;
                margin-bottom: 0.75rem !important;
            }

            /* Compact checkbox */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox {
                width: 16px !important;
                height: 16px !important;
                border-radius: 4px !important;
            }

            /* Fix checkbox icon for 16px checkbox */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked::after {
                top: 1px !important;
                left: 4px !important;
                width: 4px !important;
                height: 7px !important;
                border-width: 0 1.5px 1.5px 0 !important;
            }

            /* Compact delete button */
            .mobile-delete-btn button {
                width: 24px !important;
                height: 24px !important;
                font-size: 0.75rem !important;
            }

            /* Ultra-compact discount and savings badges */
            .bg-white.rounded-xl.shadow-sm .bg-red-100.text-red-600 {
                padding: 0.2rem 0.4rem !important;
                border-radius: 4px !important;
                font-size: 0.625rem !important;
                font-weight: 500 !important;
            }

            .bg-white.rounded-xl.shadow-sm .bg-green-100.text-green-700 {
                padding: 0.2rem 0.4rem !important;
                border-radius: 4px !important;
                font-size: 0.625rem !important;
                font-weight: 500 !important;
            }

            /* Ultra-compact bottom section */
            .bg-white.rounded-xl.shadow-sm .mt-4.pt-3.border-t {
                margin-top: 0.75rem !important;
                padding-top: 0.6rem !important;
            }

            /* Ultra-compact total price */
            .bg-white.rounded-xl.shadow-sm .text-lg.font-semibold.text-primary-dark {
                font-size: 0.95rem !important;
                font-weight: 600 !important;
            }
        }

        /* Specific optimization for 320px width */
        @media (min-width: 320px) and (max-width: 320px) {
            .cart-item-padding {
                padding: 0.65rem !important;
            }

            /* Extra compact for 320px exactly */
            .bg-white.rounded-xl.shadow-sm .w-20.h-20 {
                width: 42px !important;
                height: 42px !important;
            }

            .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-end {
                margin-left: 52px !important;
            }

            /* Ultra-compact badges specifically for 320px */
            .bg-white.rounded-xl.shadow-sm .bg-red-100.text-red-600,
            .bg-white.rounded-xl.shadow-sm .bg-green-100.text-green-700 {
                padding: 0.15rem 0.3rem !important;
                font-size: 0.55rem !important;
                border-radius: 3px !important;
                margin: 0.1rem !important;
            }

            /* Compact discount row container for 320px */
            .bg-white.rounded-xl.shadow-sm .flex.justify-between.items-center {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 0.4rem !important;
            }

            /* Fix checkbox icon for 320px - size varies between breakpoints */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox {
                width: 17px !important;
                height: 17px !important;
            }

            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked::after {
                top: 1px !important;
                left: 4.5px !important;
                width: 4.5px !important;
                height: 8px !important;
                border-width: 0 1.5px 1.5px 0 !important;
            }
        }

        /* Medium Mobile (321px - 374px) - Improved compact layout */
        @media (min-width: 321px) and (max-width: 374px) {
            .cart-item-padding {
                padding: 0.75rem !important;
            }

            /* Improved compact image */
            .bg-white.rounded-xl.shadow-sm .w-20.h-20 {
                width: 44px !important;
                height: 44px !important;
                border-radius: 9px !important;
            }

            /* Better typography */
            .bg-white.rounded-xl.shadow-sm h3 {
                font-size: 0.82rem !important;
                line-height: 1.28 !important;
                margin-bottom: 0.28rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
                font-size: 0.88rem !important;
            }

            /* Better controls positioning */
            .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-end {
                margin-left: 55px !important;
                margin-top: 0.55rem !important;
            }

            /* Better quantity controls */
            .bg-white.rounded-xl.shadow-sm .quantity-control {
                width: 25px !important;
                height: 25px !important;
                font-size: 0.68rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .quantity-input {
                width: 29px !important;
                height: 25px !important;
                font-size: 0.78rem !important;
            }

            /* Better update button */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button {
                font-size: 0.68rem !important;
                padding: 0.28rem 0.55rem !important;
            }

            /* Better gaps */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4.mb-3 {
                gap: 0.55rem !important;
                margin-bottom: 0.78rem !important;
            }

            /* Better checkbox */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox {
                width: 17px !important;
                height: 17px !important;
                border-radius: 4px !important;
            }

            /* Fix checkbox icon for 17px checkbox (321px-374px) */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked::after {
                top: 1px !important;
                left: 4.5px !important;
                width: 4.5px !important;
                height: 8px !important;
                border-width: 0 1.5px 1.5px 0 !important;
            }

            /* Better delete button */
            .mobile-delete-btn button {
                width: 26px !important;
                height: 26px !important;
                font-size: 0.78rem !important;
            }

            /* Ultra-compact discount and savings badges for 320px */
            .bg-white.rounded-xl.shadow-sm .bg-red-100.text-red-600 {
                padding: 0.2rem 0.35rem !important;
                border-radius: 4px !important;
                font-size: 0.6rem !important;
                font-weight: 500 !important;
                line-height: 1.2 !important;
                white-space: nowrap !important;
            }

            .bg-white.rounded-xl.shadow-sm .bg-green-100.text-green-700 {
                padding: 0.2rem 0.35rem !important;
                border-radius: 4px !important;
                font-size: 0.6rem !important;
                font-weight: 500 !important;
                line-height: 1.2 !important;
                white-space: nowrap !important;
            }

            /* Ultra-compact bottom section for 320px */
            .bg-white.rounded-xl.shadow-sm .mt-4.pt-3.border-t {
                margin-top: 0.7rem !important;
                padding-top: 0.55rem !important;
            }

            /* Ultra-compact total price for 320px */
            .bg-white.rounded-xl.shadow-sm .text-lg.font-semibold.text-primary-dark {
                font-size: 0.95rem !important;
                font-weight: 600 !important;
                line-height: 1.3 !important;
            }

            /* Optimize discount row layout for 320px */
            .bg-white.rounded-xl.shadow-sm .flex.flex-wrap.gap-2 {
                gap: 0.3rem !important;
                flex-wrap: wrap !important;
            }

            /* Ensure badges don't overflow on 320px */
            .bg-white.rounded-xl.shadow-sm .inline-flex.items-center {
                max-width: 100% !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }
        }

        /* Regular Mobile (375px - 413px) - Standard mobile layout */
        @media (min-width: 375px) and (max-width: 413px) {
            .cart-item-padding {
                padding: 1rem !important;
            }

            /* Standard mobile image */
            .bg-white.rounded-xl.shadow-sm .w-20.h-20 {
                width: 56px !important;
                height: 56px !important;
                border-radius: 11px !important;
            }

            /* Standard mobile typography */
            .bg-white.rounded-xl.shadow-sm h3 {
                font-size: 0.9rem !important;
                line-height: 1.32 !important;
                margin-bottom: 0.35rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .text-primary.font-medium.text-lg {
                font-size: 0.95rem !important;
            }

            /* Standard mobile controls */
            .bg-white.rounded-xl.shadow-sm .flex.flex-col.md\:flex-row.justify-end {
                margin-left: 80px !important;
                margin-top: 0.65rem !important;
            }

            /* Standard quantity controls */
            .bg-white.rounded-xl.shadow-sm .quantity-control {
                width: 28px !important;
                height: 28px !important;
                font-size: 0.72rem !important;
            }

            .bg-white.rounded-xl.shadow-sm .quantity-input {
                width: 32px !important;
                height: 28px !important;
                font-size: 0.82rem !important;
            }

            /* Standard update button */
            .bg-white.rounded-xl.shadow-sm .flex.items-center.gap-4 button {
                font-size: 0.72rem !important;
                padding: 0.32rem 0.65rem !important;
            }

            /* Standard gaps */
            .bg-white.rounded-xl.shadow-sm .flex.items-start.gap-4.mb-3 {
                gap: 0.7rem !important;
                margin-bottom: 0.85rem !important;
            }

            /* Standard checkbox */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox {
                width: 19px !important;
                height: 19px !important;
                border-radius: 5px !important;
            }

            /* Fix checkbox icon for 19px checkbox (375px-413px) */
            .bg-white.rounded-xl.shadow-sm .cart-item-checkbox:checked::after {
                top: 1.5px !important;
                left: 5.5px !important;
                width: 5px !important;
                height: 9px !important;
                border-width: 0 1.8px 1.8px 0 !important;
            }

            /* Standard delete button */
            .mobile-delete-btn button {
                width: 30px !important;
                height: 30px !important;
                font-size: 0.85rem !important;
            }

            /* Standard discount and savings badges */
            .bg-white.rounded-xl.shadow-sm .bg-red-100.text-red-600 {
                padding: 0.3rem 0.5rem !important;
                border-radius: 5px !important;
                font-size: 0.7rem !important;
                font-weight: 500 !important;
            }

            .bg-white.rounded-xl.shadow-sm .bg-green-100.text-green-700 {
                padding: 0.3rem 0.5rem !important;
                border-radius: 5px !important;
                font-size: 0.7rem !important;
                font-weight: 500 !important;
            }

            /* Standard bottom section */
            .bg-white.rounded-xl.shadow-sm .mt-4.pt-3.border-t {
                margin-top: 0.9rem !important;
                padding-top: 0.7rem !important;
            }

            /* Standard total price */
            .bg-white.rounded-xl.shadow-sm .text-lg.font-semibold.text-primary-dark {
                font-size: 1.05rem !important;
                font-weight: 675 !important;
            }
        }
    }

    /* Hiệu ứng nhảy nhẹ cho phần tiết kiệm */
    @keyframes savingsBounce {
        0%, 100% { transform: translateY(0); }
        25% { transform: translateY(-3px); }
        50% { transform: translateY(0); }
        75% { transform: translateY(-2px); }
    }

    .savings-bounce {
        animation: savingsBounce 0.6s ease-in-out;
    }

    /* Đảm bảo nút checkout luôn hiển thị đúng CSS ngay từ đầu */
    #checkout-btn {
        /* Force hiển thị background gradient ngay lập tức */
        background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%) !important;
        color: white !important;
        border-radius: 12px !important;
        min-height: 56px !important;
        padding: 16px 24px !important;
        box-shadow:
            0 6px 20px rgba(243, 115, 33, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        font-family: 'Be Vietnam Pro', sans-serif !important;
        font-weight: 600 !important;
        font-size: 16px !important;
        text-decoration: none !important;
        position: relative !important;
        overflow: hidden !important;
    }

    /* Đảm bảo hiệu ứng hover vẫn hoạt động */
    #checkout-btn:hover {
        background: linear-gradient(135deg, #e67e22 0%, #d35400 50%, #b8460e 100%) !important;
        transform: translateY(-2px) scale(1.02) !important;
        box-shadow:
            0 8px 25px rgba(243, 115, 33, 0.35),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;
    }

    /* Responsive cho mobile */
    @media (max-width: 768px) {
        #checkout-btn {
            min-height: 52px !important;
            padding: 14px 20px !important;
            font-size: 15px !important;
        }
    }

    @media (max-width: 480px) {
        #checkout-btn {
            min-height: 48px !important;
            padding: 12px 16px !important;
            font-size: 14px !important;
        }
    }

    /* CSS cho hiệu ứng loading của nút checkout */
    #checkout-btn.loading {
        pointer-events: none !important;
        cursor: not-allowed !important;
        opacity: 0.9 !important;
        /* Giữ nguyên gradient background khi loading */
        background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%) !important;
    }

    #checkout-btn.loading:hover {
        /* Vô hiệu hóa hover effect khi loading */
        transform: none !important;
        background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%) !important;
    }

    /* Spinner animation cho loading */
    .checkout-spinner {
        display: inline-block;
        width: 18px;
        height: 18px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: checkout-spin 0.8s ease-in-out infinite;
    }

    @keyframes checkout-spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>

<?php
// Thêm script xử lý số lượng giỏ hàng
echo '<script src="' . BASE_URL . '/assets/js/cart-quantity-handler.js"></script>';
// Thêm script xử lý chọn sản phẩm
echo '<script src="' . BASE_URL . '/assets/js/cart-item-selection.js"></script>';
// Thêm script cập nhật header
echo '<script src="' . BASE_URL . '/assets/js/cart-header-update.js"></script>';
?>

<!-- Script xử lý hiệu ứng loading cho nút checkout -->
<script>
/**
 * Xử lý hiệu ứng loading cho nút "Tiến hành thanh toán"
 */
function initCheckoutButtonLoading() {
    const checkoutBtn = document.getElementById('checkout-btn');

    if (!checkoutBtn) {
        console.log('Checkout button not found');
        return;
    }

    // Lưu nội dung gốc của nút
    const originalContent = checkoutBtn.innerHTML;

    // Thêm event listener cho nút checkout với priority cao (capture phase)
    checkoutBtn.addEventListener('click', function(e) {
        console.log('Checkout button clicked - starting loading effect');

        // Kiểm tra xem nút có bị disable không (href = '#')
        const href = this.getAttribute('href');
        if (href === '#' || !href) {
            console.log('Checkout button is disabled, not showing loading');
            return; // Để event listener khác xử lý thông báo lỗi
        }

        // Ngăn chặn navigation ngay lập tức
        e.preventDefault();
        e.stopPropagation(); // Ngăn chặn các event listener khác

        // Bắt đầu hiệu ứng loading
        startCheckoutLoading(this, originalContent, href);
    }, true); // true = capture phase, sẽ chạy trước các event listener khác
}

/**
 * Bắt đầu hiệu ứng loading cho nút checkout
 */
function startCheckoutLoading(button, originalContent, targetUrl) {
    console.log('Starting checkout loading animation...');

    // Thêm class loading và disable nút
    button.classList.add('loading');

    // Thay đổi nội dung nút thành loading state
    button.innerHTML = '<span class="checkout-spinner"></span> Đang xử lý';

    // Sau 900ms, chuyển đến trang checkout (KHÔNG khôi phục nội dung)
    setTimeout(() => {
        console.log('Loading complete, redirecting to:', targetUrl);

        // Chuyển đến trang checkout ngay lập tức mà không khôi phục nội dung
        // Hiệu ứng loading sẽ tiếp tục cho đến khi trang mới được load
        window.location.href = targetUrl;
    }, 900); // 900ms như yêu cầu
}

// Khởi tạo khi DOM ready với delay để đảm bảo các script khác đã load
document.addEventListener('DOMContentLoaded', function() {
    // Delay nhỏ để đảm bảo các script khác đã được khởi tạo
    setTimeout(() => {
        initCheckoutButtonLoading();
        console.log('Checkout button loading handler initialized');
    }, 100);
});
</script>

<!-- Script Debug Header Modal Issue -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Cart Debug Script Loaded');

    // Debug thông tin header
    function debugHeaderInfo() {
        const headers = document.querySelectorAll('.premium-header, .luxury-header, .three-tier-header, .elegant-header');
        console.log('📋 Header Elements Found:', headers.length);

        headers.forEach((header, index) => {
            const computedStyle = window.getComputedStyle(header);
            console.log(`📋 Header ${index + 1}:`, {
                element: header,
                className: header.className,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex,
                top: computedStyle.top,
                transform: computedStyle.transform,
                visibility: computedStyle.visibility,
                display: computedStyle.display,
                isScrolled: header.classList.contains('scrolled'),
                boundingRect: header.getBoundingClientRect()
            });
        });
    }

    // Debug thông tin modal
    function debugModalInfo() {
        const modal = document.getElementById('delete-confirmation-modal');
        if (modal) {
            const computedStyle = window.getComputedStyle(modal);
            console.log('🔍 Modal Info:', {
                element: modal,
                className: modal.className,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex,
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                opacity: computedStyle.opacity,
                isActive: modal.classList.contains('active'),
                boundingRect: modal.getBoundingClientRect()
            });
        } else {
            console.log('❌ Modal not found');
        }
    }

    // Debug thông tin body
    function debugBodyInfo() {
        const body = document.body;
        const computedStyle = window.getComputedStyle(body);
        console.log('📋 Body Info:', {
            className: body.className,
            overflow: computedStyle.overflow,
            hasModalActive: body.classList.contains('modal-active'),
            scrollTop: window.pageYOffset || document.documentElement.scrollTop,
            scrollHeight: document.documentElement.scrollHeight,
            clientHeight: document.documentElement.clientHeight
        });
    }

    // Debug z-index của tất cả elements
    function debugZIndexLayers() {
        const allElements = document.querySelectorAll('*');
        const zIndexElements = [];

        allElements.forEach(el => {
            const style = window.getComputedStyle(el);
            const zIndex = style.zIndex;
            if (zIndex !== 'auto' && zIndex !== '0') {
                zIndexElements.push({
                    element: el,
                    zIndex: parseInt(zIndex),
                    tagName: el.tagName,
                    id: el.id,
                    className: el.className,
                    position: style.position
                });
            }
        });

        // Sắp xếp theo z-index
        zIndexElements.sort((a, b) => b.zIndex - a.zIndex);
        console.log('📊 Z-Index Layers (highest first):', zIndexElements.slice(0, 20)); // Top 20

        // Log chi tiết từng element
        zIndexElements.slice(0, 10).forEach((item, index) => {
            console.log(`📊 Z-Index #${index + 1}:`, {
                zIndex: item.zIndex,
                tagName: item.tagName,
                id: item.id,
                className: item.className,
                position: item.position,
                element: item.element
            });
        });
    }

    // Monitor scroll events
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            console.log('📜 Scroll Event:', {
                scrollTop: scrollTop,
                headerScrolled: document.querySelector('.premium-header, .luxury-header, .three-tier-header, .elegant-header')?.classList.contains('scrolled')
            });
        }, 100);
    });

    // Monitor modal events
    const modal = document.getElementById('delete-confirmation-modal');
    if (modal) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    console.log('🔄 Modal Class Changed:', {
                        oldValue: mutation.oldValue,
                        newValue: modal.className,
                        isActive: modal.classList.contains('active'),
                        timestamp: new Date().toISOString()
                    });

                    // Debug khi modal active
                    if (modal.classList.contains('active')) {
                        setTimeout(() => {
                            console.log('🔍 Modal Activated - Full Debug:');
                            debugHeaderInfo();
                            debugModalInfo();
                            debugBodyInfo();
                            debugZIndexLayers();
                        }, 100);
                    }
                }
            });
        });

        observer.observe(modal, {
            attributes: true,
            attributeOldValue: true,
            attributeFilter: ['class']
        });
    }

    // Monitor body class changes
    const bodyObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                console.log('🔄 Body Class Changed:', {
                    oldValue: mutation.oldValue,
                    newValue: document.body.className,
                    hasModalActive: document.body.classList.contains('modal-active'),
                    timestamp: new Date().toISOString()
                });
            }
        });
    });

    bodyObserver.observe(document.body, {
        attributes: true,
        attributeOldValue: true,
        attributeFilter: ['class']
    });

    // Initial debug
    console.log('🚀 Initial Debug Info:');
    debugHeaderInfo();
    debugModalInfo();
    debugBodyInfo();
    debugZIndexLayers();

    // Expose debug functions globally
    window.debugCartModal = {
        header: debugHeaderInfo,
        modal: debugModalInfo,
        body: debugBodyInfo,
        zIndex: debugZIndexLayers,
        all: function() {
            debugHeaderInfo();
            debugModalInfo();
            debugBodyInfo();
            debugZIndexLayers();
        }
    };

    console.log('💡 Debug functions available: window.debugCartModal.all()');
});
</script>

<script>
// Force set padding for cart items to match order summary on desktop
document.addEventListener('DOMContentLoaded', function() {
    function adjustCartItemPadding() {
        // Only apply on desktop (min-width: 577px)
        if (window.innerWidth >= 577) {
            const cartItems = document.querySelectorAll('.cart-item-padding');
            cartItems.forEach(item => {
                item.style.setProperty('padding', '1.5rem', 'important');
                item.style.setProperty('padding-top', '1.5rem', 'important');
                item.style.setProperty('padding-right', '1.5rem', 'important');
                item.style.setProperty('padding-bottom', '1.5rem', 'important');
                item.style.setProperty('padding-left', '1.5rem', 'important');
            });
            console.log('Cart item padding adjusted to 1.5rem for', cartItems.length, 'items');
        }
    }

    // Apply immediately
    adjustCartItemPadding();

    // Apply on window resize
    window.addEventListener('resize', adjustCartItemPadding);
});
</script>

<?php
?>

<!-- Modal Xác nhận xóa sản phẩm -->
<div id="delete-confirmation-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 overflow-hidden transform transition-all">
        <div class="relative">
            <!-- Header -->
            <div class="bg-red-50 p-6">
                <div class="flex items-center justify-center">
                    <div class="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
                        <i class="fas fa-trash-alt text-red-500 text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Body -->
            <div class="p-6">
                <h3 id="delete-modal-title" class="text-xl font-bold text-gray-800 text-center mb-4">Xác nhận xóa sản phẩm</h3>
                <p id="delete-modal-message" class="text-gray-600 text-center mb-6">Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?</p>

                <!-- Product info (only shown when deleting a single product) -->
                <div id="delete-product-info" class="bg-gray-50 rounded-lg p-4 mb-6 hidden">
                    <div class="flex items-center gap-3">
                        <div id="delete-product-image" class="w-16 h-16 bg-gray-200 rounded-md overflow-hidden flex-shrink-0">
                            <img src="" alt="" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h4 id="delete-product-name" class="font-medium text-gray-800"></h4>
                            <div class="flex justify-between mt-1">
                                <span class="text-sm text-gray-500">Số lượng: <span id="delete-product-quantity" class="font-medium"></span></span>
                                <span id="delete-product-price" class="text-primary font-medium"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex gap-3">
                    <button id="delete-cancel-btn" class="flex-1 py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200 font-medium">
                        Hủy bỏ
                    </button>
                    <button id="delete-confirm-btn" class="flex-1 py-3 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition duration-200 font-medium flex items-center justify-center gap-2">
                        <span id="delete-btn-text">Xác nhận xóa</span>
                    </button>
                </div>
            </div>

            <!-- Close button -->
            <button class="absolute top-4 right-4 text-gray-400 hover:text-gray-600" id="delete-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
// Hàm hiển thị thông báo đơn giản với ID simple-notification
function showSimpleNotification(message, type = 'success', duration = 5000) {
    console.log('Hiển thị thông báo đơn giản:', message, type);

    // Xóa thông báo cũ nếu có
    const oldNotification = document.getElementById('simple-notification');
    if (oldNotification) {
        oldNotification.remove();
    }

    // Tạo thông báo mới
    const notification = document.createElement('div');
    notification.id = 'simple-notification';
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.left = '0';
    notification.style.right = '0';
    notification.style.margin = '0 auto';
    notification.style.zIndex = '9999';
    notification.style.backgroundColor = type === 'success' ? '#10B981' : (type === 'error' ? '#EF4444' : '#3B82F6');
    notification.style.color = 'white';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.justifyContent = 'center';
    notification.style.width = 'fit-content';
    notification.style.maxWidth = '500px';
    notification.style.minWidth = '300px';
    notification.style.textAlign = 'center';
    notification.style.animation = 'fadeInCenter 0.3s ease-out forwards';

    // Tạo nội dung thông báo
    notification.innerHTML = `
      <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}" style="margin-right: 10px;"></i>
      <span style="flex: 1;">${message}</span>
      <button onclick="this.parentElement.remove();" style="background: none; border: none; color: white; cursor: pointer; margin-left: 10px;">
        <i class="fas fa-times"></i>
      </button>
    `;

    // Thêm thông báo vào body
    document.body.appendChild(notification);

    // Tự động đóng thông báo sau thời gian đã định
    setTimeout(function() {
      if (notification && notification.parentNode) {
        notification.remove();
      }
    }, duration);

    return notification;
}

// CSS cho animation
const style = document.createElement('style');
style.textContent = `
    #simple-notification {
        box-sizing: border-box;
    }

    @keyframes fadeInCenter {
        0% {
            opacity: 0;
            transform: translateY(-20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }
    /* Premium Checkout Button - Inspired by search-submit-btn */
    .premium-checkout-btn {
        position: relative;
        min-height: 56px;
        padding: 16px 24px;
        border-radius: 12px;
        background: linear-gradient(135deg, #f37321 0%, #e67e22 50%, #d35400 100%);
        border: none;
        color: white;
        cursor: pointer;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        box-shadow:
            0 6px 20px rgba(243, 115, 33, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        font-family: 'Be Vietnam Pro', sans-serif;
    }

    .premium-checkout-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .premium-checkout-btn:hover {
        background: linear-gradient(135deg, #e67e22 0%, #d35400 50%, #b8460e 100%);
        transform: translateY(-2px) scale(1.02);
        box-shadow:
            0 8px 25px rgba(243, 115, 33, 0.35),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    .premium-checkout-btn:hover::before {
        left: 100%;
    }

    .premium-checkout-btn:active {
        transform: translateY(0) scale(0.98);
        transition: all 0.1s ease;
    }

    .premium-checkout-btn i {
        font-size: 18px;
        transition: transform 0.3s ease;
    }

    .premium-checkout-btn:hover i {
        transform: scale(1.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .premium-checkout-btn {
            min-height: 52px;
            padding: 14px 20px;
            font-size: 15px;
        }
    }

    @media (max-width: 480px) {
        .premium-checkout-btn {
            min-height: 48px;
            padding: 12px 16px;
            font-size: 14px;
        }
    }
`;
document.head.appendChild(style);
</script>

<script>
// Skeleton Loading Implementation - Final Version
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting skeleton removal process...');

    // Xóa skeleton và hiển thị nội dung thật
    function showRealContent() {
        console.log('Showing real content...');

        // Xóa skeleton cart items
        const cartSkeleton = document.getElementById('cart-skeleton-temp');
        if (cartSkeleton) {
            cartSkeleton.remove();
        }

        // Hiển thị cart items thật
        const cartSection = document.getElementById('cart-items-section');
        if (cartSection) {
            cartSection.style.display = '';
        }

        // Xóa skeleton order summary
        const orderSkeleton = document.getElementById('order-skeleton-temp');
        if (orderSkeleton) {
            orderSkeleton.remove();
        }

        // Hiển thị order summary thật
        const orderSection = document.getElementById('order-summary-section');
        if (orderSection) {
            const realContent = orderSection.querySelector('.bg-white.rounded-xl');
            if (realContent) {
                realContent.style.display = '';
            }
        }
    }

    // Đợi một chút để CSS load xong rồi hiển thị nội dung thật
    setTimeout(function() {
        showRealContent();
    }, 1500); // 1.5 giây để thấy rõ hiệu ứng skeleton
});

</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
