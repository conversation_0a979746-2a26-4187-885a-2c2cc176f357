<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Filter Modal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        /* Mock sidebar for testing */
        .sidebar-filters {
            display: none;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .filter-section h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .filter-option {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .filter-option input {
            margin-right: 8px;
        }
        
        .price-input {
            width: 100px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 0 5px;
        }
        
        .price-preset {
            background: white;
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            border-radius: 6px;
            margin: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .price-preset:hover {
            background: #f3f4f6;
        }
        
        .price-preset.from-orange-500 {
            background: #f97316;
            color: white;
            border-color: #f97316;
        }
        
        /* Show mobile filter on all screen sizes for testing */
        .mobile-filter-container {
            display: block !important;
        }
        
        .log {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Mobile Filter Modal</h1>
        <p>Test tính năng AJAX filtering trong mobile modal mà không cần reload trang.</p>
        
        <div class="test-section">
            <h2>📱 Mobile Filter Button</h2>
            <p>Nút filter sẽ xuất hiện ở đây (hiển thị trên tất cả kích thước màn hình để test):</p>
            
            <!-- Mock products header where mobile filter button will be inserted -->
            <div class="px-8 py-6 bg-white border-b border-gray-200">
                <h3>Danh sách sản phẩm</h3>
                <!-- Mobile filter button sẽ được chèn vào đây -->
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎛️ Mock Sidebar Filters</h2>
            <p>Sidebar giả lập để test chức năng filter:</p>
            
            <!-- Mock sidebar filters -->
            <div class="sidebar-filters">
                <div class="filter-section">
                    <h3>Danh mục</h3>
                    <div class="filter-option">
                        <input type="checkbox" name="category[]" value="1" id="cat_1">
                        <label for="cat_1">Bàn làm việc</label>
                    </div>
                    <div class="filter-option">
                        <input type="checkbox" name="category[]" value="2" id="cat_2">
                        <label for="cat_2">Ghế văn phòng</label>
                    </div>
                    <div class="filter-option">
                        <input type="checkbox" name="category[]" value="3" id="cat_3">
                        <label for="cat_3">Tủ tài liệu</label>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Khoảng giá</h3>
                    <div style="margin-bottom: 10px;">
                        <input type="text" class="price-input" data-price-field="min" placeholder="Từ">
                        <input type="text" class="price-input" data-price-field="max" placeholder="Đến">
                    </div>
                    <div>
                        <button class="price-preset" data-min="0" data-max="5000000">Dưới 5 triệu</button>
                        <button class="price-preset" data-min="5000000" data-max="10000000">5-10 triệu</button>
                        <button class="price-preset" data-min="10000000" data-max="20000000">10-20 triệu</button>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Khuyến mãi</h3>
                    <div class="filter-option">
                        <input type="checkbox" name="promotion[]" value="sale" id="promo_sale">
                        <label for="promo_sale">Đang giảm giá</label>
                    </div>
                    <div class="filter-option">
                        <input type="checkbox" name="promotion[]" value="flash_sale" id="promo_flash">
                        <label for="promo_flash">Flash Sale</label>
                    </div>
                    <div class="filter-option">
                        <input type="checkbox" name="promotion[]" value="featured" id="promo_featured">
                        <label for="promo_featured">Sản phẩm nổi bật</label>
                    </div>
                </div>
                
                <!-- Mock apply/reset buttons (sẽ bị ẩn trong modal) -->
                <div style="margin-top: 20px;">
                    <button id="applyFilters" class="test-button">Áp dụng bộ lọc</button>
                    <button id="resetFiltersBtn" class="test-button">Đặt lại bộ lọc</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Controls</h2>
            <button class="test-button" onclick="testMobileModal()">Test Mobile Modal</button>
            <button class="test-button" onclick="testFilterData()">Test Filter Data Collection</button>
            <button class="test-button" onclick="testAjaxFilter()">Test AJAX Filter Integration</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            
            <div id="testLog" class="log"></div>
        </div>
    </div>
    
    <!-- Mock AJAX Filter for testing -->
    <script>
        // Mock AJAX Filter class for testing
        window.ajaxFilter = {
            loadProducts: function(filterData, page, updateHistory, fastMode) {
                log('🚀 AJAX Filter: loadProducts called');
                log('📊 Filter Data: ' + JSON.stringify(filterData, null, 2));
                log('📄 Page: ' + page);
                log('🔄 Update History: ' + updateHistory);
                log('⚡ Fast Mode: ' + fastMode);
                
                // Simulate async operation
                return new Promise((resolve) => {
                    setTimeout(() => {
                        log('✅ Products loaded successfully (simulated)');
                        resolve();
                    }, 1000);
                });
            },
            
            handleResetFilters: function() {
                log('🔄 AJAX Filter: handleResetFilters called');
                log('✅ Filters reset successfully (simulated)');
            }
        };
        
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        function testMobileModal() {
            log('🧪 Testing mobile modal functionality...');
            if (window.mobileFilterModal) {
                log('✅ Mobile Filter Modal instance found');
                log('📱 Filter count: ' + window.mobileFilterModal.activeFiltersCount);
            } else {
                log('❌ Mobile Filter Modal not found');
            }
        }
        
        function testFilterData() {
            log('🧪 Testing filter data collection...');
            if (window.mobileFilterModal && typeof window.mobileFilterModal.collectFilterData === 'function') {
                const data = window.mobileFilterModal.collectFilterData();
                log('📊 Collected filter data: ' + JSON.stringify(data, null, 2));
            } else {
                log('❌ collectFilterData method not available');
            }
        }
        
        function testAjaxFilter() {
            log('🧪 Testing AJAX Filter integration...');
            if (window.ajaxFilter) {
                log('✅ AJAX Filter available');
                const mockData = { categories: ['1'], promotions: ['sale'] };
                window.ajaxFilter.loadProducts(mockData, 1, true, false);
            } else {
                log('❌ AJAX Filter not available');
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 DOM loaded, initializing test environment...');
            
            // Wait a bit for mobile filter modal to initialize
            setTimeout(() => {
                if (window.mobileFilterModal) {
                    log('✅ Mobile Filter Modal initialized successfully');
                } else {
                    log('❌ Mobile Filter Modal failed to initialize');
                }
            }, 500);
        });
    </script>
    
    <!-- Include the actual mobile filter modal script -->
    <script src="assets/js/mobile-filter-modal.js"></script>
</body>
</html>
