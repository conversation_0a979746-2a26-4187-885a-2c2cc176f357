<?php
/**
 * AJAX Products Endpoint
 * Handles AJAX requests for product pagination and filtering
 * Returns JSON response with products HTML and pagination data
 */

// Set content type to JSON
header('Content-Type: application/json');

// Include init để có các hàm cần thiết
require_once 'includes/init.php';

// Only allow AJAX requests
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['error' => 'Only AJAX requests allowed']);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // L<PERSON>y từ khóa tìm kiếm và các tham số filter (giống như products.php)
    $keyword = isset($_GET['keyword']) ? sanitize($_GET['keyword']) : '';

    // Xử lý multiple categories
    $category_ids = [];
    if (isset($_GET['category']) && is_array($_GET['category'])) {
        $category_ids = array_map('intval', $_GET['category']);
        $category_ids = array_filter($category_ids, function ($id) {
            return $id > 0;
        });
    } elseif (isset($_GET['category']) && !is_array($_GET['category'])) {
        $single_id = (int) $_GET['category'];
        if ($single_id > 0) {
            $category_ids = [$single_id];
        }
    }

    $price_min = isset($_GET['price_min']) && $_GET['price_min'] !== '' ? (float) $_GET['price_min'] : null;
    $price_max = isset($_GET['price_max']) && $_GET['price_max'] !== '' ? (float) $_GET['price_max'] : null;
    $sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';

    // Xử lý multiple promotion filters
    $promotion_filters = [];
    if (isset($_GET['promotion'])) {
        if (is_array($_GET['promotion'])) {
            $promotion_filters = array_map('sanitize', $_GET['promotion']);
        } else {
            $promotion_filters = [sanitize($_GET['promotion'])];
        }
    }

    // Phân trang
    $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
    $page = max(1, $page); // Ensure page is at least 1

    // Xử lý items per page
    $allowed_limits = [12, 24, 36, 48];
    if (isset($_GET['items_per_page'])) {
        $items_per_page = (int) $_GET['items_per_page'];
    } elseif (isset($_COOKIE['products_items_per_page']) && in_array((int) $_COOKIE['products_items_per_page'], $allowed_limits)) {
        $items_per_page = (int) $_COOKIE['products_items_per_page'];
    } else {
        $items_per_page = ITEMS_PER_PAGE;
    }

    $limit = in_array($items_per_page, $allowed_limits) ? $items_per_page : ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;

    // Thiết lập tùy chọn sắp xếp
    $sort_options = [];
    $sort_label = '';
    switch ($sort) {
        case 'price_asc':
            $sort_options = ['by' => 'price', 'order' => 'ASC'];
            $sort_label = 'Giá tăng dần';
            break;
        case 'price_desc':
            $sort_options = ['by' => 'price', 'order' => 'DESC'];
            $sort_label = 'Giá giảm dần';
            break;
        case 'name_asc':
            $sort_options = ['by' => 'name', 'order' => 'ASC'];
            $sort_label = 'Tên A-Z';
            break;
        case 'name_desc':
            $sort_options = ['by' => 'name', 'order' => 'DESC'];
            $sort_label = 'Tên Z-A';
            break;
        case 'newest':
        default:
            $sort_options = ['by' => 'created_at', 'order' => 'DESC'];
            $sort_label = 'Mới nhất';
            break;
    }

    // Lấy danh sách sản phẩm
    $products = get_products_with_filters(
        $category_ids,
        $promotion_filters,
        $limit,
        $offset,
        $keyword,
        1, // status
        $price_min,
        $price_max,
        $sort_options
    );

    $total_products = count_products_with_filters(
        $category_ids,
        $promotion_filters,
        $keyword,
        1, // status
        $price_min,
        $price_max
    );

    $total_pages = ceil($total_products / $limit);

    // Generate products HTML
    ob_start();
    if (!empty($products)): ?>
        <?php foreach ($products as $product): ?>
            <div class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                <div class="product-image-wrapper relative">
                    <a href="<?php echo get_product_url($product['slug']); ?>" class="block product-image">
                        <?php if ($product['image']): ?>
                            <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                                alt="<?php echo $product['name']; ?>"
                                class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                        <?php else: ?>
                            <div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                                <i class="fas fa-image text-gray-500 text-4xl"></i>
                            </div>
                        <?php endif; ?>
                    </a>

                    <!-- Premium Sale Badge -->
                    <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                        <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                        <div class="premium-sale-badge">
                            <div class="badge-content">
                                <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                                <span class="sale-text">SALE</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="product-info-wrapper flex flex-col flex-grow">
                    <!-- Product Title - Max 2 lines -->
                    <div class="product-title mb-3">
                        <a href="<?php echo get_product_url($product['slug']); ?>" class="block">
                            <h3 class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                <?php echo $product['name']; ?>
                            </h3>
                        </a>
                    </div>

                    <!-- Premium Price Section -->
                    <div class="premium-price-section">
                        <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                            <!-- Liên hệ báo giá - Giống regular price container -->
                            <div class="contact-price-container">
                                <div class="contact-price-main">
                                    GỌI NGAY
                                </div>
                                <div class="contact-price-subtitle">
                                    Liên hệ báo giá
                                </div>
                            </div>
                        <?php elseif ($product['sale_price'] > 0): ?>
                            <!-- Sản phẩm có giá sale -->
                            <div class="price-container">
                                <div class="original-price">
                                    <?php echo format_currency($product['price']); ?></div>
                                <div class="sale-price">
                                    <?php echo format_currency($product['sale_price']); ?></div>
                            </div>
                        <?php else: ?>
                            <!-- Sản phẩm giá thường -->
                            <div class="regular-price-container">
                                <div class="price-label">Giá bán</div>
                                <div class="main-price"><?php echo format_currency($product['price']); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Rating and Sales Section -->
                    <div class="product-rating-sales">
                        <div class="rating-section">
                            <div class="stars">
                                <?php
                                // Hiển thị rating từ database
                                $rating = isset($product['rating']) ? (int)$product['rating'] : 5;
                                for ($i = 1; $i <= 5; $i++):
                                ?>
                                    <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-text"><?php echo number_format($rating, 1); ?></span>
                        </div>
                        <div class="sales-section">
                            <i class="fas fa-shopping-cart"></i>
                            <span><?php echo number_format(isset($product['sold']) ? (int)$product['sold'] : 0); ?> đã bán</span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-span-full py-16 text-center">
            <div class="max-w-2xl mx-auto">
                <!-- Enhanced Empty State -->
                <div class="bg-gradient-to-br from-orange-50/50 to-amber-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-sm">
                    <!-- Animated Icon -->
                    <div class="relative mb-6">
                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-box-open text-orange-400 text-2xl"></i>
                        </div>
                        <!-- Pulse animation -->
                        <div class="absolute inset-0 w-20 h-20 mx-auto bg-orange-200/30 rounded-full animate-ping"></div>
                    </div>

                    <!-- Main Message -->
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        Không có sản phẩm trong bộ lọc này
                    </h3>

                    <!-- Detailed Description -->
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Không có sản phẩm nào phù hợp với các tiêu chí lọc hiện tại
                    </p>

                    <a href="<?php echo BASE_URL; ?>/products.php"
                       class="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Xem tất cả sản phẩm
                    </a>
                </div>
            </div>
        </div>
    <?php endif;
    $products_html = ob_get_clean();

    // Generate pagination HTML
    ob_start();
    if ($total_pages > 1):
        // Build URL parameters for pagination
        $url_params = [];
        if (!empty($keyword)) $url_params['keyword'] = $keyword;
        if (!empty($category_ids)) {
            if (count($category_ids) == 1) {
                $url_params['category'] = $category_ids[0];
            } else {
                foreach ($category_ids as $index => $cat_id) {
                    $url_params['category[' . $index . ']'] = $cat_id;
                }
            }
        }
        if (!empty($promotion_filters)) {
            if (count($promotion_filters) == 1) {
                $url_params['promotion'] = $promotion_filters[0];
            } else {
                foreach ($promotion_filters as $index => $promo) {
                    $url_params['promotion[' . $index . ']'] = $promo;
                }
            }
        }
        if ($price_min) $url_params['price_min'] = $price_min;
        if ($price_max) $url_params['price_max'] = $price_max;
        if ($sort && $sort !== 'newest') $url_params['sort'] = $sort;
        if (isset($_GET['items_per_page'])) $url_params['items_per_page'] = $_GET['items_per_page'];

        function build_ajax_pagination_url($page_num, $params) {
            $params['page'] = $page_num;
            return BASE_URL . '/products.php?' . http_build_query($params);
        }
        ?>
        
        <div class="pagination-section mt-12">
            <!-- Results Summary -->
            <div class="pagination-summary">
                <div class="results-info">
                    <span class="results-text">
                        Hiển thị <strong><?php echo (($page - 1) * $limit) + 1; ?></strong> -
                        <strong><?php echo min($page * $limit, $total_products); ?></strong>
                        trong tổng số <strong><?php echo number_format($total_products); ?></strong> sản phẩm
                    </span>
                </div>
            </div>

            <!-- Professional Pagination Navigation -->
            <nav class="pagination-nav" aria-label="Điều hướng trang">
                <ul class="pagination-list">
                    <!-- Previous Button -->
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link ajax-pagination-link"
                                href="<?php echo build_ajax_pagination_url($page - 1, $url_params); ?>"
                                data-page="<?php echo $page - 1; ?>"
                                aria-label="Trang trước">
                                <i class="fas fa-chevron-left"></i>
                                <span class="page-text">Trước</span>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link" aria-disabled="true">
                                <i class="fas fa-chevron-left"></i>
                                <span class="page-text">Trước</span>
                            </span>
                        </li>
                    <?php endif; ?>

                    <?php
                    // Smart pagination logic
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);
                    ?>

                    <!-- First page + ellipsis if needed -->
                    <?php if ($start_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link ajax-pagination-link"
                                href="<?php echo build_ajax_pagination_url(1, $url_params); ?>"
                                data-page="1"
                                aria-label="Trang 1">1</a>
                        </li>
                        <?php if ($start_page > 2): ?>
                            <li class="page-item disabled">
                                <span class="page-link ellipsis">...</span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Visible page range -->
                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link ajax-pagination-link"
                                href="<?php echo build_ajax_pagination_url($i, $url_params); ?>"
                                data-page="<?php echo $i; ?>"
                                aria-label="Trang <?php echo $i; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <!-- Last page + ellipsis if needed -->
                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <li class="page-item disabled">
                                <span class="page-link ellipsis">...</span>
                            </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link ajax-pagination-link"
                                href="<?php echo build_ajax_pagination_url($total_pages, $url_params); ?>"
                                data-page="<?php echo $total_pages; ?>"
                                aria-label="Trang <?php echo $total_pages; ?>"><?php echo $total_pages; ?></a>
                        </li>
                    <?php endif; ?>

                    <!-- Next Button -->
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link ajax-pagination-link"
                                href="<?php echo build_ajax_pagination_url($page + 1, $url_params); ?>"
                                data-page="<?php echo $page + 1; ?>"
                                aria-label="Trang sau">
                                <span class="page-text">Sau</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link" aria-disabled="true">
                                <span class="page-text">Sau</span>
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif;
    $pagination_html = ob_get_clean();

    // Return JSON response
    $response = [
        'success' => true,
        'data' => [
            'products_html' => $products_html,
            'pagination_html' => $pagination_html,
            'total_products' => $total_products,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'products_count' => count($products),
            'showing_from' => (($page - 1) * $limit) + 1,
            'showing_to' => min($page * $limit, $total_products),
            'sort_label' => $sort_label
        ]
    ];

    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
