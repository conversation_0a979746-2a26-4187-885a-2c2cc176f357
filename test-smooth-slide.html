<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Smooth Slide Animation</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            height: 200vh; /* Make page scrollable */
        }

        /* Test Header */
        .test-header {
            position: sticky;
            top: 0;
            width: 100%;
            z-index: 1000;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Top Bar */
        .test-top-bar {
            background: #202834;
            color: white;
            padding: 8px 0;
            text-align: center;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translateY(0);
            opacity: 1;
            transition: 
                transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Main Header */
        .test-main-header {
            background: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Scrolled State */
        .test-header.scrolled .test-top-bar {
            transform: translateY(-100%);
            opacity: 0;
            pointer-events: none;
        }

        .test-header.scrolled .test-main-header {
            background: #202834;
            color: white;
        }

        /* Smooth Slide Down Animation */
        .test-header:not(.scrolled) .test-top-bar {
            animation: smooth-slide-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes smooth-slide-down {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Content */
        .content {
            padding: 40px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .section {
            margin-bottom: 60px;
            padding: 40px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        /* Control Panel */
        .control-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
        }

        .control-panel button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }

        .control-panel button:hover {
            background: #0056b3;
        }

        /* Comparison Styles */
        .spring-animation {
            animation: spring-down 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
        }

        @keyframes spring-down {
            0% {
                transform: translateY(-100%) scaleY(0.8);
            }
            60% {
                transform: translateY(5%) scaleY(1.02);
            }
            80% {
                transform: translateY(-2%) scaleY(0.99);
            }
            100% {
                transform: translateY(0) scaleY(1);
            }
        }
    </style>
</head>
<body>
    <header class="test-header" id="testHeader">
        <div class="test-top-bar" id="testTopBar">
            📞 097.277.4646 | 📧 <EMAIL> | 📍 Số 91,93 Ngõ 85, đường Đức Diễn
        </div>
        <div class="test-main-header">
            <div>🏠 <strong>Nội Thất Bàng Vũ</strong></div>
            <div>🔍 Tìm kiếm sản phẩm...</div>
            <div>👤 Tài khoản | 🛒 Giỏ hàng</div>
        </div>
    </header>

    <div class="content">
        <h1>Test Smooth Slide Animation</h1>
        <p>Cuộn xuống để ẩn Top Bar, sau đó cuộn lên đầu trang để xem hiệu ứng trượt xuống mượt mà.</p>

        <div class="section">
            <h2>So sánh hiệu ứng</h2>
            <p><strong>Hiệu ứng cũ (Spring/Bounce):</strong> Có hiệu ứng nhảy lên nhảy xuống, tạo cảm giác không tự nhiên.</p>
            <p><strong>Hiệu ứng mới (Smooth Slide):</strong> Trượt xuống mượt mà, tự nhiên và professional.</p>
        </div>

        <div class="section">
            <h2>Đặc điểm hiệu ứng mới</h2>
            <ul>
                <li>✅ Trượt xuống mượt mà từ trên xuống</li>
                <li>✅ Không có hiệu ứng nhảy/bounce</li>
                <li>✅ Timing 0.5s - nhanh và responsive</li>
                <li>✅ Easing function mượt mà: cubic-bezier(0.25, 0.46, 0.45, 0.94)</li>
                <li>✅ Opacity transition đồng bộ</li>
            </ul>
        </div>

        <div class="section">
            <h2>Hướng dẫn test</h2>
            <ol>
                <li>Cuộn xuống để ẩn Top Bar (thanh màu đen)</li>
                <li>Cuộn lên đầu trang để xem Top Bar hiện lại</li>
                <li>Quan sát hiệu ứng trượt xuống mượt mà</li>
                <li>Sử dụng control panel để so sánh với hiệu ứng cũ</li>
            </ol>
        </div>

        <div class="section">
            <h2>Nội dung mẫu</h2>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>

        <div class="section">
            <h2>Thêm nội dung để test scroll</h2>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
            <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
        </div>
    </div>

    <div class="control-panel">
        <h4>🎛️ Control Panel</h4>
        <button onclick="scrollToTop()">⬆️ Scroll to Top</button>
        <button onclick="scrollToMiddle()">⬇️ Scroll to Middle</button>
        <button onclick="toggleAnimation()">🔄 Toggle Animation</button>
        <div id="animationType">Current: Smooth Slide</div>
    </div>

    <script>
        let isScrolled = false;
        let useSpringAnimation = false;
        const header = document.getElementById('testHeader');
        const topBar = document.getElementById('testTopBar');
        const animationType = document.getElementById('animationType');

        // Scroll handling
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const shouldBeScrolled = scrollTop > 10;

            if (shouldBeScrolled !== isScrolled) {
                isScrolled = shouldBeScrolled;
                
                if (shouldBeScrolled) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                    
                    // Trigger animation
                    if (useSpringAnimation) {
                        topBar.classList.remove('spring-animation');
                        void topBar.offsetWidth; // Force reflow
                        topBar.classList.add('spring-animation');
                    }
                }
            }
        });

        // Control functions
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToMiddle() {
            window.scrollTo({ top: 500, behavior: 'smooth' });
        }

        function toggleAnimation() {
            useSpringAnimation = !useSpringAnimation;
            animationType.textContent = useSpringAnimation ? 
                'Current: Spring/Bounce (Old)' : 
                'Current: Smooth Slide (New)';
        }

        // Initial check
        const initialScrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (initialScrollTop > 10) {
            isScrolled = true;
            header.classList.add('scrolled');
        }
    </script>
</body>
</html>
