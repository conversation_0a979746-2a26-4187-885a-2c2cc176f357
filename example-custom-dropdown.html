<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Dropdown Example</title>
    <link rel="stylesheet" href="assets/css/search-page.css">
</head>
<body style="padding: 2rem; background: #f9fafb;">

<h2>So sánh Select thường vs Custom Dropdown</h2>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;">
    
    <!-- Select thường -->
    <div>
        <h3>Select thường (hạn chế styling)</h3>
        <div class="filter-group">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-tags text-primary mr-1"></i>
                Danh mục (Select thường)
            </label>
            <select class="filter-select w-full px-3 py-2 border border-gray-300 rounded-lg">
                <option value="">Tất cả danh mục</option>
                <option value="1"><PERSON><PERSON> & <PERSON><PERSON><PERSON> bành</option>
                <option value="2">Bà<PERSON> & Ghế ăn</option>
                <option value="3">Giường ngủ</option>
                <option value="4">Tủ quần áo</option>
                <option value="5">Bàn làm việc</option>
            </select>
        </div>
    </div>

    <!-- Custom Dropdown -->
    <div>
        <h3>Custom Dropdown (styling hoàn toàn)</h3>
        <div class="filter-group">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-tags text-primary mr-1"></i>
                Danh mục (Custom Dropdown)
            </label>
            <div class="custom-dropdown">
                <button type="button" class="custom-dropdown-trigger">
                    Tất cả danh mục
                </button>
                <input type="hidden" name="category_custom" value="">
                <div class="custom-dropdown-menu">
                    <div class="custom-dropdown-option selected" data-value="">Tất cả danh mục</div>
                    <div class="custom-dropdown-option" data-value="1">Sofa & Ghế bành</div>
                    <div class="custom-dropdown-option" data-value="2">Bàn & Ghế ăn</div>
                    <div class="custom-dropdown-option" data-value="3">Giường ngủ</div>
                    <div class="custom-dropdown-option" data-value="4">Tủ quần áo</div>
                    <div class="custom-dropdown-option" data-value="5">Bàn làm việc</div>
                </div>
            </div>
        </div>
    </div>

</div>

<div style="background: white; padding: 1.5rem; border-radius: 0.5rem; margin-top: 2rem;">
    <h3>Ưu nhược điểm:</h3>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-top: 1rem;">
        <div>
            <h4 style="color: #666;">Select thường:</h4>
            <p style="color: #ef4444;">❌ Không thể style options đầy đủ</p>
            <p style="color: #ef4444;">❌ Giao diện phụ thuộc browser</p>
            <p style="color: #10b981;">✅ Accessibility tốt</p>
            <p style="color: #10b981;">✅ Form submission tự động</p>
            <p style="color: #10b981;">✅ Keyboard navigation</p>
        </div>
        
        <div>
            <h4 style="color: #666;">Custom Dropdown:</h4>
            <p style="color: #10b981;">✅ Styling hoàn toàn tùy chỉnh</p>
            <p style="color: #10b981;">✅ Animations mượt mà</p>
            <p style="color: #10b981;">✅ Hover effects đẹp</p>
            <p style="color: #ef4444;">❌ Cần JavaScript</p>
            <p style="color: #ef4444;">❌ Accessibility cần thêm code</p>
        </div>
    </div>
</div>

<script>
// CSS Variables
const BASE_URL = '.';

// Include the enhanced search script
</script>
<script src="assets/js/enhanced-search.js"></script>

</body>
</html>
