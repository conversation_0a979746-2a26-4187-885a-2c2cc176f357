<?php
// Thi<PERSON><PERSON> lập tiêu đề trang
$page_title = 'Thanh toán';
$page_description = 'Thanh toán đơn hàng tại Nội Thất Băng Vũ';

// Include header
include_once 'partials/header.php';

// Kiểm tra giỏ hàng
$cart_items = get_cart_items();
if (count($cart_items) === 0) {
    set_flash_message('error', 'Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi thanh toán.');
    redirect(BASE_URL . '/cart.php');
}

// Kiểm tra xem có sản phẩm được chọn không
$selected_items = [];
if (isset($_GET['selected_items']) && !empty($_GET['selected_items'])) {
    $selected_product_ids = explode(',', $_GET['selected_items']);

    // L<PERSON><PERSON> các sản phẩm được chọn
    foreach ($cart_items as $item) {
        if (in_array($item['product_id'], $selected_product_ids)) {
            $selected_items[] = $item;
        }
    }

    // Nếu có sản phẩm được chọn, sử dụng danh sách đó
    if (!empty($selected_items)) {
        $cart_items = $selected_items;
    }
}

// Lấy thông tin người dùng nếu đã đăng nhập
$user = null;
if (is_logged_in()) {
    $user = get_user_by_id($_SESSION['user_id']);
}

// Không xử lý đặt hàng ở đây nữa, đã chuyển sang processing-order.php

// Tính tổng tiền
$cart_total = get_cart_total();
?>

<!-- Breadcrumb - Thiết kế hiện đại -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>/cart.php" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </span>
                    <span>Giỏ hàng</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-credit-card"></i>
                    </span>
                    <span>Thanh toán</span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Link CSS cho checkout hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-checkout.css">

<!-- Checkout -->
<div class="modern-checkout">
    <div class="container mx-auto px-4">
        <!-- Checkout Steps -->
        <div class="checkout-steps mb-8">
            <div class="checkout-step completed">
                <div class="checkout-step-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="checkout-step-text">Giỏ hàng</div>
            </div>
            <div class="checkout-step active">
                <div class="checkout-step-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="checkout-step-text">Thanh toán</div>
            </div>
            <div class="checkout-step">
                <div class="checkout-step-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="checkout-step-text">Hoàn tất</div>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- Checkout Form -->
            <div class="lg:w-2/3">
                <div class="checkout-card p-6">
                    <h2 class="checkout-title">Thông tin thanh toán</h2>

                    <?php if (!is_logged_in()): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 text-blue-500">
                                <i class="fas fa-info-circle text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Bạn đã có tài khoản?</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Đăng nhập để theo dõi đơn hàng và nhận các ưu đãi đặc biệt!</p>
                                    <p class="mt-1">
                                        <a href="<?php echo BASE_URL; ?>/login.php" class="font-medium text-blue-600 hover:text-blue-500 inline-flex items-center">
                                            <i class="fas fa-sign-in-alt mr-1"></i> Đăng nhập
                                        </a>
                                        <span class="mx-2">hoặc</span>
                                        <a href="<?php echo BASE_URL; ?>/register.php" class="font-medium text-blue-600 hover:text-blue-500 inline-flex items-center">
                                            <i class="fas fa-user-plus mr-1"></i> Đăng ký
                                        </a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <form id="checkout-form" action="<?php echo BASE_URL; ?>/processing-order.php" method="POST" class="validate-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                        <div class="checkout-form-group">
                            <label for="full_name">Họ và tên <span class="text-red-500">*</span></label>
                            <input type="text" name="full_name" id="full_name" placeholder="Nhập họ và tên của bạn" value="<?php echo $user ? $user['full_name'] : ''; ?>" required>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="checkout-form-group">
                                <label for="email">Email <span class="text-red-500">*</span></label>
                                <input type="email" name="email" id="email" placeholder="<EMAIL>" value="<?php echo $user ? $user['email'] : ''; ?>" required>
                            </div>

                            <div class="checkout-form-group">
                                <label for="phone">Số điện thoại <span class="text-red-500">*</span></label>
                                <input type="text" name="phone" id="phone" placeholder="0xxxxxxxxx" value="<?php echo $user ? $user['phone'] : ''; ?>" required>
                            </div>
                        </div>

                        <div class="checkout-form-group">
                            <label for="address">Địa chỉ giao hàng <span class="text-red-500">*</span></label>
                            <textarea name="address" id="address" rows="3" placeholder="Nhập địa chỉ giao hàng đầy đủ" required><?php echo $user ? $user['address'] : ''; ?></textarea>
                        </div>

                        <div class="checkout-form-group">
                            <label for="note">Ghi chú đơn hàng</label>
                            <textarea name="note" id="note" rows="3" placeholder="Ghi chú về đơn hàng, ví dụ: thời gian hay chỉ dẫn địa điểm giao hàng chi tiết hơn."></textarea>
                        </div>

                        <div class="payment-methods">
                            <h3 class="checkout-title text-lg">Phương thức thanh toán</h3>

                            <div class="payment-method active" id="cod-method">
                                <input type="radio" name="payment_method" id="cod" value="cod" class="payment-method-radio" checked>
                                <div class="payment-method-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="payment-method-details">
                                    <div class="payment-method-title">Thanh toán khi nhận hàng (COD)</div>
                                    <div class="payment-method-description">Bạn chỉ phải thanh toán khi nhận được hàng</div>
                                </div>
                            </div>

                            <div class="payment-method" id="bank-method">
                                <input type="radio" name="payment_method" id="bank_transfer" value="bank_transfer" class="payment-method-radio">
                                <div class="payment-method-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div class="payment-method-details">
                                    <div class="payment-method-title">Chuyển khoản ngân hàng</div>
                                    <div class="payment-method-description">Thực hiện thanh toán vào tài khoản ngân hàng của chúng tôi</div>
                                </div>
                            </div>
                        </div>

                        <div class="bank-info-card hidden" id="bank-info">
                            <div class="bank-info-title">
                                <i class="fas fa-info-circle"></i> Thông tin chuyển khoản
                            </div>
                            <div class="bank-info-item">
                                <div class="bank-info-label">Ngân hàng:</div>
                                <div class="bank-info-value">Vietcombank</div>
                            </div>
                            <div class="bank-info-item">
                                <div class="bank-info-label">Số tài khoản:</div>
                                <div class="bank-info-value">**********</div>
                            </div>
                            <div class="bank-info-item">
                                <div class="bank-info-label">Chủ tài khoản:</div>
                                <div class="bank-info-value">CÔNG TY TNHH NỘI THẤT BĂNG VŨ</div>
                            </div>
                            <div class="bank-info-item">
                                <div class="bank-info-label">Nội dung:</div>
                                <div class="bank-info-value">Thanh toan don hang [Họ tên]</div>
                            </div>
                        </div>

                        <div class="flex items-center mb-6 mt-4">
                            <input type="checkbox" name="agree" id="agree" class="mr-2 h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" required>
                            <label for="agree" class="text-gray-700 text-sm">
                                Tôi đã đọc và đồng ý với <a href="#" class="text-primary hover:underline">điều khoản dịch vụ</a> của Nội Thất Băng Vũ
                            </label>
                        </div>

                        <button type="submit" id="checkout-submit-btn" class="checkout-submit-btn">
                            <i class="fas fa-lock"></i> Đặt hàng ngay
                        </button>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:w-1/3">
                <div class="order-summary checkout-card p-6">
                    <h2 class="order-summary-title">Đơn hàng của bạn</h2>

                    <div class="order-summary-list">
                        <?php foreach ($cart_items as $item): ?>
                        <div class="order-summary-item">
                            <div class="order-summary-item-name">
                                <?php echo $item['name']; ?>
                                <span class="order-summary-item-quantity">× <?php echo $item['quantity']; ?></span>
                            </div>
                            <div class="order-summary-item-price"><?php echo format_currency($item['price'] * $item['quantity']); ?></div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="order-summary-subtotal">
                        <div>Tạm tính</div>
                        <div><?php echo format_currency($cart_total); ?></div>
                    </div>

                    <div class="order-summary-shipping">
                        <div>Phí vận chuyển</div>
                        <div class="text-green-600 font-medium">Miễn phí</div>
                    </div>

                    <div class="order-summary-total">
                        <div class="order-summary-total-label">Tổng cộng</div>
                        <div class="order-summary-total-price"><?php echo format_currency($cart_total); ?></div>
                    </div>
                    <div class="order-summary-vat">(Đã bao gồm VAT)</div>

                    <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-100">
                        <div class="flex items-center mb-3">
                            <div class="text-primary mr-3">
                                <i class="fas fa-shield-alt text-xl"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">Thanh toán an toàn</div>
                                <div class="text-xs text-gray-500">Thông tin thanh toán của bạn được bảo mật</div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="text-primary mr-3">
                                <i class="fas fa-truck text-xl"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800">Giao hàng nhanh chóng</div>
                                <div class="text-xs text-gray-500">Giao hàng trong vòng 2-5 ngày làm việc</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý phương thức thanh toán
        const codMethod = document.getElementById('cod-method');
        const bankMethod = document.getElementById('bank-method');
        const bankInfo = document.getElementById('bank-info');
        const codRadio = document.getElementById('cod');
        const bankRadio = document.getElementById('bank_transfer');

        codMethod.addEventListener('click', function() {
            codRadio.checked = true;
            codMethod.classList.add('active');
            bankMethod.classList.remove('active');
            bankInfo.classList.add('hidden');
        });

        bankMethod.addEventListener('click', function() {
            bankRadio.checked = true;
            bankMethod.classList.add('active');
            codMethod.classList.remove('active');
            bankInfo.classList.remove('hidden');
        });

        // Kiểm tra ban đầu
        if (bankRadio.checked) {
            bankMethod.classList.add('active');
            codMethod.classList.remove('active');
            bankInfo.classList.remove('hidden');
        }
    });
</script>



<!-- Script xử lý form thanh toán -->
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // Xử lý phương thức thanh toán
        const codMethod = document.getElementById('cod-method');
        const bankMethod = document.getElementById('bank-method');
        const bankInfo = document.getElementById('bank-info');
        const codRadio = document.getElementById('cod');
        const bankRadio = document.getElementById('bank_transfer');

        codMethod.addEventListener('click', function() {
            codRadio.checked = true;
            codMethod.classList.add('active');
            bankMethod.classList.remove('active');
            bankInfo.classList.add('hidden');
        });

        bankMethod.addEventListener('click', function() {
            bankRadio.checked = true;
            bankMethod.classList.add('active');
            codMethod.classList.remove('active');
            bankInfo.classList.remove('hidden');
        });

        // Kiểm tra ban đầu
        if (bankRadio.checked) {
            bankMethod.classList.add('active');
            codMethod.classList.remove('active');
            bankInfo.classList.remove('hidden');
        }
    });
</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
