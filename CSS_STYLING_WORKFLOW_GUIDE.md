# 🎨 CSS STYLING WORKFLOW GUIDE - Nội Thất Băng <PERSON>

> **QUAN TRỌNG**: <PERSON><PERSON><PERSON> đ<PERSON>c file này trước khi thực hiện bất kỳ thay đổi CSS/styling nào!

## 📊 THÔNG TIN DỰ ÁN

### Kiến trúc CSS hiện tại:
- **Tailwind CSS**: ~30-35% (CDN + custom config)
- **Custom CSS**: ~65-70% (68 files, 842KB total)
- **Approach**: Hybrid architecture (Tailwind + Custom CSS)

### File CSS quan trọng:
- `assets/css/style.css` - Base styles
- `assets/css/premium-header.css` - Header styling
- `assets/css/search-page.css` - Largest file (84.6KB)
- `assets/css/auth.css` - Authentication pages
- `tailwind.config.js` - Tailwind configuration

## 🚨 QUY TRÌNH BẮT BUỘC TRƯỚC KHI SỬA CSS

### BƯỚC 1: PHÂN TÍCH ELEMENT
```bash
1. <PERSON><PERSON> dụng codebase-retrieval để tìm element cụ thể
2. <PERSON><PERSON><PERSON> đ<PERSON> file PHP chứa element
3. Kiểm tra class names hiện tại (Tailwind vs Custom)
4. Tìm file CSS đang control styling
```

### BƯỚC 2: KIỂM TRA CSS CONFLICTS
```bash
1. Tìm tất cả CSS rules áp dụng cho element
2. Check CSS specificity và load order
3. Kiểm tra !important declarations
4. Verify inline styles có override không
```

### BƯỚC 3: XÁC ĐỊNH APPROACH
```bash
✅ Dùng Tailwind khi:
- Simple utilities (colors, spacing, flex)
- Responsive design
- Quick prototyping

✅ Dùng Custom CSS khi:
- Complex components (sliders, mega-menu)
- Animations/transitions
- Brand-specific styling
- Component đã có CSS file riêng
```

## 📋 CHECKLIST TRƯỚC KHI SỬA

- [ ] Đã tìm hiểu element cụ thể cần sửa
- [ ] Đã xác định file CSS đang control
- [ ] Đã check conflicts và specificity
- [ ] Đã chọn approach phù hợp (Tailwind vs Custom)
- [ ] Đã backup code cũ trong comment

## 🎯 CÁCH XỬ LÝ CÁC TRƯỜNG HỢP THƯỜNG GẶP

### 1. Sửa màu sắc:
```
- Check CSS variables trong :root
- Ưu tiên dùng existing color palette
- Tailwind: bg-primary, text-primary
- Custom: var(--primary), var(--primary-dark)
```

### 2. Sửa spacing/layout:
```
- Ưu tiên Tailwind utilities: p-, m-, gap-
- Custom CSS cho complex layouts
- Luôn test responsive breakpoints
```

### 3. Sửa components phức tạp:
```
- Header: premium-header.css
- Cart: style.css + cart-specific files
- Product cards: search-page.css
- Sliders: banner-slider.css
```

### 4. Responsive design:
```
- Mobile-first approach
- Breakpoints: sm:, md:, lg:, xl:, 2xl:
- Test trên tất cả device sizes
```

## ⚠️ LỖI THƯỜNG GẶP VÀ CÁCH TRÁNH

### 1. CSS không được áp dụng:
```
❌ Nguyên nhân:
- Specificity thấp hơn rule khác
- File CSS load trước file override
- Tailwind utility bị custom CSS override

✅ Giải pháp:
- Tăng specificity hoặc dùng !important
- Sửa trong file CSS load sau cùng
- Check load order trong header.php
```

### 2. Responsive bị break:
```
❌ Nguyên nhân:
- Sửa CSS không có responsive breakpoints
- Override mobile styles

✅ Giải pháp:
- Luôn test mobile/tablet/desktop
- Dùng responsive utilities của Tailwind
```

### 3. Sửa nhầm element:
```
❌ Nguyên nhân:
- Class name giống nhau ở nhiều nơi
- CSS selector quá general

✅ Giải pháp:
- Dùng specific selectors
- Target đúng file PHP
- Test thoroughly
```

## 🔧 TEMPLATE RESPONSE KHI NHẬN YÊU CẦU SỬA CSS

```markdown
## Phân tích yêu cầu:
- Element: [tên element cụ thể]
- File: [file PHP chứa element]
- Vị trí: [desktop/mobile, section nào]

## Approach được chọn:
- [ ] Tailwind utilities
- [ ] Custom CSS file: [tên file]
- [ ] Tạo CSS mới

## Lý do chọn approach này:
[Giải thích tại sao chọn method này]

## Thay đổi sẽ thực hiện:
[Mô tả chi tiết thay đổi]
```

## 📁 FILE STRUCTURE REFERENCE

```
assets/css/
├── style.css              # Base styles
├── premium-header.css     # Header components
├── search-page.css        # Search & product listing
├── auth.css              # Login/register pages
├── cart-*.css            # Cart related styles
├── mobile-*.css          # Mobile specific
├── responsive-*.css      # Responsive utilities
└── [component].css       # Component-specific styles
```

## 🎨 CSS VARIABLES REFERENCE

```css
/* Primary Colors */
--primary: #F37321
--primary-dark: #D65A0F
--primary-light: #FF8A3D

/* Spacing */
--space-1: 0.25rem
--space-2: 0.5rem
--space-4: 1rem
--space-8: 2rem

/* Shadows */
--shadow-sm: 0 2px 4px rgba(0,0,0,0.05)
--shadow-md: 0 4px 8px rgba(0,0,0,0.1)
--shadow-lg: 0 8px 16px rgba(0,0,0,0.1)
```

## 🚀 PERFORMANCE NOTES

- Tránh tạo file CSS mới không cần thiết
- Ưu tiên sửa trong file existing
- Minify CSS khi có thể
- Sử dụng CSS variables thay vì hardcode values

---

**📌 LƯU Ý CUỐI CÙNG:**
- Luôn backup code cũ trong comment
- Test trên multiple browsers
- Verify responsive design
- Document changes made

**🔄 Cập nhật:** Luôn update file này khi có thay đổi architecture
