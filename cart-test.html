<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Cart Layout - <PERSON><PERSON><PERSON> Thất Băng <PERSON>ũ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#F37321',
                        'primary-dark': '#E55A00',
                        'primary-light': '#FF8A4C',
                        'primary-ultra-light': '#FFF4F0'
                    }
                }
            }
        }
    </script>
    <style>
        /* Professional Cart Design - Consistent with Project Theme */
        :root {
            --primary-color: #F37321;
            --primary-dark: #E55A00;
            --primary-light: #FF8A4C;
            --primary-ultra-light: #FFF4F0;
        }

        .cart-container {
            background-color: #f9fafb;
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .cart-item-professional {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .cart-item-professional:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .cart-item-layout {
            display: grid;
            grid-template-columns: 20px 160px 1fr;
            gap: 1.5rem;
            padding: 1.5rem;
            align-items: flex-start;
        }

        .checkbox-column {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 0.5rem;
        }

        .professional-checkbox {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .professional-checkbox:checked {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .professional-checkbox:checked::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 5px;
            width: 5px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .professional-checkbox:hover {
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .image-column {
            position: relative;
            display: flex;
            justify-content: center;
        }

        .product-image-container {
            width: 160px;
            height: 160px;
            border-radius: 8px;
            overflow: hidden;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.05);
        }

        .content-column {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
            min-width: 0;
        }

        .product-info-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .product-main-info {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .product-details {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 0;
        }

        .product-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.4;
            margin: 0;
            word-wrap: break-word;
        }

        .product-title:hover {
            color: var(--primary-color);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .product-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
            display: flex;
            align-items: baseline;
            gap: 0.5rem;
        }

        .original-price {
            font-size: 0.875rem;
            color: #9ca3af;
            text-decoration: line-through;
            font-weight: 500;
        }

        .discount-total-section {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 2rem;
            align-items: center;
        }

        .total-section {
            text-align: right;
            flex-shrink: 0;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
            background: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .qty-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .qty-btn:hover {
            background: var(--primary-ultra-light);
            color: var(--primary-color);
        }

        .qty-btn:active {
            background: #f3f4f6;
            transform: scale(0.95);
        }

        .qty-input {
            width: 50px;
            height: 32px;
            border: none;
            border-left: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            text-align: center;
            font-weight: 600;
            color: #1f2937;
            font-size: 0.875rem;
            background: white;
        }

        .qty-input:focus {
            outline: none;
            background: #fafafa;
            border-left-color: var(--primary-color);
            border-right-color: var(--primary-color);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem 0.875rem;
            border-radius: 6px;
            border: 1px solid;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8rem;
            text-decoration: none;
            min-width: 85px;
            justify-content: center;
            height: 32px;
        }

        .update-btn {
            background: white;
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .update-btn:hover {
            background: var(--primary-ultra-light);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
        }

        .delete-btn {
            background: white;
            color: #dc2626;
            border-color: #dc2626;
        }

        .delete-btn:hover {
            background: #fef2f2;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
        }

        .controls-row {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 1.5rem;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .quantity-section {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quantity-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
            white-space: nowrap;
        }

        .spacer {
            flex: 1;
        }

        .discount-section {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .discount-badge {
            background: #fef2f2;
            color: #dc2626;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #fecaca;
        }

        .savings-badge {
            background: #f0fdf4;
            color: #16a34a;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #bbf7d0;
        }

        .best-price-badge {
            background: #f0f9ff;
            color: #0284c7;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border: 1px solid #bae6fd;
        }

        .total-section {
            text-align: right;
            flex-shrink: 0;
        }

        .total-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .total-amount {
            font-size: 1.375rem;
            font-weight: 700;
            color: #1f2937;
            letter-spacing: -0.025em;
        }
    </style>
</head>
<body class="cart-container">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                Thiết Kế Cart Item Professional
            </h1>
            <p class="text-gray-600">Bố cục chuyên nghiệp, đồng nhất với giao diện dự án Nội Thất Băng Vũ</p>
        </div>

        <!-- Cart Item 1 - Có giảm giá -->
        <div class="cart-item-professional">
            <div class="cart-item-layout">
                <!-- Checkbox Column -->
                <div class="checkbox-column">
                    <input type="checkbox" class="professional-checkbox" checked>
                </div>

                <!-- Image Column -->
                <div class="image-column">
                    <div class="product-image-container">
                        <img src="https://via.placeholder.com/120x120/F37321/ffffff?text=SOFA"
                             alt="Sofa cao cấp"
                             class="product-image">
                    </div>
                </div>

                <!-- Content Column -->
                <div class="content-column">
                    <!-- Product Info Row -->
                    <div class="product-info-row">
                        <div class="product-main-info">
                            <div class="product-details">
                                <h3 class="product-title">Sofa Băng Vũ Luxury Collection 3 Chỗ Ngồi</h3>
                                <div class="product-price">
                                    15,500,000₫
                                    <span class="original-price">19,375,000₫</span>
                                </div>
                            </div>
                        </div>

                        <div class="discount-total-section">
                            <div class="discount-section">
                                <div class="discount-badge">
                                    <i class="fas fa-tag"></i>
                                    Giảm 20%
                                </div>
                                <div class="savings-badge">
                                    <i class="fas fa-piggy-bank"></i>
                                    Tiết kiệm 7,750,000₫
                                </div>
                            </div>

                            <div class="total-section">
                                <div class="total-label">Tổng tiền sản phẩm</div>
                                <div class="total-amount">31,000,000₫</div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Row -->
                    <div class="controls-row">
                        <div class="quantity-section">
                            <span class="quantity-label">Số lượng:</span>
                            <div class="quantity-controls">
                                <button class="qty-btn">−</button>
                                <input type="number" value="2" class="qty-input" min="1">
                                <button class="qty-btn">+</button>
                            </div>
                        </div>

                        <div class="spacer"></div>

                        <div class="action-buttons">
                            <button class="action-btn update-btn">
                                <i class="fas fa-sync-alt"></i>
                                Cập nhật
                            </button>
                            <button class="action-btn delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                Xóa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Item 2 - Không giảm giá -->
        <div class="cart-item-professional">
            <div class="cart-item-layout">
                <!-- Checkbox Column -->
                <div class="checkbox-column">
                    <input type="checkbox" class="professional-checkbox" checked>
                </div>

                <!-- Image Column -->
                <div class="image-column">
                    <div class="product-image-container">
                        <img src="https://via.placeholder.com/120x120/8B4513/ffffff?text=TABLE"
                             alt="Bàn ăn gỗ sồi"
                             class="product-image">
                    </div>
                </div>

                <!-- Content Column -->
                <div class="content-column">
                    <!-- Product Info Row -->
                    <div class="product-info-row">
                        <div class="product-main-info">
                            <div class="product-details">
                                <h3 class="product-title">Bàn Ăn Gỗ Sồi Tự Nhiên Premium 6 Chỗ Ngồi</h3>
                                <div class="product-price">8,500,000₫</div>
                            </div>
                        </div>

                        <div class="discount-total-section">
                            <div class="discount-section">
                                <div class="best-price-badge">
                                    <i class="fas fa-star"></i>
                                    Giá tốt nhất
                                </div>
                            </div>

                            <div class="total-section">
                                <div class="total-label">Tổng tiền sản phẩm</div>
                                <div class="total-amount">8,500,000₫</div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Row -->
                    <div class="controls-row">
                        <div class="quantity-section">
                            <span class="quantity-label">Số lượng:</span>
                            <div class="quantity-controls">
                                <button class="qty-btn">−</button>
                                <input type="number" value="1" class="qty-input" min="1">
                                <button class="qty-btn">+</button>
                            </div>
                        </div>

                        <div class="spacer"></div>

                        <div class="action-buttons">
                            <button class="action-btn update-btn">
                                <i class="fas fa-sync-alt"></i>
                                Cập nhật
                            </button>
                            <button class="action-btn delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                Xóa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Item 3 - Giảm giá cao -->
        <div class="cart-item-professional">
            <div class="cart-item-layout">
                <!-- Checkbox Column -->
                <div class="checkbox-column">
                    <input type="checkbox" class="professional-checkbox">
                </div>

                <!-- Image Column -->
                <div class="image-column">
                    <div class="product-image-container">
                        <img src="https://via.placeholder.com/120x120/2C3E50/ffffff?text=CHAIR"
                             alt="Ghế xoay văn phòng"
                             class="product-image">
                    </div>
                </div>

                <!-- Content Column -->
                <div class="content-column">
                    <!-- Product Info Row -->
                    <div class="product-info-row">
                        <div class="product-main-info">
                            <div class="product-details">
                                <h3 class="product-title">Ghế Xoay Văn Phòng Executive Pro Max</h3>
                                <div class="product-price">
                                    2,400,000₫
                                    <span class="original-price">4,000,000₫</span>
                                </div>
                            </div>
                        </div>

                        <div class="discount-total-section">
                            <div class="discount-section">
                                <div class="discount-badge">
                                    <i class="fas fa-bolt"></i>
                                    Giảm 40%
                                </div>
                                <div class="savings-badge">
                                    <i class="fas fa-piggy-bank"></i>
                                    Tiết kiệm 4,800,000₫
                                </div>
                            </div>

                            <div class="total-section">
                                <div class="total-label">Tổng tiền sản phẩm</div>
                                <div class="total-amount">7,200,000₫</div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Row -->
                    <div class="controls-row">
                        <div class="quantity-section">
                            <span class="quantity-label">Số lượng:</span>
                            <div class="quantity-controls">
                                <button class="qty-btn">−</button>
                                <input type="number" value="3" class="qty-input" min="1">
                                <button class="qty-btn">+</button>
                            </div>
                        </div>

                        <div class="spacer"></div>

                        <div class="action-buttons">
                            <button class="action-btn update-btn">
                                <i class="fas fa-sync-alt"></i>
                                Cập nhật
                            </button>
                            <button class="action-btn delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                Xóa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="text-center mt-12 p-6 bg-white rounded-xl shadow-sm border border-gray-100">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">
                Thiết Kế Cart Item Professional
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div class="p-4 bg-orange-50 rounded-lg border border-orange-100">
                    <div class="text-2xl mb-2 text-primary">🏢</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Chuyên nghiệp</h3>
                    <p class="text-sm text-gray-600">Thiết kế đồng nhất với giao diện dự án</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg border border-gray-100">
                    <div class="text-2xl mb-2">⚡</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Tối ưu UX</h3>
                    <p class="text-sm text-gray-600">Bố cục logic, dễ sử dụng</p>
                </div>
                <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div class="text-2xl mb-2">🎯</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Uy tín</h3>
                    <p class="text-sm text-gray-600">Màu sắc và style nhất quán</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
