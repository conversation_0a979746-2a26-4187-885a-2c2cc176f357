<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Animation Test - Mobile Filter Button</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #1f2937;
            margin-bottom: 40px;
            font-size: 2rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .option-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .option-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 20px;
        }

        .option-description {
            color: #6b7280;
            margin-bottom: 30px;
            font-size: 0.9rem;
        }

        /* Base Button Styles */
        .test-btn {
            background: linear-gradient(to right, #f97316, #ea580c);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .test-btn.has-filters {
            background: linear-gradient(to right, #f97316, #ea580c);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* Option 1: Pulse + Glow */
        .option-1 .filter-icon {
            transition: all 0.3s ease;
        }

        .option-1 .test-btn:hover .filter-icon {
            animation: pulse-glow 1.5s infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { 
                transform: scale(1);
                filter: drop-shadow(0 0 0 rgba(249, 115, 22, 0));
            }
            50% { 
                transform: scale(1.1);
                filter: drop-shadow(0 0 8px rgba(249, 115, 22, 0.6));
            }
        }

        /* Option 2: Bounce + Color Change */
        .option-2 .filter-icon {
            transition: all 0.3s ease;
        }

        .option-2 .test-btn:hover .filter-icon {
            animation: bounce-color 0.8s ease-in-out;
        }

        @keyframes bounce-color {
            0%, 100% { 
                transform: translateY(0);
                color: white;
            }
            25% { 
                transform: translateY(-4px);
                color: #fed7aa;
            }
            50% { 
                transform: translateY(-2px);
                color: #fdba74;
            }
            75% { 
                transform: translateY(-1px);
                color: #fb923c;
            }
        }

        /* Option 3: Slide + Fade */
        .option-3 .filter-icon {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .option-3 .test-btn:hover .filter-icon {
            animation: slide-fade 0.6s ease-in-out;
        }

        @keyframes slide-fade {
            0% { 
                transform: translateX(0);
                opacity: 1;
            }
            50% { 
                transform: translateX(4px);
                opacity: 0.7;
            }
            100% { 
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Option 4: Scale + Shadow */
        .option-4 .filter-icon {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .option-4 .test-btn:hover .filter-icon {
            transform: scale(1.15);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .option-4 .test-btn:active .filter-icon {
            transform: scale(0.95);
            transition-duration: 0.1s;
        }

        /* Option 5: Minimal */
        .option-5 .filter-icon {
            transition: all 0.2s ease;
        }

        .option-5 .test-btn:hover .filter-icon {
            transform: scale(1.05);
            color: #fed7aa;
        }

        /* Option 6: Shake */
        .option-6 .filter-icon {
            transition: all 0.3s ease;
        }

        .option-6 .test-btn:hover .filter-icon {
            animation: gentle-shake 0.5s ease-in-out;
        }

        @keyframes gentle-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        /* Option 7: Flip */
        .option-7 .filter-icon {
            transition: all 0.4s ease;
        }

        .option-7 .test-btn:hover .filter-icon {
            transform: rotateY(180deg) scale(1.1);
        }

        /* Option 8: Wobble */
        .option-8 .filter-icon {
            transition: all 0.3s ease;
        }

        .option-8 .test-btn:hover .filter-icon {
            animation: wobble 0.6s ease-in-out;
        }

        @keyframes wobble {
            0% { transform: rotate(0deg); }
            15% { transform: rotate(-5deg); }
            30% { transform: rotate(3deg); }
            45% { transform: rotate(-3deg); }
            60% { transform: rotate(2deg); }
            75% { transform: rotate(-1deg); }
            100% { transform: rotate(0deg); }
        }

        .code-preview {
            background: #1f2937;
            color: #e5e7eb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
            overflow-x: auto;
        }

        .highlight {
            color: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Icon Animation Test - Mobile Filter Button</h1>
        
        <div class="grid">
            <!-- Option 1: Pulse + Glow -->
            <div class="option-card option-1">
                <h3 class="option-title">Option 1: Pulse + Glow</h3>
                <p class="option-description">Icon nhấp nháy nhẹ với hiệu ứng ánh sáng xung quanh</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 2: Bounce + Color -->
            <div class="option-card option-2">
                <h3 class="option-title">Option 2: Bounce + Color</h3>
                <p class="option-description">Icon bounce nhẹ với hiệu ứng đổi màu</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 3: Slide + Fade -->
            <div class="option-card option-3">
                <h3 class="option-title">Option 3: Slide + Fade</h3>
                <p class="option-description">Icon trượt nhẹ với hiệu ứng fade</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 4: Scale + Shadow -->
            <div class="option-card option-4">
                <h3 class="option-title">Option 4: Scale + Shadow</h3>
                <p class="option-description">Icon phóng to với shadow tạo độ sâu</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 5: Minimal -->
            <div class="option-card option-5">
                <h3 class="option-title">Option 5: Minimal</h3>
                <p class="option-description">Hiệu ứng tối giản chỉ scale + đổi màu</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 6: Shake -->
            <div class="option-card option-6">
                <h3 class="option-title">Option 6: Gentle Shake</h3>
                <p class="option-description">Icon rung nhẹ nhàng trái phải</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 7: Flip -->
            <div class="option-card option-7">
                <h3 class="option-title">Option 7: 3D Flip</h3>
                <p class="option-description">Icon lật 3D theo trục Y</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>

            <!-- Option 8: Wobble -->
            <div class="option-card option-8">
                <h3 class="option-title">Option 8: Wobble</h3>
                <p class="option-description">Icon lắc lư nhẹ nhàng</p>
                
                <button class="test-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Bộ lọc sản phẩm</span>
                </button>
                
                <button class="test-btn has-filters">
                    <i class="fas fa-filter filter-icon"></i>
                    <span>Đã lọc 3 tiêu chí</span>
                </button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <p style="color: #6b7280; font-size: 14px;">
                💡 Hover vào các button để xem hiệu ứng. Chọn option nào bạn thích nhất!
            </p>
        </div>
    </div>
</body>
</html>
