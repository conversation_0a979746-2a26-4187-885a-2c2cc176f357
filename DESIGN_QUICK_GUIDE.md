# 🚀 HƯỚNG DẪN NHANH - DESIGN SYSTEM NỘI THẤT BĂNG VŨ

## 📋 CHECKLIST KHI THIẾT KẾ TRANG MỚI

### ✅ **BƯỚC 1: SETUP CƠ BẢN**
```html
<!-- Import Design System CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/design-system.css">

<!-- Container chuẩn -->
<div class="container-custom">
    <!-- Nội dung trang -->
</div>
```

### ✅ **BƯỚC 2: SỬ DỤNG MÀU CHUẨN**
```css
/* ✅ ĐÚNG - Sử dụng CSS Variables */
.my-element {
    background: var(--primary);
    color: var(--text-white);
    border: 1px solid var(--border-light);
}

/* ❌ SAI - Hardcode màu */
.my-element {
    background: #F37321;
    color: #FFFFFF;
    border: 1px solid #E5E7EB;
}
```

### ✅ **BƯỚC 3: SPACING NHẤT QUÁN**
```css
/* ✅ ĐÚNG - Sử dụng spacing system */
.section {
    padding: var(--space-20) 0;
    margin-bottom: var(--space-16);
}

.card {
    padding: var(--space-6);
    gap: var(--space-4);
}

/* ❌ SAI - Random spacing */
.section {
    padding: 73px 0;
    margin-bottom: 45px;
}
```

---

## 🎨 MÀU SẮC NHANH

### **Màu Chính (Dùng nhiều nhất)**
- `var(--primary)` - #F37321 - Cam chính
- `var(--text-primary)` - #1F2937 - Text chính  
- `var(--bg-white)` - #FFFFFF - Background chính

### **Màu Phụ (Dùng vừa phải)**
- `var(--secondary)` - #2A3B47 - Header/Footer
- `var(--text-muted)` - #6B7280 - Text phụ
- `var(--bg-light)` - #F9FAFB - Background nhẹ

### **Màu Accent (Dùng ít)**
- `var(--accent)` - #4CAF50 - Success
- `var(--blue)` - #3B82F6 - Links
- `var(--yellow)` - #FBC02D - Ratings

---

## 🎯 COMPONENT NHANH

### **Buttons**
```html
<!-- Primary Button -->
<button class="btn-primary">
    <i class="fas fa-shopping-cart"></i>
    Thêm vào giỏ
</button>

<!-- Secondary Button -->
<button class="btn-secondary">
    <i class="fas fa-eye"></i>
    Xem chi tiết
</button>

<!-- Outline Button -->
<button class="btn-outline">
    <i class="fas fa-heart"></i>
    Yêu thích
</button>
```

### **Cards**
```html
<!-- Card cơ bản -->
<div class="card">
    <div class="card-header">
        <h3>Tiêu đề</h3>
    </div>
    <div class="card-body">
        <p>Nội dung</p>
    </div>
    <div class="card-footer">
        <button class="btn-primary">Action</button>
    </div>
</div>

<!-- Card premium -->
<div class="card card-premium">
    <div class="card-body">
        <h3>Sản phẩm cao cấp</h3>
    </div>
</div>
```

### **Inputs**
```html
<!-- Input field -->
<input type="text" class="input-field" placeholder="Nhập tên sản phẩm...">

<!-- Input với label -->
<div class="form-group">
    <label class="form-label">Tên khách hàng</label>
    <input type="text" class="input-field" placeholder="Nhập tên...">
</div>
```

### **Badges**
```html
<span class="badge badge-primary">Mới</span>
<span class="badge badge-success">Còn hàng</span>
<span class="badge badge-warning">Sắp hết</span>
<span class="badge badge-error">Hết hàng</span>
```

---

## 🎪 HIỆU ỨNG NHANH

### **Hover Effects**
```css
.my-card {
    transition: var(--transition-smooth);
}

.my-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}
```

### **Animations**
```html
<!-- Fade in up -->
<div class="animate-fadeInUp">Content</div>

<!-- Slide in right -->
<div class="animate-slideInRight">Content</div>

<!-- Pulse effect -->
<div class="animate-pulse">Content</div>
```

---

## 📱 RESPONSIVE NHANH

### **Breakpoints**
```css
/* Mobile first */
.my-element {
    padding: var(--space-4);
}

/* Tablet */
@media (min-width: 768px) {
    .my-element {
        padding: var(--space-6);
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .my-element {
        padding: var(--space-8);
    }
}
```

### **Grid Layout**
```html
<!-- 2 cột trên mobile, 3 cột trên tablet, 4 cột trên desktop -->
<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
    <div class="card">Item 1</div>
    <div class="card">Item 2</div>
    <div class="card">Item 3</div>
    <div class="card">Item 4</div>
</div>
```

---

## 🎨 LAYOUT PATTERNS

### **Section Header**
```html
<div class="section-spacing">
    <div class="container-custom">
        <div class="text-center mb-12">
            <div class="badge badge-primary mb-4">
                <i class="fas fa-star"></i>
                Nổi bật
            </div>
            <h2 class="text-4xl font-bold text-primary mb-4">
                Sản Phẩm Nổi Bật
            </h2>
            <p class="text-muted max-w-2xl mx-auto">
                Khám phá những sản phẩm nội thất cao cấp được yêu thích nhất
            </p>
        </div>
        <!-- Content -->
    </div>
</div>
```

### **Product Card**
```html
<div class="card group">
    <div class="relative overflow-hidden">
        <img src="product.jpg" alt="Product" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
        <div class="absolute top-4 right-4">
            <span class="badge badge-success">Mới</span>
        </div>
    </div>
    <div class="card-body">
        <h3 class="font-semibold text-lg mb-2">Tên sản phẩm</h3>
        <p class="text-muted mb-4">Mô tả ngắn về sản phẩm</p>
        <div class="flex items-center justify-between">
            <span class="text-2xl font-bold text-primary">2.500.000đ</span>
            <button class="btn-primary">
                <i class="fas fa-cart-plus"></i>
                Thêm vào giỏ
            </button>
        </div>
    </div>
</div>
```

### **Hero Section**
```html
<div class="relative bg-gradient-to-r from-secondary to-secondary-light text-white">
    <div class="container-custom">
        <div class="py-24 lg:py-32">
            <div class="max-w-3xl">
                <div class="badge badge-primary mb-6">
                    <i class="fas fa-award"></i>
                    Nội thất cao cấp
                </div>
                <h1 class="text-5xl lg:text-6xl font-bold mb-6">
                    Thiết Kế Nội Thất
                    <span class="text-primary">Đẳng Cấp</span>
                </h1>
                <p class="text-xl mb-8 text-gray-300">
                    Tạo nên không gian sống hoàn hảo với những sản phẩm nội thất cao cấp
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <button class="btn-primary">
                        <i class="fas fa-eye"></i>
                        Xem sản phẩm
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-phone"></i>
                        Liên hệ tư vấn
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
```

---

## ⚡ TIPS & TRICKS

### **1. Luôn sử dụng CSS Variables**
```css
/* ✅ ĐÚNG */
color: var(--primary);
background: var(--bg-light);

/* ❌ SAI */
color: #F37321;
background: #F9FAFB;
```

### **2. Consistent Spacing**
```css
/* ✅ ĐÚNG - Sử dụng spacing system */
margin: var(--space-4) 0 var(--space-8);
padding: var(--space-6);

/* ❌ SAI - Random numbers */
margin: 15px 0 35px;
padding: 23px;
```

### **3. Hover Effects cho mọi interactive element**
```css
.interactive-element {
    transition: var(--transition-smooth);
}

.interactive-element:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}
```

### **4. Mobile First Responsive**
```css
/* Mobile first */
.element {
    font-size: var(--text-base);
}

/* Tablet và lớn hơn */
@media (min-width: 768px) {
    .element {
        font-size: var(--text-lg);
    }
}
```

### **5. Semantic HTML + CSS Classes**
```html
<!-- ✅ ĐÚNG -->
<article class="card">
    <header class="card-header">
        <h2>Title</h2>
    </header>
    <main class="card-body">
        <p>Content</p>
    </main>
</article>

<!-- ❌ SAI -->
<div class="card">
    <div class="card-header">
        <div>Title</div>
    </div>
</div>
```

---

## 🎯 QUALITY CHECKLIST

- [ ] ✅ Sử dụng CSS Variables thay vì hardcode
- [ ] ✅ Spacing nhất quán với design system
- [ ] ✅ Hover effects mượt mà
- [ ] ✅ Responsive trên tất cả breakpoints
- [ ] ✅ Typography hierarchy rõ ràng
- [ ] ✅ Color contrast đạt chuẩn accessibility
- [ ] ✅ Loading states và animations
- [ ] ✅ Semantic HTML structure
- [ ] ✅ Performance optimization
- [ ] ✅ Cross-browser compatibility

---

**🎨 "Consistency is the key to great design"**

*Quick Guide v1.0 - Nội Thất Băng Vũ*
