<?php
/**
 * Contact Buttons - Nội Thất Bàng Vũ
 * Các nút liên hệ nhanh và nút cuộn lên đầu trang
 */

// Lấy thông tin liên hệ từ cài đặt
$phone_number = get_setting('contact_phone', '0972774646');
$zalo_number = get_setting('zalo_number', $phone_number); // Mặc định dùng số điện thoại nếu không có số Zalo riêng
$messenger_url = get_setting('messenger_url', 'https://web.facebook.com/tunhuachatluong');

// Định dạng số điện thoại để gọi
$phone_call = preg_replace('/[^0-9]/', '', $phone_number);

// Định dạng số Zalo
$zalo_chat = preg_replace('/[^0-9]/', '', $zalo_number);
?>

<!-- Nút cuộn lên đầu trang (bên tr<PERSON><PERSON>) -->
<div class="scroll-top-container">
    <button id="scroll-to-top" class="scroll-button" aria-label="Cuộn lên đầu trang">
        <i class="fas fa-chevron-up"></i>
        <span class="contact-tooltip">Lên đầu trang</span>
    </button>
</div>

<!-- Các nút liên hệ (bên phải) -->
<div class="contact-buttons-container">
    <!-- Nút gọi điện -->
    <a href="tel:<?php echo $phone_call; ?>" class="contact-button phone-button" aria-label="Gọi điện thoại">
        <img src="<?php echo BASE_URL; ?>/assets/images/icon-nut-lien-he/phone.webp" alt="Gọi điện" class="contact-icon">
        <span class="contact-tooltip contact-tooltip-left">Gọi ngay: <?php echo $phone_number; ?></span>
    </a>

    <!-- Nút Zalo -->
    <a href="https://zalo.me/<?php echo $zalo_chat; ?>" target="_blank" class="contact-button zalo-button" aria-label="Chat Zalo">
        <img src="<?php echo BASE_URL; ?>/assets/images/icon-nut-lien-he/zalo.webp" alt="Zalo" class="contact-icon">
        <span class="contact-tooltip contact-tooltip-left">Chat Zalo</span>
    </a>

    <!-- Nút Messenger -->
    <a href="<?php echo $messenger_url; ?>" target="_blank" class="contact-button messenger-button" aria-label="Chat Messenger">
        <img src="<?php echo BASE_URL; ?>/assets/images/icon-nut-lien-he/messenger.webp" alt="Messenger" class="contact-icon">
        <span class="contact-tooltip contact-tooltip-left">Chat Messenger</span>
    </a>
</div>
