<?php
// Khởi tạo ứng dụng
require_once __DIR__ . '/../includes/init.php';

// Lấy danh sách danh mục theo cấu trúc cây (chỉ lấy danh mục có trạng thái hiển thị)
$categories = get_category_tree(1);

// Kiểm tra người dùng đã đăng nhập chưa
$user_logged_in = isset($_SESSION['user_id']);
$cart_count = isset($_SESSION['cart']) ? count($_SESSION['cart']) : 0;
?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta name="keywords" content="<?php echo isset($page_keywords) ? $page_keywords : META_KEYWORDS; ?>">

    <!-- Favicon - Đầy đủ cho tất cả thiết bị và SEO -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo BASE_URL; ?>/assets/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg">
    <link rel="icon" type="image/x-icon" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.ico">
    <link rel="manifest" href="<?php echo BASE_URL; ?>/assets/images/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#f37321">
    <meta name="msapplication-TileImage" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-192x192.png">
    <meta name="theme-color" content="#f37321">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="twitter:title" content="<?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?>">
    <meta property="twitter:description" content="<?php echo isset($page_description) ? $page_description : SITE_DESCRIPTION; ?>">
    <meta property="twitter:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/responsive-layout.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/luxury-header.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/luxury-mobile-menu.css">

    <!-- Hover Reset CSS - Tắt hover effect sau khi click vào nav link và cart -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/hover-reset.css">

    <!-- EXTREME MODE: Thêm class vào html ngay lập tức để ẩn hover box -->
    <script>
        (function() {
            const currentPage = window.location.pathname;
            const isProductPage = currentPage.includes('products.php') || currentPage.includes('/products');
            const isCartPage = currentPage.includes('cart.php') || currentPage.includes('/cart');

            if (isProductPage) {
                document.documentElement.classList.add('page-products');
            }

            if (isCartPage) {
                document.documentElement.classList.add('page-cart');
            }
        })();
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom JavaScript -->
    <script src="<?php echo BASE_URL; ?>/assets/js/luxury-header.js" defer></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/search.js" defer></script>

    <!-- Hover Reset JS - Xử lý tắt hover effect sau khi click vào nav link và cart -->
    <script src="<?php echo BASE_URL; ?>/assets/js/hover-reset.js" defer></script>
    <script>
    // Biến toàn cục cho JavaScript
    const BASE_URL = '<?php echo BASE_URL; ?>';
    </script>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- Luxury Header -->
    <header class="luxury-header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="top-bar-contact">
                    <a href="tel:+84912345678"><i class="fas fa-phone-alt"></i> 0912 345 678</a>
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <span><i class="fas fa-map-marker-alt"></i> 123 Đường Lê Lợi, Quận 1, TP. Hồ Chí Minh</span>
                </div>
                <div class="top-bar-social">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Youtube"><i class="fab fa-youtube"></i></a>
                    <a href="#" aria-label="Pinterest"><i class="fab fa-pinterest-p"></i></a>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="main-header-container">
            <div class="main-header">
                <!-- Logo -->
                <a href="<?php echo BASE_URL; ?>" class="luxury-logo">
                    <div class="luxury-logo-image">
                        <img src="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg" alt="<?php echo SITE_NAME; ?>">
                    </div>
                    <div class="luxury-logo-text">
                        <h1 class="luxury-logo-title">NỘI THẤT CHẤT LƯỢNG BÀNG VŨ</h1>
                        <p class="luxury-logo-tagline">Nâng Tầm Không Gian Sống</p>
                    </div>
                </a>

                <!-- Navigation -->
                <nav class="luxury-nav">
                    <ul class="nav-menu">
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>" class="nav-link">Trang chủ</a>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'products.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="nav-link">
                                Sản phẩm <i class="fas fa-chevron-down"></i>
                            </a>
                            <div class="dropdown-menu">
                                <a href="<?php echo BASE_URL; ?>/products.php" class="dropdown-item">
                                    <strong>Tất cả sản phẩm</strong>
                                </a>
                                <div class="dropdown-divider"></div>
                                <?php foreach ($categories as $category): ?>
                                    <?php if (isset($category['children']) && !empty($category['children'])): ?>
                                        <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $category['id']; ?>" class="dropdown-item">
                                            <strong><?php echo $category['name']; ?></strong>
                                        </a>
                                        <?php foreach ($category['children'] as $child): ?>
                                            <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $child['id']; ?>" class="dropdown-item">
                                                &nbsp;&nbsp;<?php echo $child['name']; ?>
                                            </a>
                                        <?php endforeach; ?>
                                        <?php if ($category !== end($categories)): ?>
                                            <div class="dropdown-divider"></div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $category['id']; ?>" class="dropdown-item">
                                            <?php echo $category['name']; ?>
                                        </a>
                                        <?php if ($category !== end($categories)): ?>
                                            <div class="dropdown-divider"></div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'pricing.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/pricing.php" class="nav-link">Báo giá</a>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'design-process.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/design-process.php" class="nav-link">Quy trình thiết kế</a>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'news.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/news.php" class="nav-link">Tin tức</a>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'about.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/about.php" class="nav-link">Giới thiệu</a>
                        </li>
                        <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'contact.php') ? 'active' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="nav-link">Liên hệ</a>
                        </li>
                    </ul>
                </nav>

                <!-- User Actions -->
                <div class="user-actions">
                    <!-- Search Toggle -->
                    <div class="search-container">
                        <button class="search-toggle action-btn">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-panel">
                            <form action="<?php echo BASE_URL; ?>/search.php" method="GET" class="search-form">
                                <input type="text" name="keyword" placeholder="Tìm kiếm sản phẩm..." class="search-input" autocomplete="off">
                                <button type="submit" class="search-button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- User Account -->
                    <?php if ($user_logged_in): ?>
                    <a href="<?php echo BASE_URL; ?>/account.php" class="action-btn">
                        <i class="fas fa-user-circle"></i>
                    </a>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/login.php" class="action-btn">
                        <i class="fas fa-user"></i>
                    </a>
                    <?php endif; ?>

                    <!-- Wishlist -->
                    <a href="<?php echo BASE_URL; ?>/wishlist.php" class="action-btn">
                        <i class="fas fa-heart"></i>
                    </a>

                    <!-- Cart -->
                    <a href="<?php echo BASE_URL; ?>/cart.php" class="action-btn">
                        <i class="fas fa-shopping-cart"></i>
                        <?php if ($cart_count > 0): ?>
                        <span class="badge"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu">
        <button class="mobile-menu-close">
            <i class="fas fa-times"></i>
        </button>

        <div class="mobile-menu-header">
            <a href="<?php echo BASE_URL; ?>" class="mobile-logo">
                <div class="mobile-logo-image">
                    <img src="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg" alt="<?php echo SITE_NAME; ?>">
                </div>
                <div class="mobile-logo-text">
                    <h2 class="mobile-logo-title">NỘI THẤT CHẤT LƯỢNG BÀNG VŨ</h2>
                    <p class="mobile-logo-tagline">Nâng Tầm Không Gian Sống</p>
                </div>
            </a>
        </div>

        <nav class="mobile-menu-nav">
            <ul class="mobile-menu-list">
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>" class="mobile-menu-link">Trang chủ</a>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'products.php') ? 'active' : ''; ?>">
                    <a href="javascript:void(0);" class="mobile-menu-link mobile-dropdown-toggle">
                        Sản phẩm <i class="fas fa-chevron-down"></i>
                    </a>
                    <ul class="mobile-submenu">
                        <li class="mobile-submenu-item">
                            <a href="<?php echo BASE_URL; ?>/products.php" class="mobile-submenu-link">
                                <strong>Tất cả sản phẩm</strong>
                            </a>
                        </li>
                        <?php foreach ($categories as $category): ?>
                            <li class="mobile-submenu-item">
                                <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $category['id']; ?>" class="mobile-submenu-link">
                                    <?php echo $category['name']; ?>
                                </a>
                            </li>
                            <?php if (isset($category['children']) && !empty($category['children'])): ?>
                                <?php foreach ($category['children'] as $child): ?>
                                    <li class="mobile-submenu-item">
                                        <a href="<?php echo BASE_URL; ?>/category.php?id=<?php echo $child['id']; ?>" class="mobile-submenu-link">
                                            &nbsp;&nbsp;<?php echo $child['name']; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'pricing.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>/pricing.php" class="mobile-menu-link">Báo giá</a>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'design-process.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>/design-process.php" class="mobile-menu-link">Quy trình thiết kế</a>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'news.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>/news.php" class="mobile-menu-link">Tin tức</a>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'about.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>/about.php" class="mobile-menu-link">Giới thiệu</a>
                </li>
                <li class="mobile-menu-item <?php echo (basename($_SERVER['PHP_SELF']) == 'contact.php') ? 'active' : ''; ?>">
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-menu-link">Liên hệ</a>
                </li>
            </ul>
        </nav>

        <div class="mobile-menu-footer">
            <div class="mobile-contact-info">
                <h3>Thông tin liên hệ</h3>
                <div class="mobile-contact-item">
                    <i class="fas fa-phone-alt"></i>
                    <a href="tel:+84912345678">0912 345 678</a>
                </div>
                <div class="mobile-contact-item">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="mobile-contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>123 Đường Lê Lợi, Quận 1, TP. Hồ Chí Minh</span>
                </div>
            </div>

            <div class="mobile-social">
                <a href="#" class="mobile-social-link" aria-label="Facebook">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="mobile-social-link" aria-label="Instagram">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="mobile-social-link" aria-label="Youtube">
                    <i class="fab fa-youtube"></i>
                </a>
                <a href="#" class="mobile-social-link" aria-label="Pinterest">
                    <i class="fab fa-pinterest-p"></i>
                </a>
            </div>

            <div class="mobile-cta">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="mobile-cta-button">
                    <i class="fas fa-headset"></i> Liên hệ tư vấn
                </a>
            </div>
        </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay"></div>
