<?php
// Include header
include_once 'partials/header.php';

// Include blog functions
include_once 'includes/blog-functions.php';

// Lấy slug từ URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Nếu không có slug, chuyển hướng về trang blog
if (empty($slug)) {
    redirect(BASE_URL . '/blog.php');
}

// Lấy thông tin bài viết
$post = get_blog_post_by_slug($slug);

// Nếu không tìm thấy bài viết, chuyển hướng về trang blog
if (!$post) {
    set_flash_message('error', 'Bài viết không tồn tại.');
    redirect(BASE_URL . '/blog.php');
}

// Thiết lập tiêu đề trang
$page_title = $post['title'];
$page_description = !empty($post['meta_description']) ? $post['meta_description'] : (!empty($post['excerpt']) ? $post['excerpt'] : substr(strip_tags($post['content']), 0, 160));

// Lấy bài viết liên quan
$related_posts = get_related_posts($post['id'], 3);

// Lấy bình luận
$comments = get_post_comments($post['id']);

// Xử lý gửi bình luận
$comment_error = '';
$comment_success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment_submit'])) {
    // Kiểm tra dữ liệu
    if (empty($_POST['comment_content'])) {
        $comment_error = 'Vui lòng nhập nội dung bình luận.';
    } else {
        $comment_data = [
            'post_id' => $post['id'],
            'user_id' => is_logged_in() ? $_SESSION['user_id'] : null,
            'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
            'author_name' => is_logged_in() ? '' : $_POST['author_name'],
            'author_email' => is_logged_in() ? '' : $_POST['author_email'],
            'content' => $_POST['comment_content'],
            'status' => is_logged_in() ? 1 : 0 // Tự động duyệt nếu đã đăng nhập
        ];

        // Thêm bình luận
        $result = add_comment($comment_data);
        if ($result) {
            $comment_success = is_logged_in() ? 'Bình luận của bạn đã được đăng.' : 'Bình luận của bạn đã được gửi và đang chờ duyệt.';

            // Làm mới trang để hiển thị bình luận mới (nếu đã đăng nhập)
            if (is_logged_in()) {
                redirect(BASE_URL . '/blog/' . $slug . '#comments');
            }
        } else {
            $comment_error = 'Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại sau.';
        }
    }
}
?>

<!-- Link CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/blog.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/blog-post.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/blog-author.css">

<div class="blog-post-container">
    <div class="blog-post-with-sidebar">
        <div class="blog-post-main">
            <article class="blog-post-content">
                <!-- Blog Post Header -->
                <header class="blog-post-header">
                    <div class="blog-post-categories">
                        <?php foreach ($post['categories'] as $category): ?>
                            <a href="<?php echo BASE_URL; ?>/blog.php?category=<?php echo $category['slug']; ?>" class="blog-post-category">
                                <?php echo $category['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                    <h1 class="blog-post-title"><?php echo $post['title']; ?></h1>
                    <div class="blog-post-meta">
                        <div class="blog-post-author">
                            <div class="blog-post-author-avatar">
                                <?php if (!empty($post['author']['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $post['author']['avatar']; ?>" alt="<?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?>">
                                <?php else: ?>
                                    <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?>">
                                <?php endif; ?>
                            </div>
                            <span class="blog-post-author-name"><?php echo $post['author']['name'] ?? $post['author']['full_name'] ?? 'Tác giả'; ?></span>
                        </div>
                        <div class="blog-post-date">
                            <i class="far fa-calendar-alt"></i>
                            <span><?php echo $post['formatted_date']; ?></span>
                        </div>
                        <div class="blog-post-views">
                            <i class="far fa-eye"></i>
                            <span><?php echo number_format($post['view_count']); ?> lượt xem</span>
                        </div>
                    </div>
                </header>

                <!-- Featured Image -->
                <?php if (!empty($post['featured_image'])): ?>
                    <div class="blog-post-featured-image">
                        <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $post['featured_image']; ?>" alt="<?php echo $post['title']; ?>">
                    </div>
                <?php endif; ?>

                <!-- Blog Post Body -->
                <div class="blog-post-body">
                    <?php
                    // Hiển thị nội dung bài viết
                    // Ghi log nội dung để debug
                    error_log("Blog content to display (first 500 chars): " . substr($post['content'], 0, 500));
                    error_log("Blog content length: " . strlen($post['content']));

                    // Ghi nội dung vào file để debug
                    $debug_file = __DIR__ . '/logs/blog_content_display.txt';
                    file_put_contents($debug_file, "Content to display: " . $post['content']);

                    // Nếu nội dung trống hoặc chỉ có thẻ <p><br></p> thì hiển thị thông báo
                    if (empty($post['content']) || $post['content'] == '<p><br></p>') {
                        echo '<p class="text-gray-600">Nội dung đang được cập nhật...</p>';
                    } else {
                        // Hiển thị nội dung bài viết trực tiếp
                        echo $post['content'];
                    }
                    ?>

                    <!-- Tags -->
                    <?php if (!empty($post['tags'])): ?>
                        <div class="blog-post-tags">
                            <span class="blog-post-tags-title">Tags:</span>
                            <?php foreach ($post['tags'] as $tag): ?>
                                <a href="<?php echo BASE_URL; ?>/blog.php?tag=<?php echo $tag['slug']; ?>" class="blog-post-tag">
                                    <?php echo $tag['name']; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Share Buttons -->
                    <div class="blog-post-share">
                        <span class="blog-post-share-title">Chia sẻ:</span>
                        <div class="blog-post-share-buttons">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(BASE_URL . '/blog/' . $post['slug']); ?>" target="_blank" class="blog-post-share-button share-facebook" aria-label="Chia sẻ lên Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(BASE_URL . '/blog/' . $post['slug']); ?>&text=<?php echo urlencode($post['title']); ?>" target="_blank" class="blog-post-share-button share-twitter" aria-label="Chia sẻ lên Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(BASE_URL . '/blog/' . $post['slug']); ?>&title=<?php echo urlencode($post['title']); ?>" target="_blank" class="blog-post-share-button share-linkedin" aria-label="Chia sẻ lên LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="https://pinterest.com/pin/create/button/?url=<?php echo urlencode(BASE_URL . '/blog/' . $post['slug']); ?>&media=<?php echo !empty($post['featured_image']) ? urlencode(BASE_URL . '/uploads/blog/' . $post['featured_image']) : ''; ?>&description=<?php echo urlencode($post['title']); ?>" target="_blank" class="blog-post-share-button share-pinterest" aria-label="Chia sẻ lên Pinterest">
                                <i class="fab fa-pinterest-p"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Author Box -->
                <div class="blog-author-box">
                    <h3 class="blog-author-box-header">Về tác giả</h3>
                    <div class="blog-author-content">
                        <div class="blog-author-avatar">
                            <?php if (!empty($post['author']['avatar'])): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/blog/authors/<?php echo $post['author']['avatar']; ?>" alt="<?php echo $post['author']['name']; ?>">
                            <?php else: ?>
                                <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $post['author']['name']; ?>">
                            <?php endif; ?>
                        </div>
                        <div class="blog-author-info">
                            <h4 class="blog-author-name"><?php echo $post['author']['name']; ?></h4>

                            <?php if (!empty($post['author']['position'])): ?>
                                <div class="blog-author-position">
                                    <?php echo htmlspecialchars($post['author']['position']); ?>
                                </div>
                            <?php endif; ?>

                            <div class="blog-author-bio">
                                <?php echo !empty($post['author']['bio']) ? $post['author']['bio'] : 'Tác giả bài viết tại Nội Thất Bàng Vũ.'; ?>
                            </div>

                            <?php if (!empty($post['author']['experience'])): ?>
                                <div class="blog-author-section">
                                    <h5 class="blog-author-section-title">Kinh nghiệm</h5>
                                    <div class="blog-author-section-content">
                                        <?php echo nl2br(htmlspecialchars($post['author']['experience'])); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($post['author']['education'])): ?>
                                <div class="blog-author-section">
                                    <h5 class="blog-author-section-title">Học vấn & Bằng cấp</h5>
                                    <div class="blog-author-section-content">
                                        <?php echo nl2br(htmlspecialchars($post['author']['education'])); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($post['author']['website'])): ?>
                                <a href="<?php echo $post['author']['website']; ?>" target="_blank" class="blog-author-website">
                                    <i class="fas fa-globe"></i> <?php echo $post['author']['website']; ?>
                                </a>
                            <?php endif; ?>

                            <div class="blog-author-social">
                                <?php if (!empty($post['author']['facebook'])): ?>
                                    <a href="<?php echo $post['author']['facebook']; ?>" target="_blank" aria-label="Facebook" title="Facebook">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($post['author']['zalo'])): ?>
                                    <a href="<?php echo $post['author']['zalo']; ?>" target="_blank" aria-label="Zalo" title="Zalo">
                                        <i class="fas fa-comment"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($post['author']['instagram'])): ?>
                                    <a href="<?php echo $post['author']['instagram']; ?>" target="_blank" aria-label="Instagram" title="Instagram">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($post['author']['twitter'])): ?>
                                    <a href="<?php echo $post['author']['twitter']; ?>" target="_blank" aria-label="Twitter" title="Twitter">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($post['author']['linkedin'])): ?>
                                    <a href="<?php echo $post['author']['linkedin']; ?>" target="_blank" aria-label="LinkedIn" title="LinkedIn">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Related Posts -->
            <?php if (!empty($related_posts)): ?>
                <div class="blog-post-related">
                    <h2 class="blog-post-related-title">Bài viết liên quan</h2>
                    <div class="blog-post-related-grid">
                        <?php foreach ($related_posts as $related_post): ?>
                            <div class="blog-card">
                                <div class="blog-card-image">
                                    <?php if (!empty($related_post['featured_image'])): ?>
                                        <img src="<?php echo BASE_URL; ?>/uploads/blog/<?php echo $related_post['featured_image']; ?>" alt="<?php echo $related_post['title']; ?>">
                                    <?php else: ?>
                                        <img src="<?php echo BASE_URL; ?>/assets/images/blog-placeholder.jpg" alt="<?php echo $related_post['title']; ?>">
                                    <?php endif; ?>
                                </div>
                                <div class="blog-card-content">
                                    <?php if (!empty($related_post['categories'])): ?>
                                        <a href="<?php echo BASE_URL; ?>/blog.php?category=<?php echo $related_post['categories'][0]['slug']; ?>" class="blog-card-category">
                                            <?php echo $related_post['categories'][0]['name']; ?>
                                        </a>
                                    <?php endif; ?>
                                    <h3 class="blog-card-title">
                                        <a href="<?php echo BASE_URL; ?>/blog/<?php echo $related_post['slug']; ?>"><?php echo $related_post['title']; ?></a>
                                    </h3>
                                    <div class="blog-card-meta">
                                        <div class="blog-card-date">
                                            <i class="far fa-calendar-alt"></i>
                                            <span><?php echo $related_post['formatted_date']; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Comments -->
            <div id="comments" class="blog-post-comments">
                <h2 class="blog-post-comments-title">Bình luận (<?php echo count($comments); ?>)</h2>

                <?php if (!empty($comment_success)): ?>
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                        <p><?php echo $comment_success; ?></p>
                    </div>
                <?php endif; ?>

                <?php if (!empty($comment_error)): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                        <p><?php echo $comment_error; ?></p>
                    </div>
                <?php endif; ?>

                <?php if (!empty($comments)): ?>
                    <?php foreach ($comments as $comment): ?>
                        <div class="blog-post-comment">
                            <div class="blog-post-comment-avatar">
                                <?php if (!empty($comment['avatar'])): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $comment['avatar']; ?>" alt="<?php echo $comment['full_name'] ?? $comment['author_name']; ?>">
                                <?php else: ?>
                                    <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $comment['full_name'] ?? $comment['author_name']; ?>">
                                <?php endif; ?>
                            </div>
                            <div class="blog-post-comment-content">
                                <div class="blog-post-comment-header">
                                    <span class="blog-post-comment-author"><?php echo $comment['full_name'] ?? $comment['author_name']; ?></span>
                                    <span class="blog-post-comment-date"><?php echo date('d/m/Y H:i', strtotime($comment['created_at'])); ?></span>
                                </div>
                                <div class="blog-post-comment-body">
                                    <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                                </div>
                                <a href="#comment-form" class="blog-post-comment-reply" data-parent-id="<?php echo $comment['id']; ?>" data-author="<?php echo $comment['full_name'] ?? $comment['author_name']; ?>">Trả lời</a>

                                <?php if (!empty($comment['replies'])): ?>
                                    <div class="blog-post-comment-replies">
                                        <?php foreach ($comment['replies'] as $reply): ?>
                                            <div class="blog-post-comment">
                                                <div class="blog-post-comment-avatar">
                                                    <?php if (!empty($reply['avatar'])): ?>
                                                        <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $reply['avatar']; ?>" alt="<?php echo $reply['full_name'] ?? $reply['author_name']; ?>">
                                                    <?php else: ?>
                                                        <img src="<?php echo BASE_URL; ?>/assets/images/default-avatar.png" alt="<?php echo $reply['full_name'] ?? $reply['author_name']; ?>">
                                                    <?php endif; ?>
                                                </div>
                                                <div class="blog-post-comment-content">
                                                    <div class="blog-post-comment-header">
                                                        <span class="blog-post-comment-author"><?php echo $reply['full_name'] ?? $reply['author_name']; ?></span>
                                                        <span class="blog-post-comment-date"><?php echo date('d/m/Y H:i', strtotime($reply['created_at'])); ?></span>
                                                    </div>
                                                    <div class="blog-post-comment-body">
                                                        <?php echo nl2br(htmlspecialchars($reply['content'])); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-gray-600 mb-6">Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p>
                <?php endif; ?>

                <!-- Comment Form -->
                <div id="comment-form" class="blog-post-comment-form">
                    <h3 class="blog-post-comment-form-title">Để lại bình luận</h3>
                    <form action="<?php echo BASE_URL; ?>/blog/<?php echo $post['slug']; ?>#comments" method="POST">
                        <input type="hidden" name="parent_id" id="comment-parent-id" value="">
                        <div id="reply-to-container" style="display: none;" class="mb-4 p-3 bg-gray-100 rounded-lg">
                            Đang trả lời: <span id="reply-to-name"></span>
                            <button type="button" id="cancel-reply" class="ml-2 text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i> Hủy
                            </button>
                        </div>

                        <?php if (!is_logged_in()): ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div class="form-group">
                                    <label for="author_name">Tên của bạn *</label>
                                    <input type="text" id="author_name" name="author_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="author_email">Email của bạn *</label>
                                    <input type="email" id="author_email" name="author_email" required>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="form-group mb-4">
                            <label for="comment_content">Bình luận của bạn *</label>
                            <textarea id="comment_content" name="comment_content" required></textarea>
                        </div>

                        <button type="submit" name="comment_submit" class="form-submit">Gửi bình luận</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="blog-sidebar">
            <!-- Categories Widget -->
            <div class="sidebar-widget">
                <h3 class="sidebar-widget-title">Danh mục</h3>
                <ul class="sidebar-categories">
                    <?php foreach (get_blog_categories() as $category): ?>
                        <li>
                            <a href="<?php echo BASE_URL; ?>/blog.php?category=<?php echo $category['slug']; ?>">
                                <?php echo $category['name']; ?>
                                <span class="category-count">
                                    <?php echo count_blog_posts($category['id']); ?>
                                </span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Tags Widget -->
            <div class="sidebar-widget">
                <h3 class="sidebar-widget-title">Tags</h3>
                <div class="sidebar-tags">
                    <?php foreach (get_blog_tags() as $tag): ?>
                        <a href="<?php echo BASE_URL; ?>/blog.php?tag=<?php echo $tag['slug']; ?>">
                            <?php echo $tag['name']; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript cho form bình luận -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Xử lý nút trả lời
    const replyLinks = document.querySelectorAll('.blog-post-comment-reply');
    const parentIdInput = document.getElementById('comment-parent-id');
    const replyContainer = document.getElementById('reply-to-container');
    const replyToName = document.getElementById('reply-to-name');
    const cancelReply = document.getElementById('cancel-reply');

    replyLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const parentId = this.getAttribute('data-parent-id');
            const authorName = this.getAttribute('data-author');

            parentIdInput.value = parentId;
            replyToName.textContent = authorName;
            replyContainer.style.display = 'block';

            // Cuộn đến form bình luận
            document.getElementById('comment-form').scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Xử lý nút hủy trả lời
    if (cancelReply) {
        cancelReply.addEventListener('click', function() {
            parentIdInput.value = '';
            replyContainer.style.display = 'none';
        });
    }
});
</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
