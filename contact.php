<?php
// Thiết lập tiêu đề trang
$page_title = '<PERSON><PERSON><PERSON> hệ';
$page_description = '<PERSON>ên hệ với Nội Thất Băng Vũ - Cung cấp các sản phẩm nội thất cao cấp, chất lượ<PERSON>';

// Include header
include_once 'partials/header.php';

// X<PERSON> lý form liên hệ
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        $error_message = 'Phiên làm việc đã hết hạn. Vui lòng thử lại.';
    } else {
        // Lấy dữ liệu từ form
        $name = sanitize($_POST['name']);
        $email = sanitize($_POST['email']);
        $phone = sanitize($_POST['phone']);
        $subject = sanitize($_POST['subject']);
        $message = sanitize($_POST['message']);
        
        // Kiểm tra dữ liệu
        if (empty($name) || empty($email) || empty($phone) || empty($subject) || empty($message)) {
            $error_message = 'Vui lòng nhập đầy đủ thông tin.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Email không hợp lệ.';
        } else {
            // Gửi email (giả lập)
            // Trong thực tế, bạn sẽ sử dụng hàm mail() hoặc thư viện PHPMailer để gửi email
            
            // Hiển thị thông báo thành công
            $success_message = 'Cảm ơn bạn đã liên hệ với chúng tôi. Chúng tôi sẽ phản hồi trong thời gian sớm nhất.';
            
            // Xóa dữ liệu form
            $name = $email = $phone = $subject = $message = '';
        }
    }
}
?>

<!-- Breadcrumb -->
<div class="bg-gray-100 py-3">
    <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-gray-600">
            <a href="<?php echo BASE_URL; ?>" class="hover:text-blue-500">Trang chủ</a>
            <span class="mx-2">/</span>
            <span class="text-gray-800">Liên hệ</span>
        </div>
    </div>
</div>

<!-- Contact -->
<div class="py-12 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-12">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Liên hệ với chúng tôi</h1>
            <p class="text-gray-600">
                Nếu bạn có bất kỳ câu hỏi hoặc yêu cầu nào, vui lòng liên hệ với chúng tôi. Chúng tôi sẽ phản hồi trong thời gian sớm nhất.
            </p>
        </div>
        
        <div class="flex flex-col md:flex-row md:space-x-6">
            <!-- Contact Form -->
            <div class="md:w-2/3 mb-8 md:mb-0">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Gửi tin nhắn cho chúng tôi</h2>
                    
                    <?php if ($success_message): ?>
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                        <p><?php echo $success_message; ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                        <p><?php echo $error_message; ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <form action="<?php echo BASE_URL; ?>/contact.php" method="POST" class="validate-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="mb-4">
                            <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Họ và tên <span class="text-red-500">*</span></label>
                            <input type="text" name="name" id="name" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo isset($name) ? $name : ''; ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email <span class="text-red-500">*</span></label>
                            <input type="email" name="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo isset($email) ? $email : ''; ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="phone" class="block text-gray-700 text-sm font-bold mb-2">Số điện thoại <span class="text-red-500">*</span></label>
                            <input type="text" name="phone" id="phone" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo isset($phone) ? $phone : ''; ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="subject" class="block text-gray-700 text-sm font-bold mb-2">Tiêu đề <span class="text-red-500">*</span></label>
                            <input type="text" name="subject" id="subject" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo isset($subject) ? $subject : ''; ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="message" class="block text-gray-700 text-sm font-bold mb-2">Nội dung <span class="text-red-500">*</span></label>
                            <textarea name="message" id="message" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" required><?php echo isset($message) ? $message : ''; ?></textarea>
                        </div>
                        
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200">
                            Gửi tin nhắn
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="md:w-1/3">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Thông tin liên hệ</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="text-blue-500 text-xl mr-3 mt-1">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Địa chỉ</h3>
                                <p class="text-gray-600">123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="text-blue-500 text-xl mr-3 mt-1">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Điện thoại</h3>
                                <p class="text-gray-600">0123 456 789</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="text-blue-500 text-xl mr-3 mt-1">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Email</h3>
                                <p class="text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="text-blue-500 text-xl mr-3 mt-1">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Giờ làm việc</h3>
                                <p class="text-gray-600">Thứ 2 - Thứ 7: 8:00 - 17:00</p>
                                <p class="text-gray-600">Chủ nhật: Nghỉ</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Kết nối với chúng tôi</h2>
                    
                    <div class="flex space-x-4">
                        <a href="#" class="bg-blue-600 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-blue-700 transition duration-200">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="bg-blue-400 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-blue-500 transition duration-200">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="bg-pink-600 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-pink-700 transition duration-200">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="bg-red-600 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-red-700 transition duration-200">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Map -->
<div class="py-12 bg-gray-100">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Bản đồ</h2>
            <p class="text-gray-600">
                Tìm đường đến cửa hàng của chúng tôi
            </p>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4241674197956!2d106.69173157486087!3d10.780260089318513!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f3a9d8d1bb3%3A0xd2ecb62ffd4a828a!2zMjMgTMOqIExvaSBQaMaw4budbmcgQsOqbiBOZ2jDqSBRdeG6rW4gMSBUaMOgbmggcGjhu5EgSOG7kyBDaMOtIE1pbmg!5e0!3m2!1svi!2s!4v1682333145222!5m2!1svi!2s" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
