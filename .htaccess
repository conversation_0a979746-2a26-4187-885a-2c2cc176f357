# Enable URL rewriting
RewriteEngine On

# Set the base directory
# For localhost development: /noithatbangvu/
# For production domain: /
RewriteBase /noithatbangvu/

# Redirect to HTTPS (uncomment when SSL is available)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slash
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ $1 [L,R=301]

# Friendly URLs for products
RewriteRule ^san-pham/([a-zA-Z0-9-]+)$ product.php?slug=$1 [L,QSA]

# Friendly URLs for categories
RewriteRule ^danh-muc/([a-zA-Z0-9-]+)$ category.php?slug=$1 [L,QSA]

# Friendly URLs for blog posts
RewriteRule ^blog/([a-zA-Z0-9-]+)$ blog-post.php?slug=$1 [L,QSA]

# Friendly URLs for pages
RewriteRule ^gioi-thieu$ about.php [L]
RewriteRule ^lien-he$ contact.php [L]
RewriteRule ^san-pham$ products.php [L]
RewriteRule ^tim-kiem$ search.php [L]
RewriteRule ^gio-hang$ cart.php [L]
RewriteRule ^thanh-toan$ checkout.php [L]
RewriteRule ^cam-on$ thank-you.php [L]
# Chuyển hướng URL đăng nhập/đăng ký cũ sang URL mới
RewriteRule ^dang-nhap$ auth.php [L]
RewriteRule ^dang-ky$ auth.php?register=1 [L]
RewriteRule ^dang-xuat$ logout.php [L]
RewriteRule ^blog$ blog.php [L]

# Friendly URLs for account pages
RewriteRule ^tai-khoan$ account/profile.php [L]
RewriteRule ^tai-khoan/don-hang$ account/orders.php [L]
RewriteRule ^tai-khoan/don-hang/([0-9]+)$ account/order-detail.php?id=$1 [L,QSA]
RewriteRule ^tai-khoan/doi-mat-khau$ account/change-password.php [L]

# Handle 404 errors
ErrorDocument 404 /noithatbangvu/404.php

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Enable gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect config files
<FilesMatch "^(config\.php|database\.sql)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
