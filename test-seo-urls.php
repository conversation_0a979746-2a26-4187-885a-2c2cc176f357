<?php
// Test file để kiểm tra URL thân thiện SEO
require_once 'includes/init.php';

// L<PERSON>y một vài sản phẩm để test
$products = get_products(5);

echo "<h1>Test SEO-Friendly URLs</h1>";
echo "<p>Kiểm tra các URL thân thiện SEO:</p>";

if (!empty($products)) {
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<th>Tên sản phẩm</th>";
    echo "<th>Slug</th>";
    echo "<th>URL cũ (query string)</th>";
    echo "<th>URL mới (SEO-friendly)</th>";
    echo "<th>Test Link</th>";
    echo "</tr>";
    
    foreach ($products as $product) {
        $old_url = BASE_URL . "/product.php?slug=" . $product['slug'];
        $new_url = get_product_url($product['slug']);
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . htmlspecialchars($product['slug']) . "</td>";
        echo "<td><code>" . $old_url . "</code></td>";
        echo "<td><code>" . $new_url . "</code></td>";
        echo "<td><a href='" . $new_url . "' target='_blank'>Test →</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Không có sản phẩm nào để test.</p>";
}

echo "<h2>URL Format Examples</h2>";
echo "<p>Khi bạn chuyển sang tên miền thật, URL sẽ có dạng:</p>";
echo "<ul>";
echo "<li><strong>Localhost hiện tại:</strong> <code>http://localhost/noithatbangvu/san-pham/ten-san-pham</code></li>";
echo "<li><strong>Tên miền thật:</strong> <code>https://noithatbangvu.shop/san-pham/ten-san-pham</code></li>";
echo "</ul>";

echo "<h2>Cấu hình cần thay đổi khi deploy</h2>";
echo "<ol>";
echo "<li><strong>File config/config.php:</strong> Thay đổi BASE_URL từ 'http://localhost/noithatbangvu' thành 'https://noithatbangvu.shop'</li>";
echo "<li><strong>File .htaccess:</strong> Thay đổi RewriteBase từ '/noithatbangvu/' thành '/'</li>";
echo "<li><strong>Bật HTTPS redirect:</strong> Uncomment dòng redirect HTTPS trong .htaccess</li>";
echo "</ol>";

echo "<h2>Test Helper Functions</h2>";
echo "<p>Các helper function đã được tạo:</p>";
echo "<ul>";
echo "<li><code>get_product_url(\$slug)</code> - Tạo URL sản phẩm</li>";
echo "<li><code>get_category_url(\$slug)</code> - Tạo URL danh mục</li>";
echo "<li><code>get_blog_url(\$slug)</code> - Tạo URL blog</li>";
echo "</ul>";

// Test helper functions
if (!empty($products)) {
    $test_product = $products[0];
    echo "<h3>Test Helper Functions:</h3>";
    echo "<p>Sản phẩm test: " . htmlspecialchars($test_product['name']) . "</p>";
    echo "<p>get_product_url('" . $test_product['slug'] . "') = <code>" . get_product_url($test_product['slug']) . "</code></p>";
}
?>
