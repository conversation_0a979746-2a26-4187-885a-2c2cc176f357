<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sản <PERSON>t - Furni Deluxe</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Alpine.js for interactivity -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        /* Custom styles */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F8F9FA; /* Light gray background */
        }
        .price-range-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 4px;
            background: #d3d3d3;
            outline: none;
            opacity: 0.7;
            -webkit-transition: .2s;
            transition: opacity .2s;
            border-radius: 5px;
        }
        .price-range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #F97316; /* Orange-500 */
            cursor: pointer;
            border-radius: 50%;
        }
        .price-range-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #F97316;
            cursor: pointer;
            border-radius: 50%;
        }
        .color-swatch {
            transition: transform 0.2s ease-in-out;
        }
        .color-swatch:hover {
            transform: scale(1.1);
        }
        .product-card {
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        [x-cloak] { display: none !important; }
    </style>
</head>
<body x-data="{ filterOpen: false }">

    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-40">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex-shrink-0">
                    <a href="#" class="text-2xl font-bold text-orange-600">Furni<span class="text-gray-800">Deluxe</span></a>
                </div>
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-gray-600 hover:text-orange-500 transition-colors">Trang Chủ</a>
                    <a href="#" class="text-orange-500 font-semibold border-b-2 border-orange-500 pb-1">Sản Phẩm</a>
                    <a href="#" class="text-gray-600 hover:text-orange-500 transition-colors">Bộ Sưu Tập</a>
                    <a href="#" class="text-gray-600 hover:text-orange-500 transition-colors">Về Chúng Tôi</a>
                    <a href="#" class="text-gray-600 hover:text-orange-500 transition-colors">Liên Hệ</a>
                </nav>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-orange-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                    </button>
                    <button class="text-gray-500 hover:text-orange-500 relative">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                        <span class="absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">2</span>
                    </button>
                    <button class="lg:hidden text-gray-600 hover:text-orange-500" @click="filterOpen = !filterOpen">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16m-7 6h7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumbs -->
        <div class="mb-6 text-sm text-gray-500">
            <a href="#" class="hover:text-orange-500">Trang chủ</a>
            <span class="mx-2">/</span>
            <span>Sản phẩm</span>
        </div>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Filters Sidebar (Left Column) -->
            <aside :class="{'block': filterOpen, 'hidden': !filterOpen}" class="w-full lg:w-1/4 lg:block" x-cloak>
                <div class="bg-white p-6 rounded-lg shadow-sm space-y-6">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800">Bộ Lọc</h2>
                        <button class="text-gray-500 hover:text-orange-500 text-sm font-medium">Xóa tất cả</button>
                    </div>

                    <!-- Category Filter -->
                    <div x-data="{ open: ['Phòng khách'] }" class="border-t pt-6">
                        <h3 class="font-semibold text-gray-700 mb-3">Danh Mục</h3>
                        <div class="space-y-2">
                            <!-- Parent Category 1 -->
                            <div>
                                <button @click="open.includes('Phòng khách') ? open = open.filter(i => i !== 'Phòng khách') : open.push('Phòng khách')" class="w-full flex justify-between items-center text-left">
                                    <span class="font-medium text-gray-600">Phòng khách</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform" :class="{'rotate-90': open.includes('Phòng khách')}" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
                                </button>
                                <div x-show="open.includes('Phòng khách')" class="mt-2 pl-4 space-y-2" x-cloak>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Ghế Sofa</span></label></div>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400" checked> <span class="ml-2 text-gray-600">Bàn Cafe</span></label></div>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Kệ TV</span></label></div>
                                </div>
                            </div>
                            <!-- Parent Category 2 -->
                            <div>
                                <button @click="open.includes('Phòng ngủ') ? open = open.filter(i => i !== 'Phòng ngủ') : open.push('Phòng ngủ')" class="w-full flex justify-between items-center text-left">
                                    <span class="font-medium text-gray-600">Phòng ngủ</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform" :class="{'rotate-90': open.includes('Phòng ngủ')}" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
                                </button>
                                <div x-show="open.includes('Phòng ngủ')" class="mt-2 pl-4 space-y-2" x-cloak>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Giường Ngủ</span></label></div>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Tủ Quần Áo</span></label></div>
                                    <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Bàn Trang Điểm</span></label></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Promotions Filter -->
                    <div class="border-t pt-6">
                        <h3 class="font-semibold text-gray-700 mb-3">Khuyến Mãi</h3>
                        <div class="space-y-2">
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Đang giảm giá</span></label></div>
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Hàng mới về</span></label></div>
                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="border-t pt-6" x-data="{ price: 5000000 }">
                        <h3 class="font-semibold text-gray-700 mb-3">Khoảng Giá</h3>
                        <input type="range" min="500000" max="20000000" x-model="price" class="price-range-slider">
                        <div class="flex justify-between text-sm text-gray-600 mt-2">
                            <span>500K</span>
                            <span class="font-semibold text-orange-600" x-text="new Intl.NumberFormat('vi-VN').format(price) + 'đ'"></span>
                            <span>20Tr</span>
                        </div>
                    </div>
                    
                    <!-- Rating Filter -->
                    <div class="border-t pt-6" x-data="{rating: 4}">
                        <h3 class="font-semibold text-gray-700 mb-3">Đánh Giá</h3>
                        <div class="space-y-2">
                            <div @click="rating = 5" class="flex items-center cursor-pointer">
                                <div class="flex items-center">
                                    <template x-for="i in 5">
                                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    </template>
                                </div>
                                <span class="ml-2 text-gray-600" :class="{'text-orange-600 font-semibold': rating === 5}">Từ 5 sao</span>
                            </div>
                            <div @click="rating = 4" class="flex items-center cursor-pointer">
                                <div class="flex items-center">
                                    <template x-for="i in 4">
                                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    </template>
                                     <template x-for="i in 1">
                                        <svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    </template>
                                </div>
                                <span class="ml-2 text-gray-600" :class="{'text-orange-600 font-semibold': rating === 4}">Từ 4 sao</span>
                            </div>
                             <div @click="rating = 3" class="flex items-center cursor-pointer">
                                <div class="flex items-center">
                                    <template x-for="i in 3">
                                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    </template>
                                     <template x-for="i in 2">
                                        <svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    </template>
                                </div>
                                <span class="ml-2 text-gray-600" :class="{'text-orange-600 font-semibold': rating === 3}">Từ 3 sao</span>
                            </div>
                        </div>
                    </div>

                    <!-- Color Filter -->
                    <div class="border-t pt-6">
                        <h3 class="font-semibold text-gray-700 mb-3">Màu Sắc</h3>
                        <div class="flex flex-wrap gap-3">
                            <span class="color-swatch w-8 h-8 rounded-full bg-gray-800 cursor-pointer border-2 border-orange-500 ring-2 ring-orange-200"></span>
                            <span class="color-swatch w-8 h-8 rounded-full bg-white border cursor-pointer"></span>
                            <span class="color-swatch w-8 h-8 rounded-full bg-yellow-900 cursor-pointer"></span>
                            <span class="color-swatch w-8 h-8 rounded-full bg-gray-400 cursor-pointer"></span>
                            <span class="color-swatch w-8 h-8 rounded-full bg-blue-900 cursor-pointer"></span>
                            <span class="color-swatch w-8 h-8 rounded-full bg-green-800 cursor-pointer"></span>
                        </div>
                    </div>

                    <!-- Material Filter -->
                    <div class="border-t pt-6">
                        <h3 class="font-semibold text-gray-700 mb-3">Chất Liệu</h3>
                        <div class="space-y-2">
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Gỗ Sồi</span></label></div>
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Kim Loại</span></label></div>
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400" checked> <span class="ml-2 text-gray-600">Vải Nỉ</span></label></div>
                            <div><label class="flex items-center"><input type="checkbox" class="form-checkbox text-orange-500 rounded focus:ring-orange-400"> <span class="ml-2 text-gray-600">Da Thật</span></label></div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Products Grid (Right Column) -->
            <section class="w-full lg:w-3/4">
                <!-- Header with Sorting -->
                <div class="flex flex-col sm:flex-row justify-between items-center mb-6 bg-white p-4 rounded-lg shadow-sm">
                    <p class="text-gray-600 mb-2 sm:mb-0">Hiển thị <span class="font-semibold text-gray-800">12</span> trên <span class="font-semibold text-gray-800">56</span> sản phẩm</p>
                    <div class="flex items-center space-x-4">
                        <select class="form-select rounded-md border-gray-300 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50 text-sm">
                            <option>Sắp xếp theo: Mới nhất</option>
                            <option>Giá: Thấp đến cao</option>
                            <option>Giá: Cao đến thấp</option>
                            <option>Tên: A-Z</option>
                        </select>
                        <div class="hidden sm:flex items-center space-x-2 text-gray-500">
                            <button class="p-2 rounded-md bg-orange-100 text-orange-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>
                            </button>
                            <button class="p-2 rounded-md hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" /></svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- Product Card 1 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <div class="relative">
                            <img src="https://placehold.co/400x300/FFF0E6/333333?text=Ghế+Sofa+Nordic" alt="Ghế Sofa Nordic" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                            <span class="absolute top-3 left-3 bg-green-500 text-white text-xs font-semibold px-2 py-1 rounded">MỚI</span>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Ghế Sofa Vải Nordic</h3>
                            <p class="text-gray-500 text-sm mb-3">Phong cách tối giản</p>
                            <div class="flex items-center justify-between">
                                <p class="text-xl font-bold text-orange-600">7.990.000đ</p>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Product Card 2 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <div class="relative">
                            <img src="https://placehold.co/400x300/FFF0E6/333333?text=Bàn+Cafe+Gỗ+Sồi" alt="Bàn Cafe Gỗ Sồi" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                            <span class="absolute top-3 left-3 bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded">GIẢM 20%</span>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Bàn Cafe Gỗ Sồi Mỹ</h3>
                            <p class="text-gray-500 text-sm mb-3">Thiết kế hiện đại</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-baseline gap-2">
                                    <p class="text-xl font-bold text-orange-600">2.800.000đ</p>
                                    <p class="text-sm text-gray-400 line-through">3.500.000đ</p>
                                </div>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Product Card 3 -->
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <img src="https://placehold.co/400x300/FFF0E6/333333?text=Kệ+TV+Tối+Giản" alt="Kệ TV Tối Giản" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Kệ TV Tối Giản</h3>
                            <p class="text-gray-500 text-sm mb-3">Gọn gàng, tinh tế</p>
                            <div class="flex items-center justify-between">
                                <p class="text-xl font-bold text-orange-600">4.250.000đ</p>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                     <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <img src="https://placehold.co/400x300/FFF0E6/333333?text=Giường+Ngủ+Hoàng+Gia" alt="Giường Ngủ Hoàng Gia" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Giường Ngủ Hoàng Gia</h3>
                            <p class="text-gray-500 text-sm mb-3">Sang trọng, đẳng cấp</p>
                            <div class="flex items-center justify-between">
                                <p class="text-xl font-bold text-orange-600">15.500.000đ</p>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                     <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <div class="relative">
                            <img src="https://placehold.co/400x300/FFF0E6/333333?text=Tủ+Quần+Áo+Cánh+Kính" alt="Tủ Quần Áo Cánh Kính" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                             <span class="absolute top-3 left-3 bg-blue-500 text-white text-xs font-semibold px-2 py-1 rounded">BÁN CHẠY</span>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Tủ Quần Áo Cánh Kính</h3>
                            <p class="text-gray-500 text-sm mb-3">Lưu trữ thông minh</p>
                            <div class="flex items-center justify-between">
                                <p class="text-xl font-bold text-orange-600">9.100.000đ</p>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                     <div class="bg-white rounded-lg overflow-hidden shadow-sm product-card">
                        <img src="https://placehold.co/400x300/FFF0E6/333333?text=Ghế+Armchair+Thư+Giãn" alt="Ghế Armchair Thư Giãn" class="w-full h-56 object-cover" onerror="this.onerror=null;this.src='https://placehold.co/400x300/FFF0E6/333333?text=Image+Not+Found';">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-800 truncate">Ghế Armchair Thư Giãn</h3>
                            <p class="text-gray-500 text-sm mb-3">Êm ái, thoải mái</p>
                            <div class="flex items-center justify-between">
                                <p class="text-xl font-bold text-orange-600">3.890.000đ</p>
                                <button class="bg-orange-500 text-white p-2 rounded-full hover:bg-orange-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <nav class="mt-8 flex justify-center" aria-label="Pagination">
                    <ul class="inline-flex items-center -space-x-px">
                        <li>
                            <a href="#" class="py-2 px-3 ml-0 leading-tight text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                                <span class="sr-only">Previous</span>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                            </a>
                        </li>
                        <li>
                            <a href="#" aria-current="page" class="z-10 py-2 px-3 leading-tight text-orange-600 bg-orange-50 border border-orange-300 hover:bg-orange-100 hover:text-orange-700">1</a>
                        </li>
                        <li>
                            <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">2</a>
                        </li>
                        <li>
                            <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">3</a>
                        </li>
                        <li>
                            <span class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300">...</span>
                        </li>
                        <li>
                            <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">10</a>
                        </li>
                        <li>
                            <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700">
                                <span class="sr-only">Next</span>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-orange-500 mb-4">Furni<span class="text-white">Deluxe</span></h3>
                    <p class="text-gray-400">Mang đến không gian sống đẳng cấp và ấm cúng cho ngôi nhà của bạn.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-4">Sản Phẩm</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-orange-400">Phòng Khách</a></li>
                        <li><a href="#" class="hover:text-orange-400">Phòng Ngủ</a></li>
                        <li><a href="#" class="hover:text-orange-400">Phòng Bếp</a></li>
                        <li><a href="#" class="hover:text-orange-400">Đồ Trang Trí</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-4">Hỗ Trợ</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-orange-400">Câu Hỏi Thường Gặp</a></li>
                        <li><a href="#" class="hover:text-orange-400">Chính Sách Bảo Hành</a></li>
                        <li><a href="#" class="hover:text-orange-400">Hướng Dẫn Mua Hàng</a></li>
                        <li><a href="#" class="hover:text-orange-400">Liên Hệ</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-4">Đăng Ký Nhận Tin</h4>
                    <p class="text-gray-400 mb-4">Nhận thông tin về sản phẩm mới và các chương trình khuyến mãi.</p>
                    <form class="flex">
                        <input type="email" placeholder="Email của bạn" class="w-full rounded-l-md border-0 py-2 px-3 text-gray-800">
                        <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded-r-md">Đăng Ký</button>
                    </form>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-500">
                <p>&copy; 2024 FurniDeluxe. Đã đăng ký bản quyền.</p>
            </div>
        </div>
    </footer>

</body>
</html>
