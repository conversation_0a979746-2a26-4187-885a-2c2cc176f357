<?php
// Thiết lập tiêu đề trang
$page_title = '<PERSON>ang xử lý đơn hàng';
$page_description = 'Đang xử lý đơn hàng của bạn tại Nội Thất Băng Vũ';

// Include init
require_once 'includes/init.php';

// Kiểm tra nếu form được gửi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $full_name = sanitize($_POST['full_name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $address = sanitize($_POST['address'] ?? '');
    $note = sanitize($_POST['note'] ?? '');
    $user_id = is_logged_in() ? $_SESSION['user_id'] : null;
    $csrf_token = $_POST['csrf_token'] ?? '';

    // Lưu dữ liệu vào session để sử dụng sau
    $_SESSION['checkout_data'] = [
        'full_name' => $full_name,
        'email' => $email,
        'phone' => $phone,
        'address' => $address,
        'note' => $note,
        'user_id' => $user_id,
        'csrf_token' => $csrf_token
    ];
}
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo BASE_URL; ?>/assets/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-16x16.png">
    <link rel="manifest" href="<?php echo BASE_URL; ?>/assets/images/favicon/site.webmanifest">
    <link rel="mask-icon" href="<?php echo BASE_URL; ?>/assets/images/favicon/safari-pinned-tab.svg" color="#f37321">
    <meta name="msapplication-TileColor" content="#f37321">
    <meta name="theme-color" content="#ffffff">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tailwind.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/order-processing.css">

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Be Vietnam Pro', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(249, 115, 22, 0.03) 0%, transparent 200px),
                radial-gradient(circle at 80% 70%, rgba(249, 115, 22, 0.03) 0%, transparent 200px);
            z-index: -1;
        }

        .processing-container {
            width: 100%;
            max-width: 550px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .order-processing-content {
            opacity: 1;
            transform: translateY(0);
            background-color: white;
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 0 0 1px rgba(0, 0, 0, 0.02),
                0 0 0 5px rgba(249, 115, 22, 0.03);
            animation: fadeIn 0.7s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .order-processing-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #f97316, #fb923c);
        }

        /* Hiệu ứng ánh sáng */
        .order-processing-content::after {
            content: '';
            position: absolute;
            top: 5px;
            left: -50%;
            width: 200%;
            height: 1px;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent
            );
            animation: lightBeam 3s ease-in-out infinite;
        }

        @keyframes lightBeam {
            0% {
                transform: translateX(-30%);
            }
            100% {
                transform: translateX(30%);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(249, 115, 22, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(249, 115, 22, 0);
            }
        }
    </style>
</head>
<body>
    <div class="processing-container">
        <div class="order-processing-content">
            <div class="order-processing-icon">
                <div class="processing-animation" id="processingAnimation">
                    <div class="processing-animation-circle"></div>
                    <div class="processing-animation-inner">
                        <i class="fas fa-shopping-cart processing-animation-icon"></i>
                    </div>
                    <div class="processing-animation-complete" id="processingCheck">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </div>
            <h3 class="order-processing-title">Đang xử lý đơn hàng</h3>
            <p class="order-processing-message">Hệ thống đang xác nhận đơn hàng của bạn<span class="order-processing-dots"></span></p>

            <div class="order-processing-progress">
                <div class="order-processing-progress-bar" id="processingProgressBar"></div>
            </div>

            <div class="order-processing-steps">
                <div class="order-processing-step" id="step1">
                    <div class="order-processing-step-icon"><i class="fas fa-file-invoice"></i></div>
                    <div class="order-processing-step-text">Xác nhận thông tin đơn hàng</div>
                </div>
                <div class="order-processing-step" id="step2">
                    <div class="order-processing-step-icon"><i class="fas fa-box-open"></i></div>
                    <div class="order-processing-step-text">Kiểm tra tồn kho sản phẩm</div>
                </div>
                <div class="order-processing-step" id="step3">
                    <div class="order-processing-step-icon"><i class="fas fa-envelope"></i></div>
                    <div class="order-processing-step-text">Gửi email xác nhận đơn hàng</div>
                </div>
                <div class="order-processing-step" id="step4">
                    <div class="order-processing-step-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="order-processing-step-text">Hoàn tất đơn hàng</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const processingAnimation = document.getElementById('processingAnimation');
            const processingCheck = document.getElementById('processingCheck');
            const progressBar = document.getElementById('processingProgressBar');
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');
            const step4 = document.getElementById('step4');

            // Thêm hiệu ứng âm thanh nhẹ khi hoàn thành
            const completeSound = new Audio('<?php echo BASE_URL; ?>/assets/sounds/complete.mp3');

            // Hàm chuyển đổi trạng thái bước với hiệu ứng mượt mà
            function animateStep(currentStep, nextStep, progress, delay) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        // Kích hoạt bước hiện tại
                        currentStep.classList.add('active');

                        // Cập nhật thanh tiến trình với hiệu ứng mượt mà
                        progressBar.style.width = progress + '%';

                        setTimeout(() => {
                            // Đánh dấu bước hiện tại là đã hoàn thành
                            currentStep.classList.remove('active');
                            currentStep.classList.add('completed');

                            // Kích hoạt bước tiếp theo nếu có
                            if (nextStep) {
                                nextStep.classList.add('active');
                            }

                            resolve();
                        }, 1200);
                    }, delay);
                });
            }

            // Bắt đầu hiệu ứng xử lý với Promise chain để mượt mà hơn
            setTimeout(async () => {
                // Bước 1: Xác nhận thông tin đơn hàng
                await animateStep(step1, step2, 25, 300);

                // Bước 2: Kiểm tra tồn kho sản phẩm
                await animateStep(step2, step3, 50, 300);

                // Bước 3: Gửi email xác nhận đơn hàng
                await animateStep(step3, step4, 75, 500);

                // Bước 4: Hoàn tất đơn hàng
                await animateStep(step4, null, 100, 300);

                // Hiển thị biểu tượng hoàn tất với hiệu ứng đẹp mắt
                const animationCircle = processingAnimation.querySelector('.processing-animation-circle');
                const animationInner = processingAnimation.querySelector('.processing-animation-inner');

                // Ẩn hiệu ứng quay với animation mượt mà
                animationCircle.style.opacity = '0';
                animationCircle.style.transition = 'opacity 0.5s ease';
                animationInner.style.opacity = '0';
                animationInner.style.transition = 'opacity 0.5s ease';

                // Hiển thị biểu tượng hoàn tất
                setTimeout(() => {
                    processingCheck.classList.add('active');

                    // Phát âm thanh hoàn thành nhẹ nhàng
                    try {
                        completeSound.volume = 0.3;
                        completeSound.play();
                    } catch (e) {
                        console.log('Không thể phát âm thanh', e);
                    }

                    // Gửi form xử lý đơn hàng
                    setTimeout(() => {
                        document.getElementById('processOrderForm').submit();
                    }, 1500);
                }, 500);
            }, 500);
        });
    </script>

    <!-- Form xử lý đơn hàng -->
    <form id="processOrderForm" action="<?php echo BASE_URL; ?>/process-checkout.php" method="POST" style="display: none;">
        <?php foreach ($_SESSION['checkout_data'] as $key => $value): ?>
            <input type="hidden" name="<?php echo $key; ?>" value="<?php echo htmlspecialchars($value); ?>">
        <?php endforeach; ?>
    </form>
</body>
</html>
