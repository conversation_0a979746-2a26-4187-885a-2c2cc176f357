# 🎨 HỆ THỐNG THIẾT KẾ NỘI THẤT BĂNG VŨ
## Design System & Style Guide - <PERSON><PERSON><PERSON>ế Đẳng Cấp

---

## 🌈 BẢNG MÀU CHUẨN (COLOR PALETTE)

### **🔥 MÀU CHÍNH - PRIMARY COLORS**
```css
:root {
    /* Cam ch<PERSON>h - <PERSON><PERSON><PERSON> thương hiệu */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;
}
```

### **⚡ MÀU PHỤ - SECONDARY COLORS**
```css
:root {
    /* Xanh đậm - Màu hỗ trợ */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-darker: #1a202c;
    --secondary-light: #435868;
    --secondary-lighter: #2d3748;
}
```

### **💎 MÀU ACCENT - ACCENT COLORS**
```css
:root {
    /* Xanh lá - <PERSON><PERSON><PERSON> nhấn */
    --accent: #4CAF50;
    --accent-dark: #388E3C;
    --accent-light: #81C784;
    
    /* Xanh dương - Links & Actions */
    --blue: #3B82F6;
    --blue-hover: #2563EB;
    
    /* Vàng - Ratings & Highlights */
    --yellow: #FBC02D;
    --yellow-light: #FDD835;
}
```

### **🎯 MÀU TRUNG TÍNH - NEUTRAL COLORS**
```css
:root {
    /* Text Colors */
    --text-primary: #1F2937;
    --text-secondary: #4B5563;
    --text-muted: #6B7280;
    --text-light: #9CA3AF;
    --text-white: #FFFFFF;
    
    /* Background Colors */
    --bg-white: #FFFFFF;
    --bg-light: #F9FAFB;
    --bg-gray: #F3F4F6;
    --bg-dark: #111827;
    
    /* Border Colors */
    --border-light: #E5E7EB;
    --border-gray: #D1D5DB;
    --border-dark: #374151;
}
```

### **🚨 MÀU TRẠNG THÁI - STATUS COLORS**
```css
:root {
    --success: #10B981;
    --success-light: #D1FAE5;
    --warning: #F59E0B;
    --warning-light: #FEF3C7;
    --error: #EF4444;
    --error-light: #FEE2E2;
    --info: #3B82F6;
    --info-light: #DBEAFE;
}
```

---

## 🎭 HIỆU ỨNG & ĐỔ BÓNG (SHADOWS & EFFECTS)

### **Shadow System**
```css
:root {
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    
    /* Glow Effects */
    --glow-primary: 0 0 15px rgba(243, 115, 33, 0.3);
    --glow-white: 0 0 15px rgba(255, 255, 255, 0.3);
    --glow-success: 0 0 15px rgba(16, 185, 129, 0.3);
}
```

### **Gradient System**
```css
:root {
    --gradient-primary: linear-gradient(135deg, #F37321 0%, #F39C21 100%);
    --gradient-secondary: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    --gradient-overlay: linear-gradient(to right, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
    --gradient-hero: linear-gradient(135deg, rgba(243,115,33,0.1) 0%, rgba(255,255,255,0.05) 100%);
}
```

---

## 📝 TYPOGRAPHY - HỆ THỐNG FONT CHỮ

### **Font Family**
```css
:root {
    --font-primary: 'Be Vietnam Pro', 'Montserrat', sans-serif;
    --font-secondary: 'Roboto', sans-serif;
    --font-mono: 'Fira Code', monospace;
}
```

### **Font Sizes**
```css
:root {
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */
}
```

### **Font Weights**
```css
:root {
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
}
```

---

## 🎯 SPACING SYSTEM - HỆ THỐNG KHOẢNG CÁCH

```css
:root {
    --space-1: 0.25rem;     /* 4px */
    --space-2: 0.5rem;      /* 8px */
    --space-3: 0.75rem;     /* 12px */
    --space-4: 1rem;        /* 16px */
    --space-5: 1.25rem;     /* 20px */
    --space-6: 1.5rem;      /* 24px */
    --space-8: 2rem;        /* 32px */
    --space-10: 2.5rem;     /* 40px */
    --space-12: 3rem;       /* 48px */
    --space-16: 4rem;       /* 64px */
    --space-20: 5rem;       /* 80px */
    --space-24: 6rem;       /* 96px */
}
```

---

## 🎨 COMPONENT STYLES - PHONG CÁCH COMPONENT

### **Button System**
```css
/* Primary Button */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--space-3) var(--space-6);
    border-radius: 0.75rem;
    font-weight: var(--font-semibold);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--glow-primary);
}

/* Secondary Button */
.btn-secondary {
    background: var(--bg-white);
    color: var(--primary);
    border: 2px solid var(--primary);
    padding: var(--space-3) var(--space-6);
    border-radius: 0.75rem;
    font-weight: var(--font-semibold);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--primary);
    color: var(--text-white);
    transform: translateY(-2px);
}
```

### **Card System**
```css
.card {
    background: var(--bg-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.card-premium {
    background: var(--gradient-hero);
    border: 2px solid var(--primary);
    box-shadow: var(--shadow-lg), var(--glow-primary);
}
```

### **Input System**
```css
.input-field {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--border-gray);
    border-radius: 0.5rem;
    font-family: var(--font-primary);
    font-size: var(--text-base);
    transition: all 0.3s ease;
    background: var(--bg-white);
}

.input-field:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
}
```

---

## 🎪 ANIMATION SYSTEM - HỆ THỐNG HIỆU ỨNG

### **Transition Timing**
```css
:root {
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### **Common Animations**
```css
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s var(--transition-smooth);
}

.animate-slideInRight {
    animation: slideInRight 0.6s var(--transition-smooth);
}

.animate-pulse {
    animation: pulse 2s infinite;
}
```

---

## 📱 RESPONSIVE BREAKPOINTS

```css
:root {
    --breakpoint-xs: 400px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}
```

---

## 🎯 DESIGN PRINCIPLES - NGUYÊN TẮC THIẾT KẾ

### **1. Hierarchy - Phân Cấp**
- **H1**: Tiêu đề chính (var(--text-4xl), var(--font-bold))
- **H2**: Tiêu đề phụ (var(--text-3xl), var(--font-semibold))
- **H3**: Tiêu đề mục (var(--text-2xl), var(--font-semibold))
- **Body**: Nội dung chính (var(--text-base), var(--font-normal))
- **Caption**: Chú thích (var(--text-sm), var(--font-normal))

### **2. Spacing Rules - Quy Tắc Khoảng Cách**
- **Sections**: var(--space-20) vertical spacing
- **Components**: var(--space-8) between components
- **Elements**: var(--space-4) between related elements
- **Text**: var(--space-2) between lines

### **3. Color Usage - Sử Dụng Màu Sắc**
- **Primary**: Chỉ dùng cho CTA chính, logo, links quan trọng
- **Secondary**: Headers, footers, navigation
- **Accent**: Success states, highlights, special features
- **Neutral**: Text, backgrounds, borders

### **4. Component States - Trạng Thái Component**
- **Default**: Trạng thái bình thường
- **Hover**: Hiệu ứng khi di chuột (transform: translateY(-2px))
- **Active**: Trạng thái khi click (transform: scale(0.98))
- **Focus**: Trạng thái khi focus (box-shadow với primary color)
- **Disabled**: Trạng thái vô hiệu hóa (opacity: 0.5)

---

## 🚀 IMPLEMENTATION GUIDE - HƯỚNG DẪN TRIỂN KHAI

### **Khi thiết kế trang mới:**

1. **Luôn bắt đầu với layout grid**
2. **Sử dụng color variables thay vì hardcode màu**
3. **Áp dụng spacing system nhất quán**
4. **Thêm hover effects cho tất cả interactive elements**
5. **Đảm bảo responsive trên tất cả breakpoints**
6. **Sử dụng animation để tăng trải nghiệm người dùng**

### **Quality Checklist:**
- [ ] Màu sắc nhất quán với design system
- [ ] Typography hierarchy rõ ràng
- [ ] Spacing đều đặn và logic
- [ ] Hover effects mượt mà
- [ ] Responsive hoàn hảo
- [ ] Loading states và animations
- [ ] Accessibility standards

---

**🎨 "Thiết kế không chỉ là làm cho nó đẹp - thiết kế là làm cho nó hoạt động hoàn hảo"**

*Design System v1.0 - Nội Thất Băng Vũ*
