<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cập nhật Database - Popular Search Feature</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px 5px;
            font-weight: bold;
        }
        .btn:hover { background: #0056b3; color: white; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .info { 
            color: #0c5460; 
            background: #d1ecf1; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0; 
            border-left: 4px solid #bee5eb;
        }
        .warning { 
            color: #856404; 
            background: #fff3cd; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0; 
            border-left: 4px solid #ffeaa7;
        }
        h1 { color: #333; margin-bottom: 20px; }
        h2 { color: #555; margin-top: 30px; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Cập nhật Database: Popular Search Feature</h1>
        
        <div class="info">
            <strong>📋 Mô tả:</strong><br>
            Script này sẽ thêm tính năng "Tìm kiếm phổ biến" vào hệ thống quản lý danh mục.
        </div>

        <h2>📊 Những gì sẽ được thêm:</h2>
        <ul>
            <li>✅ Cột <span class="highlight">show_in_popular_search</span> vào bảng categories</li>
            <li>✅ Cột <span class="highlight">popular_search_order</span> vào bảng categories</li>
            <li>✅ Thiết lập 8 danh mục phổ biến mặc định</li>
            <li>✅ Giao diện admin để quản lý</li>
            <li>✅ Hiển thị tags trên trang products.php</li>
        </ul>

        <div class="warning">
            <strong>⚠️ Lưu ý:</strong><br>
            - Backup database trước khi chạy migration<br>
            - Đảm bảo bạn có quyền admin<br>
            - Migration sẽ tự động kiểm tra và không ghi đè dữ liệu hiện có
        </div>

        <h2>🎯 Cách thực hiện:</h2>
        
        <div style="text-align: center; margin: 30px 0;">
            <h3>Chọn một trong hai cách:</h3>
            
            <div style="margin: 20px 0;">
                <h4>🔧 Cách 1: Sử dụng Admin Update System (Khuyến nghị)</h4>
                <a href="admin/update-database.php?file=../sql_updates/add_popular_search_fields.sql" class="btn btn-success">
                    🚀 Chạy Migration qua Admin System
                </a>
                <p><small>Sử dụng hệ thống admin có sẵn, an toàn và có log chi tiết</small></p>
            </div>

            <div style="margin: 20px 0;">
                <h4>⚡ Cách 2: Migration Script Trực tiếp</h4>
                <a href="migration_popular_search.php" class="btn">
                    🔄 Chạy Migration Script
                </a>
                <p><small>Script migration chi tiết với giao diện đẹp</small></p>
            </div>
        </div>

        <h2>📝 Sau khi chạy migration:</h2>
        <ol>
            <li>Truy cập <a href="admin/categories.php">Admin Categories</a> để quản lý</li>
            <li>Kiểm tra <a href="products.php">Trang Products</a> để xem kết quả</li>
            <li>Có thể xóa các file migration sau khi hoàn thành</li>
        </ol>

        <div class="info">
            <strong>🎉 Kết quả mong đợi:</strong><br>
            - Admin có thể bật/tắt danh mục hiển thị trong "Tìm kiếm phổ biến"<br>
            - Có thể sắp xếp thứ tự hiển thị<br>
            - Trang products.php sẽ hiển thị tags theo thiết lập admin<br>
            - Tags được căn giữa và có hiệu ứng đẹp
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="index.php" class="btn">🏠 Về trang chủ</a>
            <a href="admin/index.php" class="btn">🔧 Vào Admin</a>
        </div>
    </div>
</body>
</html>
