<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Results - <PERSON><PERSON><PERSON>t <PERSON>ng <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #F37321 0%, #ff6b35 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #F37321;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 1.2rem;
        }
        
        .test-link {
            display: inline-block;
            background: #F37321;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .test-link:hover {
            background: #e55a0b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
        }
        
        .test-link.secondary {
            background: #6b7280;
        }
        
        .test-link.secondary:hover {
            background: #4b5563;
        }
        
        .test-instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .test-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
        
        .test-instructions li {
            margin-bottom: 8px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 5px 0;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        
        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #1a202c;
            font-size: 1.1rem;
        }
        
        .feature-card p {
            margin: 0;
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-search-plus"></i> Test Kết Quả Tìm Kiếm</h1>
            <p>Kiểm tra hiển thị kết quả tìm kiếm trong trang products.php</p>
        </div>
        
        <div class="test-content">
            <div class="test-instructions">
                <h4><i class="fas fa-info-circle"></i> Tính năng đã hoàn thành:</h4>
                <ul>
                    <li><strong>Chuyển hướng:</strong> search.php → products.php (301 redirect)</li>
                    <li><strong>Hiển thị kết quả:</strong> Sản phẩm được hiển thị trong Products Content</li>
                    <li><strong>Breadcrumb:</strong> Hiển thị "Tìm kiếm: từ khóa" thay vì "Tất cả sản phẩm"</li>
                    <li><strong>Tiêu đề:</strong> "Kết quả tìm kiếm" thay vì "Shop Nội Thất Băng Vũ"</li>
                    <li><strong>Thống kê:</strong> "Tìm thấy X sản phẩm cho từ khóa Y"</li>
                    <li><strong>Không có kết quả:</strong> Thông báo đặc biệt với gợi ý tìm kiếm</li>
                </ul>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search"></i> Test Tìm Kiếm Có Kết Quả</h3>
                <p>Các liên kết này sẽ hiển thị kết quả tìm kiếm trong trang products.php:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=sofa" class="test-link" target="_blank">
                    <i class="fas fa-couch"></i> Tìm "sofa"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=bàn" class="test-link" target="_blank">
                    <i class="fas fa-table"></i> Tìm "bàn"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=ghế" class="test-link" target="_blank">
                    <i class="fas fa-chair"></i> Tìm "ghế"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=tủ" class="test-link" target="_blank">
                    <i class="fas fa-archive"></i> Tìm "tủ"
                </a>
                
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Kết quả sẽ hiển thị trong phần Products Content
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search-minus"></i> Test Tìm Kiếm Không Có Kết Quả</h3>
                <p>Các liên kết này sẽ hiển thị thông báo "Không tìm thấy kết quả":</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=xyz123" class="test-link secondary" target="_blank">
                    <i class="fas fa-question"></i> Tìm "xyz123"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=abcdef" class="test-link secondary" target="_blank">
                    <i class="fas fa-question"></i> Tìm "abcdef"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=notfound" class="test-link secondary" target="_blank">
                    <i class="fas fa-question"></i> Tìm "notfound"
                </a>
                
                <div class="status-info">
                    <i class="fas fa-info-circle"></i> 💡 Sẽ hiển thị thông báo đặc biệt với gợi ý tìm kiếm phổ biến
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-filter"></i> Test Tìm Kiếm Với Filter</h3>
                <p>Test tìm kiếm kết hợp với các bộ lọc:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=sofa&category=1" class="test-link" target="_blank">
                    <i class="fas fa-tags"></i> Sofa + Danh mục
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=bàn&price_min=1000000&price_max=5000000" class="test-link" target="_blank">
                    <i class="fas fa-dollar-sign"></i> Bàn + Khoảng giá
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=ghế&sort=price_asc" class="test-link" target="_blank">
                    <i class="fas fa-sort-amount-up"></i> Ghế + Sắp xếp
                </a>
                
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Các filter sẽ được giữ nguyên khi tìm kiếm
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-list-check"></i> Các Tính Năng Đã Hoàn Thành</h3>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4><i class="fas fa-route text-orange-500"></i> Redirect 301</h4>
                        <p>Tự động chuyển hướng từ search.php sang products.php với đầy đủ tham số</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-database text-blue-500"></i> Tìm Kiếm Database</h4>
                        <p>Sử dụng hàm get_products() với tham số search để tìm kiếm trong database</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-eye text-green-500"></i> Hiển Thị Kết Quả</h4>
                        <p>Kết quả tìm kiếm hiển thị trong phần Products Content với layout nhất quán</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-tags text-purple-500"></i> Breadcrumb Động</h4>
                        <p>Breadcrumb thay đổi theo từ khóa tìm kiếm: "Tìm kiếm: từ khóa"</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-heading text-red-500"></i> Tiêu Đề Động</h4>
                        <p>Tiêu đề trang thay đổi thành "Kết quả tìm kiếm" khi có từ khóa</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-bar text-yellow-500"></i> Thống Kê Kết Quả</h4>
                        <p>Hiển thị số lượng sản phẩm tìm thấy: "Tìm thấy X sản phẩm cho từ khóa Y"</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-exclamation-triangle text-gray-500"></i> Không Có Kết Quả</h4>
                        <p>Thông báo đặc biệt với gợi ý tìm kiếm phổ biến khi không tìm thấy</p>
                    </div>
                    
                    <div class="feature-card">
                        <h4><i class="fas fa-filter text-indigo-500"></i> Giữ Nguyên Filter</h4>
                        <p>Các bộ lọc (category, price, sort) được giữ nguyên khi tìm kiếm</p>
                    </div>
                </div>
            </div>
            
            <div class="test-instructions">
                <h4><i class="fas fa-rocket"></i> Hướng dẫn test:</h4>
                <ol>
                    <li>Click vào các liên kết test ở trên</li>
                    <li>Kiểm tra URL - phải là products.php?keyword=...</li>
                    <li>Kiểm tra breadcrumb hiển thị "Tìm kiếm: từ khóa"</li>
                    <li>Kiểm tra tiêu đề hiển thị "Kết quả tìm kiếm"</li>
                    <li>Kiểm tra thống kê "Tìm thấy X sản phẩm"</li>
                    <li>Test tìm kiếm từ header trang chính</li>
                    <li>Test với từ khóa không tồn tại để xem thông báo</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
