<?php
// REDIRECT TO PRODUCTS.PHP - Chuyển hướng tìm kiếm sang trang sản phẩm
// Xây dựng URL với tất cả tham số
$redirect_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') .
                '://' . $_SERVER['HTTP_HOST'] .
                dirname($_SERVER['SCRIPT_NAME']) . '/products.php';

$params = [];

// Giữ nguyên tất cả tham số GET
foreach ($_GET as $key => $value) {
    if (!empty($value)) {
        $params[] = urlencode($key) . '=' . urlencode($value);
    }
}

if (!empty($params)) {
    $redirect_url .= '?' . implode('&', $params);
}

// Thực hiện redirect với status code 301 (permanent redirect)
header('HTTP/1.1 301 Moved Permanently');
header('Location: ' . $redirect_url);
exit;

// CODE CŨ - GIỮ LẠI ĐỂ THAM KHẢO (sẽ không được thực thi do exit ở trên)
// Include header
include_once 'partials/header.php';

// Lấy từ khóa tìm kiếm và các filter
$keyword = isset($_GET['keyword']) ? sanitize($_GET['keyword']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : null;
$price_min = isset($_GET['price_min']) && $_GET['price_min'] !== '' ? (float)$_GET['price_min'] : null;
$price_max = isset($_GET['price_max']) && $_GET['price_max'] !== '' ? (float)$_GET['price_max'] : null;
$sort_by = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';
$promotion_filter = isset($_GET['promotion']) ? sanitize($_GET['promotion']) : '';

// Thiết lập tiêu đề trang
$page_title = 'Tìm kiếm: ' . $keyword;
$page_description = 'Kết quả tìm kiếm cho từ khóa "' . $keyword . '" tại Nội Thất Băng Vũ';

// Phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Xử lý sort options
$sort_options = [];
switch ($sort_by) {
    case 'price_asc':
        $sort_options = ['by' => 'price', 'order' => 'ASC'];
        break;
    case 'price_desc':
        $sort_options = ['by' => 'price', 'order' => 'DESC'];
        break;
    case 'name_asc':
        $sort_options = ['by' => 'name', 'order' => 'ASC'];
        break;
    case 'name_desc':
        $sort_options = ['by' => 'name', 'order' => 'DESC'];
        break;
    case 'newest':
    default:
        $sort_options = ['by' => 'created_at', 'order' => 'DESC'];
        break;
}

// Lấy sản phẩm với filters
$products = get_products($limit, $offset, $category_filter, null, $keyword, 1, $price_min, $price_max, null, null, $sort_options);
$total_products = count_products($category_filter, $keyword, 1, $price_min, $price_max);
$total_pages = ceil($total_products / $limit);

// Lấy danh sách categories cho filter
$categories = get_categories(null, 1);

// Filter sản phẩm theo promotion nếu có
if ($promotion_filter) {
    $filtered_products = [];
    foreach ($products as $product) {
        switch ($promotion_filter) {
            case 'sale':
                if ($product['sale_price'] > 0) {
                    $filtered_products[] = $product;
                }
                break;
            case 'featured':
                if ($product['featured'] == 1) {
                    $filtered_products[] = $product;
                }
                break;
            case 'flash_sale':
                if ($product['flash_sale'] == 1) {
                    $filtered_products[] = $product;
                }
                break;
        }
    }
    $products = $filtered_products;
    $total_products = count($products);
    $total_pages = ceil($total_products / $limit);
}
?>

<!-- Breadcrumb - Thiết kế hiện đại giống profile.php -->
<div class="modern-breadcrumb">
    <div class="container mx-auto px-4">
        <div class="breadcrumb-wrapper">
            <div class="breadcrumb-item">
                <a href="<?php echo BASE_URL; ?>" class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>Trang chủ</span>
                </a>
            </div>
            <div class="breadcrumb-divider">
                <i class="fas fa-chevron-right"></i>
            </div>
            <div class="breadcrumb-item active">
                <span class="breadcrumb-link">
                    <span class="breadcrumb-icon">
                        <i class="fas fa-search"></i>
                    </span>
                    <span>Tìm kiếm: <?php echo htmlspecialchars($keyword); ?></span>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Link CSS cho breadcrumb hiện đại -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-breadcrumb.css">

<!-- Link CSS cho search page -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/search-page.css">

<!-- Professional Pagination CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/modern-pagination.css">

<!-- Enhanced Search JavaScript -->
<script src="<?php echo BASE_URL; ?>/assets/js/enhanced-search.js" defer></script>

<!-- Search Results - Thiết kế đơn giản và tập trung -->
<div class="py-8 bg-gradient-to-b from-white to-gray-50">
    <div class="container mx-auto px-4">
        <!-- Header Section - Đơn giản và hiệu quả -->
        <div class="mb-8 text-center md:text-left">
            <div class="section-title-badge inline-flex items-center bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full mb-4">
                <span class="flex w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
                Kết quả tìm kiếm
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 relative inline-block mb-4">
                <span class="relative z-10">Tìm kiếm: "<?php echo htmlspecialchars($keyword); ?>"</span>
                <span class="absolute bottom-0 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-2xl mx-auto md:mx-0">
                <?php if (count($products) > 0): ?>
                    Tìm thấy <strong class="text-primary"><?php echo $total_products; ?> sản phẩm</strong> phù hợp với từ khóa của bạn
                <?php else: ?>
                    Không tìm thấy sản phẩm nào phù hợp với từ khóa "<strong class="text-primary"><?php echo htmlspecialchars($keyword); ?></strong>"
                <?php endif; ?>
            </p>
        </div>

        <!-- Enhanced Search Form & Filters -->
        <div class="filter-container mb-8">
            <form action="<?php echo BASE_URL; ?>/products.php" method="GET" class="space-y-6">
                <!-- Main Search Input -->
                <div class="relative">
                    <div class="search-input-container">
                        <input type="text"
                               name="keyword"
                               value="<?php echo htmlspecialchars($keyword); ?>"
                               placeholder="Tìm kiếm sản phẩm nội thất..."
                               class="search-input-enhanced w-full px-4 py-3 pl-12 pr-20 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all"
                               autocomplete="off"
                               id="main-search-input">

                        <!-- Search Icon (Left) -->
                        <div class="search-icon-left">
                            <i class="fas fa-search search-icon"></i>
                            <i class="fas fa-keyboard typing-icon"></i>
                        </div>

                        <!-- Action Buttons (Right) -->
                        <div class="search-actions-right">
                            <!-- Clear Button -->
                            <button type="button" class="search-clear-btn" id="search-clear-btn">
                                <i class="fas fa-times"></i>
                            </button>

                            <!-- Quick Search Button -->
                            <button type="submit" class="search-submit-btn" id="search-submit-btn">
                                <i class="fas fa-search"></i>
                                <span>Tìm kiếm</span>
                            </button>
                        </div>
                    </div>
                    <!-- Search Suggestions Container -->
                    <div id="search-suggestions" class="search-suggestions-container hidden absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50">
                        <!-- Suggestions will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Advanced Filters Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Category Filter - Custom Dropdown -->
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tags text-primary mr-1"></i>
                            Danh mục
                        </label>
                        <div class="custom-dropdown">
                            <button type="button" class="custom-dropdown-trigger">
                                <?php
                                if ($category_filter) {
                                    foreach ($categories as $category) {
                                        if ($category['id'] == $category_filter) {
                                            echo htmlspecialchars($category['name']);
                                            break;
                                        }
                                    }
                                } else {
                                    echo 'Tất cả danh mục';
                                }
                                ?>
                            </button>
                            <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                            <div class="custom-dropdown-menu">
                                <div class="custom-dropdown-option <?php echo !$category_filter ? 'selected' : ''; ?>" data-value="">
                                    Tất cả danh mục
                                </div>
                                <?php foreach ($categories as $category): ?>
                                <div class="custom-dropdown-option <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>" data-value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Promotion Filter - Custom Dropdown -->
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-gift text-primary mr-1"></i>
                            Khuyến mãi
                        </label>
                        <div class="custom-dropdown">
                            <button type="button" class="custom-dropdown-trigger">
                                <?php
                                switch($promotion_filter) {
                                    case 'sale':
                                        echo 'Đang giảm giá';
                                        break;
                                    case 'featured':
                                        echo 'Sản phẩm nổi bật';
                                        break;
                                    case 'flash_sale':
                                        echo 'Flash Sale';
                                        break;
                                    default:
                                        echo 'Tất cả sản phẩm';
                                }
                                ?>
                            </button>
                            <input type="hidden" name="promotion" value="<?php echo $promotion_filter; ?>">
                            <div class="custom-dropdown-menu">
                                <div class="custom-dropdown-option <?php echo !$promotion_filter ? 'selected' : ''; ?>" data-value="">
                                    Tất cả sản phẩm
                                </div>
                                <div class="custom-dropdown-option <?php echo $promotion_filter == 'sale' ? 'selected' : ''; ?>" data-value="sale">
                                    <i class="fas fa-percentage text-red-500 mr-2"></i>
                                    Đang giảm giá
                                </div>
                                <div class="custom-dropdown-option <?php echo $promotion_filter == 'featured' ? 'selected' : ''; ?>" data-value="featured">
                                    <i class="fas fa-star text-yellow-500 mr-2"></i>
                                    Sản phẩm nổi bật
                                </div>
                                <div class="custom-dropdown-option <?php echo $promotion_filter == 'flash_sale' ? 'selected' : ''; ?>" data-value="flash_sale">
                                    <i class="fas fa-bolt text-orange-500 mr-2"></i>
                                    Flash Sale
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Price Range Filter - Custom Dropdown -->
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-dollar-sign text-primary mr-1"></i>
                            Khoảng giá
                        </label>
                        <div class="custom-dropdown">
                            <button type="button" class="custom-dropdown-trigger" title="<?php
                                // Determine current price range display for title
                                if ($price_min && $price_max) {
                                    if ($price_min == 0 && $price_max == 1000000) {
                                        echo 'Dưới 1 triệu';
                                    } elseif ($price_min == 1000000 && $price_max == 3000000) {
                                        echo '1 - 3 triệu';
                                    } elseif ($price_min == 3000000 && $price_max == 5000000) {
                                        echo '3 - 5 triệu';
                                    } elseif ($price_min == 5000000 && $price_max == 10000000) {
                                        echo '5 - 10 triệu';
                                    } elseif ($price_min == 10000000 && $price_max == 20000000) {
                                        echo '10 - 20 triệu';
                                    } elseif ($price_min == 20000000 && !$price_max) {
                                        echo 'Trên 20 triệu';
                                    } else {
                                        echo number_format($price_min, 0, '.', '.') . ' - ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                    }
                                } elseif ($price_min) {
                                    if ($price_min == 20000000) {
                                        echo 'Trên 20 triệu';
                                    } else {
                                        echo 'Từ ' . number_format($price_min, 0, '.', '.') . ' VNĐ';
                                    }
                                } elseif ($price_max) {
                                    echo 'Đến ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                } else {
                                    echo 'Tất cả mức giá';
                                }
                            ?>">
                                <?php
                                // Determine current price range display
                                if ($price_min && $price_max) {
                                    if ($price_min == 0 && $price_max == 1000000) {
                                        echo 'Dưới 1 triệu';
                                    } elseif ($price_min == 1000000 && $price_max == 3000000) {
                                        echo '1 - 3 triệu';
                                    } elseif ($price_min == 3000000 && $price_max == 5000000) {
                                        echo '3 - 5 triệu';
                                    } elseif ($price_min == 5000000 && $price_max == 10000000) {
                                        echo '5 - 10 triệu';
                                    } elseif ($price_min == 10000000 && $price_max == 20000000) {
                                        echo '10 - 20 triệu';
                                    } elseif ($price_min == 20000000 && !$price_max) {
                                        echo 'Trên 20 triệu';
                                    } else {
                                        echo number_format($price_min, 0, '.', '.') . ' - ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                    }
                                } elseif ($price_min) {
                                    if ($price_min == 20000000) {
                                        echo 'Trên 20 triệu';
                                    } else {
                                        echo 'Từ ' . number_format($price_min, 0, '.', '.') . ' VNĐ';
                                    }
                                } elseif ($price_max) {
                                    echo 'Đến ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                } else {
                                    echo 'Tất cả mức giá';
                                }
                                ?>
                            </button>
                            <div class="custom-dropdown-menu">
                                <div class="custom-dropdown-option <?php echo (!$price_min && !$price_max) ? 'selected' : ''; ?>"
                                     data-value="" data-price-min="" data-price-max="">
                                    <i class="fas fa-list text-gray-500 mr-2"></i>
                                    Tất cả mức giá
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 0 && $price_max == 1000000) ? 'selected' : ''; ?>"
                                     data-value="0-1000000" data-price-min="0" data-price-max="1000000">
                                    <i class="fas fa-coins text-green-500 mr-2"></i>
                                    Dưới 1 triệu
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 1000000 && $price_max == 3000000) ? 'selected' : ''; ?>"
                                     data-value="1000000-3000000" data-price-min="1000000" data-price-max="3000000">
                                    <i class="fas fa-money-bill text-blue-500 mr-2"></i>
                                    1 - 3 triệu
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 3000000 && $price_max == 5000000) ? 'selected' : ''; ?>"
                                     data-value="3000000-5000000" data-price-min="3000000" data-price-max="5000000">
                                    <i class="fas fa-money-bill-wave text-indigo-500 mr-2"></i>
                                    3 - 5 triệu
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 5000000 && $price_max == 10000000) ? 'selected' : ''; ?>"
                                     data-value="5000000-10000000" data-price-min="5000000" data-price-max="10000000">
                                    <i class="fas fa-credit-card text-purple-500 mr-2"></i>
                                    5 - 10 triệu
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 10000000 && $price_max == 20000000) ? 'selected' : ''; ?>"
                                     data-value="10000000-20000000" data-price-min="10000000" data-price-max="20000000">
                                    <i class="fas fa-gem text-pink-500 mr-2"></i>
                                    10 - 20 triệu
                                </div>
                                <div class="custom-dropdown-option <?php echo ($price_min == 20000000 && !$price_max) ? 'selected' : ''; ?>"
                                     data-value="20000000-" data-price-min="20000000" data-price-max="">
                                    <i class="fas fa-crown text-yellow-500 mr-2"></i>
                                    Trên 20 triệu
                                </div>
                                <div class="custom-dropdown-option custom-price-option" data-value="custom">
                                    <i class="fas fa-edit text-orange-500 mr-2"></i>
                                    Tùy chỉnh khoảng giá
                                </div>
                            </div>
                        </div>
                        <!-- Hidden inputs for price values -->
                        <input type="hidden" name="price_min" value="<?php echo $price_min; ?>">
                        <input type="hidden" name="price_max" value="<?php echo $price_max; ?>">
                    </div>

                    <!-- Custom Price Range Modal (Hidden by default) -->
                    <div id="custom-price-modal" class="hidden">
                        <div class="modal-content-box">
                            <!-- Modal Header -->
                            <div class="modal-header">
                                <div class="modal-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h3 class="modal-title">Tùy chỉnh khoảng giá</h3>
                                <p class="modal-subtitle">Nhập khoảng giá mong muốn để tìm kiếm chính xác hơn</p>
                                <button type="button" id="close-price-modal" class="modal-close-btn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <!-- Modal Body -->
                            <div class="modal-body">
                                <!-- Info Card -->
                                <div class="info-card">
                                    <div class="info-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div class="info-content">
                                        <h4 class="info-title">Hướng dẫn sử dụng</h4>
                                        <ul class="info-list">
                                            <li>Nhập số tiền không cần dấu chấm (VD: 1000000)</li>
                                            <li>Để trống "Giá đến" nếu không giới hạn</li>
                                            <li>Số tiền sẽ tự động định dạng khi hiển thị</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Input Fields -->
                                <div class="input-group">
                                    <div class="input-field">
                                        <label class="input-label">
                                            <i class="fas fa-arrow-up text-green-500 mr-2"></i>
                                            Giá từ (VNĐ)
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="text" id="custom-price-min" placeholder="Nhập giá tối thiểu"
                                                   class="price-input">
                                            <div class="input-icon">
                                                <i class="fas fa-dong-sign"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="input-field">
                                        <label class="input-label">
                                            <i class="fas fa-arrow-down text-red-500 mr-2"></i>
                                            Giá đến (VNĐ)
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="text" id="custom-price-max" placeholder="Nhập giá tối đa"
                                                   class="price-input">
                                            <div class="input-icon">
                                                <i class="fas fa-dong-sign"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Modal Footer -->
                            <div class="modal-footer">
                                <button type="button" id="cancel-custom-price" class="btn-secondary">
                                    <i class="fas fa-times mr-2"></i>
                                    Hủy bỏ
                                </button>
                                <button type="button" id="apply-custom-price" class="btn-primary">
                                    <i class="fas fa-search mr-2"></i>
                                    Áp dụng lọc
                                </button>
                            </div>
                        </div>
                    </div>

                    

                    <!-- Sort Options - Custom Dropdown -->
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-sort text-primary mr-1"></i>
                            Sắp xếp
                        </label>
                        <div class="custom-dropdown">
                            <button type="button" class="custom-dropdown-trigger">
                                <?php
                                switch($sort_by) {
                                    case 'price_asc':
                                        echo 'Giá: Thấp → Cao';
                                        break;
                                    case 'price_desc':
                                        echo 'Giá: Cao → Thấp';
                                        break;
                                    case 'name_asc':
                                        echo 'Tên: A → Z';
                                        break;
                                    case 'name_desc':
                                        echo 'Tên: Z → A';
                                        break;
                                    case 'newest':
                                    default:
                                        echo 'Mới nhất';
                                }
                                ?>
                            </button>
                            <input type="hidden" name="sort" value="<?php echo $sort_by; ?>">
                            <div class="custom-dropdown-menu">
                                <div class="custom-dropdown-option <?php echo $sort_by == 'newest' ? 'selected' : ''; ?>" data-value="newest">
                                    <i class="fas fa-clock text-blue-500 mr-2"></i>
                                    Mới nhất
                                </div>
                                <div class="custom-dropdown-option <?php echo $sort_by == 'price_asc' ? 'selected' : ''; ?>" data-value="price_asc">
                                    <i class="fas fa-sort-amount-up text-green-500 mr-2"></i>
                                    Giá: Thấp → Cao
                                </div>
                                <div class="custom-dropdown-option <?php echo $sort_by == 'price_desc' ? 'selected' : ''; ?>" data-value="price_desc">
                                    <i class="fas fa-sort-amount-down text-red-500 mr-2"></i>
                                    Giá: Cao → Thấp
                                </div>
                                <div class="custom-dropdown-option <?php echo $sort_by == 'name_asc' ? 'selected' : ''; ?>" data-value="name_asc">
                                    <i class="fas fa-sort-alpha-down text-purple-500 mr-2"></i>
                                    Tên: A → Z
                                </div>
                                <div class="custom-dropdown-option <?php echo $sort_by == 'name_desc' ? 'selected' : ''; ?>" data-value="name_desc">
                                    <i class="fas fa-sort-alpha-up text-indigo-500 mr-2"></i>
                                    Tên: Z → A
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                    <?php if ($keyword || $category_filter || $price_min || $price_max || $promotion_filter): ?>
                    <div class="flex-1 flex flex-wrap gap-2 items-center">
                        <span class="text-sm font-medium text-gray-700 mr-2">
                            <i class="fas fa-filter text-primary mr-1"></i>
                            Bộ lọc:
                        </span>

                        <!-- Keyword Badge -->
                        <?php if ($keyword): ?>
                        <div class="filter-badge keyword-badge">
                            <i class="fas fa-search mr-1 flex-shrink-0"></i>
                            <span class="keyword-text" title="<?php echo htmlspecialchars($keyword); ?>">
                                <?php echo htmlspecialchars($keyword); ?>
                            </span>
                            <button type="button" class="remove-filter ml-2 flex-shrink-0" onclick="removeFilter('keyword')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <?php endif; ?>

                        <!-- Category Badge -->
                        <?php if ($category_filter): ?>
                        <div class="filter-badge category-badge">
                            <i class="fas fa-tags mr-1 flex-shrink-0"></i>
                            <span class="badge-text" title="<?php
                                foreach ($categories as $category) {
                                    if ($category['id'] == $category_filter) {
                                        echo htmlspecialchars($category['name']);
                                        break;
                                    }
                                }
                            ?>">
                                <?php
                                foreach ($categories as $category) {
                                    if ($category['id'] == $category_filter) {
                                        echo htmlspecialchars($category['name']);
                                        break;
                                    }
                                }
                                ?>
                            </span>
                            <button type="button" class="remove-filter ml-2 flex-shrink-0" onclick="removeFilter('category')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <?php endif; ?>

                        <!-- Price Range Badge -->
                        <?php if ($price_min || $price_max): ?>
                        <div class="filter-badge price-badge">
                            <i class="fas fa-dollar-sign mr-1 flex-shrink-0"></i>
                            <span class="badge-text" title="<?php
                                if ($price_min && $price_max) {
                                    echo number_format($price_min, 0, '.', '.') . ' - ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                } elseif ($price_min) {
                                    echo 'Từ ' . number_format($price_min, 0, '.', '.') . ' VNĐ';
                                } elseif ($price_max) {
                                    echo 'Đến ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                }
                            ?>">
                                <?php
                                if ($price_min && $price_max) {
                                    echo number_format($price_min, 0, '.', '.') . ' - ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                } elseif ($price_min) {
                                    echo 'Từ ' . number_format($price_min, 0, '.', '.') . ' VNĐ';
                                } elseif ($price_max) {
                                    echo 'Đến ' . number_format($price_max, 0, '.', '.') . ' VNĐ';
                                }
                                ?>
                            </span>
                            <button type="button" class="remove-filter ml-2 flex-shrink-0" onclick="removeFilter('price')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <?php endif; ?>

                        <!-- Promotion Badge -->
                        <?php if ($promotion_filter): ?>
                        <div class="filter-badge promotion-badge">
                            <?php
                            switch($promotion_filter) {
                                case 'sale':
                                    echo '<i class="fas fa-percentage mr-1 flex-shrink-0"></i>';
                                    echo '<span class="badge-text" title="Đang giảm giá">Đang giảm giá</span>';
                                    break;
                                case 'featured':
                                    echo '<i class="fas fa-star mr-1 flex-shrink-0"></i>';
                                    echo '<span class="badge-text" title="Sản phẩm nổi bật">Sản phẩm nổi bật</span>';
                                    break;
                                case 'flash_sale':
                                    echo '<i class="fas fa-bolt mr-1 flex-shrink-0"></i>';
                                    echo '<span class="badge-text" title="Flash Sale">Flash Sale</span>';
                                    break;
                            }
                            ?>
                            <button type="button" class="remove-filter ml-2 flex-shrink-0" onclick="removeFilter('promotion')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <?php endif; ?>

                        <!-- Clear All Badge -->
                        <div class="filter-badge clear-all-badge" onclick="clearAllFilters()" style="cursor: pointer;">
                            <i class="fas fa-broom mr-1 flex-shrink-0"></i>
                            <span class="badge-text">Xóa tất cả</span>
                            <i class="fas fa-times ml-2 flex-shrink-0"></i>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <?php if (count($products) > 0): ?>
        <!-- Products Grid -->
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 md-plus:grid-cols-3 lg:grid-cols-3 lg-plus:grid-cols-4 xl:grid-cols-4 xxl:grid-cols-4 ultra:grid-cols-4 gap-6">
            <?php foreach ($products as $product): ?>
            <div class="group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 hover:-translate-y-2">
                <div class="product-image-wrapper relative">
                    <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $product['slug']; ?>"
                        class="block product-image">
                        <?php if ($product['image']): ?>
                        <img src="<?php echo BASE_URL; ?>/uploads/products/<?php echo $product['image']; ?>"
                            alt="<?php echo $product['name']; ?>"
                            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                        <?php else: ?>
                        <div class="w-full h-full bg-gray-300 flex items-center justify-center absolute top-0 left-0">
                            <i class="fas fa-image text-gray-500 text-4xl"></i>
                        </div>
                        <?php endif; ?>
                    </a>

                    <!-- Premium Sale Badge -->
                    <?php if (isset($product['sale_price']) && $product['sale_price'] > 0 && $product['price'] > $product['sale_price']): ?>
                    <?php $discount_percent = round(($product['price'] - $product['sale_price']) / $product['price'] * 100); ?>
                    <div class="premium-sale-badge">
                        <div class="badge-content">
                            <span class="discount-percent">-<?php echo $discount_percent; ?>%</span>
                            <span class="sale-text">SALE</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="product-info-wrapper flex flex-col flex-grow">
                    <!-- Product Title - Max 2 lines -->
                    <div class="product-title mb-3">
                        <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $product['slug']; ?>" class="block">
                            <h3 class="text-lg font-semibold text-gray-800 hover:text-blue-500 transition duration-200 line-clamp-2 leading-tight">
                                <?php echo $product['name']; ?>
                            </h3>
                        </a>
                    </div>

                    <!-- Premium Price Section -->
                    <div class="premium-price-section">
                        <?php if (isset($product['price_type']) && $product['price_type'] === 'contact'): ?>
                        <!-- Liên hệ báo giá - Giống products.php -->
                        <div class="contact-price-container">
                            <div class="contact-price-main">
                                GỌI NGAY
                            </div>
                            <div class="contact-price-subtitle">
                                Liên hệ báo giá
                            </div>
                        </div>
                        <?php elseif ($product['sale_price'] > 0): ?>
                        <!-- Sản phẩm có giá sale -->
                        <div class="price-container">
                            <div class="original-price"><?php echo format_currency($product['price']); ?></div>
                            <div class="sale-price"><?php echo format_currency($product['sale_price']); ?></div>
                        </div>
                        <?php else: ?>
                        <!-- Sản phẩm giá thường -->
                        <div class="regular-price-container">
                            <div class="price-label">Giá bán</div>
                            <div class="main-price"><?php echo format_currency($product['price']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Rating and Sales Section -->
                    <div class="product-rating-sales">
                        <div class="rating-section">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">5.0</span>
                        </div>
                        <div class="sales-section">
                            <i class="fas fa-shopping-cart"></i>
                            <span>8 đã bán</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            </div>
        </div>

        <!-- Professional Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination-section mt-12">
            <!-- Results Summary -->
            <div class="pagination-summary">
                <div class="results-info">
                    <span class="results-text">
                        Hiển thị <strong><?php echo (($page - 1) * $limit) + 1; ?></strong> - <strong><?php echo min($page * $limit, $total_products); ?></strong>
                        trong tổng số <strong><?php echo number_format($total_products); ?></strong> sản phẩm
                    </span>
                </div>
            </div>

            <!-- Professional Pagination Navigation -->
            <nav class="pagination-nav" aria-label="Điều hướng trang">
                <ul class="pagination-list">
                    <?php
                    // Build URL parameters for pagination
                    $url_params = [];
                    if ($keyword) $url_params['keyword'] = $keyword;
                    if ($category_filter) $url_params['category'] = $category_filter;
                    if ($price_min) $url_params['price_min'] = $price_min;
                    if ($price_max) $url_params['price_max'] = $price_max;
                    if ($sort_by && $sort_by !== 'newest') $url_params['sort'] = $sort_by;
                    if ($promotion_filter) $url_params['promotion'] = $promotion_filter;

                    function build_pagination_url($page_num, $params) {
                        $params['page'] = $page_num;
                        return BASE_URL . '/search.php?' . http_build_query($params);
                    }
                    ?>

                    <!-- Previous Button -->
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo build_pagination_url($page - 1, $url_params); ?>" aria-label="Trang trước">
                            <i class="fas fa-chevron-left"></i>
                            <span class="page-text">Trước</span>
                        </a>
                    </li>
                    <?php else: ?>
                    <li class="page-item disabled">
                        <span class="page-link" aria-disabled="true">
                            <i class="fas fa-chevron-left"></i>
                            <span class="page-text">Trước</span>
                        </span>
                    </li>
                    <?php endif; ?>

                    <?php
                    // Smart pagination logic
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);
                    ?>

                    <!-- First page + ellipsis if needed -->
                    <?php if ($start_page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo build_pagination_url(1, $url_params); ?>" aria-label="Trang 1">1</a>
                    </li>
                    <?php if ($start_page > 2): ?>
                    <li class="page-item disabled">
                        <span class="page-link ellipsis">...</span>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <!-- Visible page range -->
                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo build_pagination_url($i, $url_params); ?>" aria-label="Trang <?php echo $i; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <!-- Last page + ellipsis if needed -->
                    <?php if ($end_page < $total_pages): ?>
                    <?php if ($end_page < $total_pages - 1): ?>
                    <li class="page-item disabled">
                        <span class="page-link ellipsis">...</span>
                    </li>
                    <?php endif; ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo build_pagination_url($total_pages, $url_params); ?>" aria-label="Trang <?php echo $total_pages; ?>"><?php echo $total_pages; ?></a>
                    </li>
                    <?php endif; ?>

                    <!-- Next Button -->
                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo build_pagination_url($page + 1, $url_params); ?>" aria-label="Trang sau">
                            <span class="page-text">Sau</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php else: ?>
                    <li class="page-item disabled">
                        <span class="page-link" aria-disabled="true">
                            <span class="page-text">Sau</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>

        </div>
        <?php endif; ?>

        <?php else: ?>
        <!-- No Results Section - Thiết kế tối giản và hiện đại -->
        <div class="no-results-container">
            <div class="no-results-card">
                <!-- Icon và thông báo chính -->
                <div class="no-results-icon">
                    <div class="icon-wrapper">
                        <i class="fas fa-search-minus"></i>
                    </div>
                </div>

                <div class="no-results-content">
                    <h2 class="no-results-title">Không tìm thấy kết quả</h2>
                    <p class="no-results-message">
                        Không có sản phẩm nào phù hợp với từ khóa
                        <span class="keyword-highlight">"<?php echo htmlspecialchars($keyword); ?>"</span>
                    </p>
                </div>

                <!-- Gợi ý đơn giản -->
                <div class="search-suggestions-simple">
                    <div class="suggestion-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>Thử tìm kiếm với từ khóa khác</span>
                    </div>
                    <div class="suggestion-item">
                        <i class="fas fa-spell-check"></i>
                        <span>Kiểm tra chính tả</span>
                    </div>
                </div>

                <!-- Nút hành động -->
                <div class="no-results-actions">
                    <a href="<?php echo BASE_URL; ?>" class="btn-home">
                        <i class="fas fa-home"></i>
                        Về trang chủ
                    </a>
                    <button type="button" class="btn-new-search" onclick="focusSearchInput()">
                        <i class="fas fa-search"></i>
                        Tìm kiếm mới
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>



<!-- Pagination Loading Effect with Delay -->
<script>
// Loading effect with 1.5s delay for better UX
document.addEventListener('DOMContentLoaded', function() {
    const paginationLinks = document.querySelectorAll('.page-link[href]');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent immediate navigation
            e.preventDefault();

            // Store the target URL
            const targetUrl = this.href;

            // Apply loading state immediately
            this.style.opacity = '0.6';
            this.style.pointerEvents = 'none';

            // Show loading spinner with text
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang tải...';

            // Navigate after 1.5 seconds delay
            setTimeout(() => {
                window.location.href = targetUrl;
            }, 1500);

            // Fallback restore (in case something goes wrong)
            setTimeout(() => {
                this.innerHTML = originalText;
                this.style.opacity = '';
                this.style.pointerEvents = '';
            }, 5000);
        });
    });
});
</script>



<?php
// Include footer
include_once 'partials/footer.php';
?>