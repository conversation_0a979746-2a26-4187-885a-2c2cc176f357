<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Reset Button - Mobile Filter Modal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .test-section {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            margin: 5px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Demo modal footer */
        .demo-modal-footer {
            display: flex;
            gap: 16px;
            padding: 24px;
            background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
            border-radius: 16px;
            border: 1px solid #fed7aa;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.2rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .features {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #22c55e;
            margin: 20px 0;
        }
        
        .features h3 {
            color: #166534;
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        
        .features ul {
            margin: 0;
            padding-left: 20px;
            color: #15803d;
        }
        
        .features li {
            margin-bottom: 5px;
        }
        
        .status {
            padding: 15px;
            border-radius: 12px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 Simple Reset Button</h1>
        <p>Nút "Đặt lại bộ lọc" được thiết kế lại đơn giản, không có lỗi layout</p>
        
        <div class="test-section">
            <h2>✨ Clean & Simple Design</h2>
            
            <div class="demo-modal-footer">
                <button class="btn btn-secondary" id="demoResetBtn">
                    <i class="fas fa-refresh"></i>
                    <span>Đặt lại</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
                <button class="btn btn-primary" id="demoApplyBtn">
                    <i class="fas fa-search"></i>
                    <span>Áp dụng bộ lọc</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="testReset()">🔄 Test Reset</button>
                <button class="control-btn" onclick="testApply()">🎯 Test Apply</button>
                <button class="control-btn" onclick="testBoth()">⚡ Test Both</button>
                <button class="control-btn" onclick="resetAll()">🔧 Reset All</button>
            </div>
        </div>
        
        <div class="features">
            <h3>✅ Simple Design Features:</h3>
            <ul>
                <li><strong>No Layout Shifts:</strong> Kích thước nút không thay đổi</li>
                <li><strong>Simple Loading:</strong> Chỉ có opacity và spinner đơn giản</li>
                <li><strong>Clean Text:</strong> "Đang xử lý..." thay vì text dài</li>
                <li><strong>No Complex Animations:</strong> Không có border glow, pulse</li>
                <li><strong>Stable Width:</strong> Chiều rộng cố định</li>
                <li><strong>Fast Response:</strong> Không có delay không cần thiết</li>
                <li><strong>Reliable:</strong> Không có xung đột CSS</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test nút đặt lại đơn giản
        </div>
    </div>
    
    <script>
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function testReset() {
            const btn = document.getElementById('demoResetBtn');
            updateStatus('🔄 Testing Simple Reset Button...', 'testing');
            
            // Simple loading
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang xử lý...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-spinner"></i>';
            
            setTimeout(() => {
                // Simple reset
                btn.disabled = false;
                btn.classList.remove('loading');
                btn.querySelector('span').textContent = 'Đặt lại';
                btn.querySelector('.btn-spinner').innerHTML = '';
                
                updateStatus('✅ Reset Button: Simple and stable!', 'success');
            }, 1000);
        }
        
        function testApply() {
            const btn = document.getElementById('demoApplyBtn');
            updateStatus('🎯 Testing Apply Button...', 'testing');
            
            // Keep apply button with original design
            btn.disabled = true;
            btn.classList.add('loading');
            btn.querySelector('span').textContent = 'Đang áp dụng...';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-spinner"></i>';
            
            setTimeout(() => {
                btn.disabled = false;
                btn.classList.remove('loading');
                btn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                btn.querySelector('.btn-spinner').innerHTML = '';
                
                updateStatus('✅ Apply Button: Working normally', 'success');
            }, 1000);
        }
        
        function testBoth() {
            updateStatus('⚡ Testing Both Buttons...', 'testing');
            
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            // Test both
            applyBtn.disabled = true;
            applyBtn.classList.add('loading');
            applyBtn.querySelector('span').textContent = 'Đang áp dụng...';
            applyBtn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-spinner"></i>';
            
            resetBtn.disabled = true;
            resetBtn.classList.add('loading');
            resetBtn.querySelector('span').textContent = 'Đang xử lý...';
            resetBtn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-spinner"></i>';
            
            setTimeout(() => {
                // Reset both
                applyBtn.disabled = false;
                applyBtn.classList.remove('loading');
                applyBtn.querySelector('span').textContent = 'Áp dụng bộ lọc';
                applyBtn.querySelector('.btn-spinner').innerHTML = '';
                
                resetBtn.disabled = false;
                resetBtn.classList.remove('loading');
                resetBtn.querySelector('span').textContent = 'Đặt lại';
                resetBtn.querySelector('.btn-spinner').innerHTML = '';
                
                updateStatus('✅ Both Buttons: Simple and stable!', 'success');
            }, 1500);
        }
        
        function resetAll() {
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            [applyBtn, resetBtn].forEach((btn, index) => {
                btn.disabled = false;
                btn.classList.remove('loading');
                btn.querySelector('span').textContent = index === 0 ? 'Áp dụng bộ lọc' : 'Đặt lại';
                btn.querySelector('.btn-spinner').innerHTML = '';
            });
            
            updateStatus('🔧 All buttons reset to original state', 'ready');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('🚀 Simple Reset Button Test Ready', 'ready');
        });
    </script>
</body>
</html>
