# Giải pháp khắc phục Pagination bị dịch chuyển khi Loading

## Vấn đề

Khi người dùng click vào pagination tại trang `products.php`, hiệu ứng loading skeleton xuất hiện nhưng gây ra vấn đề:

1. **<PERSON><PERSON>u cao thay đổi**: Skeleton loading có chiều cao khác với sản phẩm thực tế
2. **Pagination nhảy**: Vị trí pagination bị dịch chuyển lên/xuống
3. **Trải nghiệm kém**: Người dùng mất định hướng khi pagination thay đổi vị trí

## Giải pháp

### 1. Lưu chiều cao hiện tại
```javascript
// Lưu chiều cao của products grid trước khi loading
const currentHeight = productsGrid.offsetHeight;
const minHeight = Math.max(currentHeight, 400);
productsGrid.style.minHeight = minHeight + 'px';
```

### 2. T<PERSON>h toán số skeleton cards phù hợp
```javascript
// Sử dụng số sản phẩm hiện tại hoặc items_per_page
const currentProducts = productsGrid.querySelectorAll('.product-card:not(.skeleton-card)');
let skeletonCount = currentProducts.length;

// Xử lý trường hợp trang cuối có ít sản phẩm
if (skeletonCount < expectedItemsPerPage) {
    skeletonCount = expectedItemsPerPage; // Giữ nguyên chiều cao
}
```

### 3. Skeleton cards match chính xác với sản phẩm thực
```javascript
// Skeleton card match 100% với cấu trúc sản phẩm thực
<div class="product-card skeleton-card group h-full flex flex-col bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 transition-all duration-500">
    <!-- Khối hình ảnh - match product-image-wrapper -->
    <div class="product-image-wrapper relative aspect-square bg-gray-200 overflow-hidden">
        <div class="animate-shimmer"></div>
        <!-- Badge sale ở góc phải trên -->
        <div class="absolute top-3 right-3 w-12 h-6 bg-gray-300 rounded animate-pulse"></div>
    </div>

    <!-- Khối nội dung - match chính xác product-info-wrapper -->
    <div class="product-info-wrapper flex flex-col flex-grow" style="padding: 1.25rem 1.25rem 1rem 1.25rem;">
        <!-- Title - match product-title -->
        <div class="product-title" style="margin-bottom: 0;">
            <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
            </div>
        </div>

        <!-- Price - match premium-price-section -->
        <div class="premium-price-section" style="margin-top: auto; display: flex; align-items: center; justify-content: flex-start; margin-bottom: 0;">
            <div class="space-y-1">
                <div class="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div class="h-5 bg-gray-200 rounded w-24 animate-pulse"></div>
            </div>
        </div>

        <!-- Rating - match product-rating-sales -->
        <div class="product-rating-sales" style="margin-top: 0.5rem; display: flex; justify-content: space-between; align-items: center;">
            <div class="flex items-center space-x-2">
                <div class="flex space-x-1">
                    <!-- 5 stars -->
                </div>
                <div class="h-3 bg-gray-200 rounded w-8 animate-pulse"></div>
            </div>
            <div class="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
        </div>
    </div>
</div>
```

### 4. CSS đảm bảo chiều cao nhất quán
```css
.skeleton-card {
    min-height: 400px; /* Chiều cao tối thiểu */
}

.products-grid.loading-skeleton {
    min-height: inherit !important; /* Giữ nguyên chiều cao */
}

#productsGrid {
    transition: min-height 0.3s ease-out; /* Transition mượt mà */
}
```

### 5. Reset min-height sau khi loading xong
```javascript
setTimeout(() => {
    const newHeight = productsGrid.offsetHeight;
    if (newHeight > 100) { // Đảm bảo có nội dung thực sự
        productsGrid.style.minHeight = '';
    }
}, 300);
```

## Files được thay đổi

### 1. `assets/js/ajax-filter.js`
- **showImmediateLoadingSkeleton()**: Lưu chiều cao hiện tại và đặt min-height
- **showProductsLoadingSkeleton()**: Tính toán số skeleton cards phù hợp
- **createSkeletonCard()**: Tạo skeleton với cấu trúc giống sản phẩm thực
- **updateProductsGridContent()**: Reset min-height sau khi loading xong

### 2. `products.php`
- Thêm CSS styles cho skeleton cards
- Cải thiện responsive design
- Include file CSS mới

### 3. `assets/css/pagination-loading-fix.css` (Mới)
- CSS chuyên biệt cho pagination loading fix
- Responsive adjustments
- Performance optimizations

## Lợi ích

### 1. Trải nghiệm người dùng tốt hơn
- ✅ Pagination không bị dịch chuyển
- ✅ Loading smooth và nhất quán
- ✅ Không có hiệu ứng "nhảy trang"

### 2. Performance
- ✅ Sử dụng min-height thay vì fixed height
- ✅ Transition mượt mà
- ✅ Optimized cho mobile

### 3. Responsive
- ✅ Hoạt động tốt trên tất cả thiết bị
- ✅ Chiều cao skeleton phù hợp với từng breakpoint
- ✅ Reduce motion support

### 4. Maintainable
- ✅ Code được tổ chức rõ ràng
- ✅ CSS riêng biệt dễ maintain
- ✅ Logging để debug

## Cách hoạt động

1. **Trước khi loading**: Lưu chiều cao hiện tại của products grid
2. **Đặt min-height**: Đảm bảo grid không thay đổi chiều cao
3. **Hiển thị skeleton**: Với số lượng cards phù hợp
4. **Load nội dung mới**: AJAX request và cập nhật
5. **Reset min-height**: Cho phép chiều cao tự nhiên

## Testing

### Test cases cần kiểm tra:
1. ✅ Click pagination từ trang đầy sản phẩm
2. ✅ Click pagination từ trang cuối (ít sản phẩm)
3. ✅ Thay đổi items per page
4. ✅ Filter + pagination
5. ✅ Mobile responsive
6. ✅ Slow network conditions

### Expected behavior:
- Pagination luôn ở vị trí cố định
- Loading skeleton mượt mà
- Không có layout shift
- Performance tốt trên mobile

## Troubleshooting

### Nếu pagination vẫn bị dịch chuyển:
1. Kiểm tra console logs để xem min-height có được set đúng không
2. Verify skeleton cards có đúng số lượng không
3. Check CSS conflicts với pagination-loading-fix.css

### Nếu skeleton không hiển thị đúng:
1. Kiểm tra createSkeletonCard() function
2. Verify CSS cho skeleton-card class
3. Check responsive breakpoints

## Skeleton Structure Update

### 🎯 **Skeleton với Badge Sale:**

#### **1. Khối hình ảnh:**
- Khối màu xám với shimmer effect
- Tỷ lệ aspect-ratio: 1 (vuông)
- **Badge sale** ở góc phải trên với responsive sizing

#### **2. Responsive Badge Sizes:**
- **Desktop**: 48x24px (w-12 h-6) - top-3 right-3
- **Tablet**: 44x22px (w-11 h-5.5) - top-3 right-3
- **Mobile**: 40x20px (w-10 h-5) - top-2 right-2

#### **3. Badge Styling:**
- Background: `#d1d5db` (gray-300) - đậm hơn để nổi bật
- Border-radius: `0.375rem` (rounded)
- Animation: `animate-pulse`

## Future improvements

1. **Adaptive skeleton count**: Tự động điều chỉnh dựa trên viewport
2. **Preload next page**: Cache trang tiếp theo để loading nhanh hơn
3. **Virtual scrolling**: Cho danh sách sản phẩm rất dài
4. **Progressive loading**: Load từng phần thay vì toàn bộ
5. **Dynamic badge**: Hiển thị badge skeleton chỉ khi cần thiết
