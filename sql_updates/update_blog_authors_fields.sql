-- T<PERSON><PERSON> bảng blog_authors nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS `blog_authors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Tê<PERSON> tác <PERSON>',
  `bio` text COMMENT '<PERSON>i<PERSON><PERSON> sử',
  `position` varchar(255) DEFAULT NULL COMMENT 'Chức danh/vị trí',
  `experience` text COMMENT 'Kinh nghiệm',
  `education` text COMMENT 'Học vấn/bằng cấp',
  `avatar` varchar(255) DEFAULT NULL COMMENT 'Ảnh đại diện',
  `facebook` varchar(255) DEFAULT NULL COMMENT 'Link Facebook',
  `zalo` varchar(255) DEFAULT NULL COMMENT 'Link Zalo',
  `twitter` varchar(255) DEFAULT NULL COMMENT 'Link Twitter',
  `linkedin` varchar(255) DEFAULT NULL COMMENT 'Link LinkedIn',
  `instagram` varchar(255) DEFAULT NULL COMMENT 'Link Instagram',
  `website` varchar(255) DEFAULT NULL COMMENT 'Website cá nhân',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Cập nhật các bài viết blog để liên kết với tác giả từ bảng blog_authors
-- Kiểm tra xem trường blog_author_id đã tồn tại chưa
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_posts'
AND COLUMN_NAME = 'blog_author_id';

-- Thêm trường blog_author_id vào bảng blog_posts nếu chưa tồn tại
SET @add_column_sql = IF(@column_exists = 0,
    'ALTER TABLE `blog_posts` ADD COLUMN `blog_author_id` INT NULL COMMENT "ID tác giả từ bảng blog_authors" AFTER `author_id`',
    'SELECT "Column blog_author_id already exists"');
PREPARE stmt FROM @add_column_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Kiểm tra xem chỉ mục đã tồn tại chưa
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_posts'
AND INDEX_NAME = 'idx_blog_posts_author';

-- Tạo chỉ mục cho trường blog_author_id nếu chưa tồn tại
SET @create_index_sql = IF(@index_exists = 0,
    'CREATE INDEX `idx_blog_posts_author` ON `blog_posts`(`blog_author_id`)',
    'SELECT "Index idx_blog_posts_author already exists"');
PREPARE stmt FROM @create_index_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thêm khóa ngoại cho trường blog_author_id
-- Lưu ý: Có thể gặp lỗi nếu bảng blog_authors chưa có dữ liệu
-- ALTER TABLE `blog_posts`
-- ADD CONSTRAINT `fk_blog_posts_authors`
-- FOREIGN KEY (`blog_author_id`) REFERENCES `blog_authors` (`id`)
-- ON DELETE SET NULL;
