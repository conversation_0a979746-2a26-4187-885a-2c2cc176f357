-- =====================================================
-- Fix Footer FOUC (Flash of Unstyled Content) Issue
-- Khắc phục vấn đề footer hiển thị HTML trước khi CSS được tải
-- =====================================================

-- Tạo bảng để lưu trữ thông tin về các cải tiến UI/UX nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS `ui_improvements` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `improvement_name` varchar(255) NOT NULL,
    `description` text,
    `implementation_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    `status` enum('implemented','testing','completed') DEFAULT 'implemented',
    `files_modified` text,
    `notes` text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `improvement_name` (`improvement_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm bản ghi về việc khắc phục FOUC cho footer
INSERT INTO `ui_improvements` (
    `improvement_name`,
    `description`,
    `implementation_date`,
    `status`,
    `files_modified`,
    `notes`
) VALUES (
    'fix_footer_fouc_issue',
    'Khắc phục vấn đề FOUC (Flash of Unstyled Content) cho footer - Footer hiển thị HTML trước khi CSS được tải, gây ra trải nghiệm người dùng không tốt khi tải lại trang',
    NOW(),
    'completed',
    'partials/header.php, partials/footer.php',
    'Giải pháp: 1) Di chuyển CSS footer từ footer.php lên header.php để load sớm hơn. 2) Thêm CSS inline cơ bản trong header để đảm bảo footer có style ngay từ đầu. 3) Loại bỏ CSS footer khỏi cuối file footer.php. Kết quả: Footer sẽ hiển thị đúng style ngay từ khi trang bắt đầu load, không còn hiện tượng flash content không có style.'
) ON DUPLICATE KEY UPDATE
    `description` = VALUES(`description`),
    `implementation_date` = NOW(),
    `status` = VALUES(`status`),
    `files_modified` = VALUES(`files_modified`),
    `notes` = VALUES(`notes`);

-- Cập nhật cài đặt site nếu có bảng site_settings
UPDATE `site_settings` 
SET `setting_value` = 'true' 
WHERE `setting_key` = 'footer_fouc_fixed' 
AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'site_settings');

-- Thêm cài đặt mới nếu bảng site_settings tồn tại nhưng chưa có setting này
INSERT IGNORE INTO `site_settings` (`setting_key`, `setting_value`, `setting_description`)
SELECT 'footer_fouc_fixed', 'true', 'Đánh dấu rằng vấn đề FOUC của footer đã được khắc phục'
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'site_settings');

-- Thông báo hoàn thành
SELECT 'Footer FOUC issue has been fixed successfully!' as message,
       'CSS files moved to header.php to prevent Flash of Unstyled Content' as details,
       NOW() as timestamp;
