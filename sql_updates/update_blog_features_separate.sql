-- C<PERSON><PERSON> nhật bảng blog_posts để thêm trường cho mục lục
ALTER TABLE `blog_posts` ADD COLUMN `has_toc` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'Bật/tắt mục lục' AFTER `is_featured`;
ALTER TABLE `blog_posts` ADD COLUMN `toc_title` VARCHAR(255) DEFAULT 'Mục lục' AFTER `has_toc`;

-- Tạo bảng lưu trữ thông tin tác giả blog
CREATE TABLE IF NOT EXISTS `blog_authors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `bio` text,
  `avatar` varchar(255) DEFAULT NULL,
  `facebook` varchar(255) DEFAULT NULL,
  `twitter` varchar(255) DEFAULT NULL,
  `linkedin` varchar(255) DEFAULT NULL,
  `instagram` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Thêm khóa ngoại cho bảng blog_authors
ALTER TABLE `blog_authors` ADD CONSTRAINT `blog_authors_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Tạo bảng lưu trữ bài viết đã lưu/đánh dấu
CREATE TABLE IF NOT EXISTS `blog_bookmarks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_post` (`user_id`,`post_id`),
  KEY `post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Thêm khóa ngoại cho bảng blog_bookmarks
ALTER TABLE `blog_bookmarks` ADD CONSTRAINT `blog_bookmarks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
ALTER TABLE `blog_bookmarks` ADD CONSTRAINT `blog_bookmarks_ibfk_2` FOREIGN KEY (`post_id`) REFERENCES `blog_posts` (`id`) ON DELETE CASCADE;

-- Tạo bảng lưu trữ lượt thích bình luận
CREATE TABLE IF NOT EXISTS `blog_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `comment_user` (`comment_id`,`user_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Thêm khóa ngoại cho bảng blog_comment_likes
ALTER TABLE `blog_comment_likes` ADD CONSTRAINT `blog_comment_likes_ibfk_1` FOREIGN KEY (`comment_id`) REFERENCES `blog_comments` (`id`) ON DELETE CASCADE;
ALTER TABLE `blog_comment_likes` ADD CONSTRAINT `blog_comment_likes_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Tạo bảng liên kết bài viết với sản phẩm
CREATE TABLE IF NOT EXISTS `blog_post_products` (
  `post_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  PRIMARY KEY (`post_id`,`product_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Thêm khóa ngoại cho bảng blog_post_products
ALTER TABLE `blog_post_products` ADD CONSTRAINT `blog_post_products_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `blog_posts` (`id`) ON DELETE CASCADE;
ALTER TABLE `blog_post_products` ADD CONSTRAINT `blog_post_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;
