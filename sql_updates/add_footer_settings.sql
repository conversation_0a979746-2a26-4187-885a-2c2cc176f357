-- SQL Update để thêm cài đặt footer vào bảng site_settings

-- Kiểm tra xem bảng site_settings có tồn tại không
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'site_settings';

-- <PERSON><PERSON><PERSON> bảng không tồn tại, tạo bảng
SET @create_table = IF(@table_exists = 0, 
    'CREATE TABLE site_settings (
        id INT(11) NOT NULL AUTO_INCREMENT,
        setting_key VARCHAR(255) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (setting_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci',
    'SELECT "Bảng site_settings đã tồn tại" AS message');

PREPARE stmt FROM @create_table;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Thêm hoặc cập nhật các cài đặt footer
INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col1_content', 'Nội Thất Chất Lượng Hà Nội cung cấp các sản phẩm nội thất cao cấp, chất lượng với giá thành hợp lý. Chúng tôi tự hào mang đến những sản phẩm đẹp, bền và phù hợp với mọi không gian sống.')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('company_name', 'Công ty TNHH Nội Thất Chất Lượng Hà Nội')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('company_address', '123 Đường ABC, Quận XYZ, Hà Nội')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('company_phone', '0123.456.789')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('company_email', '<EMAIL>')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('business_hours', 'Thứ 2 - Chủ nhật: 8:00 - 20:00')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col2_title', 'Liên kết nhanh')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col3_title', 'Hỗ trợ khách hàng')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col4_title', 'Kết nối với chúng tôi')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Thêm liên kết nhanh mẫu
INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col2_links', '[
    {"text": "Trang chủ", "url": "/"},
    {"text": "Giới thiệu", "url": "/about.php"},
    {"text": "Sản phẩm", "url": "/products.php"},
    {"text": "Tin tức", "url": "/blog.php"},
    {"text": "Liên hệ", "url": "/contact.php"}
]')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Thêm liên kết hỗ trợ khách hàng mẫu
INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_col3_links', '[
    {"text": "Đổi trả bảo hành", "url": "/warranty.php"},
    {"text": "Hình thức thanh toán", "url": "/payment.php"},
    {"text": "Vận chuyển giao hàng", "url": "/shipping.php"},
    {"text": "Chính sách bảo mật", "url": "/privacy-policy.php"},
    {"text": "Điều khoản sử dụng", "url": "/terms-of-service.php"}
]')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Thêm liên kết mạng xã hội mẫu
INSERT INTO site_settings (setting_key, setting_value) VALUES
('facebook_url', 'https://web.facebook.com/tunhuachatluong')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('instagram_url', 'https://instagram.com/noithatbangvu')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('youtube_url', 'https://youtube.com/noithatbangvu')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('tiktok_url', 'https://tiktok.com/@noithatbangvu')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('zalo_url', 'https://zalo.me/noithatbangvu')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

INSERT INTO site_settings (setting_key, setting_value) VALUES
('footer_copyright_text', 'Tất cả quyền được bảo lưu.')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Thông báo hoàn thành
SELECT 'Cập nhật cài đặt footer thành công' AS message;
