-- Thêm trường status nếu chưa tồn tại
ALTER TABLE users
ADD COLUMN IF NOT EXISTS status ENUM('active', 'locked') NOT NULL DEFAULT 'active' AFTER role;

-- Thêm trường reset_token và reset_token_expires_at nếu chưa tồn tại
ALTER TABLE users
ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255) DEFAULT NULL AFTER status,
ADD COLUMN IF NOT EXISTS reset_token_expires_at DATETIME DEFAULT NULL AFTER reset_token;

-- Thêm chỉ mục cho trường status
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
