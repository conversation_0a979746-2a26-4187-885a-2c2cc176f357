-- =====================================================
-- Fix Banner Image Display Issue
-- Khắc phục vấn đề hiển thị ảnh banner khi chuyển slide
-- =====================================================

-- Mô tả vấn đề:
-- Khi người dùng cuộn trang xuống một phần và sau đó chuyển slide banner,
-- phần ảnh banner không hiển thị đầy đủ do hiệu ứng parallax không được reset đúng cách.

-- Giải pháp:
-- 1. Cập nhật logic hiệu ứng parallax để chỉ áp dụng cho slide đang active
-- 2. Th<PERSON><PERSON> hàm reset transform cho tất cả ảnh banner khi chuyển slide
-- 3. <PERSON><PERSON><PERSON> b<PERSON><PERSON> ảnh được reset về vị trí ban đầu khi slide mới được kích hoạt

-- Các file đã được cập nhật:
-- - assets/js/banner-slider.js: Sửa logic hiệu ứng parallax và thêm reset transform
-- - assets/js/index.js: Cập nhật logic xử lý slide change

-- Thay đổi chính:
-- 1. Hiệu ứng parallax chỉ áp dụng cho slide đang active (.swiper-slide-active)
-- 2. Thêm hàm resetAllBannerImages() để reset tất cả ảnh banner
-- 3. Gọi reset function trong các event: init, slideChangeTransitionStart
-- 4. Áp dụng lại parallax cho slide mới trong slideChangeTransitionEnd

-- Không cần thay đổi database cho fix này
-- Chỉ cần cập nhật JavaScript files

SELECT 'Banner image display issue has been fixed' as status;
