-- <PERSON><PERSON><PERSON> tra xem trường avatar đã tồn tại trong bảng users chưa
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'avatar';

-- <PERSON><PERSON>u trường avatar chưa tồn tại, thêm vào
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE users ADD COLUMN avatar VARCHAR(255) DEFAULT NULL AFTER address', 
    'SELECT "Trường avatar đã tồn tại trong bảng users" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
