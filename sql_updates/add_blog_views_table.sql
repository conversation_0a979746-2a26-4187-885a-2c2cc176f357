-- T<PERSON><PERSON> bảng blog_views để theo dõi lượt xem bài viết
CREATE TABLE IF NOT EXISTS `blog_views` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `post_id` INT NOT NULL,
    `visitor_identifier` VARCHAR(255) NOT NULL,
    `view_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`post_id`) REFERENCES `blog_posts`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_view` (`post_id`, `visitor_identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Chỉ mục để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS `idx_blog_views_post_id` ON `blog_views`(`post_id`);
CREATE INDEX IF NOT EXISTS `idx_blog_views_visitor` ON `blog_views`(`visitor_identifier`);
