-- Fix Order Status Update
-- Tạo bảng order_product_sold nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS order_product_sold (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_order_product (order_id, product_id)
);

-- Tạo chỉ mục cho bảng order_product_sold nếu chưa tồn tại
CREATE INDEX IF NOT EXISTS idx_order_product_sold_order_id ON order_product_sold(order_id);
CREATE INDEX IF NOT EXISTS idx_order_product_sold_product_id ON order_product_sold(product_id);

-- <PERSON><PERSON><PERSON> bảo trường status trong bảng orders có đủ các giá trị hợp lệ
ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'processing', 'shipping', 'completed', 'cancelled') DEFAULT 'pending';

-- Thêm trường updated_at vào bảng orders nếu chưa tồn tại
ALTER TABLE orders ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Cập nhật trạng thái đơn hàng nếu có giá trị không hợp lệ
UPDATE orders SET status = 'pending' WHERE status NOT IN ('pending', 'processing', 'shipping', 'completed', 'cancelled');
