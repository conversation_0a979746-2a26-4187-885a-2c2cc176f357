-- <PERSON><PERSON><PERSON> tra xem bảng blog_authors đã tồn tại chưa
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors';

-- <PERSON><PERSON>u bảng đã tồn tại, kiểm tra xem có ràng buộc khóa ngoại không
SET @constraint_exists = 0;
SELECT COUNT(*) INTO @constraint_exists
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND CONSTRAINT_NAME = 'blog_authors_ibfk_1';

-- Nếu có ràng buộc khóa ngoại, xóa nó
SET @drop_constraint_sql = IF(@constraint_exists > 0,
    'ALTER TABLE `blog_authors` DROP FOREIGN KEY `blog_authors_ibfk_1`',
    'SELECT "Constraint blog_authors_ibfk_1 does not exist"');
PREPARE stmt FROM @drop_constraint_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Kiểm tra xem cột user_id có tồn tại không
SET @user_id_exists = 0;
SELECT COUNT(*) INTO @user_id_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'user_id';

-- Nếu cột user_id tồn tại, kiểm tra xem có chỉ mục UNIQUE không
SET @unique_index_exists = 0;
SELECT COUNT(*) INTO @unique_index_exists
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'user_id'
AND NON_UNIQUE = 0;

-- Nếu có chỉ mục UNIQUE, xóa nó
SET @drop_index_sql = IF(@unique_index_exists > 0,
    'ALTER TABLE `blog_authors` DROP INDEX `user_id`',
    'SELECT "Unique index on user_id does not exist"');
PREPARE stmt FROM @drop_index_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Nếu cột user_id tồn tại, thay đổi nó thành NULL được
SET @modify_user_id_sql = IF(@user_id_exists > 0,
    'ALTER TABLE `blog_authors` MODIFY COLUMN `user_id` INT NULL',
    'SELECT "Column user_id does not exist"');
PREPARE stmt FROM @modify_user_id_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Kiểm tra xem cột name đã tồn tại chưa
SET @name_exists = 0;
SELECT COUNT(*) INTO @name_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'name';

-- Nếu cột name chưa tồn tại, thêm nó vào
SET @add_name_sql = IF(@name_exists = 0,
    'ALTER TABLE `blog_authors` ADD COLUMN `name` VARCHAR(255) NOT NULL COMMENT "Tên tác giả" AFTER `id`',
    'SELECT "Column name already exists"');
PREPARE stmt FROM @add_name_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Kiểm tra các cột khác và thêm nếu chưa tồn tại
-- Cột position
SET @position_exists = 0;
SELECT COUNT(*) INTO @position_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'position';

SET @add_position_sql = IF(@position_exists = 0,
    'ALTER TABLE `blog_authors` ADD COLUMN `position` VARCHAR(255) NULL COMMENT "Chức danh/vị trí" AFTER `bio`',
    'SELECT "Column position already exists"');
PREPARE stmt FROM @add_position_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cột experience
SET @experience_exists = 0;
SELECT COUNT(*) INTO @experience_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'experience';

SET @add_experience_sql = IF(@experience_exists = 0,
    'ALTER TABLE `blog_authors` ADD COLUMN `experience` TEXT NULL COMMENT "Kinh nghiệm" AFTER `position`',
    'SELECT "Column experience already exists"');
PREPARE stmt FROM @add_experience_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cột education
SET @education_exists = 0;
SELECT COUNT(*) INTO @education_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'education';

SET @add_education_sql = IF(@education_exists = 0,
    'ALTER TABLE `blog_authors` ADD COLUMN `education` TEXT NULL COMMENT "Học vấn/bằng cấp" AFTER `experience`',
    'SELECT "Column education already exists"');
PREPARE stmt FROM @add_education_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cột zalo
SET @zalo_exists = 0;
SELECT COUNT(*) INTO @zalo_exists
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'blog_authors'
AND COLUMN_NAME = 'zalo';

SET @add_zalo_sql = IF(@zalo_exists = 0,
    'ALTER TABLE `blog_authors` ADD COLUMN `zalo` VARCHAR(255) NULL COMMENT "Link Zalo" AFTER `facebook`',
    'SELECT "Column zalo already exists"');
PREPARE stmt FROM @add_zalo_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
