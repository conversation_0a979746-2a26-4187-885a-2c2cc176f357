-- Ki<PERSON><PERSON> tra và thêm cột province_code nếu chưa tồn tại
SET @column_exists_province = 0;
SELECT COUNT(*) INTO @column_exists_province 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'province_code';

-- Nếu trường province_code chưa tồn tại, thêm vào
SET @query_province = IF(@column_exists_province = 0, 
    'ALTER TABLE users ADD COLUMN province_code INT DEFAULT NULL AFTER address', 
    'SELECT "Trường province_code đã tồn tại trong bảng users" AS message');

PREPARE stmt_province FROM @query_province;
EXECUTE stmt_province;
DEALLOCATE PREPARE stmt_province;

-- Kiểm tra và thêm cột district_code nếu chưa tồn tại
SET @column_exists_district = 0;
SELECT COUNT(*) INTO @column_exists_district 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'district_code';

-- Nếu trường district_code chưa tồn tại, thêm vào
SET @query_district = IF(@column_exists_district = 0, 
    'ALTER TABLE users ADD COLUMN district_code INT DEFAULT NULL AFTER province_code', 
    'SELECT "Trường district_code đã tồn tại trong bảng users" AS message');

PREPARE stmt_district FROM @query_district;
EXECUTE stmt_district;
DEALLOCATE PREPARE stmt_district;

-- Kiểm tra và thêm cột ward_code nếu chưa tồn tại
SET @column_exists_ward = 0;
SELECT COUNT(*) INTO @column_exists_ward 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'ward_code';

-- Nếu trường ward_code chưa tồn tại, thêm vào
SET @query_ward = IF(@column_exists_ward = 0, 
    'ALTER TABLE users ADD COLUMN ward_code INT DEFAULT NULL AFTER district_code', 
    'SELECT "Trường ward_code đã tồn tại trong bảng users" AS message');

PREPARE stmt_ward FROM @query_ward;
EXECUTE stmt_ward;
DEALLOCATE PREPARE stmt_ward;

-- Kiểm tra và thêm cột address_detail nếu chưa tồn tại
SET @column_exists_address_detail = 0;
SELECT COUNT(*) INTO @column_exists_address_detail 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'address_detail';

-- Nếu trường address_detail chưa tồn tại, thêm vào
SET @query_address_detail = IF(@column_exists_address_detail = 0, 
    'ALTER TABLE users ADD COLUMN address_detail TEXT AFTER ward_code', 
    'SELECT "Trường address_detail đã tồn tại trong bảng users" AS message');

PREPARE stmt_address_detail FROM @query_address_detail;
EXECUTE stmt_address_detail;
DEALLOCATE PREPARE stmt_address_detail;
