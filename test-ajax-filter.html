<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Filter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #f97316;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #ea580c;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            background: #fee;
            color: #c00;
        }
        .success {
            background: #efe;
            color: #060;
        }
    </style>
</head>
<body>
    <h1>Test AJAX Filter API</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic API Call</h2>
        <button onclick="testBasicCall()">Test Basic Call</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Filter by Category</h2>
        <button onclick="testCategoryFilter()">Test Category Filter</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Filter by Price Range</h2>
        <button onclick="testPriceFilter()">Test Price Filter</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Multiple Filters</h2>
        <button onclick="testMultipleFilters()">Test Multiple Filters</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        const API_URL = 'api/filter-products.php';

        async function makeApiCall(data, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.textContent = 'Loading...';
            resultElement.className = 'result';

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultElement.className = 'result success';
                    resultElement.textContent = `Success!\nTotal products: ${result.data.pagination.total_products}\nProducts returned: ${result.data.products.length}\nCurrent page: ${result.data.pagination.current_page}\nTotal pages: ${result.data.pagination.total_pages}`;
                } else {
                    resultElement.className = 'result error';
                    resultElement.textContent = `Error: ${result.error || 'Unknown error'}\nStatus: ${response.status}`;
                }
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.textContent = `Network Error: ${error.message}`;
            }
        }

        function testBasicCall() {
            makeApiCall({
                page: 1,
                items_per_page: 12
            }, 'result1');
        }

        function testCategoryFilter() {
            makeApiCall({
                categories: [1], // Assuming category ID 1 exists
                page: 1,
                items_per_page: 12
            }, 'result2');
        }

        function testPriceFilter() {
            makeApiCall({
                price_min: 100000,
                price_max: 1000000,
                page: 1,
                items_per_page: 12
            }, 'result3');
        }

        function testMultipleFilters() {
            makeApiCall({
                categories: [1],
                price_min: 100000,
                price_max: 5000000,
                promotions: ['sale'],
                sort: 'price_asc',
                page: 1,
                items_per_page: 12
            }, 'result4');
        }
    </script>
</body>
</html>
