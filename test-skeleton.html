<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Loading Skeleton</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Skeleton loading animation */
        .skeleton-card {
            pointer-events: none;
            user-select: none;
            position: relative;
            overflow: hidden;
        }

        .skeleton-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: skeleton-shine 2s infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes skeleton-shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .skeleton-card > * {
            position: relative;
            z-index: 0;
        }

        .skeleton-card .animate-pulse {
            animation: skeleton-pulse 1.5s ease-in-out infinite;
        }

        @keyframes skeleton-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .animate-shimmer {
            background-size: 200% 100%;
            animation: shimmer 1.8s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .skeleton-card .bg-gray-200 {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
        }

        .skeleton-card .bg-gray-300 {
            background: linear-gradient(90deg, #e5e5e5 25%, #d0d0d0 50%, #e5e5e5 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.8s infinite;
        }

        @keyframes skeleton-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-8 text-center">Test Loading Skeleton</h1>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" id="products-grid">
            <!-- Skeleton cards sẽ được thêm bằng JavaScript -->
        </div>

        <div class="text-center mt-8 space-y-4">
            <div>
                <button onclick="showSkeleton()" class="bg-blue-500 text-white px-6 py-2 rounded-lg mr-4">
                    Hiển thị Skeleton
                </button>
                <button onclick="showProducts()" class="bg-green-500 text-white px-6 py-2 rounded-lg">
                    Hiển thị Sản phẩm
                </button>
            </div>

            <div>
                <button onclick="simulateImmediateLoading()" class="bg-orange-500 text-white px-6 py-2 rounded-lg mr-4">
                    🚀 Test Loading Ngay Lập Tức
                </button>
                <button onclick="simulateFilterClick()" class="bg-purple-500 text-white px-6 py-2 rounded-lg">
                    🔍 Test Click Bộ Lọc
                </button>
            </div>
        </div>
    </div>

    <script>
        function createSkeletonCard() {
            return `
                <div class="product-card skeleton-card bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md">
                    <!-- Skeleton Image Container -->
                    <div class="relative aspect-square bg-gray-200 overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
                        <!-- Skeleton Sale Badge -->
                        <div class="absolute top-3 right-3 w-12 h-6 bg-gray-300 rounded animate-pulse"></div>
                    </div>
                    
                    <!-- Skeleton Content -->
                    <div class="p-4 space-y-4">
                        <!-- Skeleton Title -->
                        <div class="space-y-2">
                            <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div class="h-4 bg-gray-200 rounded w-4/5 animate-pulse"></div>
                        </div>
                        
                        <!-- Skeleton Price Section -->
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <div class="h-5 bg-gray-200 rounded w-20 animate-pulse"></div>
                                <div class="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                            </div>
                            <div class="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                        
                        <!-- Skeleton Rating -->
                        <div class="flex items-center space-x-1">
                            <div class="flex space-x-1">
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                                <div class="w-3 h-3 bg-gray-200 rounded animate-pulse"></div>
                            </div>
                            <div class="h-3 bg-gray-200 rounded w-8 animate-pulse"></div>
                        </div>
                        
                        <!-- Skeleton Button -->
                        <div class="h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                    </div>
                </div>
            `;
        }

        function showSkeleton() {
            const grid = document.getElementById('products-grid');
            let skeletonHTML = '';
            for (let i = 0; i < 9; i++) {
                skeletonHTML += createSkeletonCard();
            }
            grid.innerHTML = skeletonHTML;
        }

        function showProducts() {
            const grid = document.getElementById('products-grid');
            grid.innerHTML = `
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="aspect-square bg-blue-100 flex items-center justify-center">
                        <span class="text-blue-600 font-semibold">Sản phẩm 1</span>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800">Tên sản phẩm 1</h3>
                        <p class="text-orange-600 font-bold">1.000.000 VNĐ</p>
                        <button class="w-full mt-3 bg-orange-500 text-white py-2 rounded-lg">Xem chi tiết</button>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="aspect-square bg-green-100 flex items-center justify-center">
                        <span class="text-green-600 font-semibold">Sản phẩm 2</span>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800">Tên sản phẩm 2</h3>
                        <p class="text-orange-600 font-bold">2.000.000 VNĐ</p>
                        <button class="w-full mt-3 bg-orange-500 text-white py-2 rounded-lg">Xem chi tiết</button>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="aspect-square bg-purple-100 flex items-center justify-center">
                        <span class="text-purple-600 font-semibold">Sản phẩm 3</span>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800">Tên sản phẩm 3</h3>
                        <p class="text-orange-600 font-bold">3.000.000 VNĐ</p>
                        <button class="w-full mt-3 bg-orange-500 text-white py-2 rounded-lg">Xem chi tiết</button>
                    </div>
                </div>
            `;
        }

        function simulateImmediateLoading() {
            const grid = document.getElementById('products-grid');

            // Hiển thị sản phẩm giả trước
            showProducts();

            // Sau 1 giây, simulate click và hiển thị skeleton ngay lập tức
            setTimeout(() => {
                console.log('🚀 Simulating FAST immediate skeleton loading...');

                // Fade out nội dung hiện tại nhanh hơn
                grid.style.transition = 'opacity 0.15s ease-out';
                grid.style.opacity = '0.3';

                setTimeout(() => {
                    // Hiển thị skeleton ngay lập tức
                    showSkeleton();

                    // Fade in skeleton
                    grid.style.opacity = '1';

                    // Reset transition
                    setTimeout(() => {
                        grid.style.transition = '';
                    }, 150);

                    // Sau 800ms (nhanh hơn), hiển thị sản phẩm mới
                    setTimeout(() => {
                        simulateContentUpdate();
                    }, 800); // Giảm từ 1500ms xuống 800ms
                }, 150); // Giảm từ 200ms xuống 150ms
            }, 1000);
        }

        function simulateFilterClick() {
            const grid = document.getElementById('products-grid');

            console.log('🔍 Simulating FAST filter click...');

            // Ngay lập tức hiển thị skeleton nhanh hơn
            grid.style.transition = 'opacity 0.15s ease-out';
            grid.style.opacity = '0.3';

            setTimeout(() => {
                showSkeleton();
                grid.style.opacity = '1';

                setTimeout(() => {
                    grid.style.transition = '';
                }, 150);

                // Sau 1 giây (nhanh hơn), hiển thị kết quả
                setTimeout(() => {
                    simulateContentUpdate();
                }, 1000); // Giảm từ 2000ms xuống 1000ms
            }, 150); // Giảm từ 200ms xuống 150ms
        }

        function simulateContentUpdate() {
            const grid = document.getElementById('products-grid');

            // Fade out skeleton nhanh hơn
            grid.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
            grid.style.opacity = '0.3';
            grid.style.transform = 'translateY(-3px)';

            setTimeout(() => {
                // Hiển thị sản phẩm mới
                showProducts();

                // Reset transform và fade in
                grid.style.transform = 'translateY(3px)';

                setTimeout(() => {
                    grid.style.opacity = '1';
                    grid.style.transform = 'translateY(0)';

                    // Reset transition nhanh hơn
                    setTimeout(() => {
                        grid.style.transition = '';
                        grid.style.transform = '';
                    }, 200); // Giảm từ 400ms xuống 200ms
                }, 30); // Giảm từ 50ms xuống 30ms
            }, 200); // Giảm từ 400ms xuống 200ms
        }

        // Hiển thị sản phẩm khi trang load
        showProducts();
    </script>
</body>
</html>
