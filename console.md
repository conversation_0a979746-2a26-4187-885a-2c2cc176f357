(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
search-z-index-helper.js:159 Search Z-Index Helper initialized
simple-notification-modal.js:8 Simple Notification Modal Enhancement loaded
search-overflow-solution1.js:50 Search Overflow Solution 1: Initialized successfully
products.php?sort=newest&page=3&items_per_page=12:6426 ✅ Giải pháp 1: Overflow Visible đã được tích hợp thành công!
products.php?sort=newest&page=3&items_per_page=12:6684 ✅ Smooth dropdown animations initialized successfully!
products.php?sort=newest&page=3&items_per_page=12:7014 Initializing filter micro-interactions...
products.php?sort=newest&page=3&items_per_page=12:7211 Filter micro-interactions initialized successfully!
cart-realtime.js:16 Cart realtime: Initializing...
recently-viewed-trending.js:7 Recently viewed trending JS loaded
recently-viewed-trending.js:13 Recently viewed section found: true
recently-viewed-trending.js:14 Trending section found: true
recently-viewed-trending.js:17 Recently viewed section styles: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
recently-viewed-trending.js:21 Trending section styles: CSSStyleDeclaration {0: 'accent-color', 1: 'align-content', 2: 'align-items', 3: 'align-self', 4: 'alignment-baseline', 5: 'anchor-name', 6: 'anchor-scope', 7: 'animation-composition', 8: 'animation-delay', 9: 'animation-direction', 10: 'animation-duration', 11: 'animation-fill-mode', 12: 'animation-iteration-count', 13: 'animation-name', 14: 'animation-play-state', 15: 'animation-range-end', 16: 'animation-range-start', 17: 'animation-timeline', 18: 'animation-timing-function', 19: 'app-region', 20: 'appearance', 21: 'backdrop-filter', 22: 'backface-visibility', 23: 'background-attachment', 24: 'background-blend-mode', 25: 'background-clip', 26: 'background-color', 27: 'background-image', 28: 'background-origin', 29: 'background-position', 30: 'background-repeat', 31: 'background-size', 32: 'baseline-shift', 33: 'baseline-source', 34: 'block-size', 35: 'border-block-end-color', 36: 'border-block-end-style', 37: 'border-block-end-width', 38: 'border-block-start-color', 39: 'border-block-start-style', 40: 'border-block-start-width', 41: 'border-bottom-color', 42: 'border-bottom-left-radius', 43: 'border-bottom-right-radius', 44: 'border-bottom-style', 45: 'border-bottom-width', 46: 'border-collapse', 47: 'border-end-end-radius', 48: 'border-end-start-radius', 49: 'border-image-outset', 50: 'border-image-repeat', 51: 'border-image-slice', 52: 'border-image-source', 53: 'border-image-width', 54: 'border-inline-end-color', 55: 'border-inline-end-style', 56: 'border-inline-end-width', 57: 'border-inline-start-color', 58: 'border-inline-start-style', 59: 'border-inline-start-width', 60: 'border-left-color', 61: 'border-left-style', 62: 'border-left-width', 63: 'border-right-color', 64: 'border-right-style', 65: 'border-right-width', 66: 'border-start-end-radius', 67: 'border-start-start-radius', 68: 'border-top-color', 69: 'border-top-left-radius', 70: 'border-top-right-radius', 71: 'border-top-style', 72: 'border-top-width', 73: 'bottom', 74: 'box-decoration-break', 75: 'box-shadow', 76: 'box-sizing', 77: 'break-after', 78: 'break-before', 79: 'break-inside', 80: 'buffered-rendering', 81: 'caption-side', 82: 'caret-color', 83: 'clear', 84: 'clip', 85: 'clip-path', 86: 'clip-rule', 87: 'color', 88: 'color-interpolation', 89: 'color-interpolation-filters', 90: 'color-rendering', 91: 'column-count', 92: 'column-gap', 93: 'column-rule-color', 94: 'column-rule-style', 95: 'column-rule-width', 96: 'column-span', 97: 'column-width', 98: 'contain-intrinsic-block-size', 99: 'contain-intrinsic-height', …}
products.php?sort=newest&page=3&items_per_page=12:7257 Initializing AJAX Filter...
ajax-filter.js:8 🚀 AJAX Filter: Constructor called
ajax-filter.js:21 ✅ AJAX Filter: Properties initialized
ajax-filter.js:26 AJAX Filter: Init called
ajax-filter.js:36 🔄 Initialize keywordRemoved = false (no URL keyword)
ajax-filter.js:45 AJAX Filter: bindEvents called
ajax-filter.js:49 AJAX Filter: ajaxFilterActive flag set
ajax-filter.js:170 AJAX Filter: Overriding apply filters button
ajax-filter.js:94 🔍 AJAX Filter: bindMainSearchEvents called
ajax-filter.js:101 🔍 AJAX Filter: Elements found: {mainSearchForm: true, mainSearchInput: true, searchSubmitBtn: true}
ajax-filter.js:112 ✅ AJAX Filter: Found main search form, binding events
ajax-filter.js:160 AJAX Filter: Main search events bound successfully
ajax-filter.js:3087 AJAX Filter: Binding sort and pagination events
ajax-filter.js:3096 AJAX Filter: Sort select bound
ajax-filter.js:3106 AJAX Filter: Items per page select bound
ajax-filter.js:62 AJAX Filter: bindEvents completed
ajax-filter.js:41 AJAX Filter: Init completed
products.php?sort=newest&page=3&items_per_page=12:7269 AJAX Filter initialized successfully - ready for immediate pagination
center-notifications.js:401 Center notifications system initialized
products.php?sort=newest&page=3&items_per_page=12:7601 Đã xóa lớp phủ: div.mobile-menu-overlay
mobile-menu-toggle-fix.js:7 Mobile Menu Toggle Fix loaded
mobile-menu-toggle-fix.js:16 Mobile menu toggle: button.mobile-header-menu-toggle
mobile-menu-toggle-fix.js:17 Mobile menu: div#mobile-menu.mobile-menu
mobile-menu-toggle-fix.js:18 Mobile menu overlay: null
mobile-menu-toggle-fix.js:19 Contact buttons container: div.contact-buttons-container
mobile-menu-toggle-fix.js:20 Scroll top container: div.scroll-top-container
mobile-dropdown-fix-v4.js:9 Mobile Dropdown Fix V4 loaded
mobile-bottom-nav.js:7 Mobile Bottom Nav JS loaded
mobile-bottom-nav.js:13 BASE_URL: http://localhost/noithatbangvu
mobile-bottom-nav.js:14 Mobile nav items: 5
mobile-bottom-nav.js:18 Item 0 href: http://localhost/noithatbangvu
mobile-bottom-nav.js:18 Item 1 href: http://localhost/noithatbangvu/products.php
mobile-bottom-nav.js:18 Item 2 href: http://localhost/noithatbangvu/search.php
mobile-bottom-nav.js:18 Item 3 href: http://localhost/noithatbangvu/cart.php
mobile-bottom-nav.js:18 Item 4 href: http://localhost/noithatbangvu/account/profile.php
mobile-bottom-nav.js:73 Current path: /noithatbangvu/products.php
mobile-bottom-nav.js:127 Active items: 1
mobile-cart-animation-fix.js:7 Mobile cart animation fix loaded
mobile-cart-animation-fix.js:179 New cart animation implementation applied
cart-sync.js:13 Cart sync initialized
cart-sync.js:210 Registering add to cart events for 0 buttons
mobile-cart-badge-fix.js:7 Mobile Cart Badge Fix loaded
mobile-cart-badge-fix.js:23 Registering mobile cart events for 0 buttons
mobile-cart-badge-fix.js:32 Existing cart handlers detected, skipping direct event registration
mobile-cart-badge-fix.js:205 Existing cart handlers detected, skipping initialization
mini-cart-update.js:7 Mini Cart Update JS loaded
user-actions.js:7 User Actions JS loaded
tablet-mega-menu.js:7 Tablet mega menu script loaded
tablet-mega-menu.js:25 Tablet status check: {width: 1440, height: 844, isTabletSize: true, isTouchDevice: true, result: true}
tablet-mega-menu.js:40 Tablet mega menu initialized for touch device
tablet-mega-menu.js:80 Tablet mega menu created successfully
tablet-mega-menu.js:739 Tablet mega menu setup complete with auto-scroll features
mobile-filter-modal.js:101 ✅ Modal created successfully: {overlay: div.filter-modal-overlay, modal: div.filter-modal, overlayInDOM: true, modalInDOM: true}
products.php?sort=newest&page=3&items_per_page=12:6476 Desktop header calculation: {totalHeight: 143, topBarHeight: 9, finalHeaderHeight: 134}
products.php?sort=newest&page=3&items_per_page=12:6519 Auto-scroll to Products: {isMobile: false, finalHeaderHeight: 134, targetPosition: 409, productsOffset: 543, calculation: '543 - 134 = 409'}
cart-realtime.js:696 Initializing cart badges from localStorage with count: 3
cart-realtime.js:716 updateAllCartBadgesImmediate: Updating all cart badges with count: 3
cart-realtime.js:805 Cart badges updated immediately: {count: 3, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true, hasMobileCartBadge: true}
center-notifications.js:12 convertFlashMessageToCenterNotification CALLED
center-notifications.js:26 Checking window.flashMessage: undefined
center-notifications.js:48 Flash message container (new): null
center-notifications.js:53 Flash message container (old): null
ajax-filter.js:3215 Initializing AJAX Filter...
ajax-filter.js:8 🚀 AJAX Filter: Constructor called
ajax-filter.js:21 ✅ AJAX Filter: Properties initialized
ajax-filter.js:26 AJAX Filter: Init called
ajax-filter.js:36 🔄 Initialize keywordRemoved = false (no URL keyword)
ajax-filter.js:45 AJAX Filter: bindEvents called
ajax-filter.js:49 AJAX Filter: ajaxFilterActive flag set
ajax-filter.js:170 AJAX Filter: Overriding apply filters button
ajax-filter.js:94 🔍 AJAX Filter: bindMainSearchEvents called
ajax-filter.js:101 🔍 AJAX Filter: Elements found: {mainSearchForm: true, mainSearchInput: true, searchSubmitBtn: true}
ajax-filter.js:112 ✅ AJAX Filter: Found main search form, binding events
ajax-filter.js:160 AJAX Filter: Main search events bound successfully
ajax-filter.js:3087 AJAX Filter: Binding sort and pagination events
ajax-filter.js:3096 AJAX Filter: Sort select bound
ajax-filter.js:3106 AJAX Filter: Items per page select bound
ajax-filter.js:62 AJAX Filter: bindEvents completed
ajax-filter.js:41 AJAX Filter: Init completed
ajax-filter.js:3218 AJAX Filter initialized successfully
sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:134 Uncaught b {message: 'init not called with valid version', innerError: undefined}
a @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:134
process @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:167
f @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:172
(anonymous) @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:172
p @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:172
parse @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:172
parse @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:190
(anonymous) @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:73
(anonymous) @ sdk.js?hash=c07d6b36d006f9730a51db3f16d3ff7e:63
(anonymous) @ modern-footer.js:371
setTimeout
initFacebookContainerEffect @ modern-footer.js:370
(anonymous) @ modern-footer.js:24
mobile-dropdown-fix-v4.js:267 Mobile dropdown toggles: 6
mobile-dropdown-fix-v4.js:283 Mobile back buttons: 6
cart-realtime.js:25 Cart count initialized: 3
cart-sync.js:74 Syncing cart count: {serverCount: 3, localCount: 3, lastKnownCount: -1}
cart-sync.js:111 Updating all cart badges: {count: 3, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
mobile-cart-badge-fix.js:153 Updating mobile cart badge: {count: 3, hasMobileNavBadge: true, hasMobileNavItem: true}
mobile-dropdown-fix-v4.js:115 Opening submenu, height: 305
ajax-filter.js:3222 AJAX Filter: Final override...
ajax-filter.js:170 AJAX Filter: Overriding apply filters button
ajax-filter.js:3228 🔧 AJAX Filter: Re-defining removeKeywordFilter...
ajax-filter.js:3287 🔧 AJAX Filter: Overriding onclick handler for keyword remove button...
ajax-filter.js:3303 ❌ Keyword remove button not found for override
(anonymous) @ ajax-filter.js:3303
setTimeout
(anonymous) @ ajax-filter.js:3221
setTimeout
(anonymous) @ ajax-filter.js:3214
ajax-filter.js:3306 ✅ removeKeywordFilter re-defined and event delegation set up successfully
cart-realtime.js:827 Updating cart count: {count: 3, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
cart-realtime.js:924 Cart count updated successfully: 3
mini-cart-update.js:28 Updating mini cart: {cartCount: 3, cartItems: Array(1), cartTotal: '21.000.000 đ'}
cart-realtime.js:827 Updating cart count: {count: 3, cartBadges: 1, hasMobileBadge: true, hasMobileNavBadge: true}
cart-realtime.js:924 Cart count updated successfully: 3
recently-viewed-trending.js:137 Section viewed: recently_viewed
recently-viewed-trending.js:137 Section viewed: trending
ajax-filter.js:69 🔥 AJAX Filter: Pagination link clicked - taking control! <a class=​"page-link ajax-pagination-link loading" href=​"http:​/​/​localhost/​noithatbangvu/​products.php?items_per_page=12&page=4&scroll=1" data-page=​"4" aria-label=​"Trang 4" data-original-content=​"
4                                            " style=​"transition:​ 0.3s cubic-bezier(0.4, 0, 0.2, 1)​;​ transform:​ scale(0.95)​;​ pointer-events:​ none;​ opacity:​ 0.3;​">​…​</a>​flex
ajax-filter.js:75 🚀 AJAX Filter: Loading page 4 with skeleton loading
ajax-filter.js:1586 AJAX Filter: Adding enhanced loading state to pagination link
ajax-filter.js:230 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: false keywordRemoved: false
ajax-filter.js:259 🔍 AJAX Filter: Final keyword in data: None
ajax-filter.js:975 AJAX Filter: loadProducts called {filterData: {…}, page: 4, updateHistory: true, fastMode: false}
ajax-filter.js:1902 AJAX Filter: Showing immediate loading skeleton
ajax-filter.js:1912 AJAX Filter: Current products grid height: 1929px
ajax-filter.js:1918 AJAX Filter: Set min-height to 1929px to preserve pagination position
ajax-filter.js:1101 AJAX Filter: Showing sophisticated loading state
ajax-filter.js:1003 AJAX Filter: Sending request to api/filter-products.php with data {sort: 'newest', items_per_page: 12, page: 4}
ajax-filter.js:69 🔥 AJAX Filter: Pagination link clicked - taking control! <a class=​"page-link ajax-pagination-link loading" href=​"http:​/​/​localhost/​noithatbangvu/​products.php?items_per_page=12&page=4&scroll=1" data-page=​"4" aria-label=​"Trang 4" data-original-content=​"
4                                            " style=​"transition:​ 0.3s cubic-bezier(0.4, 0, 0.2, 1)​;​ transform:​ scale(0.95)​;​ pointer-events:​ none;​ opacity:​ 0.3;​">​…​</a>​flex
ajax-filter.js:75 🚀 AJAX Filter: Loading page 4 with skeleton loading
ajax-filter.js:1586 AJAX Filter: Adding enhanced loading state to pagination link
ajax-filter.js:230 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: false keywordRemoved: false
ajax-filter.js:259 🔍 AJAX Filter: Final keyword in data: None
ajax-filter.js:975 AJAX Filter: loadProducts called {filterData: {…}, page: 4, updateHistory: true, fastMode: false}
ajax-filter.js:1902 AJAX Filter: Showing immediate loading skeleton
ajax-filter.js:1912 AJAX Filter: Current products grid height: 1929px
ajax-filter.js:1918 AJAX Filter: Set min-height to 1929px to preserve pagination position
ajax-filter.js:1101 AJAX Filter: Showing sophisticated loading state
ajax-filter.js:1003 AJAX Filter: Sending request to api/filter-products.php with data {sort: 'newest', items_per_page: 12, page: 4}
ajax-filter.js:1014 AJAX Filter: Response received 200 true
ajax-filter.js:1014 AJAX Filter: Response received 200 true
ajax-filter.js:1941 AJAX Filter: Showing products loading skeleton
ajax-filter.js:1971 AJAX Filter: Creating 12 skeleton cards to match current layout
ajax-filter.js:1972 AJAX Filter: Current products grid min-height: 1929px
ajax-filter.js:1023 AJAX Filter: API response {success: true, data: {…}}
ajax-filter.js:1030 AJAX Filter: Processing completed in 2430ms, waiting additional 0ms
ajax-filter.js:1033 ⏳ AJAX Filter: Ensuring minimum loading time for better UX
ajax-filter.js:1941 AJAX Filter: Showing products loading skeleton
ajax-filter.js:1971 AJAX Filter: Creating 12 skeleton cards to match current layout
ajax-filter.js:1972 AJAX Filter: Current products grid min-height: 1929px
ajax-filter.js:1023 AJAX Filter: API response {success: true, data: {…}}
ajax-filter.js:1030 AJAX Filter: Processing completed in 3477ms, waiting additional 0ms
ajax-filter.js:1033 ⏳ AJAX Filter: Ensuring minimum loading time for better UX
ajax-filter.js:1747 AJAX Filter: Handling smooth UX - scroll first, then show content
ajax-filter.js:1751 AJAX Filter: Updating active state immediately after loading
ajax-filter.js:1694 AJAX Filter: Updating pagination active state immediately
ajax-filter.js:1702 AJAX Filter: Setting page 4 as active
ajax-filter.js:1738 AJAX Filter: Could not find number button for page 4
updatePaginationActiveState @ ajax-filter.js:1738
handleSmoothUX @ ajax-filter.js:1752
loadProducts @ ajax-filter.js:1044
await in loadProducts
(anonymous) @ ajax-filter.js:85
ajax-filter.js:1233 🎯 AJAX Filter: Hiding sophisticated loading state for Apply Filters button
ajax-filter.js:1241 ✅ AJAX Filter: Apply Filters button found, proceeding to hide loading state
ajax-filter.js:1655 AJAX Filter: Removing enhanced loading state from pagination links
ajax-filter.js:1747 AJAX Filter: Handling smooth UX - scroll first, then show content
ajax-filter.js:1751 AJAX Filter: Updating active state immediately after loading
ajax-filter.js:1694 AJAX Filter: Updating pagination active state immediately
ajax-filter.js:1702 AJAX Filter: Setting page 4 as active
ajax-filter.js:1738 AJAX Filter: Could not find number button for page 4
updatePaginationActiveState @ ajax-filter.js:1738
handleSmoothUX @ ajax-filter.js:1752
loadProducts @ ajax-filter.js:1044
await in loadProducts
(anonymous) @ ajax-filter.js:85
ajax-filter.js:1233 🎯 AJAX Filter: Hiding sophisticated loading state for Apply Filters button
ajax-filter.js:1241 ✅ AJAX Filter: Apply Filters button found, proceeding to hide loading state
ajax-filter.js:1655 AJAX Filter: Removing enhanced loading state from pagination links
ajax-filter.js:1441 AJAX Filter: Auto-scrolling to products section
ajax-filter.js:1446 AJAX Filter: Scrolling immediately for smooth UX
ajax-filter.js:1441 AJAX Filter: Auto-scrolling to products section
ajax-filter.js:1446 AJAX Filter: Scrolling immediately for smooth UX
ajax-filter.js:1493 AJAX Filter: Desktop header calculation: {totalHeight: 143, topBarHeight: 9, finalHeaderHeight: 134, calculation: '143 - 9 = 134'}
ajax-filter.js:1516 AJAX Filter: Final scroll calculation: {isMobile: false, productsOffset: 543, finalHeaderHeight: 134, targetPosition: 409, calculation: '543 - 134 = 409'}
ajax-filter.js:1542 AJAX Filter: Starting smooth scroll {from: 2385, to: 409, distance: -1976, duration: 400}
ajax-filter.js:1493 AJAX Filter: Desktop header calculation: {totalHeight: 143, topBarHeight: 9, finalHeaderHeight: 134, calculation: '143 - 9 = 134'}
ajax-filter.js:1516 AJAX Filter: Final scroll calculation: {isMobile: false, productsOffset: 543, finalHeaderHeight: 134, targetPosition: 409, calculation: '543 - 134 = 409'}
ajax-filter.js:1542 AJAX Filter: Starting smooth scroll {from: 2385, to: 409, distance: -1976, duration: 400}
recently-viewed-trending.js:137 Section viewed: recently_viewed
recently-viewed-trending.js:137 Section viewed: trending
ajax-filter.js:1573 AJAX Filter: Scroll animation completed
ajax-filter.js:1573 AJAX Filter: Scroll animation completed
ajax-filter.js:1760 AJAX Filter: Scroll completed, updating content
ajax-filter.js:3152 AJAX Filter: updateProductsStats called {current_page: 4, total_pages: 23, total_products: 267, items_per_page: 12, has_next: true, …}
ajax-filter.js:3173 AJAX Filter: Products stats calculation {currentPage: 4, itemsPerPage: 12, totalProducts: 267, startItem: 37, endItem: 48, …}
ajax-filter.js:3186 AJAX Filter: Products stats updated successfully
ajax-filter.js:2750 AJAX Filter: updateFilterBadge called {keyword: '', categories: Array(0), price_min: null, price_max: null, promotions: Array(0), …}
ajax-filter.js:2776 AJAX Filter: Active filters count: 0
ajax-filter.js:2852 AJAX Filter: Filter badge updated successfully
ajax-filter.js:2856 AJAX Filter: Syncing sidebar UI with current filter state
ajax-filter.js:230 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: false keywordRemoved: false
ajax-filter.js:259 🔍 AJAX Filter: Final keyword in data: None
ajax-filter.js:670 AJAX Filter: Syncing search inputs with keyword:
ajax-filter.js:691 AJAX Filter: Search inputs synced
ajax-filter.js:2880 AJAX Filter: Syncing price preset buttons {priceMin: undefined, priceMax: undefined}
ajax-filter.js:2912 AJAX Filter: Price preset buttons synced
ajax-filter.js:2916 AJAX Filter: Syncing category checkboxes []
ajax-filter.js:2926 AJAX Filter: Category checkboxes synced
ajax-filter.js:2930 AJAX Filter: Syncing promotion checkboxes []
ajax-filter.js:2940 AJAX Filter: Promotion checkboxes synced
ajax-filter.js:2944 AJAX Filter: Syncing price inputs {priceMin: undefined, priceMax: undefined}
ajax-filter.js:2958 AJAX Filter: Price inputs synced
ajax-filter.js:2876 AJAX Filter: Sidebar UI sync completed
ajax-filter.js:1777 AJAX Filter: UI synced with URL state
ajax-filter.js:1790 AJAX Filter: Updating content after skeleton is already shown
ajax-filter.js:2670 AJAX Filter: updateFilterResultsHeaderContent called (for simultaneous update) {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: ''}
ajax-filter.js:2700 AJAX Filter: No filter results header HTML from API, removing existing header
ajax-filter.js:2708 AJAX Filter: Filter results header content updated successfully (simultaneous)
ajax-filter.js:1760 AJAX Filter: Scroll completed, updating content
ajax-filter.js:3152 AJAX Filter: updateProductsStats called {current_page: 4, total_pages: 23, total_products: 267, items_per_page: 12, has_next: true, …}
ajax-filter.js:3173 AJAX Filter: Products stats calculation {currentPage: 4, itemsPerPage: 12, totalProducts: 267, startItem: 37, endItem: 48, …}
ajax-filter.js:3186 AJAX Filter: Products stats updated successfully
ajax-filter.js:2750 AJAX Filter: updateFilterBadge called {keyword: '', categories: Array(0), price_min: null, price_max: null, promotions: Array(0), …}
ajax-filter.js:2776 AJAX Filter: Active filters count: 0
ajax-filter.js:2852 AJAX Filter: Filter badge updated successfully
ajax-filter.js:2856 AJAX Filter: Syncing sidebar UI with current filter state
ajax-filter.js:230 🔍 AJAX Filter: collectFilterData called, ignoreUrlKeyword: false keywordRemoved: false
ajax-filter.js:259 🔍 AJAX Filter: Final keyword in data: None
ajax-filter.js:670 AJAX Filter: Syncing search inputs with keyword:
ajax-filter.js:691 AJAX Filter: Search inputs synced
ajax-filter.js:2880 AJAX Filter: Syncing price preset buttons {priceMin: undefined, priceMax: undefined}
ajax-filter.js:2912 AJAX Filter: Price preset buttons synced
ajax-filter.js:2916 AJAX Filter: Syncing category checkboxes []
ajax-filter.js:2926 AJAX Filter: Category checkboxes synced
ajax-filter.js:2930 AJAX Filter: Syncing promotion checkboxes []
ajax-filter.js:2940 AJAX Filter: Promotion checkboxes synced
ajax-filter.js:2944 AJAX Filter: Syncing price inputs {priceMin: undefined, priceMax: undefined}
ajax-filter.js:2958 AJAX Filter: Price inputs synced
ajax-filter.js:2876 AJAX Filter: Sidebar UI sync completed
ajax-filter.js:1777 AJAX Filter: UI synced with URL state
ajax-filter.js:1790 AJAX Filter: Updating content after skeleton is already shown
ajax-filter.js:2670 AJAX Filter: updateFilterResultsHeaderContent called (for simultaneous update) {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: ''}
ajax-filter.js:2700 AJAX Filter: No filter results header HTML from API, removing existing header
ajax-filter.js:2708 AJAX Filter: Filter results header content updated successfully (simultaneous)
ajax-filter.js:2039 AJAX Filter: Updating products grid content after skeleton
ajax-filter.js:2039 AJAX Filter: Updating products grid content after skeleton
ajax-filter.js:2188 AJAX Filter: updateProductsGrid called {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: ''}
ajax-filter.js:2196 AJAX Filter: productsGrid element found <div class=​"products-grid grid grid-cols-1 gap-6" id=​"productsGrid" style=​"min-height:​ 1929px;​ opacity:​ 0.5;​ transition:​ opacity 0.25s ease-out;​">​…​</div>​grid
ajax-filter.js:2201 AJAX Filter: Rendering 12 products
ajax-filter.js:2233 AJAX Filter: Fading out old products
ajax-filter.js:2188 AJAX Filter: updateProductsGrid called {products: Array(12), pagination: {…}, filters: {…}, filter_results_header_html: ''}
ajax-filter.js:2196 AJAX Filter: productsGrid element found <div class=​"products-grid grid grid-cols-1 gap-6" id=​"productsGrid" style=​"min-height:​ 1929px;​ opacity:​ 1;​ transition:​ opacity 0.25s ease-out;​ transform:​ translateY(0px)​;​">​…​</div>​grid
ajax-filter.js:2201 AJAX Filter: Rendering 12 products
ajax-filter.js:2233 AJAX Filter: Fading out old products
ajax-filter.js:2244 AJAX Filter: Products transition completed
ajax-filter.js:2244 AJAX Filter: Products transition completed
ajax-filter.js:2066 AJAX Filter: New content height: 1929px
ajax-filter.js:2071 AJAX Filter: Reset min-height after content loaded
ajax-filter.js:2066 AJAX Filter: New content height: 1929px
ajax-filter.js:2071 AJAX Filter: Reset min-height after content loaded
