<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagination Text Size</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-pagination {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .pagination-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .page-item {
            margin: 0;
        }
        .page-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            height: 44px;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #ffffff;
            color: #6b7280;
            font-weight: 500;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            cursor: pointer;
        }
        .page-link:hover {
            background: rgba(243, 115, 33, 0.05);
            border-color: rgba(243, 115, 33, 0.3);
            color: #F37321;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(243, 115, 33, 0.15);
        }
        
        /* Navigation Buttons (Previous/Next) */
        .page-link .page-text {
            font-weight: 500;
            font-size: 0.875rem; /* Đảm bảo kích thước nhất quán */
            line-height: 1.2;
        }
        
        /* Enhanced Loading State for Pagination Links */
        .page-link.loading {
            pointer-events: none !important;
            cursor: not-allowed !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            position: relative !important;
        }

        /* Force remove any pseudo-element spinners */
        .page-link.loading::before,
        .page-link.loading::after {
            display: none !important;
            content: none !important;
        }

        /* Override any FontAwesome spinner */
        .page-link.loading .fa-spinner,
        .page-link.loading .fa-spin,
        .page-link.loading i[class*="fa-spin"] {
            display: none !important;
            animation: none !important;
        }
        
        /* Pagination Loading Content */
        .pagination-loading-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Enhanced Loading Dots Animation */
        .loading-dots {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-dots .dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #F37321;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots .dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dots .dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        .loading-dots .dot:nth-child(3) {
            animation-delay: 0s;
        }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        /* Loading Text Animation - Improved Size */
        .loading-text {
            font-size: 0.875rem !important; /* Tăng từ 0.75rem lên 0.875rem (14px) */
            font-weight: 600 !important; /* Tăng từ 500 lên 600 để đậm hơn */
            color: #495057 !important; /* Đậm hơn từ #6c757d */
            animation: loadingTextPulse 2s ease-in-out infinite;
            letter-spacing: 0.025em; /* Thêm letter-spacing để dễ đọc hơn */
            line-height: 1.2 !important; /* Đảm bảo line-height tốt */
            white-space: nowrap; /* Đảm bảo text không bị wrap */
        }

        /* Specific styling for loading text in pagination links */
        .page-link.loading .loading-text {
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            color: #495057 !important;
            margin: 0 0.25rem; /* Thêm margin để tạo khoảng cách với dots */
        }

        @keyframes loadingTextPulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        
        .page-item.active .page-link {
            background: #F37321;
            border-color: #F37321;
            color: white;
        }
        .page-item.disabled .page-link {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .size-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .size-demo {
            text-align: center;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
        }
        .size-demo h5 {
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        .old-size {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6c757d;
        }
        .new-size {
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
            letter-spacing: 0.025em;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <h1>📏 Test Pagination Text Size</h1>
        <p>Kiểm tra kích thước text "Đang tải" trong nút Trước/Sau đã được cải thiện</p>
        
        <div class="test-section">
            <h3>📊 So Sánh Kích Thước Text</h3>
            <p>So sánh kích thước text trước và sau khi cải thiện:</p>
            
            <div class="size-comparison">
                <div class="size-demo">
                    <h5>❌ Trước (Nhỏ)</h5>
                    <div class="old-size">Đang tải...</div>
                    <small>0.75rem (12px), font-weight: 500</small>
                </div>
                
                <div class="size-demo">
                    <h5>✅ Sau (Hợp Lý)</h5>
                    <div class="new-size">Đang tải...</div>
                    <small>0.875rem (14px), font-weight: 600</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔄 Demo Loading Text</h3>
            <p>Click vào nút "Trước" và "Sau" để xem text loading với kích thước mới:</p>
            
            <nav class="demo-pagination">
                <ul class="pagination-list">
                    <!-- Previous Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">
                            <i class="fas fa-chevron-left"></i>
                            <span class="page-text">Trước</span>
                        </a>
                    </li>
                    
                    <!-- Page Numbers -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">1</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link demo-pagination-link" href="#" data-page="2">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">3</a>
                    </li>
                    
                    <!-- Next Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">
                            <span class="page-text">Sau</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div id="demo-result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Thực Tế</h3>
            <p>Test với products page thực tế để xem text size mới</p>
            
            <button class="test-button" onclick="openProductsPage()">Mở Products Page</button>
            <button class="test-button" onclick="showTestInstructions()">Hướng Dẫn Test</button>
            
            <div id="real-test-result"></div>
        </div>
    </div>

    <script>
        // Demo pagination loading effect with improved text size
        document.addEventListener('click', function(e) {
            if (e.target.closest('.demo-pagination-link')) {
                e.preventDefault();
                const link = e.target.closest('.demo-pagination-link');
                
                // Add loading state
                addDemoLoadingState(link);
                
                // Simulate loading time
                setTimeout(() => {
                    removeDemoLoadingState(link);
                    showResult('demo-result', `✅ Text "Đang tải" với kích thước mới (0.875rem, font-weight: 600)`, 'success');
                }, 2000);
            }
        });

        function addDemoLoadingState(clickedLink) {
            // Save original content
            if (!clickedLink.dataset.originalContent) {
                clickedLink.dataset.originalContent = clickedLink.innerHTML;
            }
            
            // Add loading state with enhanced animation
            clickedLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            clickedLink.style.transform = 'scale(0.95)';
            clickedLink.style.pointerEvents = 'none';
            clickedLink.classList.add('loading');

            // Create loading content with improved text size
            const originalContent = clickedLink.dataset.originalContent;
            let loadingHTML = '';

            if (originalContent.includes('page-text')) {
                // Previous/Next buttons with improved text
                if (originalContent.includes('Trước')) {
                    loadingHTML = `
                        <div class="pagination-loading-content">
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                            <span class="page-text loading-text">Đang tải</span>
                        </div>
                    `;
                } else if (originalContent.includes('Sau')) {
                    loadingHTML = `
                        <div class="pagination-loading-content">
                            <span class="page-text loading-text">Đang tải</span>
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                    `;
                }
            } else {
                // Number buttons - dots only
                loadingHTML = `
                    <div class="pagination-loading-content">
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                `;
            }

            // Fade out old content, then fade in new content
            clickedLink.style.opacity = '0.3';
            
            setTimeout(() => {
                clickedLink.innerHTML = loadingHTML;
                clickedLink.style.opacity = '0.8';
            }, 150);
        }

        function removeDemoLoadingState(link) {
            // Fade out loading content
            link.style.opacity = '0.3';
            
            setTimeout(() => {
                // Restore original content
                if (link.dataset.originalContent) {
                    link.innerHTML = link.dataset.originalContent;
                    delete link.dataset.originalContent;
                }

                // Fade in and restore styles with smooth animation
                link.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                link.style.opacity = '1';
                link.style.transform = 'scale(1)';
                link.style.pointerEvents = '';
                link.classList.remove('loading');

                // Reset styles after animation completes
                setTimeout(() => {
                    link.style.transition = '';
                    link.style.transform = '';
                }, 300);
            }, 150);
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function openProductsPage() {
            window.open('http://localhost/noithatbangvu/products.php', '_blank');
            showResult('real-test-result', '✅ Products page opened in new tab', 'success');
        }

        function showTestInstructions() {
            showResult('real-test-result', `
                📋 <strong>Hướng Dẫn Test Text Size:</strong><br><br>
                
                <strong>🎯 Mục Tiêu:</strong> Kiểm tra text "Đang tải" có kích thước hợp lý<br><br>
                
                <strong>📝 Các Bước Test:</strong><br>
                1. <strong>Mở products page</strong> trong tab mới<br>
                2. <strong>Scroll xuống</strong> phần pagination<br>
                3. <strong>Click nút "Trước" hoặc "Sau"</strong><br>
                4. <strong>Quan sát text "Đang tải":</strong><br>
                   &nbsp;&nbsp;• Kích thước: 0.875rem (14px)<br>
                   &nbsp;&nbsp;• Font weight: 600 (đậm hơn)<br>
                   &nbsp;&nbsp;• Màu: #495057 (đậm hơn)<br>
                   &nbsp;&nbsp;• Letter spacing: 0.025em<br><br>
                
                <strong>✅ Kết Quả Mong Đợi:</strong><br>
                • Text "Đang tải" dễ đọc và có kích thước hợp lý<br>
                • Không quá nhỏ như trước đây<br>
                • Cân đối với kích thước nút pagination
            `, 'info');
        }

        // Show initial demo message
        window.addEventListener('load', function() {
            showResult('demo-result', '👆 Click vào nút "Trước" hoặc "Sau" để xem text loading với kích thước mới', 'info');
        });
    </script>
</body>
</html>
