<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Products Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 2px solid #f97316;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            max-height: 80vh;
            overflow-y: auto;
        }
        .debug-panel h3 {
            margin: 0 0 10px 0;
            color: #f97316;
        }
        .debug-panel button {
            background: #f97316;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .debug-panel button:hover {
            background: #ea580c;
        }
        .debug-log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        .debug-status {
            padding: 5px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 12px;
        }
        .status-success {
            background: #d1fae5;
            color: #065f46;
        }
        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h3>🔧 Debug Panel</h3>
        
        <div id="debugStatus" class="debug-status status-warning">
            Initializing...
        </div>
        
        <button onclick="testAjaxFilterExists()">Test AJAX Filter</button>
        <button onclick="testAPICall()">Test API Call</button>
        <button onclick="testFormData()">Test Form Data</button>
        <button onclick="testElementExists()">Test Elements</button>
        <button onclick="clearDebugLog()">Clear Log</button>
        
        <div id="debugLog" class="debug-log"></div>
    </div>

    <!-- Include the actual products page content -->
    <iframe src="products.php" width="100%" height="600" style="border: 1px solid #ddd; border-radius: 8px;"></iframe>

    <script>
        function debugLog(message, type = 'info') {
            const log = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            log.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(message, type) {
            const status = document.getElementById('debugStatus');
            status.textContent = message;
            status.className = `debug-status status-${type}`;
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function testAjaxFilterExists() {
            debugLog('=== Testing AJAX Filter Existence ===');
            
            const iframe = document.querySelector('iframe');
            if (!iframe || !iframe.contentWindow) {
                debugLog('ERROR: Cannot access iframe', 'error');
                return;
            }

            try {
                const iframeWindow = iframe.contentWindow;
                debugLog('Iframe window accessible: ' + !!iframeWindow);
                
                if (iframeWindow.ajaxFilter) {
                    debugLog('✓ window.ajaxFilter exists', 'success');
                    debugLog('AJAX Filter type: ' + typeof iframeWindow.ajaxFilter);
                    
                    if (iframeWindow.ajaxFilterActive) {
                        debugLog('✓ ajaxFilterActive flag is set', 'success');
                    } else {
                        debugLog('⚠ ajaxFilterActive flag not set', 'warning');
                    }
                } else {
                    debugLog('✗ window.ajaxFilter not found', 'error');
                }
                
                updateStatus('AJAX Filter test completed', 'success');
            } catch (error) {
                debugLog('ERROR: ' + error.message, 'error');
                updateStatus('AJAX Filter test failed', 'error');
            }
        }

        function testAPICall() {
            debugLog('=== Testing Direct API Call ===');
            
            const testData = {
                page: 1,
                items_per_page: 5
            };
            
            debugLog('Sending test data: ' + JSON.stringify(testData));
            
            fetch('api/filter-products.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                debugLog('Response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    debugLog('✓ API call successful', 'success');
                    debugLog('Products returned: ' + data.data.products.length);
                    debugLog('Total products: ' + data.data.pagination.total_products);
                    updateStatus('API test successful', 'success');
                } else {
                    debugLog('✗ API returned error: ' + data.error, 'error');
                    updateStatus('API test failed', 'error');
                }
            })
            .catch(error => {
                debugLog('✗ API call failed: ' + error.message, 'error');
                updateStatus('API test failed', 'error');
            });
        }

        function testFormData() {
            debugLog('=== Testing Form Data Collection ===');
            
            const iframe = document.querySelector('iframe');
            if (!iframe || !iframe.contentWindow) {
                debugLog('ERROR: Cannot access iframe', 'error');
                return;
            }

            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Test form elements
                const applyBtn = iframeDoc.getElementById('applyFilters');
                debugLog('Apply button exists: ' + !!applyBtn);
                
                const categoryInputs = iframeDoc.querySelectorAll('input[name="category[]"]');
                debugLog('Category inputs found: ' + categoryInputs.length);
                
                const priceMinInput = iframeDoc.getElementById('price-min');
                const priceMaxInput = iframeDoc.getElementById('price-max');
                debugLog('Price inputs exist: min=' + !!priceMinInput + ', max=' + !!priceMaxInput);
                
                const promotionInputs = iframeDoc.querySelectorAll('input[name="promotion[]"]');
                debugLog('Promotion inputs found: ' + promotionInputs.length);
                
                updateStatus('Form data test completed', 'success');
            } catch (error) {
                debugLog('ERROR: ' + error.message, 'error');
                updateStatus('Form data test failed', 'error');
            }
        }

        function testElementExists() {
            debugLog('=== Testing Required Elements ===');
            
            const iframe = document.querySelector('iframe');
            if (!iframe || !iframe.contentWindow) {
                debugLog('ERROR: Cannot access iframe', 'error');
                return;
            }

            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const productsGrid = iframeDoc.getElementById('productsGrid');
                debugLog('productsGrid exists: ' + !!productsGrid);
                if (productsGrid) {
                    debugLog('productsGrid class: ' + productsGrid.className);
                    debugLog('productsGrid children: ' + productsGrid.children.length);
                }
                
                const applyBtn = iframeDoc.getElementById('applyFilters');
                debugLog('applyFilters button exists: ' + !!applyBtn);
                if (applyBtn) {
                    debugLog('Button has ajax-filter attribute: ' + applyBtn.hasAttribute('data-ajax-filter'));
                }
                
                updateStatus('Element test completed', 'success');
            } catch (error) {
                debugLog('ERROR: ' + error.message, 'error');
                updateStatus('Element test failed', 'error');
            }
        }

        // Auto-run initial tests
        setTimeout(() => {
            debugLog('🚀 Starting debug tests...');
            updateStatus('Running tests...', 'warning');
            
            setTimeout(testAjaxFilterExists, 1000);
            setTimeout(testElementExists, 2000);
            setTimeout(testAPICall, 3000);
        }, 2000);
    </script>
</body>
</html>
