<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Redirect - <PERSON><PERSON><PERSON> Thất Băng <PERSON>ũ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #F37321 0%, #ff6b35 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #F37321;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1a202c;
            font-size: 1.2rem;
        }
        
        .test-link {
            display: inline-block;
            background: #F37321;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .test-link:hover {
            background: #e55a0b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
        }
        
        .test-instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .test-instructions h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .test-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #92400e;
        }
        
        .test-instructions li {
            margin-bottom: 8px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 5px 0;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .url-display {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background: #f8fafc;
            font-weight: 600;
        }
        
        .old-url {
            color: #dc2626;
            text-decoration: line-through;
        }
        
        .new-url {
            color: #059669;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-route"></i> Test Chuyển Hướng Tìm Kiếm</h1>
            <p>Kiểm tra việc redirect từ search.php sang products.php</p>
        </div>
        
        <div class="test-content">
            <div class="test-instructions">
                <h4><i class="fas fa-info-circle"></i> Thông tin về thay đổi:</h4>
                <ul>
                    <li><strong>Trước:</strong> Tìm kiếm chuyển đến <span class="old-url">search.php</span></li>
                    <li><strong>Sau:</strong> Tìm kiếm chuyển đến <span class="new-url">products.php</span></li>
                    <li><strong>Redirect:</strong> search.php tự động chuyển hướng 301 sang products.php</li>
                    <li><strong>Lợi ích:</strong> Tận dụng trang sản phẩm hiện có, SEO tốt hơn</li>
                </ul>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-search"></i> Test Tìm Kiếm từ Header</h3>
                <p>Các liên kết này sẽ chuyển trực tiếp đến <strong>products.php</strong>:</p>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=sofa" class="test-link" target="_blank">
                    <i class="fas fa-couch"></i> Tìm "sofa"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=bàn" class="test-link" target="_blank">
                    <i class="fas fa-table"></i> Tìm "bàn"
                </a>
                
                <a href="http://localhost/noithatbangvu/products.php?keyword=ghế" class="test-link" target="_blank">
                    <i class="fas fa-chair"></i> Tìm "ghế"
                </a>
                
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Header form đã được cập nhật để gửi đến products.php
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-redirect"></i> Test Redirect từ URL Cũ</h3>
                <p>Các liên kết này sẽ <strong>redirect 301</strong> từ search.php sang products.php:</p>
                
                <a href="http://localhost/noithatbangvu/search.php?keyword=sofa" class="test-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i> search.php?keyword=sofa
                </a>
                
                <a href="http://localhost/noithatbangvu/search.php?keyword=bàn&category=1" class="test-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i> search.php với filter
                </a>
                
                <a href="http://localhost/noithatbangvu/search.php?keyword=ghế&price_min=1000000&price_max=5000000" class="test-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i> search.php với giá
                </a>
                
                <div class="status-info">
                    <i class="fas fa-info-circle"></i> 💡 Mở Developer Tools (F12) để xem redirect 301
                </div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-table"></i> So Sánh URL</h3>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Trường hợp</th>
                            <th>URL Cũ (search.php)</th>
                            <th>URL Mới (products.php)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Tìm kiếm cơ bản</td>
                            <td class="old-url">search.php?keyword=sofa</td>
                            <td class="new-url">products.php?keyword=sofa</td>
                        </tr>
                        <tr>
                            <td>Với danh mục</td>
                            <td class="old-url">search.php?keyword=bàn&category=1</td>
                            <td class="new-url">products.php?keyword=bàn&category=1</td>
                        </tr>
                        <tr>
                            <td>Với khoảng giá</td>
                            <td class="old-url">search.php?keyword=ghế&price_min=1000000</td>
                            <td class="new-url">products.php?keyword=ghế&price_min=1000000</td>
                        </tr>
                        <tr>
                            <td>Với sắp xếp</td>
                            <td class="old-url">search.php?keyword=tủ&sort=price_asc</td>
                            <td class="new-url">products.php?keyword=tủ&sort=price_asc</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-clipboard-check"></i> Checklist Hoàn Thành</h3>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Cập nhật form tìm kiếm trong header (partials/header.php)
                </div>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Cập nhật JavaScript search.js
                </div>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Cập nhật JavaScript search-improved.js
                </div>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Cập nhật JavaScript enhanced-search.js
                </div>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Tạo redirect 301 trong search.php
                </div>
                <div class="status-success">
                    <i class="fas fa-check-circle"></i> ✅ Cập nhật CSS selectors
                </div>
            </div>
            
            <div class="test-instructions">
                <h4><i class="fas fa-rocket"></i> Hướng dẫn test:</h4>
                <ol>
                    <li>Click vào các liên kết test ở trên</li>
                    <li>Kiểm tra URL trong thanh địa chỉ - phải là products.php</li>
                    <li>Mở Developer Tools (F12) → Network tab để xem redirect 301</li>
                    <li>Test tìm kiếm từ header trang chính</li>
                    <li>Đảm bảo tất cả tham số (keyword, category, price) được giữ nguyên</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
