<?php
// Bắt đầu session và include các file cần thiết
require_once 'includes/init.php';

// Kiểm tra xem người dùng đã đăng nhập chưa
if (is_logged_in()) {
    // Nếu đã đăng nhập, chuyển hướng đến trang chủ hoặc trang được chỉ định
    $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : BASE_URL;
    redirect($redirect);
}

// Xác định chế độ đăng ký hay đăng nhập
$is_register = isset($_GET['register']) && $_GET['register'] == 1;

// Lấy tham số redirect nếu có
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';

// Xử lý đăng nhập
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
        redirect(BASE_URL . '/auth.php' . ($redirect ? '?redirect=' . urlencode($redirect) : ''));
    }

    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) ? true : false;

    // Đăng nhập
    $result = login_user($username, $password);

    if ($result['success']) {
        // Nếu đăng nhập thành công
        set_flash_message('success', $result['message']);

        // Chuyển hướng đến trang được chỉ định hoặc trang chủ
        redirect($redirect ? $redirect : BASE_URL);
    } else {
        // Nếu đăng nhập thất bại
        set_flash_message('error', $result['message']);
        redirect(BASE_URL . '/auth.php' . ($redirect ? '?redirect=' . urlencode($redirect) : ''));
    }
}

// Xử lý đăng ký
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'register') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
        redirect(BASE_URL . '/auth.php?register=1' . ($redirect ? '&redirect=' . urlencode($redirect) : ''));
    }

    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = $_POST['full_name'] ?? '';
    $avatar = isset($_FILES['avatar']) ? $_FILES['avatar'] : null;

    // Kiểm tra mật khẩu xác nhận
    if ($password !== $confirm_password) {
        set_flash_message('error', 'Mật khẩu xác nhận không khớp.');
        redirect(BASE_URL . '/auth.php?register=1' . ($redirect ? '&redirect=' . urlencode($redirect) : ''));
    }

    // Đăng ký
    $result = register_user($username, $email, $password, $full_name, $avatar);

    if ($result['success']) {
        // Nếu đăng ký thành công

        // Lưu thông tin đăng nhập để điền sẵn vào form đăng nhập
        $_SESSION['registered_username'] = $username;
        $_SESSION['registered_password'] = $password; // Lưu tạm để điền form (sẽ bị xóa sau khi điền)

        // Kiểm tra xem có đơn hàng đang chờ liên kết không
        if (isset($_SESSION['pending_order_id']) && !empty($_SESSION['pending_order_id'])) {
            $order_id = $_SESSION['pending_order_id'];

            // Liên kết đơn hàng với tài khoản mới tạo
            $link_result = link_order_to_user($result['user_id'], $order_id);

            if ($link_result['success']) {
                // Thêm thông báo về việc liên kết đơn hàng thành công với màu xanh lá cây
                set_flash_message('success', 'Đăng ký tài khoản thành công! Đơn hàng #' . $order_id . ' đã được liên kết với tài khoản của bạn.');

                // Xóa thông tin đơn hàng khỏi session
                unset($_SESSION['pending_order_id']);
                unset($_SESSION['pending_order_email']);
                unset($_SESSION['pending_order_full_name']);

                // Đăng nhập người dùng tự động (trường hợp có đơn hàng)
                $_SESSION['user_id'] = $result['user_id'];
                $_SESSION['username'] = $username;
                $_SESSION['user_role'] = 'customer';
                $_SESSION['full_name'] = $full_name;

                // Chuyển hướng đến trang đơn hàng
                redirect(BASE_URL . '/account/orders.php');
            } else {
                // Nếu liên kết thất bại, vẫn thông báo đăng ký thành công với màu xanh lá cây
                set_flash_message('success', 'Đăng ký tài khoản thành công! Đơn hàng của bạn đã sẵn sàng.');

                // Ghi log lỗi
                error_log("Lỗi liên kết đơn hàng #$order_id với user_id={$result['user_id']}: " . $link_result['message']);

                // Đăng nhập người dùng tự động (trường hợp có đơn hàng nhưng liên kết thất bại)
                $_SESSION['user_id'] = $result['user_id'];
                $_SESSION['username'] = $username;
                $_SESSION['user_role'] = 'customer';
                $_SESSION['full_name'] = $full_name;

                // Chuyển hướng đến trang đơn hàng
                redirect(BASE_URL . '/account/orders.php');
            }
        } else {
            // Nếu không có đơn hàng cần liên kết
            set_flash_message('success', 'Đăng ký tài khoản thành công! Tên đăng nhập đã được điền sẵn, vui lòng nhập mật khẩu để đăng nhập.');

            // Chuyển hướng đến trang đăng nhập với thông tin đã điền sẵn
            redirect(BASE_URL . '/auth.php' . ($redirect ? '?redirect=' . urlencode($redirect) : ''));
        }
    } else {
        // Nếu đăng ký thất bại
        set_flash_message('error', $result['message']);
        redirect(BASE_URL . '/auth.php?register=1' . ($redirect ? '&redirect=' . urlencode($redirect) : ''));
    }
}

// Thiết lập tiêu đề trang
$page_title = $is_register ? 'Đăng ký tài khoản' : 'Đăng nhập';
$page_description = $is_register ? 'Đăng ký tài khoản mới tại Nội Thất Băng Vũ' : 'Đăng nhập vào tài khoản của bạn tại Nội Thất Băng Vũ';
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">

    <!-- Favicon - Đầy đủ cho tất cả thiết bị và SEO -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo BASE_URL; ?>/assets/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg">
    <link rel="icon" type="image/x-icon" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.ico">
    <link rel="manifest" href="<?php echo BASE_URL; ?>/assets/images/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#f37321">
    <meta name="msapplication-TileImage" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-192x192.png">
    <meta name="theme-color" content="#f37321">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:title" content="<?php echo $page_title; ?> - <?php echo SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="twitter:title" content="<?php echo $page_title; ?> - <?php echo SITE_NAME; ?>">
    <meta property="twitter:description" content="<?php echo $page_description; ?>">
    <meta property="twitter:image" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-512x512.png">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tailwind.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/auth.css">
    <style>
        /* Password Guidelines CSS */
        .password-guidelines {
            margin-top: 0.5rem;
            padding: 1rem;
            border-radius: 8px;
            background-color: #f8f9fa;
            border: 1px solid #e2e8f0;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            padding: 0 1rem;
            margin-top: 0;
        }

        .password-guidelines.show {
            max-height: 300px;
            padding: 1rem;
            opacity: 1;
            margin-top: 0.5rem;
            border: 1px solid #e2e8f0;
        }

        .guidelines-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            color: #2d3748;
            font-weight: 600;
        }

        .guidelines-header i {
            margin-right: 0.5rem;
            color: #4a5568;
            font-size: 1rem;
        }

        .guidelines-list {
            list-style: none;
            padding: 0;
            margin: 0 0 0.75rem 0;
        }

        .guidelines-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #4a5568;
            transition: all 0.3s ease;
        }

        .guidelines-list li i {
            font-size: 0.75rem;
            margin-right: 0.5rem;
            color: #a0aec0;
            transition: all 0.3s ease;
        }

        .guidelines-list li.valid {
            color: #2f855a;
        }

        .guidelines-list li.valid i {
            color: #48bb78;
        }

        .guidelines-list li.valid i:before {
            content: "\f058"; /* fa-check-circle */
        }

        .guidelines-tips {
            padding-top: 0.75rem;
            border-top: 1px dashed #e2e8f0;
            color: #4a5568;
            font-size: 0.8125rem;
        }

        .guidelines-tips p {
            margin: 0;
            line-height: 1.4;
        }

        .guidelines-tips i {
            color: #ecc94b;
            margin-right: 0.25rem;
        }

        /* Responsive CSS - Ẩn phần hình ảnh trên thiết bị di động */
        @media screen and (max-width: 768px) {
            .auth-image-section {
                display: none;
            }

            .auth-container {
                grid-template-columns: 1fr;
            }

            .auth-form-section {
                max-width: 100%;
                padding: 2rem 1.5rem;
            }

            /* Điều chỉnh hiển thị form trên mobile */
            .auth-form {
                width: 100%;
            }

            .auth-logo {
                margin-bottom: 1.5rem;
            }

            .social-login {
                flex-direction: column;
                gap: 0.75rem;
            }

            .social-button {
                width: 100%;
            }
        }

        /* Modal thông báo đăng nhập mạng xã hội */
        #socialLoginModal .modal-content {
            max-width: 400px;
            padding: 2rem;
            text-align: center;
        }

        #socialLoginModal .modal-header {
            margin-bottom: 1.5rem;
        }

        #socialLoginModal .modal-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            font-size: 24px;
            color: #fff;
            background-color: #3B82F6;
        }

        #socialLoginModal .modal-icon.google {
            background-color: #DB4437;
        }

        #socialLoginModal .modal-icon.facebook {
            background-color: #1877F2;
        }

        #socialLoginModal .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        #socialLoginModal .social-login-message {
            margin-bottom: 1.5rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #4a5568;
        }

        #socialLoginModal .form-actions {
            display: flex;
            justify-content: center;
        }

        #socialLoginModal .primary-button {
            min-width: 150px;
            transition: all 0.3s ease;
        }

        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            overflow-y: auto;
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
            justify-content: center;
            align-items: center;
        }

        .modal-overlay.active {
            display: flex;
            opacity: 1;
        }

        .modal-content {
            position: relative;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.08);
            transform: scale(0.85) translateY(20px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
            width: 400px;
            max-width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(243, 115, 33, 0.5) rgba(243, 115, 33, 0.1);
            margin: 0 auto;
        }

        .modal-content::-webkit-scrollbar {
            width: 6px;
        }

        .modal-content::-webkit-scrollbar-track {
            background: rgba(243, 115, 33, 0.1);
            border-radius: 10px;
        }

        .modal-content::-webkit-scrollbar-thumb {
            background-color: rgba(243, 115, 33, 0.5);
            border-radius: 10px;
        }

        .modal-overlay.active .modal-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border: none;
            background: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #718096;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .modal-close:hover {
            background-color: #f7fafc;
            color: #4a5568;
        }

        /* Hiệu ứng khi nhấn nút đăng nhập mạng xã hội */
        .social-button.button-clicked {
            transform: scale(0.95);
            opacity: 0.8;
            transition: transform 0.2s ease, opacity 0.2s ease;
        }

        .modal-content.ripple-effect {
            animation: ripple-in 0.4s ease-out;
        }

        @keyframes ripple-in {
            0% {
                transform: scale(0.9);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.02);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Geometric Pattern Element -->
    <div class="geometric-pattern-top-right"></div>

    <div class="auth-container">
        <!-- Form Section -->
        <div class="auth-form-section">
            <!-- Logo -->
            <div class="auth-logo">
                <a href="<?php echo BASE_URL; ?>">
                    <img src="<?php echo BASE_URL; ?>/assets/images/logo/logo.svg" alt="<?php echo SITE_NAME; ?>" class="auth-logo-img">
                </a>
            </div>

            <!-- Welcome Message -->
            <div class="auth-welcome">
                <h1 class="auth-title">
                    <?php if ($is_register): ?>
                        Tạo tài khoản mới <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/party-popper.webp" alt="🎉" class="welcome-icon party-popper">
                    <?php else: ?>
                        Chào mừng trở lại <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/Waving-Hand-3d.webp" alt="👋" class="welcome-icon waving-hand">
                    <?php endif; ?>
                </h1>
                <p class="auth-subtitle">
                    <?php if ($is_register): ?>
                        Đăng ký để khám phá và trải nghiệm những sản phẩm nội thất cao cấp, thiết kế độc đáo và dịch vụ chuyên nghiệp của chúng tôi
                    <?php else: ?>
                        Đăng nhập để tiếp tục khám phá bộ sưu tập nội thất cao cấp và nhận những ưu đãi đặc biệt dành riêng cho bạn
                    <?php endif; ?>
                </p>
            </div>

            <!-- Flash Messages -->
            <?php
            // Kiểm tra nếu có thông báo đăng ký thành công
            if (isset($_SESSION['flash_messages']['success_register'])) {
                $success_register_message = $_SESSION['flash_messages']['success_register'];
                unset($_SESSION['flash_messages']['success_register']);
            ?>
                <div class="alert-container">
                    <div class="custom-alert custom-alert-register-success fade-in" role="alert">
                        <div class="alert-icon"><i class="fas fa-check-circle"></i></div>
                        <div class="alert-content">
                            <div><?php echo $success_register_message; ?></div>
                            <button type="button" id="login-now-btn" class="register-success-btn">
                                <i class="fas fa-sign-in-alt"></i> Đăng nhập ngay
                            </button>
                        </div>
                        <button type="button" class="alert-close" onclick="this.parentElement.classList.add('fade-out'); setTimeout(() => this.parentElement.parentElement.remove(), 300);">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            <?php
            }
            // Hiển thị các thông báo flash khác
            display_flash_message();
            ?>

            <!-- Auth Form -->
            <?php if ($is_register): ?>
                <!-- Register Form -->
                <form action="<?php echo BASE_URL; ?>/auth.php?register=1<?php echo $redirect ? '&redirect=' . urlencode($redirect) : ''; ?>" method="POST" class="auth-form" id="registerForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="register">

                    <!-- Step Indicator -->
                    <div class="step-indicator">
                        <div class="step-dots">
                            <div class="step-dot active" data-step="1">
                                <div class="dot-inner"></div>
                                <span class="step-label">Thông tin</span>
                            </div>
                            <div class="step-dot" data-step="2">
                                <div class="dot-inner"></div>
                                <span class="step-label">Tài khoản</span>
                            </div>
                            <div class="step-dot" data-step="3">
                                <div class="dot-inner"></div>
                                <span class="step-label">Bảo mật</span>
                            </div>
                        </div>
                        <div class="step-progress">
                            <div class="step-progress-bar"></div>
                        </div>
                    </div>

                    <!-- Step 1: Personal Information -->
                    <div class="form-step active" id="step1">
                    <!-- Full Name -->
                    <div class="form-group">
                        <label for="full_name" class="form-label">Họ và tên</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-user"></i></span>
                            <input type="text" id="full_name" name="full_name" class="form-input" placeholder="Nhập họ và tên của bạn" required>
                        </div>
                    </div>

                    <!-- Avatar Upload -->
                    <div class="form-group">
                        <label for="avatar" class="form-label">Ảnh đại diện <span class="text-sm text-gray-500">(tùy chọn)</span></label>
                        <div class="avatar-upload-container">
                            <div class="avatar-preview" id="avatar-preview">
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                                <img src="#" alt="Ảnh đại diện" class="avatar-image hidden">
                            </div>
                            <div class="avatar-input-container">
                                <label for="avatar" class="avatar-upload-button">
                                    <i class="fas fa-camera"></i> Chọn ảnh
                                </label>
                                <input type="file" id="avatar" name="avatar" class="avatar-input" accept="image/jpeg,image/png,image/gif,image/webp">
                                <div class="avatar-info">
                                    <p class="avatar-filename" id="avatar-filename">Chưa chọn file nào</p>
                                    <p class="avatar-hint">Hỗ trợ: JPG, PNG, GIF, WEBP. Tối đa 5MB.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- Navigation -->
                        <div class="form-nav">
                            <div></div> <!-- Empty div for flex spacing -->
                            <button type="button" class="next-step-button" data-next="2">
                                Tiếp tục <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Account Information -->
                    <div class="form-step" id="step2">
                    <!-- Username -->
                    <div class="form-group">
                        <label for="username" class="form-label">Tên đăng nhập</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-user-circle"></i></span>
                            <input type="text" id="username" name="username" class="form-input" placeholder="Nhập tên đăng nhập" required>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-envelope"></i></span>
                            <input type="email" id="email" name="email" class="form-input" placeholder="Nhập địa chỉ email" required>
                        </div>
                    </div>

                        <!-- Navigation -->
                        <div class="form-nav">
                            <button type="button" class="prev-step-button" data-prev="1">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </button>
                            <button type="button" class="next-step-button" data-next="3">
                                Tiếp tục <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Security Information -->
                    <div class="form-step" id="step3">
                    <!-- Password -->
                    <div class="form-group">
                        <label for="password" class="form-label">Mật khẩu</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-lock"></i></span>
                            <input type="password" id="password" name="password" class="form-input" placeholder="Nhập mật khẩu" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')" title="Hiện mật khẩu">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                            <div class="password-strength-meter">
                                <div class="strength-bar"></div>
                                <span class="strength-text">Độ mạnh mật khẩu</span>
                            </div>
                            <!-- Thêm hướng dẫn mật khẩu -->
                            <div class="password-guidelines">
                                <div class="guidelines-header">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Hướng dẫn tạo mật khẩu mạnh</span>
                                </div>
                                <ul class="guidelines-list">
                                    <li id="length-check"><i class="fas fa-circle"></i> Ít nhất 8 ký tự</li>
                                    <li id="uppercase-check"><i class="fas fa-circle"></i> Có ít nhất 1 chữ hoa (A-Z)</li>
                                    <li id="lowercase-check"><i class="fas fa-circle"></i> Có ít nhất 1 chữ thường (a-z)</li>
                                    <li id="number-check"><i class="fas fa-circle"></i> Có ít nhất 1 số (0-9)</li>
                                    <li id="special-check"><i class="fas fa-circle"></i> Có ít nhất 1 ký tự đặc biệt (!@#$%^&*)</li>
                                </ul>
                                <div class="guidelines-tips">
                                    <p><i class="fas fa-lightbulb"></i> <strong>Mẹo:</strong> Hãy tạo mật khẩu dễ nhớ nhưng khó đoán. Tránh dùng thông tin cá nhân như ngày sinh, tên hoặc các từ phổ biến.</p>
                                </div>
                            </div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Xác nhận mật khẩu</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-lock"></i></span>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-input" placeholder="Nhập lại mật khẩu" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')" title="Hiện mật khẩu">
                                <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-match-indicator hidden">
                                <i class="fas fa-check-circle"></i> Mật khẩu khớp
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="form-nav">
                            <button type="button" class="prev-step-button" data-prev="2">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </button>
                            <button type="submit" class="submit-button">
                                Hoàn tất đăng ký <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Submit Button (Hidden but kept for possible fallback) -->
                    <div class="form-group hidden">
                        <button type="submit" class="auth-button">Đăng ký</button>
                    </div>

                    <!-- Divider -->
                    <div class="auth-divider multi-step-divider">
                        <div class="divider-line"></div>
                        <span>hoặc</span>
                        <div class="divider-line"></div>
                    </div>

                    <!-- Social Login -->
                    <div class="social-login multi-step-social">
                        <a href="#" class="social-button google-button">
                            <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/Google.webp" alt="Google" class="social-icon">
                            <span>Google</span>
                        </a>
                        <a href="#" class="social-button facebook-button">
                            <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/Facebook.webp" alt="Facebook" class="social-icon">
                            <span>Facebook</span>
                        </a>
                    </div>

                    <!-- Login Link -->
                    <div class="auth-link">
                        <p>Đã có tài khoản? <a href="<?php echo BASE_URL; ?>/auth.php<?php echo $redirect ? '?redirect=' . urlencode($redirect) : ''; ?>">Đăng nhập</a></p>
                    </div>
                    </form>
                <?php else: ?>
                <!-- Login Form -->
                <form action="<?php echo BASE_URL; ?>/auth.php<?php echo $redirect ? '?redirect=' . urlencode($redirect) : ''; ?>" method="POST" class="auth-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="login">

                    <!-- Username -->
                    <div class="form-group">
                        <label for="username" class="form-label">Tên đăng nhập</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-user"></i></span>
                            <input type="text" id="username" name="username" class="form-input" placeholder="Nhập tên đăng nhập" required>
                        </div>
                    </div>

                    <!-- Password -->
                    <div class="form-group">
                        <label for="password" class="form-label">Mật khẩu</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-lock"></i></span>
                            <input type="password" id="password" name="password" class="form-input" placeholder="Nhập mật khẩu" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')" title="Hiện mật khẩu">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="form-options">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Ghi nhớ đăng nhập</label>
                        </div>
                        <a href="javascript:void(0);" class="forgot-password" id="openForgotPasswordModal">Quên mật khẩu?</a>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group">
                        <button type="submit" class="auth-button">Đăng nhập</button>
                    </div>

                    <!-- Divider -->
                    <div class="auth-divider">
                        <div class="divider-line"></div>
                        <span>hoặc</span>
                        <div class="divider-line"></div>
                    </div>

                    <!-- Social Login -->
                    <div class="social-login">
                        <a href="#" class="social-button google-button">
                            <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/Google.webp" alt="Google" class="social-icon">
                            <span>Google</span>
                        </a>
                        <a href="#" class="social-button facebook-button">
                            <img src="<?php echo BASE_URL; ?>/assets/images/logo-socal-login/Facebook.webp" alt="Facebook" class="social-icon">
                            <span>Facebook</span>
                        </a>
                    </div>

                    <!-- Register Link -->
                    <div class="auth-link">
                        <p>Chưa có tài khoản? <a href="<?php echo BASE_URL; ?>/auth.php?register=1<?php echo $redirect ? '&redirect=' . urlencode($redirect) : ''; ?>">Đăng ký</a></p>
                    </div>
                    </form>
                <?php endif; ?>
        </div>

        <!-- Image Section -->
        <div class="auth-image-section">
            <!-- Chú thích hình ảnh -->
            <div class="image-caption">
                <?php if ($is_register): ?>
                    <h3>Thiết kế không gian sống</h3>
                    <p>Tham gia cùng chúng tôi để khám phá những ý tưởng thiết kế nội thất độc đáo và sáng tạo</p>
                <?php else: ?>
                    <h3>Không gian sống hoàn hảo</h3>
                    <p>Chào mừng trở lại với bộ sưu tập nội thất cao cấp và dịch vụ thiết kế chuyên nghiệp</p>
                <?php endif; ?>
            </div>

            <div class="auth-image-container">
                <?php if ($is_register): ?>
                    <img src="<?php echo BASE_URL; ?>/assets/images/auth/register-image.jpg" alt="Đăng ký" class="auth-image">
                <?php else: ?>
                    <img src="<?php echo BASE_URL; ?>/assets/images/auth/login-image.jpg" alt="Đăng nhập" class="auth-image">
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Hiệu ứng khi trang tải xong
        document.addEventListener('DOMContentLoaded', function() {
            // Thêm class để kích hoạt hiệu ứng fade-in
            document.body.classList.add('loaded');

            // Thêm hiệu ứng cho các input khi focus và có giá trị
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                // Khi focus vào input
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('input-focused');
                    this.parentElement.classList.add('active');
                });

                // Khi blur khỏi input
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('input-focused');

                    // Nếu input không có giá trị, bỏ class active
                    if (!this.value) {
                        this.parentElement.classList.remove('active');
                    }

                    // Kiểm tra tên đăng nhập và email khi blur
                    if ((this.id === 'username' || this.id === 'email') && this.value.trim() !== '') {
                        checkCredentialExists(this.id, this.value);
                    }
                });

                // Khi input thay đổi giá trị
                input.addEventListener('input', function() {
                    if (this.value) {
                        this.parentElement.classList.add('active');
                    } else {
                        this.parentElement.classList.remove('active');

                        // Ẩn thông báo validation nếu input trống
                        const validationMessage = this.parentElement.parentElement.querySelector('.validation-message');
                        if (validationMessage) {
                            validationMessage.classList.remove('show');
                        }
                    }
                });

                // Kiểm tra nếu input đã có giá trị khi trang tải
                if (input.value) {
                    input.parentElement.classList.add('active');
                }

                // Tạo hiệu ứng ripple khi click vào input
                input.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('input-ripple');
                    this.parentElement.appendChild(ripple);

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size/2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size/2) + 'px';

                    ripple.classList.add('active');

                    setTimeout(() => {
                        ripple.remove();
                    }, 500);
                });
            });

            // Hiệu ứng cho checkbox
            const checkbox = document.getElementById('remember');
            if(checkbox) {
                checkbox.addEventListener('change', function() {
                    if(this.checked) {
                        this.classList.add('checked');
                    } else {
                        this.classList.remove('checked');
                    }
                });
            }

            // Thêm các phần tử validation message cho username và email
            if (document.getElementById('username')) {
                addValidationMessage('username');
            }
            if (document.getElementById('email')) {
                addValidationMessage('email');
            }

            // Hiệu ứng confetti cho trang đăng ký
            const authTitle = document.querySelector('.auth-title');
            const isRegisterPage = <?php echo $is_register ? 'true' : 'false'; ?>;

            if (authTitle && isRegisterPage) {
                // Thêm hiệu ứng confetti khi hover vào tiêu đề trang đăng ký
                authTitle.addEventListener('mouseenter', function() {
                    createConfetti(this);
                });

                // Tạo hiệu ứng confetti nhỏ khi trang tải xong
                setTimeout(() => {
                    createConfetti(authTitle, 0.7);
                }, 1000);
            }

            // Hàm tạo hiệu ứng confetti
            function createConfetti(element, scale = 1) {
                if (!element) return;

                // Tạo container cho confetti
                const confettiContainer = document.createElement('div');
                confettiContainer.classList.add('confetti-container');
                confettiContainer.style.position = 'absolute';
                confettiContainer.style.top = '-50px';
                confettiContainer.style.left = '50%';
                confettiContainer.style.transform = 'translateX(-50%)';
                confettiContainer.style.width = '250px';
                confettiContainer.style.height = '100px';
                confettiContainer.style.pointerEvents = 'none';
                confettiContainer.style.zIndex = '5';
                confettiContainer.style.overflow = 'visible';

                element.appendChild(confettiContainer);

                // Tạo các confetti
                const colors = [
                    'var(--primary)',
                    'var(--primary-light)',
                    'var(--accent)',
                    'var(--primary-lighter)',
                    'var(--secondary-light)'
                ];

                const confettiCount = Math.floor(15 * scale);

                for (let i = 0; i < confettiCount; i++) {
                    const confetti = document.createElement('div');
                    const color = colors[Math.floor(Math.random() * colors.length)];

                    confetti.style.position = 'absolute';
                    confetti.style.width = (Math.random() * 6 + 3) * scale + 'px';
                    confetti.style.height = (Math.random() * 6 + 3) * scale + 'px';
                    confetti.style.backgroundColor = color;
                    confetti.style.borderRadius = Math.random() > 0.5 ? '50%' : '2px';
                    confetti.style.opacity = Math.random() * 0.8 + 0.2;
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.top = '0';

                    // Thiết lập animation
                    confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
                    confetti.style.animation = `confetti-fall-${i} ${Math.random() * 2 + 1}s ease-out forwards`;

                    // Thêm keyframes cho animation
                    const style = document.createElement('style');
                    style.innerHTML = `
                        @keyframes confetti-fall-${i} {
                            0% {
                                transform: translateY(0) rotate(${Math.random() * 360}deg);
                                opacity: ${Math.random() * 0.8 + 0.2};
                            }
                            100% {
                                transform: translateY(${Math.random() * 100 + 50}px) translateX(${Math.random() * 100 - 50}px) rotate(${Math.random() * 360}deg);
                                opacity: 0;
                            }
                        }
                    `;
                    document.head.appendChild(style);

                    confettiContainer.appendChild(confetti);
                }

                // Xóa confetti sau animation
                setTimeout(() => {
                    confettiContainer.remove();
                }, 3000);
            }

            // Xử lý đăng nhập mạng xã hội
            const socialLoginButtons = document.querySelectorAll('.social-button');
            const socialLoginModal = document.getElementById('socialLoginModal');
            const closeSocialLoginModal = document.getElementById('closeSocialLoginModal');
            const closeSocialLoginBtn = document.getElementById('closeSocialLoginBtn');
            const socialModalIcon = document.getElementById('socialModalIcon');
            const socialModalTitle = document.getElementById('socialModalTitle');
            const socialLoginMessage = document.getElementById('socialLoginMessage');

            // Thêm sự kiện click cho các nút đăng nhập mạng xã hội
            socialLoginButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Xác định loại mạng xã hội
                    let socialType = 'social';
                    let socialName = 'Mạng xã hội';
                    let iconClass = 'fa-info-circle';
                    let iconColor = '#3B82F6';

                    if (this.classList.contains('google-button')) {
                        socialType = 'google';
                        socialName = 'Google';
                        iconClass = 'fa-google';
                        iconColor = '#DB4437';
                    } else if (this.classList.contains('facebook-button')) {
                        socialType = 'facebook';
                        socialName = 'Facebook';
                        iconClass = 'fa-facebook-f';
                        iconColor = '#1877F2';
                    }

                    // Cập nhật modal
                    socialModalIcon.innerHTML = `<i class="fab ${iconClass}"></i>`;
                    socialModalIcon.className = `modal-icon ${socialType}`;
                    socialModalTitle.textContent = `Đăng nhập bằng ${socialName}`;
                    socialLoginMessage.innerHTML = `
                        Chức năng đăng nhập bằng ${socialName} đang được cập nhật.<br>
                        Vui lòng đăng ký tài khoản trực tiếp trên hệ thống để sử dụng dịch vụ.
                    `;

                    // Hiển thị modal
                    showSocialLoginModal();

                    // Thêm hiệu ứng nhấp nháy cho nút
                    this.classList.add('button-clicked');
                    setTimeout(() => {
                        this.classList.remove('button-clicked');
                    }, 300);
                });
            });

            // Hàm hiển thị modal đăng nhập mạng xã hội
            function showSocialLoginModal() {
                socialLoginModal.classList.add('active');
                document.body.style.overflow = 'hidden'; // Ngăn cuộn trang

                // Thêm hiệu ứng ripple cho modal
                setTimeout(() => {
                    const modalContent = socialLoginModal.querySelector('.modal-content');
                    modalContent.classList.add('ripple-effect');
                    setTimeout(() => {
                        modalContent.classList.remove('ripple-effect');
                    }, 500);
                }, 100);
            }

            // Đóng modal đăng nhập mạng xã hội
            function closeSocialModal() {
                socialLoginModal.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép cuộn trang trở lại
            }

            // Thêm sự kiện đóng modal
            if (closeSocialLoginModal) {
                closeSocialLoginModal.addEventListener('click', closeSocialModal);
            }

            if (closeSocialLoginBtn) {
                closeSocialLoginBtn.addEventListener('click', closeSocialModal);
            }

            // Đóng modal khi click bên ngoài
            socialLoginModal.addEventListener('click', function(e) {
                if (e.target === socialLoginModal) {
                    closeSocialModal();
                }
            });
        });

        // Hàm hiển thị/ẩn mật khẩu
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            const toggleButton = input.nextElementSibling;

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                toggleButton.setAttribute('title', 'Ẩn mật khẩu');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                toggleButton.setAttribute('title', 'Hiện mật khẩu');
            }

            // Thêm hiệu ứng khi nhấn nút
            toggleButton.classList.add('pressed');
            setTimeout(() => {
                toggleButton.classList.remove('pressed');
            }, 200);
        }

        // Multi-step form functionality
        const registerForm = document.getElementById('registerForm');

        if (registerForm) {
            const formSteps = registerForm.querySelectorAll('.form-step');
            const stepDots = registerForm.querySelectorAll('.step-dot');
            const progressBar = registerForm.querySelector('.step-progress-bar');
            const nextButtons = registerForm.querySelectorAll('.next-step-button');
            const prevButtons = registerForm.querySelectorAll('.prev-step-button');
            const submitButton = registerForm.querySelector('.submit-button');

            let currentStep = 1;
            const totalSteps = formSteps.length;

            // Update progress bar
            function updateProgressBar() {
                const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
                progressBar.style.width = `${progress}%`;
            }

            // Update step dots
            function updateStepDots() {
                stepDots.forEach((dot, index) => {
                    const stepNum = index + 1;
                    dot.classList.remove('active', 'completed');

                    if (stepNum === currentStep) {
                        dot.classList.add('active');
                    } else if (stepNum < currentStep) {
                        dot.classList.add('completed');
                    }
                });

                updateProgressBar();
            }

            // Go to specific step
            function goToStep(stepNumber) {
                formSteps.forEach((step, index) => {
                    step.classList.remove('active');
                    if (index + 1 === stepNumber) {
                        step.classList.add('active');
                    }
                });

                currentStep = stepNumber;
                updateStepDots();
            }

            // Event listeners for next buttons
            nextButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const nextStep = parseInt(button.getAttribute('data-next'));

                    // Validate current step before proceeding
                    if (validateStep(currentStep)) {
                        goToStep(nextStep);

                        // Add subtle confetti effect when moving to the next step
                        if (button.classList.contains('next-step-button')) {
                            const confettiContainer = document.createElement('div');
                            confettiContainer.style.position = 'absolute';
                            confettiContainer.style.top = '50%';
                            confettiContainer.style.right = '10%';
                            confettiContainer.style.zIndex = '10';

                            button.appendChild(confettiContainer);
                            createConfetti(confettiContainer, 0.4);

                            setTimeout(() => {
                                confettiContainer.remove();
                            }, 1000);
                        }
                    }
                });
            });

            // Event listeners for previous buttons
            prevButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const prevStep = parseInt(button.getAttribute('data-prev'));
                    goToStep(prevStep);
                });
            });

            // Event listeners for step dots (direct navigation)
            stepDots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    const clickedStep = index + 1;

                    // Only allow going to completed steps or the next step
                    if (clickedStep < currentStep || clickedStep === currentStep + 1) {
                        if (clickedStep < currentStep || validateStep(currentStep)) {
                            goToStep(clickedStep);
                        }
                    }
                });
            });

            // Xử lý xem trước ảnh đại diện
            const avatarInput = document.getElementById('avatar');
            const avatarPreview = document.getElementById('avatar-preview');
            const avatarImage = avatarPreview.querySelector('.avatar-image');
            const avatarPlaceholder = avatarPreview.querySelector('.avatar-placeholder');
            const avatarFilename = document.getElementById('avatar-filename');

            if (avatarInput) {
                avatarInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const file = this.files[0];

                        // Kiểm tra kích thước file (tối đa 5MB)
                        if (file.size > 5 * 1024 * 1024) {
                            showInputError(this, 'Kích thước file quá lớn (tối đa 5MB)');
                            this.value = '';
                            return;
                        }

                        // Kiểm tra loại file
                        const fileType = file.type;
                        if (!fileType.match('image/jpeg') && !fileType.match('image/png') &&
                            !fileType.match('image/gif') && !fileType.match('image/webp')) {
                            showInputError(this, 'Chỉ chấp nhận file hình ảnh (JPG, PNG, GIF, WEBP)');
                            this.value = '';
                            return;
                        }

                        // Hiển thị tên file (giới hạn độ dài nếu cần)
                        const maxLength = 25; // Giới hạn độ dài tên file hiển thị
                        if (file.name.length > maxLength) {
                            const extension = file.name.split('.').pop();
                            const fileName = file.name.substring(0, file.name.length - extension.length - 1);
                            const truncatedName = fileName.substring(0, maxLength - extension.length - 3) + '...' + '.' + extension;
                            avatarFilename.textContent = truncatedName;
                            // Thêm title để hiển thị đầy đủ tên file khi hover
                            avatarFilename.title = file.name;
                        } else {
                            avatarFilename.textContent = file.name;
                            avatarFilename.title = file.name;
                        }

                        // Hiển thị ảnh xem trước
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            avatarImage.src = e.target.result;
                            avatarImage.classList.remove('hidden');
                            avatarPlaceholder.classList.add('hidden');

                            // Thêm hiệu ứng khi tải ảnh thành công
                            avatarPreview.classList.add('upload-success');
                            setTimeout(() => {
                                avatarPreview.classList.remove('upload-success');
                            }, 1000);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Kiểm tra tên đăng nhập hợp lệ (không dấu, không khoảng trắng)
            function isValidUsername(username) {
                // Kiểm tra không có dấu cách
                if (username.includes(' ')) {
                    return false;
                }

                // Kiểm tra không có dấu tiếng Việt
                const vietnamesePattern = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
                if (vietnamesePattern.test(username)) {
                    return false;
                }

                // Chỉ cho phép chữ cái, số và dấu gạch dưới
                const validPattern = /^[a-zA-Z0-9_]+$/;
                return validPattern.test(username);
            }

            // Validate each step
            function validateStep(step) {
                switch(step) {
                    case 1:
                        // Validate personal information
                        const fullName = document.getElementById('full_name');
                        if (!fullName.value.trim()) {
                            showInputError(fullName, 'Vui lòng nhập họ và tên');
                            return false;
                        }
                        return true;

                    case 2:
                        // Validate account information
                        const username = document.getElementById('username');
                        const email = document.getElementById('email');
                        let isValid = true;

                        if (!username.value.trim()) {
                            showInputError(username, 'Vui lòng nhập tên đăng nhập');
                            isValid = false;
                        } else if (!isValidUsername(username.value)) {
                            showInputError(username, 'Tên đăng nhập chỉ được phép chứa chữ cái không dấu, số và dấu gạch dưới');
                            isValid = false;
                        }

                        if (!email.value.trim()) {
                            showInputError(email, 'Vui lòng nhập địa chỉ email');
                            isValid = false;
                        } else if (!isValidEmail(email.value)) {
                            showInputError(email, 'Email không hợp lệ');
                            isValid = false;
                        }

                        return isValid;

                    case 3:
                        // Already at the last step, no validation needed for navigation
                        return true;

                    default:
                        return true;
                }
            }

            // Show input error
            function showInputError(inputElement, message) {
                inputElement.classList.add('input-error');

                // Create or update error message
                let errorElement = inputElement.parentElement.parentElement.querySelector('.input-error-message');

                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'input-error-message';
                    inputElement.parentElement.parentElement.appendChild(errorElement);
                }

                errorElement.textContent = message;
                errorElement.style.display = 'block';

                // Clear error after 3 seconds
                setTimeout(() => {
                    inputElement.classList.remove('input-error');
                    errorElement.style.display = 'none';
                }, 3000);

                // Clear error on input
                inputElement.addEventListener('input', function clearError() {
                    inputElement.classList.remove('input-error');
                    if (errorElement) errorElement.style.display = 'none';
                    inputElement.removeEventListener('input', clearError);
                });
            }

            // Validate email format
            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            // Password strength meter
            const passwordInput = document.getElementById('password');
            const strengthBar = document.querySelector('.strength-bar');
            const strengthText = document.querySelector('.strength-text');

            if (passwordInput && strengthBar && strengthText) {
                const passwordGuidelines = document.querySelector('.password-guidelines');

                // Hiện hướng dẫn khi focus vào trường mật khẩu
                passwordInput.addEventListener('focus', () => {
                    if (passwordGuidelines) {
                        passwordGuidelines.classList.add('show');
                    }
                });

                // Ẩn hướng dẫn khi blur khỏi trường mật khẩu nếu không có giá trị
                passwordInput.addEventListener('blur', () => {
                    if (passwordGuidelines && !passwordInput.value) {
                        passwordGuidelines.classList.remove('show');
                    }
                });

                passwordInput.addEventListener('input', () => {
                    const strength = checkPasswordStrength(passwordInput.value);
                    updateStrengthIndicator(strength);
                    updatePasswordGuidelines(passwordInput.value);
                });

                function checkPasswordStrength(password) {
                    if (!password) return 0;

                    let score = 0;

                    // Length check
                    if (password.length >= 8) score += 1;
                    if (password.length >= 12) score += 1;

                    // Complexity checks
                    if (/[A-Z]/.test(password)) score += 1;
                    if (/[a-z]/.test(password)) score += 1;
                    if (/[0-9]/.test(password)) score += 1;
                    if (/[^A-Za-z0-9]/.test(password)) score += 1;

                    return Math.min(Math.floor(score * 20), 100); // Convert to percentage (max 100)
                }

                function updateStrengthIndicator(strength) {
                    // Update the bar width
                    strengthBar.style.width = `${strength}%`;

                    // Update color based on strength
                    if (strength < 40) {
                        strengthBar.style.backgroundColor = '#F44336'; // Red (weak)
                        strengthText.textContent = 'Mật khẩu yếu';
                    } else if (strength < 70) {
                        strengthBar.style.backgroundColor = '#FFA000'; // Amber (medium)
                        strengthText.textContent = 'Mật khẩu trung bình';
                    } else {
                        strengthBar.style.backgroundColor = '#4CAF50'; // Green (strong)
                        strengthText.textContent = 'Mật khẩu mạnh';
                    }
                }

                // Hàm kiểm tra và cập nhật hướng dẫn mật khẩu
                function updatePasswordGuidelines(password) {
                    // Lấy các phần tử kiểm tra
                    const lengthCheck = document.getElementById('length-check');
                    const uppercaseCheck = document.getElementById('uppercase-check');
                    const lowercaseCheck = document.getElementById('lowercase-check');
                    const numberCheck = document.getElementById('number-check');
                    const specialCheck = document.getElementById('special-check');

                    // Kiểm tra từng yêu cầu
                    if (password.length >= 8) {
                        lengthCheck.classList.add('valid');
                    } else {
                        lengthCheck.classList.remove('valid');
                    }

                    if (/[A-Z]/.test(password)) {
                        uppercaseCheck.classList.add('valid');
                    } else {
                        uppercaseCheck.classList.remove('valid');
                    }

                    if (/[a-z]/.test(password)) {
                        lowercaseCheck.classList.add('valid');
                    } else {
                        lowercaseCheck.classList.remove('valid');
                    }

                    if (/[0-9]/.test(password)) {
                        numberCheck.classList.add('valid');
                    } else {
                        numberCheck.classList.remove('valid');
                    }

                    if (/[^A-Za-z0-9]/.test(password)) {
                        specialCheck.classList.add('valid');
                    } else {
                        specialCheck.classList.remove('valid');
                    }
                }
            }

            // Password match check
            const confirmPasswordInput = document.getElementById('confirm_password');
            const matchIndicator = document.querySelector('.password-match-indicator');

            if (passwordInput && confirmPasswordInput && matchIndicator) {
                function checkPasswordsMatch() {
                    if (confirmPasswordInput.value) {
                        matchIndicator.classList.remove('hidden');
                        matchIndicator.classList.add('visible');

                        if (passwordInput.value === confirmPasswordInput.value) {
                            matchIndicator.innerHTML = '<i class="fas fa-check-circle"></i> Mật khẩu khớp';
                            matchIndicator.classList.remove('not-matching');
                            return true;
                        } else {
                            matchIndicator.innerHTML = '<i class="fas fa-times-circle"></i> Mật khẩu không khớp';
                            matchIndicator.classList.add('not-matching');
                            return false;
                        }
                    } else {
                        matchIndicator.classList.remove('visible');
                        matchIndicator.classList.add('hidden');
                        return false;
                    }
                }

                confirmPasswordInput.addEventListener('input', checkPasswordsMatch);
                passwordInput.addEventListener('input', () => {
                    if (confirmPasswordInput.value) {
                        checkPasswordsMatch();
                    }
                });
            }

            // Form submission
            registerForm.addEventListener('submit', function(e) {
                // Validate all fields before submitting
                if (!validateStep(1) || !validateStep(2)) {
                    e.preventDefault();
                    return false;
                }

                // Validate passwords
                if (passwordInput.value !== confirmPasswordInput.value) {
                    showInputError(confirmPasswordInput, 'Mật khẩu không khớp');
                    e.preventDefault();
                    return false;
                }

                // If all validations pass, form submits normally
                return true;
            });

            // Initialize
            updateProgressBar();
        }

        // Thêm phần tử validation message
        function addValidationMessage(inputId) {
            const inputGroup = document.getElementById(inputId).parentElement.parentElement;
            const validationMessage = document.createElement('div');
            validationMessage.className = 'validation-message';
            validationMessage.id = inputId + '-validation';
            inputGroup.appendChild(validationMessage);
        }

        // Kiểm tra tên đăng nhập hoặc email đã tồn tại chưa
        function checkCredentialExists(field, value) {
            // Lấy phần tử input group và validation message
            const inputGroup = document.getElementById(field).parentElement;
            const validationMessage = document.getElementById(field + '-validation');

            // Nếu giá trị trống, không cần kiểm tra
            if (!value.trim()) {
                validationMessage.classList.remove('show');
                return;
            }

            // Kiểm tra tên đăng nhập hợp lệ
            if (field === 'username') {
                if (!isValidUsername(value)) {
                    // Xóa class checking
                    inputGroup.classList.remove('checking');

                    // Hiển thị thông báo lỗi
                    validationMessage.textContent = 'Tên đăng nhập chỉ được phép chứa chữ cái không dấu, số và dấu gạch dưới';
                    validationMessage.classList.add('show');
                    validationMessage.classList.add('error');
                    validationMessage.classList.remove('success');
                    return;
                }
            }

            // Thêm class checking để hiển thị spinner
            inputGroup.classList.add('checking');

            // Tạo timeout để tránh gửi quá nhiều request
            if (window.checkTimeout) {
                clearTimeout(window.checkTimeout);
            }

            window.checkTimeout = setTimeout(() => {
                // Gửi request kiểm tra
                fetch('<?php echo BASE_URL; ?>/api/check-credentials.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ field, value })
                })
                .then(response => response.json())
                .then(data => {
                    // Xóa class checking
                    inputGroup.classList.remove('checking');

                    // Hiển thị thông báo
                    validationMessage.textContent = data.message;
                    validationMessage.classList.add('show');

                    if (data.exists) {
                        validationMessage.classList.add('error');
                        validationMessage.classList.remove('success');
                    } else {
                        validationMessage.classList.add('success');
                        validationMessage.classList.remove('error');
                    }
                })
                .catch(error => {
                    console.error('Error checking credentials:', error);
                    inputGroup.classList.remove('checking');
                });
            }, 500); // Đợi 500ms trước khi gửi request
        }

        // Auto-fill login form after registration and handle "Login Now" button
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there are registration credentials to auto-fill
            <?php if (!$is_register && isset($_SESSION['registered_username']) && isset($_SESSION['registered_password'])): ?>
            const loginForm = document.querySelector('form[action*="auth.php"]');
            if (loginForm) {
                const usernameInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');

                if (usernameInput && passwordInput) {
                    // Auto-fill only the username
                    usernameInput.value = '<?php echo $_SESSION['registered_username']; ?>';

                    // Add active class to show styling for username field
                    usernameInput.parentElement.classList.add('active');

                    // Highlight the username field briefly to draw attention
                    usernameInput.parentElement.classList.add('highlight-autofill');

                    setTimeout(() => {
                        usernameInput.parentElement.classList.remove('highlight-autofill');
                    }, 2000);

                    // Auto focus on password field for better UX
                    setTimeout(() => {
                        passwordInput.focus();
                        // Add a subtle highlight to password field to indicate where to type
                        passwordInput.parentElement.classList.add('focus-hint');

                        // Add a temporary placeholder hint
                        const originalPlaceholder = passwordInput.placeholder;
                        passwordInput.placeholder = 'Nhập mật khẩu để đăng nhập →';

                        setTimeout(() => {
                            passwordInput.parentElement.classList.remove('focus-hint');
                            passwordInput.placeholder = originalPlaceholder;
                        }, 3000);
                    }, 800);

                    // Clear the stored credentials from session after using them
                    <?php
                        unset($_SESSION['registered_username']);
                        unset($_SESSION['registered_password']);
                    ?>
                }
            }
            <?php endif; ?>

            // Handle "Login Now" button click
            const loginNowBtn = document.getElementById('login-now-btn');
            if (loginNowBtn) {
                loginNowBtn.addEventListener('click', function() {
                    // Submit the login form
                    document.querySelector('form[action*="auth.php"]').submit();
                });
            }

            // Quên mật khẩu modal functionality
            const openForgotPasswordModal = document.getElementById('openForgotPasswordModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            const closeForgotPasswordModal = document.getElementById('closeForgotPasswordModal');
            const cancelForgotPassword = document.getElementById('cancelForgotPassword');

            // Mở modal quên mật khẩu
            if (openForgotPasswordModal && forgotPasswordModal) {
                openForgotPasswordModal.addEventListener('click', function() {
                    forgotPasswordModal.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn cuộn trang
                });
            }

            // Đóng modal quên mật khẩu
            const closeModal = function() {
                forgotPasswordModal.classList.remove('active');
                document.body.style.overflow = '';

                // Reset về bước 1 sau một thời gian
                setTimeout(() => {
                    goToRecoveryStep(1);
                }, 300);
            };

            if (closeForgotPasswordModal) {
                closeForgotPasswordModal.addEventListener('click', closeModal);
            }

            if (cancelForgotPassword) {
                cancelForgotPassword.addEventListener('click', closeModal);
            }

            // Đóng modal khi click bên ngoài
            forgotPasswordModal.addEventListener('click', function(e) {
                if (e.target === forgotPasswordModal) {
                    closeModal();
                }
            });

            // Xử lý các bước khôi phục mật khẩu
            const recoverySteps = document.querySelector('.recovery-steps');
            const sendRecoveryEmail = document.getElementById('sendRecoveryEmail');
            const resendRecoveryEmail = document.getElementById('resendRecoveryEmail');
            const backToEmailInput = document.getElementById('backToEmailInput');
            const doneRecovery = document.getElementById('doneRecovery');
            const recoveryEmail = document.getElementById('recovery_email');
            const confirmedEmail = document.getElementById('confirmedEmail');

            function goToRecoveryStep(step) {
                // Lưu bước hiện tại để tạo hiệu ứng chuyển đổi
                const currentStep = parseInt(recoverySteps.dataset.currentStep) || 1;
                const isForward = step > currentStep;

                // Cập nhật trạng thái các bước
                document.querySelectorAll('.recovery-step').forEach(el => {
                    const stepNum = parseInt(el.dataset.step);

                    // Xóa các class hiện tại
                    el.classList.remove('active');

                    // Thêm class mới dựa trên bước hiện tại
                    if (stepNum === step) {
                        // Thêm hiệu ứng khi active một bước mới
                        el.classList.add('active');

                        // Thêm hiệu ứng highlight tạm thời
                        el.querySelector('.step-number').classList.add('highlight');
                        setTimeout(() => {
                            el.querySelector('.step-number').classList.remove('highlight');
                        }, 1000);
                    } else if (stepNum < step) {
                        // Nếu bước trước đó chưa được đánh dấu hoàn thành, thêm hiệu ứng
                        if (!el.classList.contains('completed')) {
                            el.classList.add('completing');
                            setTimeout(() => {
                                el.classList.remove('completing');
                                el.classList.add('completed');

                                // Thêm icon dấu tích màu cam
                                const stepNumber = el.querySelector('.step-number');
                                if (stepNumber) {
                                    stepNumber.innerHTML = '<i class="fas fa-check"></i>';
                                    stepNumber.style.backgroundColor = '#F37321'; // Màu cam
                                    stepNumber.style.color = 'white';
                                    stepNumber.style.border = '2px solid #F37321';
                                }
                            }, 300);
                        } else {
                            el.classList.add('completed');

                            // Đảm bảo icon dấu tích màu cam được hiển thị
                            const stepNumber = el.querySelector('.step-number');
                            if (stepNumber && !stepNumber.querySelector('.fa-check')) {
                                stepNumber.innerHTML = '<i class="fas fa-check"></i>';
                                stepNumber.style.backgroundColor = '#F37321'; // Màu cam
                                stepNumber.style.color = 'white';
                                stepNumber.style.border = '2px solid #F37321';
                            }
                        }
                    } else {
                        // Xóa class completed nếu quay lại bước trước
                        el.classList.remove('completed');
                    }
                });

                // Cập nhật thanh tiến trình
                recoverySteps.dataset.currentStep = step;

                // Hiển thị step tương ứng với hiệu ứng fade
                document.querySelectorAll('.forgot-password-step').forEach(el => {
                    const stepNum = parseInt(el.dataset.step);

                    if (stepNum === step) {
                        // Thêm class để chuẩn bị hiệu ứng fade in
                        el.classList.add('preparing');

                        // Đợi một chút để browser kịp render
                        setTimeout(() => {
                            document.querySelectorAll('.forgot-password-step').forEach(otherEl => {
                                if (otherEl !== el) otherEl.classList.remove('active');
                            });

                            el.classList.add('active');
                            el.classList.remove('preparing');
                        }, 50);
                    }
                });
            }

            // Gửi email khôi phục
            if (sendRecoveryEmail) {
                sendRecoveryEmail.addEventListener('click', function() {
                    const email = recoveryEmail.value.trim();

                    if (!email) {
                        showInputError(recoveryEmail, 'Vui lòng nhập địa chỉ email');
                        return;
                    }

                    if (!isValidEmail(email)) {
                        showInputError(recoveryEmail, 'Email không hợp lệ');
                        return;
                    }

                    // Hiển thị loading
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
                    this.disabled = true;

                    // Hiển thị thông báo đang xử lý
                    const processingNotification = document.createElement('div');
                    processingNotification.className = 'processing-notification';
                    processingNotification.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý yêu cầu...';
                    processingNotification.style.color = '#3498db';
                    processingNotification.style.fontSize = '14px';
                    processingNotification.style.marginTop = '10px';
                    processingNotification.style.fontWeight = '500';
                    processingNotification.style.padding = '8px 12px';
                    processingNotification.style.backgroundColor = '#e3f2fd';
                    processingNotification.style.borderRadius = '4px';
                    processingNotification.style.textAlign = 'center';

                    // Thêm vào form
                    document.querySelector('.forgot-password-form').appendChild(processingNotification);

                    // Gọi API gửi mã OTP
                    fetch('<?php echo BASE_URL; ?>/api/forgot-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            action: 'send_otp',
                            email: email
                        })
                    })
                    .then(response => {
                        // Xóa thông báo đang xử lý
                        processingNotification.remove();

                        // Reset button
                        this.innerHTML = 'Tiếp tục <i class="fas fa-arrow-right"></i>';
                        this.disabled = false;

                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Cập nhật email đã xác nhận
                            confirmedEmail.textContent = email;

                            // Chuyển sang bước 2
                            goToRecoveryStep(2);

                            // Khởi động timer đếm ngược
                            startResendTimer();

                            // Tự động cuộn xuống để hiển thị phần nhập OTP và các nút
                            setTimeout(() => {
                                const modalContent = document.querySelector('.modal-content');
                                if (modalContent) {
                                    modalContent.scrollTo({
                                        top: modalContent.scrollHeight,
                                        behavior: 'smooth'
                                    });
                                }

                                // Focus vào ô nhập OTP
                                const otpInput = document.getElementById('otp_code');
                                if (otpInput) {
                                    otpInput.focus();
                                }
                            }, 500);

                            // Hiển thị thông báo thành công
                            showSuccessNotification(data.message);
                        } else {
                            // Hiển thị thông báo lỗi
                            showInputError(recoveryEmail, data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.innerHTML = 'Tiếp tục <i class="fas fa-arrow-right"></i>';
                        this.disabled = false;

                        // Xóa thông báo đang xử lý nếu còn tồn tại
                        const processingNotif = document.querySelector('.processing-notification');
                        if (processingNotif) {
                            processingNotif.remove();
                        }

                        // Kiểm tra nếu lỗi là do response không phải JSON
                        let errorDetails = 'Không xác định';
                        if (error.message && error.message.includes('JSON')) {
                            errorDetails = 'Lỗi máy chủ: Phản hồi không hợp lệ. Vui lòng liên hệ quản trị viên.';

                            // Ghi log lỗi chi tiết hơn
                            console.error('Lỗi phân tích JSON. Có thể máy chủ trả về HTML thay vì JSON.');
                        } else {
                            errorDetails = error.message || 'Không xác định';
                        }

                        // Hiển thị thông báo lỗi chi tiết
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'error-notification';
                        errorMessage.innerHTML = `
                            <div style="font-weight: bold; margin-bottom: 5px;"><i class="fas fa-exclamation-triangle"></i> Đã xảy ra lỗi</div>
                            <div>Không thể gửi mã xác nhận. Vui lòng thử lại sau hoặc liên hệ quản trị viên.</div>
                            <div style="font-size: 12px; margin-top: 5px; color: #666;">Chi tiết lỗi: ${errorDetails}</div>
                        `;
                        errorMessage.style.color = '#721c24';
                        errorMessage.style.fontSize = '14px';
                        errorMessage.style.marginTop = '10px';
                        errorMessage.style.padding = '10px 15px';
                        errorMessage.style.backgroundColor = '#f8d7da';
                        errorMessage.style.borderRadius = '4px';
                        errorMessage.style.border = '1px solid #f5c6cb';

                        // Thêm vào form
                        document.querySelector('.forgot-password-form').appendChild(errorMessage);

                        // Xóa thông báo sau 8s
                        setTimeout(() => {
                            errorMessage.remove();
                        }, 8000);

                        // Hiển thị gợi ý kiểm tra email
                        const emailHint = document.createElement('div');
                        emailHint.className = 'email-hint';
                        emailHint.innerHTML = `
                            <div style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeeba; border-radius: 4px; color: #856404;">
                                <div style="font-weight: bold;"><i class="fas fa-lightbulb"></i> Gợi ý:</div>
                                <ul style="margin-top: 5px; padding-left: 20px;">
                                    <li>Kiểm tra lại địa chỉ email đã nhập chính xác chưa</li>
                                    <li>Đảm bảo email đã được đăng ký trong hệ thống</li>
                                    <li>Thử làm mới trang và thử lại</li>
                                </ul>
                            </div>
                        `;

                        // Thêm vào form
                        document.querySelector('.forgot-password-form').appendChild(emailHint);

                        // Xóa gợi ý sau 12s
                        setTimeout(() => {
                            emailHint.remove();
                        }, 12000);

                        showInputError(recoveryEmail, 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
                    });
                });
            }

            // Xác thực mã OTP
            const verifyOtpButton = document.getElementById('verifyOtpButton');
            const otpInput = document.getElementById('otp_code');
            const otpInputContainer = document.getElementById('otpInputContainer');
            const backToOtpInput = document.getElementById('backToOtpInput');

            // Xử lý sự kiện khi người dùng bấm vào vùng nhập OTP
            if (otpInputContainer) {
                otpInputContainer.addEventListener('click', function() {
                    // Cuộn xuống cuối khung hiển thị để đảm bảo các nút luôn hiển thị
                    setTimeout(() => {
                        const modalContent = document.querySelector('.modal-content');
                        if (modalContent) {
                            modalContent.scrollTo({
                                top: modalContent.scrollHeight,
                                behavior: 'smooth'
                            });
                        }

                        // Focus vào ô nhập OTP
                        otpInput.focus();
                    }, 100);
                });
            }

            if (verifyOtpButton && otpInput) {
                verifyOtpButton.addEventListener('click', function() {
                    const otp = otpInput.value.trim();
                    const email = confirmedEmail.textContent.trim();

                    if (!otp) {
                        showInputError(otpInput, 'Vui lòng nhập mã xác nhận');
                        return;
                    }

                    if (otp.length !== 6 || !/^\d+$/.test(otp)) {
                        showInputError(otpInput, 'Mã xác nhận phải gồm 6 chữ số');
                        return;
                    }

                    // Hiển thị loading
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xác thực...';
                    this.disabled = true;

                    // Gọi API xác thực mã OTP
                    fetch('<?php echo BASE_URL; ?>/api/forgot-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            action: 'verify_otp',
                            email: email,
                            otp_code: otp
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button
                        this.innerHTML = 'Xác nhận <i class="fas fa-arrow-right"></i>';
                        this.disabled = false;

                        if (data.success) {
                            // Chuyển sang bước 3
                            goToRecoveryStep(3);

                            // Hiển thị thông báo thành công
                            showSuccessNotification(data.message);
                        } else {
                            // Hiển thị thông báo lỗi
                            showInputError(otpInput, data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.innerHTML = 'Xác nhận <i class="fas fa-arrow-right"></i>';
                        this.disabled = false;
                        showInputError(otpInput, 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
                    });
                });
            }

            // Đặt lại mật khẩu
            const resetPasswordButton = document.getElementById('resetPasswordButton');
            const newPasswordInput = document.getElementById('new_password');
            const confirmNewPasswordInput = document.getElementById('confirm_new_password');

            if (resetPasswordButton && newPasswordInput && confirmNewPasswordInput) {
                resetPasswordButton.addEventListener('click', function() {
                    const newPassword = newPasswordInput.value.trim();
                    const confirmNewPassword = confirmNewPasswordInput.value.trim();

                    if (!newPassword) {
                        showInputError(newPasswordInput, 'Vui lòng nhập mật khẩu mới');
                        return;
                    }

                    if (newPassword.length < 6) {
                        showInputError(newPasswordInput, 'Mật khẩu phải có ít nhất 6 ký tự');
                        return;
                    }

                    if (!confirmNewPassword) {
                        showInputError(confirmNewPasswordInput, 'Vui lòng xác nhận mật khẩu');
                        return;
                    }

                    if (newPassword !== confirmNewPassword) {
                        showInputError(confirmNewPasswordInput, 'Mật khẩu xác nhận không khớp');
                        return;
                    }

                    // Hiển thị loading
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
                    this.disabled = true;

                    // Gọi API đặt lại mật khẩu
                    fetch('<?php echo BASE_URL; ?>/api/forgot-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            action: 'reset_password',
                            password: newPassword,
                            confirm_password: confirmNewPassword
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Reset button
                        this.innerHTML = 'Hoàn tất <i class="fas fa-check"></i>';
                        this.disabled = false;

                        if (data.success) {
                            // Chuyển sang bước 4 (thành công)
                            goToRecoveryStep(4);
                        } else {
                            // Hiển thị thông báo lỗi
                            showInputError(newPasswordInput, data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.innerHTML = 'Hoàn tất <i class="fas fa-check"></i>';
                        this.disabled = false;
                        showInputError(newPasswordInput, 'Đã xảy ra lỗi. Vui lòng thử lại sau.');
                    });
                });
            }

            // Kiểm tra email hợp lệ
            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            // Hiển thị lỗi input
            function showInputError(inputElement, message) {
                inputElement.classList.add('input-error');

                let errorElement = inputElement.parentElement.parentElement.querySelector('.validation-message');

                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'validation-message';
                    inputElement.parentElement.parentElement.appendChild(errorElement);
                }

                errorElement.textContent = message;
                errorElement.classList.add('error', 'show');

                // Xóa thông báo lỗi sau 3s
                setTimeout(() => {
                    inputElement.classList.remove('input-error');
                    errorElement.classList.remove('show');
                }, 3000);
            }

            // Hiển thị thông báo thành công
            function showSuccessNotification(message) {
                const notification = document.createElement('div');
                notification.className = 'success-notification';
                notification.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                notification.style.color = '#4CAF50';
                notification.style.fontSize = '14px';
                notification.style.marginTop = '10px';
                notification.style.fontWeight = '500';
                notification.style.padding = '8px 12px';
                notification.style.backgroundColor = '#E8F5E9';
                notification.style.borderRadius = '4px';
                notification.style.textAlign = 'center';

                // Thêm vào form
                document.querySelector('.forgot-password-form').appendChild(notification);

                // Xóa thông báo sau 3s
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            // Quay lại bước nhập email
            if (backToEmailInput) {
                backToEmailInput.addEventListener('click', function() {
                    goToRecoveryStep(1);
                });
            }

            // Quay lại bước nhập OTP
            if (backToOtpInput) {
                backToOtpInput.addEventListener('click', function() {
                    goToRecoveryStep(2);
                });
            }

            // Hoàn tất và đóng modal
            if (doneRecovery) {
                doneRecovery.addEventListener('click', function() {
                    closeModal();
                    // Chuyển hướng đến trang đăng nhập
                    window.location.href = '<?php echo BASE_URL; ?>/auth.php';
                });
            }

            // Đếm ngược để gửi lại email
            let resendTimer;
            let countdown = 60;

            function startResendTimer() {
                const timerElement = document.getElementById('resendTimer');
                resendRecoveryEmail.style.opacity = '0.5';
                resendRecoveryEmail.style.pointerEvents = 'none';

                countdown = 60;

                clearInterval(resendTimer);
                resendTimer = setInterval(() => {
                    countdown--;
                    timerElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(resendTimer);
                        resendRecoveryEmail.style.opacity = '1';
                        resendRecoveryEmail.style.pointerEvents = 'auto';
                    }
                }, 1000);
            }

            // Gửi lại mã OTP
            if (resendRecoveryEmail) {
                resendRecoveryEmail.addEventListener('click', function() {
                    const email = confirmedEmail.textContent.trim();

                    // Hiển thị loading
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang gửi lại...';

                    // Gọi API gửi lại mã OTP
                    fetch('<?php echo BASE_URL; ?>/api/forgot-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            action: 'send_otp',
                            email: email
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.innerHTML = '<i class="fas fa-redo"></i> Gửi lại mã';

                        if (data.success) {
                            startResendTimer();

                            // Hiển thị thông báo
                            const notification = document.createElement('div');
                            notification.className = 'resend-notification';
                            notification.innerHTML = '<i class="fas fa-check-circle"></i> Đã gửi lại mã xác nhận!';
                            notification.style.color = '#4CAF50';
                            notification.style.fontSize = '12px';
                            notification.style.marginTop = '5px';
                            notification.style.fontWeight = '500';

                            this.parentElement.appendChild(notification);

                            setTimeout(() => {
                                notification.remove();
                            }, 3000);
                        } else {
                            // Hiển thị thông báo lỗi
                            const notification = document.createElement('div');
                            notification.className = 'resend-notification';
                            notification.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${data.message}`;
                            notification.style.color = '#F44336';
                            notification.style.fontSize = '12px';
                            notification.style.marginTop = '5px';
                            notification.style.fontWeight = '500';

                            this.parentElement.appendChild(notification);

                            setTimeout(() => {
                                notification.remove();
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.innerHTML = '<i class="fas fa-redo"></i> Gửi lại mã';

                        // Hiển thị thông báo lỗi
                        const notification = document.createElement('div');
                        notification.className = 'resend-notification';
                        notification.innerHTML = '<i class="fas fa-exclamation-circle"></i> Đã xảy ra lỗi. Vui lòng thử lại sau.';
                        notification.style.color = '#F44336';
                        notification.style.fontSize = '12px';
                        notification.style.marginTop = '5px';
                        notification.style.fontWeight = '500';

                        this.parentElement.appendChild(notification);

                        setTimeout(() => {
                            notification.remove();
                        }, 3000);
                    });
                });
            }

            // Kiểm tra mật khẩu khớp nhau
            if (newPasswordInput && confirmNewPasswordInput) {
                const passwordMatchIndicator = document.querySelector('.password-match-indicator');

                function checkPasswordsMatch() {
                    if (confirmNewPasswordInput.value) {
                        passwordMatchIndicator.classList.remove('hidden');
                        passwordMatchIndicator.classList.add('visible');

                        if (newPasswordInput.value === confirmNewPasswordInput.value) {
                            passwordMatchIndicator.innerHTML = '<i class="fas fa-check-circle"></i> Mật khẩu khớp';
                            passwordMatchIndicator.classList.remove('not-matching');
                            return true;
                        } else {
                            passwordMatchIndicator.innerHTML = '<i class="fas fa-times-circle"></i> Mật khẩu không khớp';
                            passwordMatchIndicator.classList.add('not-matching');
                            return false;
                        }
                    } else {
                        passwordMatchIndicator.classList.remove('visible');
                        passwordMatchIndicator.classList.add('hidden');
                        return false;
                    }
                }

                confirmNewPasswordInput.addEventListener('input', checkPasswordsMatch);
                newPasswordInput.addEventListener('input', () => {
                    if (confirmNewPasswordInput.value) {
                        checkPasswordsMatch();
                    }
                });
            }
        });
    </script>

    <style>
        /* Hiệu ứng fade-in khi trang tải xong */
        body {
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        body.loaded {
            opacity: 1;
            /* Các styling khác được định nghĩa trong auth.css */
        }

        /* Hiệu ứng highlight khi tự động điền thông tin */
        .highlight-autofill {
            animation: autofill-highlight 2s ease-in-out;
        }

        @keyframes autofill-highlight {
            0% {
                background-color: rgba(243, 115, 33, 0.1);
                border-color: var(--primary);
                box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.2);
            }
            50% {
                background-color: rgba(243, 115, 33, 0.05);
                border-color: var(--primary-light);
                box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.1);
            }
            100% {
                background-color: transparent;
                border-color: var(--medium-gray);
                box-shadow: none;
            }
        }

        /* Hiệu ứng gợi ý focus vào ô mật khẩu */
        .focus-hint {
            animation: focus-hint-pulse 1.5s ease-in-out;
        }

        @keyframes focus-hint-pulse {
            0%, 100% {
                border-color: var(--medium-gray);
                box-shadow: none;
            }
            25% {
                border-color: var(--primary-light);
                box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.1);
            }
            50% {
                border-color: var(--primary);
                box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.15);
            }
            75% {
                border-color: var(--primary-light);
                box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.1);
            }
        }



        /* Hiệu ứng cho input khi focus */
        .input-focused .input-icon {
            color: var(--primary);
            transform: translateY(-50%);
        }

        /* Hiệu ứng cho input active */
        .input-group.active .input-icon {
            color: var(--primary);
        }

        .input-group.active .form-input {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.1);
        }

        /* Hiệu ứng ripple cho input */
        .input-group {
            overflow: hidden;
        }

        .input-ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(243, 115, 33, 0.08);
            transform: scale(0);
            opacity: 1;
            pointer-events: none;
            z-index: 0;
        }

        .input-ripple.active {
            animation: ripple 0.5s ease-out;
        }

        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* Hiệu ứng cho nút toggle password */
        .password-toggle.pressed {
            transform: translateY(-50%) scale(0.85);
        }

        /* Hiệu ứng cho form */
        .auth-form-section {
            animation: fadeInLeft 0.8s ease-out;
        }

        .auth-image-section {
            animation: fadeInRight 0.8s ease-out;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Styles for OTP input */
        .otp-input-container {
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(to bottom, rgba(243, 115, 33, 0.03), rgba(255, 255, 255, 0.5));
            border-radius: 12px;
            border: 1px solid rgba(243, 115, 33, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
            position: relative;
            overflow: hidden;
        }

        .otp-input-container .text-hint {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            color: var(--secondary);
            margin-top: var(--spacing-sm);
            background-color: rgba(243, 115, 33, 0.05);
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 2px solid var(--primary-light);
        }

        .otp-input-container .text-hint::before {
            content: "\f017"; /* fa-clock icon */
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 8px;
            color: var(--primary);
        }

        .otp-input-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--primary-light), var(--primary), var(--primary-light));
            opacity: 0.7;
        }

        .otp-input-container::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }

        .otp-input-group {
            display: flex;
            justify-content: center;
            margin: 15px 0;
            position: relative;
        }

        .otp-input-group::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, transparent 80%);
            z-index: -1;
        }

        .otp-input {
            font-size: 22px;
            letter-spacing: 12px;
            text-align: center;
            font-weight: 600;
            padding: 12px 15px 12px 25px;
            background-color: white;
            border: 1px solid rgba(243, 115, 33, 0.2);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            color: var(--secondary);
            transition: all 0.3s ease;
            width: 100%;
            max-width: 220px;
        }

        .otp-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
        }

        .otp-input::placeholder {
            color: #aab7c4;
            letter-spacing: normal;
            font-size: 14px;
        }

        /* Success icon in step 4 */
        .confirmation-icon.success {
            background-color: #4CAF50;
            color: white;
        }

        /* Reset password form */
        .reset-password-form {
            text-align: center;
            padding: 10px 0;
        }

        .reset-password-form h4 {
            font-size: 20px;
            font-weight: 700;
            color: var(--secondary);
            margin-bottom: var(--spacing-sm);
            position: relative;
            display: inline-block;
        }

        .reset-password-form h4::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(to right, var(--primary-light), var(--primary), var(--primary-light));
            border-radius: var(--radius-full);
        }

        .reset-password-form p {
            font-size: 14px;
            color: var(--dark-gray);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
            max-width: 90%;
            margin-left: auto;
            margin-right: auto;
        }

        .reset-success-confirmation {
            text-align: center;
            padding: 20px 0;
        }

        /* Validation message */
        .validation-message {
            margin-top: 5px;
            font-size: 12px;
            display: none;
            transition: all 0.3s ease;
        }

        .validation-message.show {
            display: block;
        }

        .validation-message.error {
            color: #F44336;
        }

        .validation-message.success {
            color: #4CAF50;
        }

        /* Success notification */
        .success-notification {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #E8F5E9;
            color: #4CAF50;
            text-align: center;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Styles for Modal */
        @media (max-width: 576px) {
            .modal-container {
                max-width: 100%;
                margin: 0 10px;
                height: 100%;
                display: flex;
                align-items: center;
            }

            .modal-content {
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
            }

            .modal-header {
                padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
            }

            .modal-body {
                padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md);
            }

            .modal-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
                margin-bottom: var(--spacing-sm);
            }

            .modal-title {
                font-size: 20px;
            }

            .modal-subtitle {
                font-size: 13px;
                max-width: 95%;
            }

            .recovery-steps {
                margin-bottom: var(--spacing-md);
            }

            .step-number {
                width: 26px;
                height: 26px;
                font-size: 12px;
            }

            .step-label {
                font-size: 10px;
            }

            .form-actions {
                flex-direction: column-reverse;
                gap: var(--spacing-sm);
                margin-top: var(--spacing-md);
            }

            .primary-button, .secondary-button {
                width: 100%;
                justify-content: center;
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .form-group {
                margin-bottom: var(--spacing-sm);
            }

            .form-input {
                height: 45px;
                font-size: 13px;
            }

            .input-icon {
                font-size: 15px;
            }

            .confirmation-icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
                margin-bottom: var(--spacing-md);
            }

            .email-sent-confirmation h4,
            .reset-password-form h4,
            .reset-success-confirmation h4 {
                font-size: 18px;
            }

            .email-sent-confirmation p,
            .reset-password-form p,
            .reset-success-confirmation p {
                font-size: 13px;
                margin-bottom: var(--spacing-sm);
            }

            .otp-input-container {
                margin: 15px 0;
                padding: 15px;
            }

            .otp-input {
                font-size: 18px;
                letter-spacing: 8px;
                padding: 10px 10px 10px 15px;
                max-width: 180px;
            }

            .otp-input-container .text-hint {
                font-size: 12px;
                padding: 6px 10px;
            }
        }
    </style>
</body>

<!-- Modal Quên Mật Khẩu -->
<div id="forgotPasswordModal" class="modal-overlay">
    <div class="modal-container" style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
        <div class="modal-content forgot-password-modal" style="padding: 25px 0 25px 0; width: 100%; box-sizing: border-box;">
            <button type="button" class="modal-close" id="closeForgotPasswordModal">
                <i class="fas fa-times"></i>
            </button>

            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-unlock-alt"></i>
                </div>
                <h3 class="modal-title">Khôi phục mật khẩu</h3>
                <p class="modal-subtitle">Nhập email đã đăng ký để nhận liên kết đặt lại mật khẩu</p>
            </div>

            <div class="modal-body">
                <div class="recovery-steps" data-current-step="1">
                    <div class="recovery-step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-label">Xác minh email</div>
                    </div>
                    <div class="recovery-progress"></div>
                    <div class="recovery-step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-label">Nhập mã xác nhận</div>
                    </div>
                    <div class="recovery-progress"></div>
                    <div class="recovery-step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-label">Đặt lại mật khẩu</div>
                    </div>
                </div>

                <form id="forgotPasswordForm" class="forgot-password-form">
                    <div class="forgot-password-step active" data-step="1">
                        <div class="form-group">
                            <label for="recovery_email" class="form-label">Địa chỉ email</label>
                            <div class="input-group">
                                <span class="input-icon"><i class="fas fa-envelope"></i></span>
                                <input type="email" id="recovery_email" name="recovery_email" class="form-input" placeholder="Nhập email đã đăng ký" required>
                            </div>
                            <div class="text-hint">Chúng tôi sẽ gửi mã xác nhận đến email của bạn</div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" id="cancelForgotPassword">
                                <i class="fas fa-arrow-left"></i> Quay lại đăng nhập
                            </button>
                            <button type="button" class="primary-button" id="sendRecoveryEmail">
                                Tiếp tục <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <div class="forgot-password-step" data-step="2">
                        <div class="email-sent-confirmation">
                            <div class="confirmation-icon">
                                <i class="fas fa-envelope-open-text"></i>
                            </div>
                            <h4>Đã gửi mã xác nhận</h4>
                            <p>Chúng tôi vừa gửi một email chứa mã xác nhận đến <strong id="confirmedEmail"><EMAIL></strong></p>

                            <div class="otp-input-container" id="otpInputContainer">
                                <label for="otp_code" class="form-label">Nhập mã xác nhận (6 chữ số)</label>
                                <div class="otp-input-group">
                                    <input type="text" id="otp_code" name="otp_code" class="form-input otp-input" placeholder="Nhập mã xác nhận" maxlength="6" required>
                                </div>
                                <div class="text-hint">Mã xác nhận có hiệu lực trong 15 phút</div>
                            </div>

                            <div class="confirmation-actions">
                                <div class="text-hint">Không nhận được mã?</div>
                                <button type="button" class="link-button resend-button" id="resendRecoveryEmail">
                                    <i class="fas fa-redo"></i> Gửi lại mã
                                </button>
                                <div class="resend-timer">
                                    <span>Gửi lại sau <span id="resendTimer">60</span>s</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" id="backToEmailInput">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </button>
                            <button type="button" class="primary-button" id="verifyOtpButton">
                                Xác nhận <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <div class="forgot-password-step" data-step="3">
                        <div class="reset-password-form">
                            <div class="confirmation-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <h4>Đặt lại mật khẩu</h4>
                            <p>Vui lòng nhập mật khẩu mới cho tài khoản của bạn</p>

                            <div class="form-group">
                                <label for="new_password" class="form-label">Mật khẩu mới</label>
                                <div class="input-group">
                                    <span class="input-icon"><i class="fas fa-lock"></i></span>
                                    <input type="password" id="new_password" name="new_password" class="form-input" placeholder="Nhập mật khẩu mới" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('new_password')" title="Hiện mật khẩu">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="confirm_new_password" class="form-label">Xác nhận mật khẩu</label>
                                <div class="input-group">
                                    <span class="input-icon"><i class="fas fa-lock"></i></span>
                                    <input type="password" id="confirm_new_password" name="confirm_new_password" class="form-input" placeholder="Nhập lại mật khẩu mới" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('confirm_new_password')" title="Hiện mật khẩu">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-match-indicator hidden"></div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="secondary-button" id="backToOtpInput">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </button>
                            <button type="button" class="primary-button" id="resetPasswordButton">
                                Hoàn tất <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>

                    <div class="forgot-password-step" data-step="4">
                        <div class="reset-success-confirmation">
                            <div class="confirmation-icon success" style="width: 80px; height: 80px; background: linear-gradient(135deg, #4CAF50, #81C784); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3); position: relative; animation: pulse-success 2s infinite;">
                                <i class="fas fa-check-circle" style="font-size: 40px; color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"></i>
                                <style>
                                    @keyframes pulse-success {
                                        0%, 100% {
                                            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
                                            transform: scale(1);
                                        }
                                        50% {
                                            box-shadow: 0 12px 24px rgba(76, 175, 80, 0.5);
                                            transform: scale(1.05);
                                        }
                                    }
                                </style>
                            </div>
                            <h4 style="font-size: 22px; color: #4CAF50; margin: 15px 0; text-align: center; font-weight: 700; text-shadow: 0 1px 1px rgba(0,0,0,0.05); position: relative; padding-bottom: 10px;">
                                Đặt lại mật khẩu thành công!
                                <span style="position: absolute; bottom: 0; left: 50%; transform: translateX(-50%); width: 60px; height: 3px; background: linear-gradient(to right, #4CAF50, #81C784); border-radius: 3px;"></span>
                            </h4>
                            <p style="font-size: 15px; color: #555; text-align: center; line-height: 1.6; margin-bottom: 20px; background-color: rgba(76, 175, 80, 0.05); padding: 12px; border-radius: 8px; border: 1px dashed rgba(76, 175, 80, 0.3);">
                                <i class="fas fa-shield-alt" style="color: #4CAF50; margin-right: 8px;"></i>
                                Mật khẩu của bạn đã được cập nhật thành công. Bạn có thể đăng nhập bằng mật khẩu mới ngay bây giờ.
                            </p>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="primary-button" id="doneRecovery" style="background: linear-gradient(135deg, #4CAF50, #2E7D32); color: white; padding: 12px 24px; border: none; border-radius: 8px; font-weight: 600; font-size: 15px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3); display: flex; align-items: center; justify-content: center; gap: 8px; width: 100%; margin-top: 10px; position: relative; overflow: hidden;">
                                <i class="fas fa-sign-in-alt" style="font-size: 16px;"></i> Đăng nhập ngay
                                <span style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); animation: shine 2s infinite;"></span>
                                <style>
                                    @keyframes shine {
                                        0% { left: -100%; }
                                        100% { left: 100%; }
                                    }
                                    #doneRecovery:hover {
                                        transform: translateY(-3px);
                                        box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
                                    }
                                    #doneRecovery:active {
                                        transform: translateY(0);
                                        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
                                    }
                                </style>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Thông Báo Đăng Nhập Mạng Xã Hội -->
<div id="socialLoginModal" class="modal-overlay">
    <div class="modal-container" style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;">
        <div class="modal-content social-login-modal" style="padding: 25px; width: 100%; box-sizing: border-box;">
            <button type="button" class="modal-close" id="closeSocialLoginModal">
                <i class="fas fa-times"></i>
            </button>

            <div class="modal-header">
                <div class="modal-icon" id="socialModalIcon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="modal-title" id="socialModalTitle">Thông báo</h3>
            </div>

            <div class="modal-body">
                <div class="social-login-message">
                    <p id="socialLoginMessage">Chức năng đang được cập nhật. Vui lòng đăng ký tài khoản trực tiếp trên hệ thống.</p>
                </div>

                <div class="form-actions">
                    <button type="button" class="primary-button" id="closeSocialLoginBtn">
                        <i class="fas fa-check"></i> Đã hiểu
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Scrollbar Script -->
<script src="<?php echo BASE_URL; ?>/assets/js/custom-scrollbar.js"></script>
<script>
    // Khởi tạo custom scrollbar khi trang load xong
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof initCustomScrollbar === 'function') {
            initCustomScrollbar();
        }
    });
</script>

</html>