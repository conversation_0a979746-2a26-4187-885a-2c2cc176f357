<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Filter Button Update</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .test-section {
            margin-bottom: 40px;
            text-align: center;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            margin: 5px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Mock mobile filter button */
        .mobile-filter-container {
            display: block;
            width: 100%;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 16px;
            border: 2px solid #e2e8f0;
        }
        
        /* Mock sidebar for testing */
        .sidebar-filters {
            display: none;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .filter-section h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .filter-option {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .filter-option input {
            margin-right: 8px;
        }
        
        .price-input {
            width: 100px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 0 5px;
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.2rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .info-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
            margin: 20px 0;
        }
        
        .info-box h3 {
            color: #1e40af;
            margin: 0 0 10px 0;
            font-size: 1.1rem;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 20px;
            color: #1d4ed8;
        }
        
        .info-box li {
            margin-bottom: 5px;
        }
        
        .status {
            padding: 15px;
            border-radius: 12px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .log {
            background: #1e293b;
            color: #10b981;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 Mobile Filter Button Update Test</h1>
        <p>Kiểm tra việc cập nhật text của mobile filter button khi áp dụng/đặt lại bộ lọc</p>
        
        <div class="test-section">
            <h2>🎯 Mobile Filter Button</h2>
            
            <!-- Mock mobile filter button -->
            <div class="mobile-filter-container">
                <button class="mobile-filter-btn" id="mobile-filter-btn">
                    <i class="fas fa-filter filter-icon"></i>
                    <span class="filter-text">Bộ lọc sản phẩm</span>
                </button>
            </div>
            
            <!-- Mock sidebar filters (hidden) -->
            <div class="sidebar-filters">
                <div class="filter-section">
                    <h3>Danh mục</h3>
                    <div class="filter-option">
                        <input type="checkbox" name="category[]" value="1" id="cat_1">
                        <label for="cat_1">Bàn làm việc</label>
                    </div>
                    <div class="filter-option">
                        <input type="checkbox" name="category[]" value="2" id="cat_2">
                        <label for="cat_2">Ghế văn phòng</label>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Khoảng giá</h3>
                    <input type="text" class="price-input" data-price-field="min" placeholder="Từ">
                    <input type="text" class="price-input" data-price-field="max" placeholder="Đến">
                </div>
                
                <div class="filter-section">
                    <h3>Khuyến mãi</h3>
                    <div class="filter-option">
                        <input type="checkbox" name="promotion[]" value="sale" id="promo_sale">
                        <label for="promo_sale">Đang giảm giá</label>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="simulateApplyFilter()">🎯 Simulate Apply Filter</button>
                <button class="control-btn" onclick="simulateResetFilter()">🔄 Simulate Reset Filter</button>
                <button class="control-btn" onclick="addRandomFilters()">🎲 Add Random Filters</button>
                <button class="control-btn" onclick="clearAllFilters()">🗑️ Clear All Filters</button>
            </div>
        </div>
        
        <div class="info-box">
            <h3>🔍 Test Features:</h3>
            <ul>
                <li><strong>Auto Update:</strong> Button text tự động cập nhật khi filter thay đổi</li>
                <li><strong>Real-time Count:</strong> Đếm chính xác số lượng filter đã áp dụng</li>
                <li><strong>Smart Text:</strong> "Đã lọc X tiêu chí" hoặc "Bộ lọc sản phẩm"</li>
                <li><strong>Visual Feedback:</strong> Animation khi text thay đổi</li>
                <li><strong>Class Toggle:</strong> has-filters class được thêm/xóa tự động</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test mobile filter button update
        </div>
        
        <div id="testLog" class="log"></div>
    </div>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function simulateApplyFilter() {
            updateStatus('🎯 Simulating Apply Filter...', 'testing');
            log('🎯 Simulating apply filter action');
            
            // Add some random filters
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach((cb, index) => {
                if (index < 2) cb.checked = true;
            });
            
            // Add price filter
            document.querySelector('[data-price-field="min"]').value = '1000000';
            document.querySelector('[data-price-field="max"]').value = '5000000';
            
            // Simulate mobile filter modal update
            if (window.mobileFilterModal) {
                window.mobileFilterModal.updateFilterCount();
                log('✅ Mobile filter button updated via mobileFilterModal.updateFilterCount()');
            } else {
                log('❌ window.mobileFilterModal not found');
            }
            
            updateStatus('✅ Apply Filter simulation complete', 'success');
        }
        
        function simulateResetFilter() {
            updateStatus('🔄 Simulating Reset Filter...', 'testing');
            log('🔄 Simulating reset filter action');
            
            // Clear all filters
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            const priceInputs = document.querySelectorAll('[data-price-field]');
            priceInputs.forEach(input => input.value = '');
            
            // Simulate mobile filter modal update
            if (window.mobileFilterModal) {
                window.mobileFilterModal.updateFilterCount();
                log('✅ Mobile filter button updated via mobileFilterModal.updateFilterCount()');
            } else {
                log('❌ window.mobileFilterModal not found');
            }
            
            updateStatus('✅ Reset Filter simulation complete', 'success');
        }
        
        function addRandomFilters() {
            updateStatus('🎲 Adding Random Filters...', 'testing');
            log('🎲 Adding random filters');
            
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const randomCount = Math.floor(Math.random() * checkboxes.length) + 1;
            
            // Clear all first
            checkboxes.forEach(cb => cb.checked = false);
            
            // Add random selections
            for (let i = 0; i < randomCount; i++) {
                const randomIndex = Math.floor(Math.random() * checkboxes.length);
                checkboxes[randomIndex].checked = true;
            }
            
            // Maybe add price
            if (Math.random() > 0.5) {
                document.querySelector('[data-price-field="min"]').value = '500000';
            }
            
            // Update button
            if (window.mobileFilterModal) {
                window.mobileFilterModal.updateFilterCount();
                log(`✅ Added ${randomCount} random filters, button updated`);
            }
            
            updateStatus('✅ Random filters added', 'success');
        }
        
        function clearAllFilters() {
            updateStatus('🗑️ Clearing All Filters...', 'testing');
            log('🗑️ Clearing all filters');
            
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            const priceInputs = document.querySelectorAll('[data-price-field]');
            priceInputs.forEach(input => input.value = '');
            
            if (window.mobileFilterModal) {
                window.mobileFilterModal.updateFilterCount();
                log('✅ All filters cleared, button updated');
            }
            
            updateStatus('✅ All filters cleared', 'success');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Mobile Filter Button Update Test initialized');
            updateStatus('🚀 Test environment ready', 'ready');
            
            // Check if mobile filter modal is available
            setTimeout(() => {
                if (window.mobileFilterModal) {
                    log('✅ window.mobileFilterModal found and ready');
                    updateStatus('✅ Mobile Filter Modal detected', 'success');
                } else {
                    log('❌ window.mobileFilterModal not found');
                    updateStatus('⚠️ Mobile Filter Modal not detected', 'testing');
                }
            }, 1000);
        });
    </script>
    
    <!-- Include the actual mobile filter modal script -->
    <script src="assets/js/mobile-filter-modal.js"></script>
</body>
</html>
