<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Loading Icon Alignment</title>
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        /* Debug styles */
        .debug-container {
            border: 2px solid red;
            margin: 20px;
            padding: 20px;
        }
        
        .debug-item {
            border: 1px solid blue;
            margin: 10px 0;
            padding: 10px;
        }
        
        /* Original CSS from search-page.css */
        .search-loading-message-original {
            padding: 0;
            border-bottom: none;
            background-color: rgba(243, 115, 33, 0.02);
        }
        
        /* Fixed CSS */
        .search-loading-message-fixed {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 1rem !important;
            gap: 0.5rem !important;
            border-bottom: none;
            background-color: rgba(243, 115, 33, 0.02);
            color: #6b7280;
            font-size: 0.875rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .search-loading-message-fixed i {
            color: #f37321 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            vertical-align: middle !important;
        }
        
        .search-loading-message-fixed span {
            display: inline-flex !important;
            align-items: center !important;
        }
        
        /* Test different approaches */
        .test-approach-1 {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px;
            background: rgba(243, 115, 33, 0.02);
        }
        
        .test-approach-2 {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px;
            background: rgba(243, 115, 33, 0.02);
        }
        
        .test-approach-2 i {
            margin-right: 8px;
            line-height: 1;
            vertical-align: middle;
        }
        
        .test-approach-3 {
            text-align: center;
            padding: 12px;
            background: rgba(243, 115, 33, 0.02);
        }
        
        .test-approach-3 i {
            vertical-align: middle;
            margin-right: 8px;
        }
        
        /* Debugging grid */
        .debug-grid {
            background-image: 
                linear-gradient(to right, rgba(255,0,0,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,0,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <h1 class="text-2xl font-bold mb-6">Debug Loading Icon Alignment</h1>
    
    <!-- Test 1: Exact copy from JavaScript -->
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 1: Exact copy from enhanced-search.js</h2>
        <div class="search-loading-message">
            <div class="flex items-center justify-center p-3">
                <i class="fas fa-search text-primary mr-2"></i>
                <span class="text-gray-600 text-sm">Đang tìm kiếm sản phẩm...</span>
            </div>
        </div>
    </div>
    
    <!-- Test 2: With original CSS -->
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 2: With original CSS (search-page.css)</h2>
        <div class="search-loading-message-original">
            <div class="flex items-center justify-center p-3">
                <i class="fas fa-search text-primary mr-2"></i>
                <span class="text-gray-600 text-sm">Đang tìm kiếm sản phẩm...</span>
            </div>
        </div>
    </div>
    
    <!-- Test 3: With fixed CSS -->
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 3: With fixed CSS</h2>
        <div class="search-loading-message-fixed">
            <div class="flex items-center justify-center p-3">
                <i class="fas fa-search text-primary mr-2"></i>
                <span class="text-gray-600 text-sm">Đang tìm kiếm sản phẩm...</span>
            </div>
        </div>
    </div>
    
    <!-- Test 4: Different approaches -->
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 4: Approach 1 - Flex with gap</h2>
        <div class="test-approach-1">
            <i class="fas fa-search" style="color: #f37321;"></i>
            <span style="color: #6b7280;">Đang tìm kiếm sản phẩm...</span>
        </div>
    </div>
    
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 5: Approach 2 - Flex with margin</h2>
        <div class="test-approach-2">
            <i class="fas fa-search" style="color: #f37321;"></i>
            <span style="color: #6b7280;">Đang tìm kiếm sản phẩm...</span>
        </div>
    </div>
    
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Test 6: Approach 3 - Text align center</h2>
        <div class="test-approach-3">
            <i class="fas fa-search" style="color: #f37321;"></i>
            <span style="color: #6b7280;">Đang tìm kiếm sản phẩm...</span>
        </div>
    </div>
    
    <!-- Test 7: With debug grid -->
    <div class="debug-container debug-grid">
        <h2 class="text-lg font-semibold mb-4">Test 7: With debug grid</h2>
        <div class="test-approach-1">
            <i class="fas fa-search" style="color: #f37321; border: 1px solid red;"></i>
            <span style="color: #6b7280; border: 1px solid blue;">Đang tìm kiếm sản phẩm...</span>
        </div>
    </div>
    
    <!-- Console debug -->
    <div class="debug-container">
        <h2 class="text-lg font-semibold mb-4">Console Debug Info</h2>
        <button onclick="debugElements()" class="bg-blue-500 text-white px-4 py-2 rounded">
            Debug Elements in Console
        </button>
        <div id="debug-output" class="mt-4 p-4 bg-gray-200 rounded"></div>
    </div>

    <script>
        // Debug function
        function debugElements() {
            console.log('=== DEBUG LOADING ICON ALIGNMENT ===');
            
            // Get all test elements
            const elements = [
                '.search-loading-message',
                '.search-loading-message-original', 
                '.search-loading-message-fixed',
                '.test-approach-1',
                '.test-approach-2', 
                '.test-approach-3'
            ];
            
            elements.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    const computedStyle = window.getComputedStyle(element);
                    const icon = element.querySelector('i');
                    const span = element.querySelector('span');
                    
                    console.log(`\n--- ${selector} ---`);
                    console.log('Container styles:', {
                        display: computedStyle.display,
                        alignItems: computedStyle.alignItems,
                        justifyContent: computedStyle.justifyContent,
                        flexDirection: computedStyle.flexDirection,
                        gap: computedStyle.gap,
                        padding: computedStyle.padding,
                        lineHeight: computedStyle.lineHeight,
                        verticalAlign: computedStyle.verticalAlign
                    });
                    
                    if (icon) {
                        const iconStyle = window.getComputedStyle(icon);
                        console.log('Icon styles:', {
                            display: iconStyle.display,
                            verticalAlign: iconStyle.verticalAlign,
                            lineHeight: iconStyle.lineHeight,
                            fontSize: iconStyle.fontSize,
                            marginTop: iconStyle.marginTop,
                            marginBottom: iconStyle.marginBottom,
                            transform: iconStyle.transform
                        });
                        
                        // Get bounding box
                        const iconRect = icon.getBoundingClientRect();
                        console.log('Icon position:', {
                            top: iconRect.top,
                            left: iconRect.left,
                            width: iconRect.width,
                            height: iconRect.height
                        });
                    }
                    
                    if (span) {
                        const spanStyle = window.getComputedStyle(span);
                        console.log('Span styles:', {
                            display: spanStyle.display,
                            verticalAlign: spanStyle.verticalAlign,
                            lineHeight: spanStyle.lineHeight,
                            fontSize: spanStyle.fontSize
                        });
                        
                        // Get bounding box
                        const spanRect = span.getBoundingClientRect();
                        console.log('Span position:', {
                            top: spanRect.top,
                            left: spanRect.left,
                            width: spanRect.width,
                            height: spanRect.height
                        });
                    }
                }
            });
            
            // Output to page as well
            const output = document.getElementById('debug-output');
            output.innerHTML = 'Debug info logged to console. Press F12 to view.';
        }
        
        // Auto-run debug on load
        window.addEventListener('load', () => {
            setTimeout(debugElements, 1000);
        });
    </script>
</body>
</html>
