<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Products AJAX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        button {
            background: #f97316;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #ea580c;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .error {
            background: #fee;
            border-color: #fcc;
            color: #c00;
        }
        .success {
            background: #efe;
            border-color: #cfc;
            color: #060;
        }
        .loading {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .product-name {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .product-price {
            color: #f97316;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Test Products AJAX Filter</h1>
    
    <div class="test-section">
        <h2>AJAX Filter Test</h2>
        
        <div class="filter-form">
            <div class="form-group">
                <label for="keyword">Từ khóa:</label>
                <input type="text" id="keyword" placeholder="Nhập từ khóa...">
            </div>
            
            <div class="form-group">
                <label for="categories">Danh mục (ID):</label>
                <input type="text" id="categories" placeholder="1,2,3">
            </div>
            
            <div class="form-group">
                <label for="price_min">Giá tối thiểu:</label>
                <input type="number" id="price_min" placeholder="100000">
            </div>
            
            <div class="form-group">
                <label for="price_max">Giá tối đa:</label>
                <input type="number" id="price_max" placeholder="1000000">
            </div>
            
            <div class="form-group">
                <label for="sort">Sắp xếp:</label>
                <select id="sort">
                    <option value="newest">Mới nhất</option>
                    <option value="price_asc">Giá tăng dần</option>
                    <option value="price_desc">Giá giảm dần</option>
                    <option value="name_asc">Tên A-Z</option>
                    <option value="name_desc">Tên Z-A</option>
                </select>
            </div>
        </div>
        
        <button onclick="testAjaxFilter()">Test AJAX Filter</button>
        <button onclick="testAjaxFilterClass()">Test AJAX Filter Class</button>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="productsGrid" class="products-grid"></div>
    </div>

    <!-- Include AJAX Filter script -->
    <script>
        window.BASE_URL = '';
    </script>
    <script src="assets/js/ajax-filter.js"></script>

    <script>
        // Test function 1: Direct API call
        async function testAjaxFilter() {
            const result = document.getElementById('result');
            const productsGrid = document.getElementById('productsGrid');
            
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = 'Testing direct API call...';
            productsGrid.innerHTML = '';

            try {
                const data = collectFormData();
                console.log('Sending data:', data);
                
                const response = await fetch('api/filter-products.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const responseData = await response.json();
                console.log('Response:', responseData);

                if (response.ok && responseData.success) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ Direct API Call Success!</h3>
                        <p>Total products: ${responseData.data.pagination.total_products}</p>
                        <p>Products returned: ${responseData.data.products.length}</p>
                        <p>Current page: ${responseData.data.pagination.current_page}/${responseData.data.pagination.total_pages}</p>
                    `;
                    
                    renderProducts(responseData.data.products);
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ Direct API Call Failed!</h3>
                        <p>Error: ${responseData.error || 'Unknown error'}</p>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    `;
                }

            } catch (error) {
                console.error('Error:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ Network Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        // Test function 2: Using AJAX Filter class
        function testAjaxFilterClass() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = 'Testing AJAX Filter class...';

            try {
                if (typeof window.ajaxFilter !== 'undefined') {
                    // Simulate form data
                    const data = collectFormData();
                    
                    // Call the AJAX filter class method
                    window.ajaxFilter.loadProducts(data, 1);
                    
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ AJAX Filter Class Test!</h3>
                        <p>Called ajaxFilter.loadProducts() successfully</p>
                        <p>Check console for details</p>
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ AJAX Filter Class Not Found!</h3>
                        <p>window.ajaxFilter is not defined</p>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ AJAX Filter Class Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        function collectFormData() {
            const data = {};
            
            const keyword = document.getElementById('keyword').value.trim();
            if (keyword) data.keyword = keyword;
            
            const categories = document.getElementById('categories').value.trim();
            if (categories) {
                data.categories = categories.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id) && id > 0);
            }
            
            const priceMin = document.getElementById('price_min').value;
            const priceMax = document.getElementById('price_max').value;
            if (priceMin) data.price_min = parseFloat(priceMin);
            if (priceMax) data.price_max = parseFloat(priceMax);
            
            const sort = document.getElementById('sort').value;
            if (sort) data.sort = sort;
            
            data.page = 1;
            data.items_per_page = 12;
            
            return data;
        }

        function renderProducts(products) {
            const productsGrid = document.getElementById('productsGrid');
            
            if (!products || products.length === 0) {
                productsGrid.innerHTML = '<p>Không có sản phẩm nào.</p>';
                return;
            }
            
            productsGrid.innerHTML = products.map(product => `
                <div class="product-card">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${formatCurrency(product.price)}</div>
                    ${product.sale_price > 0 ? `<div style="text-decoration: line-through; color: #999;">${formatCurrency(product.sale_price)}</div>` : ''}
                    <div style="margin-top: 10px; font-size: 14px; color: #666;">
                        <div>ID: ${product.id}</div>
                        <div>Danh mục: ${product.category_name || 'N/A'}</div>
                        <div>Rating: ${product.rating || 5}/5</div>
                        <div>Đã bán: ${product.sold || 0}</div>
                    </div>
                </div>
            `).join('');
        }

        function formatCurrency(amount) {
            if (!amount || isNaN(amount)) return '0 đ';
            return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' đ';
        }

        // Log when script loads
        console.log('Test script loaded');
        console.log('window.ajaxFilter:', typeof window.ajaxFilter);
    </script>
</body>
</html>
