<?php
/**
 * <PERSON>rang xử lý đơn hàng
 * Nhận dữ liệu từ form checkout và xử lý đơn hàng
 */

// Include init
require_once 'includes/init.php';

// Ki<PERSON><PERSON> tra phương thức request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect(BASE_URL);
}

// Kiểm tra CSRF token
if (!isset($_POST['csrf_token']) || !check_csrf_token($_POST['csrf_token'])) {
    set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
    redirect(BASE_URL . '/checkout.php');
}

// Lấy dữ liệu từ form
$full_name = sanitize($_POST['full_name'] ?? '');
$email = sanitize($_POST['email'] ?? '');
$phone = sanitize($_POST['phone'] ?? '');
$address = sanitize($_POST['address'] ?? '');
$note = sanitize($_POST['note'] ?? '');
$user_id = is_logged_in() ? $_SESSION['user_id'] : null;

// Kiểm tra dữ liệu
if (empty($full_name) || empty($email) || empty($phone) || empty($address)) {
    set_flash_message('error', 'Vui lòng nhập đầy đủ thông tin.');
    redirect(BASE_URL . '/checkout.php');
}

// Kiểm tra định dạng email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    set_flash_message('error', 'Email không hợp lệ.');
    redirect(BASE_URL . '/checkout.php');
}

// Lấy giỏ hàng
$cart_items = get_cart_items();
if (count($cart_items) === 0) {
    set_flash_message('error', 'Giỏ hàng của bạn đang trống.');
    redirect(BASE_URL . '/checkout.php');
}

// Tạo đơn hàng
$order_data = [
    'user_id' => $user_id,
    'full_name' => $full_name,
    'email' => $email,
    'phone' => $phone,
    'address' => $address,
    'note' => $note
];

$result = create_order($order_data, $cart_items);

if ($result['success']) {
    // Xóa giỏ hàng
    clear_cart();
    
    // Gửi email xác nhận đơn hàng
    if (function_exists('send_order_confirmation_email')) {
        $email_result = send_order_confirmation_email($result['order_id']);
        
        // Ghi log kết quả gửi email
        if (!$email_result['success']) {
            error_log("Lỗi gửi email xác nhận đơn hàng ID {$result['order_id']}: " . json_encode($email_result));
        }
    }
    
    // Xóa dữ liệu checkout khỏi session
    if (isset($_SESSION['checkout_data'])) {
        unset($_SESSION['checkout_data']);
    }
    
    // Chuyển hướng đến trang cảm ơn
    set_flash_message('success', $result['message']);
    redirect(BASE_URL . '/thank-you.php?order_id=' . $result['order_id']);
} else {
    set_flash_message('error', $result['message']);
    redirect(BASE_URL . '/checkout.php');
}
