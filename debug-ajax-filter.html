<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug AJAX Filter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background: #f97316;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #ea580c;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .error {
            background: #fee;
            border-color: #fcc;
            color: #c00;
        }
        .success {
            background: #efe;
            border-color: #cfc;
            color: #060;
        }
        .loading {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .product-name {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .product-price {
            color: #f97316;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Debug AJAX Filter</h1>
    
    <div class="debug-section">
        <h2>Test AJAX Filter</h2>
        
        <form class="filter-form" id="filterForm">
            <div class="form-group">
                <label for="keyword">Từ khóa:</label>
                <input type="text" id="keyword" name="keyword" placeholder="Nhập từ khóa...">
            </div>
            
            <div class="form-group">
                <label for="categories">Danh mục (ID):</label>
                <input type="text" id="categories" name="categories" placeholder="1,2,3" title="Nhập ID danh mục, cách nhau bằng dấu phẩy">
            </div>
            
            <div class="form-group">
                <label for="price_min">Giá tối thiểu:</label>
                <input type="number" id="price_min" name="price_min" placeholder="100000">
            </div>
            
            <div class="form-group">
                <label for="price_max">Giá tối đa:</label>
                <input type="number" id="price_max" name="price_max" placeholder="1000000">
            </div>
            
            <div class="form-group">
                <label for="promotions">Khuyến mãi:</label>
                <select id="promotions" name="promotions" multiple>
                    <option value="sale">Sale</option>
                    <option value="flash_sale">Flash Sale</option>
                    <option value="featured">Nổi bật</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="sort">Sắp xếp:</label>
                <select id="sort" name="sort">
                    <option value="newest">Mới nhất</option>
                    <option value="price_asc">Giá tăng dần</option>
                    <option value="price_desc">Giá giảm dần</option>
                    <option value="name_asc">Tên A-Z</option>
                    <option value="name_desc">Tên Z-A</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="page">Trang:</label>
                <input type="number" id="page" name="page" value="1" min="1">
            </div>
            
            <div class="form-group">
                <label for="items_per_page">Số sản phẩm/trang:</label>
                <select id="items_per_page" name="items_per_page">
                    <option value="12">12</option>
                    <option value="24">24</option>
                    <option value="36">36</option>
                    <option value="48">48</option>
                </select>
            </div>
        </form>
        
        <button type="button" id="testFilter" onclick="testAjaxFilter()">Test AJAX Filter</button>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div id="productGrid" class="product-grid"></div>
    </div>

    <script>
        window.BASE_URL = '';
        const API_URL = 'api/filter-products.php';

        async function testAjaxFilter() {
            const button = document.getElementById('testFilter');
            const result = document.getElementById('result');
            const productGrid = document.getElementById('productGrid');
            
            // Disable button và hiển thị loading
            button.disabled = true;
            button.textContent = 'Đang test...';
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = 'Đang gửi request...';
            productGrid.innerHTML = '';

            try {
                // Collect form data
                const formData = collectFormData();
                
                console.log('Sending data:', formData);
                
                // Send AJAX request
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);

                if (response.ok && data.success) {
                    // Success
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ Thành công!</h3>
                        <p><strong>Tổng sản phẩm:</strong> ${data.data.pagination.total_products}</p>
                        <p><strong>Sản phẩm trả về:</strong> ${data.data.products.length}</p>
                        <p><strong>Trang hiện tại:</strong> ${data.data.pagination.current_page}/${data.data.pagination.total_pages}</p>
                        <p><strong>Filters áp dụng:</strong></p>
                        <ul>
                            ${data.data.filters.keyword ? `<li>Từ khóa: ${data.data.filters.keyword}</li>` : ''}
                            ${data.data.filters.categories && data.data.filters.categories.length > 0 ? `<li>Danh mục: ${data.data.filters.categories.map(c => c.name).join(', ')}</li>` : ''}
                            ${data.data.filters.price_min ? `<li>Giá tối thiểu: ${data.data.filters.price_min.toLocaleString()} đ</li>` : ''}
                            ${data.data.filters.price_max ? `<li>Giá tối đa: ${data.data.filters.price_max.toLocaleString()} đ</li>` : ''}
                            ${data.data.filters.promotions && data.data.filters.promotions.length > 0 ? `<li>Khuyến mãi: ${data.data.filters.promotions.join(', ')}</li>` : ''}
                            ${data.data.filters.sort ? `<li>Sắp xếp: ${data.data.filters.sort}</li>` : ''}
                        </ul>
                    `;
                    
                    // Render products
                    renderProducts(data.data.products);
                    
                } else {
                    // Error
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ Lỗi!</h3>
                        <p><strong>Message:</strong> ${data.error || 'Unknown error'}</p>
                        <p><strong>Status:</strong> ${response.status}</p>
                        ${data.debug ? `<pre>${JSON.stringify(data.debug, null, 2)}</pre>` : ''}
                    `;
                }

            } catch (error) {
                console.error('Network error:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ Network Error!</h3>
                    <p>${error.message}</p>
                `;
            } finally {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Test AJAX Filter';
            }
        }

        function collectFormData() {
            const data = {};
            
            // Keyword
            const keyword = document.getElementById('keyword').value.trim();
            if (keyword) data.keyword = keyword;
            
            // Categories
            const categories = document.getElementById('categories').value.trim();
            if (categories) {
                data.categories = categories.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id) && id > 0);
            }
            
            // Price range
            const priceMin = document.getElementById('price_min').value;
            const priceMax = document.getElementById('price_max').value;
            if (priceMin) data.price_min = parseFloat(priceMin);
            if (priceMax) data.price_max = parseFloat(priceMax);
            
            // Promotions
            const promotions = Array.from(document.getElementById('promotions').selectedOptions).map(option => option.value);
            if (promotions.length > 0) data.promotions = promotions;
            
            // Sort
            const sort = document.getElementById('sort').value;
            if (sort) data.sort = sort;
            
            // Pagination
            const page = parseInt(document.getElementById('page').value) || 1;
            const itemsPerPage = parseInt(document.getElementById('items_per_page').value) || 12;
            data.page = page;
            data.items_per_page = itemsPerPage;
            
            return data;
        }

        function renderProducts(products) {
            const productGrid = document.getElementById('productGrid');
            
            if (!products || products.length === 0) {
                productGrid.innerHTML = '<p>Không có sản phẩm nào.</p>';
                return;
            }
            
            productGrid.innerHTML = products.map(product => `
                <div class="product-card">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${formatCurrency(product.price)}</div>
                    ${product.sale_price > 0 ? `<div style="text-decoration: line-through; color: #999;">${formatCurrency(product.sale_price)}</div>` : ''}
                    <div style="margin-top: 10px; font-size: 14px; color: #666;">
                        <div>ID: ${product.id}</div>
                        <div>Danh mục: ${product.category_name || 'N/A'}</div>
                        <div>Rating: ${product.rating || 5}/5</div>
                        <div>Đã bán: ${product.sold || 0}</div>
                    </div>
                </div>
            `).join('');
        }

        function formatCurrency(amount) {
            if (!amount || isNaN(amount)) return '0 đ';
            return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + ' đ';
        }
    </script>
</body>
</html>
