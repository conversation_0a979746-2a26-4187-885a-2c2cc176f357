<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagination Active State</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-pagination {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .pagination-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .page-item {
            margin: 0;
        }
        .page-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            height: 44px;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #ffffff;
            color: #6b7280;
            font-weight: 500;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            cursor: pointer;
        }
        .page-link:hover {
            background: rgba(243, 115, 33, 0.05);
            border-color: rgba(243, 115, 33, 0.3);
            color: #F37321;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(243, 115, 33, 0.15);
        }
        
        /* Active State */
        .page-item.active .page-link {
            background: linear-gradient(135deg, #F37321 0%, #e55a00 100%);
            border-color: #F37321;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
            transform: translateY(-1px);
        }
        
        .page-item.active .page-link:hover {
            background: linear-gradient(135deg, #e55a00 0%, #cc4f00 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(243, 115, 33, 0.4);
        }
        
        /* Loading State */
        .page-link.loading {
            pointer-events: none !important;
            cursor: not-allowed !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            position: relative !important;
        }
        
        /* Pagination Loading Content */
        .pagination-loading-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Loading Dots */
        .loading-dots {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-dots .dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #F37321;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots .dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dots .dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        .loading-dots .dot:nth-child(3) {
            animation-delay: 0s;
        }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        .page-item.disabled .page-link {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        .page-text {
            margin: 0 0.25rem;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .active-demo {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        .demo-item {
            text-align: center;
            flex: 1;
        }
        .demo-item h5 {
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        .demo-page {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.875rem;
            margin: 0 5px;
        }
        .demo-page.normal {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }
        .demo-page.active {
            background: #F37321;
            color: white;
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.3);
        }
        .arrow {
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.6);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <h1>🎯 Test Pagination Active State</h1>
        <p>Kiểm tra active state tự động cập nhật khi click pagination</p>
        
        <div class="test-section">
            <h3>🔄 Active State Logic</h3>
            <p>Khi click vào số trang, sau khi loading xong:</p>
            
            <div class="active-demo">
                <div class="demo-item">
                    <h5>Trước Click</h5>
                    <div class="demo-page normal">1</div>
                    <div class="demo-page active">2</div>
                    <div class="demo-page normal">3</div>
                    <div class="demo-page normal">4</div>
                </div>
                
                <div class="arrow">→</div>
                
                <div class="demo-item">
                    <h5>Click Trang 3</h5>
                    <div class="demo-page normal">1</div>
                    <div class="demo-page normal">2</div>
                    <div class="demo-page" style="background: #ffc107; color: #000;">3</div>
                    <div class="demo-page normal">4</div>
                    <small>Loading...</small>
                </div>
                
                <div class="arrow">→</div>
                
                <div class="demo-item">
                    <h5>Sau Loading</h5>
                    <div class="demo-page normal">1</div>
                    <div class="demo-page normal">2</div>
                    <div class="demo-page active">3</div>
                    <div class="demo-page normal">4</div>
                    <small>Active!</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 Demo Interactive</h3>
            <p>Click vào các số trang để xem active state thay đổi:</p>
            
            <nav class="demo-pagination">
                <ul class="pagination-list">
                    <!-- Previous Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">
                            <i class="fas fa-chevron-left"></i>
                            <span class="page-text">Trước</span>
                        </a>
                    </li>
                    
                    <!-- Page Numbers -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="1">1</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link demo-pagination-link" href="#" data-page="2">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="4">4</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="5">5</a>
                    </li>
                    
                    <!-- Next Button -->
                    <li class="page-item">
                        <a class="page-link demo-pagination-link" href="#" data-page="3">
                            <span class="page-text">Sau</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div id="demo-result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Thực Tế</h3>
            <p>Test với products page thực tế</p>
            
            <button class="test-button" onclick="openProductsPage()">Mở Products Page</button>
            <button class="test-button" onclick="showTestInstructions()">Hướng Dẫn Test</button>
            
            <div id="real-test-result"></div>
        </div>
    </div>

    <script>
        let currentActivePage = 2; // Trang active hiện tại

        // Demo pagination active state
        document.addEventListener('click', function(e) {
            if (e.target.closest('.demo-pagination-link')) {
                e.preventDefault();
                const link = e.target.closest('.demo-pagination-link');
                const page = parseInt(link.dataset.page);
                
                if (!page || link.classList.contains('loading')) return;
                
                // Add loading state
                addDemoLoadingState(link);
                
                // Simulate loading time
                setTimeout(() => {
                    removeDemoLoadingState(link);
                    updateActiveState(page);
                    showResult('demo-result', `✅ Trang ${page} đã được set active, trang ${currentActivePage} trở về bình thường`, 'success');
                    currentActivePage = page;
                }, 2000);
            }
        });

        function addDemoLoadingState(clickedLink) {
            // Save original content
            if (!clickedLink.dataset.originalContent) {
                clickedLink.dataset.originalContent = clickedLink.innerHTML;
            }
            
            // Add loading state
            clickedLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            clickedLink.style.transform = 'scale(0.95)';
            clickedLink.style.pointerEvents = 'none';
            clickedLink.classList.add('loading');

            // Create loading content
            const originalContent = clickedLink.dataset.originalContent;
            let loadingHTML = '';

            if (originalContent.includes('page-text')) {
                // Previous/Next buttons
                if (originalContent.includes('Trước')) {
                    loadingHTML = `
                        <div class="pagination-loading-content">
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                            <span class="page-text">Đang tải</span>
                        </div>
                    `;
                } else if (originalContent.includes('Sau')) {
                    loadingHTML = `
                        <div class="pagination-loading-content">
                            <span class="page-text">Đang tải</span>
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                    `;
                }
            } else {
                // Number buttons
                loadingHTML = `
                    <div class="pagination-loading-content">
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                `;
            }

            // Fade out old content, then fade in new content
            clickedLink.style.opacity = '0.3';
            
            setTimeout(() => {
                clickedLink.innerHTML = loadingHTML;
                clickedLink.style.opacity = '0.8';
            }, 150);
        }

        function removeDemoLoadingState(link) {
            // Fade out loading content
            link.style.opacity = '0.3';
            
            setTimeout(() => {
                // Restore original content
                if (link.dataset.originalContent) {
                    link.innerHTML = link.dataset.originalContent;
                    delete link.dataset.originalContent;
                }

                // Fade in and restore styles
                link.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                link.style.opacity = '1';
                link.style.transform = 'scale(1)';
                link.style.pointerEvents = '';
                link.classList.remove('loading');

                // Reset styles
                setTimeout(() => {
                    link.style.transition = '';
                    link.style.transform = '';
                }, 300);
            }, 150);
        }

        function updateActiveState(newActivePage) {
            // Remove active from all pages
            document.querySelectorAll('.page-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active to clicked page
            const targetLink = document.querySelector(`.demo-pagination-link[data-page="${newActivePage}"]`);
            if (targetLink) {
                const targetItem = targetLink.closest('.page-item');
                if (targetItem) {
                    targetItem.classList.add('active');
                }
            }
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function openProductsPage() {
            window.open('http://localhost/noithatbangvu/products.php', '_blank');
            showResult('real-test-result', '✅ Products page opened in new tab', 'success');
        }

        function showTestInstructions() {
            showResult('real-test-result', `
                📋 <strong>Hướng Dẫn Test Active State:</strong><br><br>
                
                <strong>🎯 Mục Tiêu:</strong> Kiểm tra active state tự động cập nhật<br><br>
                
                <strong>📝 Các Bước Test:</strong><br>
                1. <strong>Mở products page</strong> trong tab mới<br>
                2. <strong>Quan sát trang active hiện tại</strong> (có background cam)<br>
                3. <strong>Click vào số trang khác</strong> (ví dụ: từ trang 1 → trang 3)<br>
                4. <strong>Quan sát loading</strong> → dots animation<br>
                5. <strong>Sau loading xong:</strong><br>
                   &nbsp;&nbsp;• Trang được click → trở thành active (cam)<br>
                   &nbsp;&nbsp;• Trang active cũ → trở về bình thường (trắng)<br><br>
                
                <strong>✅ Kết Quả Mong Đợi:</strong><br>
                • Active state tự động chuyển sang trang được click<br>
                • Trang cũ tự động mất active state<br>
                • Visual feedback rõ ràng cho user<br>
                • Không cần refresh trang
            `, 'info');
        }

        // Show initial message
        window.addEventListener('load', function() {
            showResult('demo-result', '👆 Click vào các số trang để xem active state thay đổi tự động', 'info');
        });
    </script>
</body>
</html>
