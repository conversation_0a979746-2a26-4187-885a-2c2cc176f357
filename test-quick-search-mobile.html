<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quick Search Button Mobile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/search-page.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .test-section {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            margin: 5px;
            font-size: 12px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Mock search container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .search-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 8px 12px;
            font-size: 14px;
            background: transparent;
        }
        
        /* Layout stability indicators */
        .layout-indicator {
            position: absolute;
            width: 2px;
            height: 100%;
            background: red;
            opacity: 0.3;
            pointer-events: none;
            z-index: 1000;
        }
        
        .left-indicator {
            left: 0;
        }
        
        .right-indicator {
            right: 0;
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.8rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .info-box {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #f59e0b;
            margin: 15px 0;
        }
        
        .info-box h3 {
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 1rem;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 16px;
            color: #78350f;
            font-size: 13px;
        }
        
        .info-box li {
            margin-bottom: 4px;
        }
        
        .status {
            padding: 12px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 13px;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .log {
            background: #1e293b;
            color: #10b981;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 150px;
            overflow-y: auto;
            margin-top: 12px;
        }
        
        /* Custom styles for products.php CSS */
        .search-submit-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 140px;
            max-width: 220px;
            width: auto;
            white-space: nowrap;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
        }

        @media (max-width: 768px) {
            .search-submit-btn {
                min-width: 100px;
                max-width: 100px;
                width: 100px;
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }
        }

        .search-submit-btn.loading {
            background: linear-gradient(135deg, #f97316, #ea580c, #dc2626) !important;
            border-color: #ea580c !important;
            transform: scale(0.98);
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.2);
            pointer-events: none;
            min-width: 140px;
            max-width: 220px;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
        }

        @media (max-width: 768px) {
            .search-submit-btn.loading {
                min-width: 100px;
                max-width: 100px;
                width: 100px;
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }
        }

        .search-submit-btn.success {
            background: linear-gradient(135deg, #059669, #10b981, #34d399) !important;
            border-color: #10b981 !important;
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
            animation: successPulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 140px;
            max-width: 220px;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        @media (max-width: 768px) {
            .search-submit-btn.success {
                min-width: 100px;
                max-width: 100px;
                width: 100px;
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                transform: scale(1.01);
            }
        }

        @keyframes successPulse {
            0% { box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2); }
            50% { box-shadow: 0 12px 35px rgba(16, 185, 129, 0.6); }
            100% { box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Quick Search Mobile Test</h1>
        <p>Kiểm tra chiều rộng nhất quán của Quick Search Button trên mobile</p>
        
        <div class="test-section">
            <h2>📱 Mobile Search Button</h2>
            
            <div class="search-container" style="position: relative;">
                <!-- Layout stability indicators -->
                <div class="layout-indicator left-indicator"></div>
                <div class="layout-indicator right-indicator"></div>
                
                <input type="text" class="search-input" placeholder="Nhập từ khóa tìm kiếm...">
                
                <button type="submit" class="search-submit-btn" id="searchBtn">
                    <div class="btn-content">
                        <div class="btn-spinner" style="display: none;"></div>
                        <div class="btn-success" style="display: none;">
                            <i class="fas fa-check"></i>
                        </div>
                        <i class="fas fa-search btn-search-icon"></i>
                        <span class="btn-text">Tìm kiếm</span>
                    </div>
                </button>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="testNormal()">📏 Normal State</button>
                <button class="control-btn" onclick="testLoading()">🔄 Loading State</button>
                <button class="control-btn" onclick="testSuccess()">✅ Success State</button>
                <button class="control-btn" onclick="testSequence()">⚡ Full Sequence</button>
            </div>
        </div>
        
        <div class="info-box">
            <h3>🎯 Mobile Width Consistency:</h3>
            <ul>
                <li><strong>Normal:</strong> 100px width trên mobile</li>
                <li><strong>Loading:</strong> Giữ nguyên 100px width</li>
                <li><strong>Success:</strong> Giữ nguyên 100px width</li>
                <li><strong>Padding:</strong> 0.75rem consistent</li>
                <li><strong>No Layout Shift:</strong> Đường đỏ để kiểm tra</li>
                <li><strong>Responsive:</strong> 140px trên desktop, 100px trên mobile</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test Quick Search Button mobile
        </div>
        
        <div id="testLog" class="log"></div>
    </div>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function measureButton() {
            const btn = document.getElementById('searchBtn');
            const width = btn.offsetWidth;
            const height = btn.offsetHeight;
            const computedStyle = window.getComputedStyle(btn);
            const paddingLeft = computedStyle.paddingLeft;
            const paddingRight = computedStyle.paddingRight;
            
            log(`Button: ${width}x${height}px, padding: ${paddingLeft}|${paddingRight}`);
            return { width, height, paddingLeft, paddingRight };
        }
        
        function testNormal() {
            const btn = document.getElementById('searchBtn');
            updateStatus('📏 Testing Normal State...', 'testing');
            
            // Reset to normal
            btn.classList.remove('loading', 'success');
            btn.querySelector('.btn-spinner').style.display = 'none';
            btn.querySelector('.btn-success').style.display = 'none';
            btn.querySelector('.btn-search-icon').style.display = '';
            btn.querySelector('.btn-text').textContent = 'Tìm kiếm';
            
            setTimeout(() => {
                const measurements = measureButton();
                updateStatus(`✅ Normal: ${measurements.width}px width`, 'success');
            }, 100);
        }
        
        function testLoading() {
            const btn = document.getElementById('searchBtn');
            updateStatus('🔄 Testing Loading State...', 'testing');
            
            btn.classList.add('loading');
            btn.querySelector('.btn-spinner').style.display = 'inline-flex';
            btn.querySelector('.btn-spinner').innerHTML = '<div class="spinner-border spinner-border-sm"></div>';
            btn.querySelector('.btn-search-icon').style.display = 'none';
            btn.querySelector('.btn-text').textContent = 'Đang tìm...';
            
            setTimeout(() => {
                const measurements = measureButton();
                updateStatus(`🔄 Loading: ${measurements.width}px width`, 'testing');
            }, 100);
        }
        
        function testSuccess() {
            const btn = document.getElementById('searchBtn');
            updateStatus('✅ Testing Success State...', 'testing');
            
            btn.classList.remove('loading');
            btn.classList.add('success');
            btn.querySelector('.btn-spinner').style.display = 'none';
            btn.querySelector('.btn-success').style.display = 'inline-flex';
            btn.querySelector('.btn-search-icon').style.display = 'none';
            btn.querySelector('.btn-text').textContent = 'Thành công';
            
            setTimeout(() => {
                const measurements = measureButton();
                updateStatus(`✅ Success: ${measurements.width}px width`, 'success');
            }, 100);
        }
        
        function testSequence() {
            updateStatus('⚡ Testing Full Sequence...', 'testing');
            log('🚀 Starting full sequence test');
            
            // Normal
            testNormal();
            
            setTimeout(() => {
                // Loading
                testLoading();
                
                setTimeout(() => {
                    // Success
                    testSuccess();
                    
                    setTimeout(() => {
                        // Back to normal
                        testNormal();
                        updateStatus('⚡ Full sequence complete', 'success');
                        log('✅ Full sequence test completed');
                    }, 1500);
                }, 1500);
            }, 1000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Quick Search Mobile Test initialized');
            updateStatus('🚀 Test environment ready', 'ready');
            
            // Initial measurement
            setTimeout(() => {
                measureButton();
                updateStatus('📏 Initial measurements logged', 'ready');
            }, 500);
        });
    </script>
</body>
</html>
