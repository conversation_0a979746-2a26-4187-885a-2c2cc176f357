<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quick Search Button Mobile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/search-page.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .test-section {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
            font-size: 12px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        /* Mock search container */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 8px;
            margin: 20px 0;
        }
        
        .search-input-enhanced {
            flex: 1;
            border: none;
            outline: none;
            padding: 8px 12px;
            font-size: 14px;
            padding-right: 70px !important;
        }
        
        /* Include the actual search button styles from products.php */
        .search-submit-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 140px;
            max-width: 220px;
            width: auto;
            white-space: nowrap;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
        }

        @media (max-width: 768px) {
            .search-submit-btn {
                min-width: unset;
                max-width: unset;
                width: auto;
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        .search-submit-btn.loading {
            background: linear-gradient(135deg, #f97316, #ea580c, #dc2626) !important;
            border-color: #ea580c !important;
            transform: scale(0.98);
            box-shadow: 0 4px 15px rgba(249, 115, 22, 0.2);
            pointer-events: none;
            min-width: 140px;
            max-width: 220px;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
        }

        .search-submit-btn.success {
            background: linear-gradient(135deg, #059669, #10b981, #34d399) !important;
            border-color: #10b981 !important;
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
            animation: successPulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 140px;
            max-width: 220px;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        /* Mobile 480px - Override cho loading và success states */
        @media (max-width: 480px) {
            .search-submit-btn.loading,
            .search-submit-btn.success {
                width: 30px !important;
                min-width: 30px !important;
                max-width: 30px !important;
                padding: 0 8px !important;
                padding-left: 8px !important;
                padding-right: 8px !important;
                font-size: 10px !important;
            }

            .search-submit-btn.loading span,
            .search-submit-btn.success span {
                display: none !important;
            }
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            gap: 0.5rem;
            min-height: 1.5rem;
            position: relative;
        }

        .btn-spinner {
            display: none;
            align-items: center;
            justify-content: center;
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
        }

        .btn-success {
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
        }

        .btn-search-icon {
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-text {
            white-space: nowrap;
            flex-shrink: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes successPulse {
            0% { box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2); }
            50% { box-shadow: 0 12px 35px rgba(16, 185, 129, 0.6); }
            100% { box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4); }
        }

        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.8rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        p {
            color: #6b7280;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .info-box {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #f59e0b;
            margin: 15px 0;
            font-size: 13px;
        }
        
        .info-box h3 {
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 16px;
            color: #78350f;
        }
        
        .info-box li {
            margin-bottom: 4px;
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
            text-align: center;
            font-size: 13px;
        }
        
        .status.ready {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.testing {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }

        /* Force mobile view for testing */
        @media (min-width: 481px) {
            .test-container {
                max-width: 350px;
            }
            
            /* Force mobile styles for testing */
            .search-submit-btn {
                min-width: 70px;
                padding: 0 8px;
                font-size: 10px;
            }

            .search-submit-btn span {
                display: none;
            }

            .search-submit-btn {
                width: 30px;
                min-width: 30px;
            }

            .search-submit-btn.loading,
            .search-submit-btn.success {
                width: 30px !important;
                min-width: 30px !important;
                max-width: 30px !important;
                padding: 0 8px !important;
                padding-left: 8px !important;
                padding-right: 8px !important;
                font-size: 10px !important;
            }

            .search-submit-btn.loading span,
            .search-submit-btn.success span {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Quick Search Mobile Test</h1>
        <p>Test nút Quick Search Button trên mobile (480px) với loading và success states</p>
        
        <div class="test-section">
            <h2>📱 Mobile Search Button</h2>
            
            <div class="search-container">
                <input type="text" class="search-input-enhanced" placeholder="Tìm kiếm sản phẩm...">
                
                <button type="submit" class="search-submit-btn" id="search-submit-btn">
                    <div class="btn-content">
                        <div class="btn-spinner" style="display: none;"></div>
                        <div class="btn-success" style="display: none;">
                            <i class="fas fa-check"></i>
                        </div>
                        <i class="fas fa-search btn-search-icon"></i>
                        <span class="btn-text">Tìm kiếm</span>
                    </div>
                </button>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="testDefault()">🔍 Default State</button>
                <button class="control-btn" onclick="testLoading()">⏳ Loading State</button>
                <button class="control-btn" onclick="testSuccess()">✅ Success State</button>
                <button class="control-btn" onclick="resetButton()">🔧 Reset</button>
            </div>
        </div>
        
        <div class="info-box">
            <h3>🎯 Mobile Consistency Check:</h3>
            <ul>
                <li><strong>Default:</strong> 30px width, 8px padding</li>
                <li><strong>Loading:</strong> Same 30px width, 8px padding</li>
                <li><strong>Success:</strong> Same 30px width, 8px padding</li>
                <li><strong>Text:</strong> Hidden trên mobile (480px)</li>
                <li><strong>Icon Only:</strong> Chỉ hiển thị icon trên mobile</li>
            </ul>
        </div>
        
        <div id="status" class="status ready">
            ✨ Sẵn sàng test Quick Search Button mobile
        </div>
    </div>
    
    <script>
        function updateStatus(message, type = 'ready') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function testDefault() {
            const btn = document.getElementById('search-submit-btn');
            resetButton();
            updateStatus('🔍 Default State: 30px width, icon only', 'ready');
        }
        
        function testLoading() {
            const btn = document.getElementById('search-submit-btn');
            updateStatus('⏳ Testing Loading State...', 'testing');
            
            // Reset first
            resetButton();
            
            // Add loading state
            btn.classList.add('loading');
            btn.querySelector('.btn-spinner').style.display = 'flex';
            btn.querySelector('.btn-spinner').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            btn.querySelector('.btn-search-icon').style.display = 'none';
            
            setTimeout(() => {
                updateStatus('⏳ Loading State: Should maintain 30px width', 'testing');
            }, 100);
        }
        
        function testSuccess() {
            const btn = document.getElementById('search-submit-btn');
            updateStatus('✅ Testing Success State...', 'testing');
            
            // Reset first
            resetButton();
            
            // Add success state
            btn.classList.add('success');
            btn.querySelector('.btn-success').style.display = 'flex';
            btn.querySelector('.btn-search-icon').style.display = 'none';
            
            setTimeout(() => {
                updateStatus('✅ Success State: Should maintain 30px width', 'success');
            }, 100);
        }
        
        function resetButton() {
            const btn = document.getElementById('search-submit-btn');
            
            // Remove all states
            btn.classList.remove('loading', 'success');
            
            // Reset elements
            btn.querySelector('.btn-spinner').style.display = 'none';
            btn.querySelector('.btn-spinner').innerHTML = '';
            btn.querySelector('.btn-success').style.display = 'none';
            btn.querySelector('.btn-search-icon').style.display = '';
            
            updateStatus('🔧 Button reset to default state', 'ready');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('🚀 Quick Search Mobile Test Ready', 'ready');
            
            // Log current viewport width
            console.log('Viewport width:', window.innerWidth);
            
            // Check button dimensions
            const btn = document.getElementById('search-submit-btn');
            console.log('Button dimensions:', {
                width: btn.offsetWidth,
                height: btn.offsetHeight,
                padding: getComputedStyle(btn).padding
            });
        });
        
        // Monitor resize
        window.addEventListener('resize', function() {
            console.log('New viewport width:', window.innerWidth);
        });
    </script>
</body>
</html>
