<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagination Active State Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .problem-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-case {
            padding: 20px;
            border-radius: 12px;
            border: 2px solid;
        }
        .demo-case.problem {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-color: #ff6b6b;
            color: #721c24;
        }
        .demo-case.fixed {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-color: #20c997;
            color: #155724;
        }
        .demo-case h4 {
            margin: 0 0 15px 0;
            text-align: center;
            font-size: 1.1rem;
        }
        .demo-scenario {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            font-size: 0.875rem;
        }
        .scenario-step {
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
            color: white;
        }
        .step-icon.click { background: #007bff; }
        .step-icon.wrong { background: #dc3545; }
        .step-icon.right { background: #28a745; }
        .test-scenarios {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .scenario {
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .scenario h5 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1rem;
        }
        .scenario-steps {
            font-size: 0.875rem;
            color: #6c757d;
            line-height: 1.4;
        }
        .expected-result {
            margin-top: 10px;
            padding: 8px 12px;
            background: #d4edda;
            color: #155724;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Pagination Active State Fix</h1>
        <p>Kiểm tra fix cho vấn đề active state của nút Previous/Next</p>
        
        <div class="test-section">
            <h3>🐛 Vấn Đề Đã Sửa</h3>
            <p>So sánh hành vi trước và sau khi sửa:</p>
            
            <div class="problem-demo">
                <div class="demo-case problem">
                    <h4>❌ Trước (Có Lỗi)</h4>
                    
                    <div class="demo-scenario">
                        <strong>Trường hợp 1 - OK:</strong>
                        <div class="scenario-step">
                            <div class="step-icon click">1</div>
                            <span>Trang 1 → Trang 2 → Trang 3</span>
                        </div>
                        <div class="scenario-step">
                            <div class="step-icon right">✓</div>
                            <span>Active state đúng (số trang)</span>
                        </div>
                    </div>
                    
                    <div class="demo-scenario">
                        <strong>Trường hợp 2 - Lỗi:</strong>
                        <div class="scenario-step">
                            <div class="step-icon click">1</div>
                            <span>Trang 4 → Click "Trước" → Trang 3</span>
                        </div>
                        <div class="scenario-step">
                            <div class="step-icon wrong">✗</div>
                            <span>Nút "Trước" bị active (sai!)</span>
                        </div>
                    </div>
                </div>
                
                <div class="demo-case fixed">
                    <h4>✅ Sau (Đã Sửa)</h4>
                    
                    <div class="demo-scenario">
                        <strong>Trường hợp 1 - OK:</strong>
                        <div class="scenario-step">
                            <div class="step-icon click">1</div>
                            <span>Trang 1 → Trang 2 → Trang 3</span>
                        </div>
                        <div class="scenario-step">
                            <div class="step-icon right">✓</div>
                            <span>Active state đúng (số trang)</span>
                        </div>
                    </div>
                    
                    <div class="demo-scenario">
                        <strong>Trường hợp 2 - Fixed:</strong>
                        <div class="scenario-step">
                            <div class="step-icon click">1</div>
                            <span>Trang 4 → Click "Trước" → Trang 3</span>
                        </div>
                        <div class="scenario-step">
                            <div class="step-icon right">✓</div>
                            <span>Trang 3 được active (đúng!)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Scenarios</h3>
            <p>Các tình huống cần test để đảm bảo fix hoạt động:</p>
            
            <div class="test-scenarios">
                <div class="scenario">
                    <h5>📈 Test Tăng Trang</h5>
                    <div class="scenario-steps">
                        1. Bắt đầu từ trang 1<br>
                        2. Click trang 2, 3, 4 lần lượt<br>
                        3. Kiểm tra: Mỗi lần click → số trang tương ứng active
                    </div>
                    <div class="expected-result">
                        ✅ Expected: Trang 1 → 2 → 3 → 4 (số trang active)
                    </div>
                </div>
                
                <div class="scenario">
                    <h5>📉 Test Giảm Trang</h5>
                    <div class="scenario-steps">
                        1. Bắt đầu từ trang 4<br>
                        2. Click trang 3, 2, 1 lần lượt<br>
                        3. Kiểm tra: Mỗi lần click → số trang tương ứng active
                    </div>
                    <div class="expected-result">
                        ✅ Expected: Trang 4 → 3 → 2 → 1 (số trang active)
                    </div>
                </div>
                
                <div class="scenario">
                    <h5>⬅️ Test Nút "Trước"</h5>
                    <div class="scenario-steps">
                        1. Đi đến trang 5<br>
                        2. Click nút "Trước"<br>
                        3. Kiểm tra: Trang 4 phải active (không phải nút "Trước")
                    </div>
                    <div class="expected-result">
                        ✅ Expected: Trang 4 active, nút "Trước" không active
                    </div>
                </div>
                
                <div class="scenario">
                    <h5>➡️ Test Nút "Sau"</h5>
                    <div class="scenario-steps">
                        1. Đi đến trang 3<br>
                        2. Click nút "Sau"<br>
                        3. Kiểm tra: Trang 4 phải active (không phải nút "Sau")
                    </div>
                    <div class="expected-result">
                        ✅ Expected: Trang 4 active, nút "Sau" không active
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Thực Tế</h3>
            <p>Mở products page và test các scenario</p>
            
            <button class="test-button" onclick="openProductsPage()">Mở Products Page</button>
            <button class="test-button" onclick="showDetailedInstructions()">Hướng Dẫn Chi Tiết</button>
            
            <div id="test-result"></div>
        </div>

        <div class="test-section">
            <h3>✅ Checklist Verification</h3>
            <p>Đánh dấu các test case đã pass:</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test1">
                    <span>Tăng trang: 1→2→3→4 (số trang active)</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test2">
                    <span>Giảm trang: 4→3→2→1 (số trang active)</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test3">
                    <span>Nút "Trước": Trang 5→4 (trang 4 active)</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test4">
                    <span>Nút "Sau": Trang 3→4 (trang 4 active)</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test5">
                    <span>Nút Previous/Next không bao giờ active</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <input type="checkbox" id="test6">
                    <span>Chỉ có 1 trang active tại một thời điểm</span>
                </label>
            </div>
            
            <button class="test-button" onclick="checkResults()" style="margin-top: 15px;">Kiểm Tra Kết Quả</button>
            <div id="checklist-result"></div>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function openProductsPage() {
            window.open('http://localhost/noithatbangvu/products.php', '_blank');
            showResult('test-result', '✅ Products page opened in new tab', 'success');
        }

        function showDetailedInstructions() {
            showResult('test-result', `
                📋 <strong>Hướng Dẫn Test Chi Tiết:</strong><br><br>
                
                <strong>🎯 Mục Tiêu:</strong> Kiểm tra active state luôn ở số trang, không bao giờ ở nút Previous/Next<br><br>
                
                <strong>📝 Test Case 1 - Tăng Trang:</strong><br>
                1. Bắt đầu từ trang 1<br>
                2. Click lần lượt: 2 → 3 → 4<br>
                3. Mỗi lần click, kiểm tra số trang tương ứng có active không<br><br>
                
                <strong>📝 Test Case 2 - Giảm Trang:</strong><br>
                1. Đi đến trang 4<br>
                2. Click lần lượt: 3 → 2 → 1<br>
                3. Mỗi lần click, kiểm tra số trang tương ứng có active không<br><br>
                
                <strong>📝 Test Case 3 - Nút "Trước":</strong><br>
                1. Đi đến trang 5<br>
                2. Click nút "Trước" (◀ Trước)<br>
                3. Kiểm tra: Trang 4 active, nút "Trước" KHÔNG active<br><br>
                
                <strong>📝 Test Case 4 - Nút "Sau":</strong><br>
                1. Đi đến trang 3<br>
                2. Click nút "Sau" (Sau ▶)<br>
                3. Kiểm tra: Trang 4 active, nút "Sau" KHÔNG active<br><br>
                
                <strong>🔑 Key Points:</strong><br>
                • Chỉ có số trang được active (background cam)<br>
                • Nút Previous/Next không bao giờ được active<br>
                • Chỉ có 1 element active tại một thời điểm
            `, 'info');
        }

        function checkResults() {
            const checkboxes = document.querySelectorAll('input[id^="test"]');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            
            let resultType = 'info';
            let message = '';
            
            if (checkedCount === totalCount) {
                resultType = 'success';
                message = `🎉 <strong>Perfect Fix!</strong> (${checkedCount}/${totalCount})<br>
                          Active state logic đã được sửa hoàn hảo!<br>
                          Previous/Next buttons không còn bị active sai nữa.`;
            } else if (checkedCount >= totalCount * 0.8) {
                resultType = 'success';
                message = `✅ <strong>Gần Hoàn Hảo!</strong> (${checkedCount}/${totalCount})<br>
                          Hầu hết test cases đã pass, chỉ cần kiểm tra lại một số chi tiết.`;
            } else if (checkedCount >= totalCount * 0.5) {
                resultType = 'warning';
                message = `🔧 <strong>Cần Cải Thiện!</strong> (${checkedCount}/${totalCount})<br>
                          Fix đã có hiệu quả nhưng vẫn còn một số vấn đề.`;
            } else {
                resultType = 'warning';
                message = `❌ <strong>Cần Sửa Thêm!</strong> (${checkedCount}/${totalCount})<br>
                          Logic active state vẫn chưa hoạt động đúng như mong đợi.`;
            }
            
            showResult('checklist-result', message, resultType);
        }

        // Show initial message
        window.addEventListener('load', function() {
            showResult('test-result', '👆 Click "Hướng Dẫn Chi Tiết" để bắt đầu test active state fix', 'info');
        });
    </script>
</body>
</html>
