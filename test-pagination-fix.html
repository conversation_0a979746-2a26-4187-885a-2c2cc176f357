<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Pagination Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .console-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test AJAX Pagination Fix</h1>
        <p>Kiểm tra xem AJAX pagination đã hoạt động đúng chưa (không reload trang)</p>
        
        <div class="test-section">
            <h3>1. Kiểm tra JavaScript Classes</h3>
            <p>Kiểm tra xem các class cần thiết đã được load chưa</p>
            <button class="test-button" onclick="testJavaScriptClasses()">Test JS Classes</button>
            <div id="js-classes-result"></div>
        </div>

        <div class="test-section">
            <h3>2. Kiểm tra DOM Elements</h3>
            <p>Kiểm tra xem các element cần thiết có tồn tại không</p>
            <button class="test-button" onclick="testDOMElements()">Test DOM Elements</button>
            <div id="dom-elements-result"></div>
        </div>

        <div class="test-section">
            <h3>3. Kiểm tra AJAX API</h3>
            <p>Test AJAX endpoint để đảm bảo API hoạt động</p>
            <button class="test-button" onclick="testAjaxAPI()">Test AJAX API</button>
            <div id="ajax-api-result"></div>
        </div>

        <div class="test-section">
            <h3>4. Kiểm tra Event Listeners</h3>
            <p>Kiểm tra xem event listeners có được bind đúng không</p>
            <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
            <div id="event-listeners-result"></div>
        </div>

        <div class="test-section">
            <h3>5. Test Pagination Links</h3>
            <p>Mở trang products và test pagination links</p>
            <button class="test-button" onclick="openProductsPage()">Mở Products Page</button>
            <button class="test-button" onclick="testPaginationLinks()">Test Pagination</button>
            <div id="pagination-test-result"></div>
        </div>

        <div class="test-section">
            <h3>📋 Console Logs</h3>
            <div id="console-logs" class="console-log">
                Console logs sẽ hiển thị ở đây...
            </div>
            <button class="test-button" onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <script>
        // Set BASE_URL for testing
        window.BASE_URL = 'http://localhost/noithatbangvu';
        
        // Console log capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('console-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logsContainer.textContent += logEntry;
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog(args.join(' '), 'warn');
        };

        function clearLogs() {
            document.getElementById('console-logs').textContent = 'Console logs sẽ hiển thị ở đây...\n';
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="result ${type}">
                    <span class="status-indicator status-${type}"></span>
                    ${message}
                </div>
            `;
        }

        function testJavaScriptClasses() {
            console.log('Testing JavaScript Classes...');
            let results = [];
            
            // Test AjaxFilter class
            if (typeof AjaxFilter !== 'undefined') {
                results.push('✅ AjaxFilter class: Available');
            } else {
                results.push('❌ AjaxFilter class: Not found');
            }
            
            // Test AjaxPagination class
            if (typeof AjaxPagination !== 'undefined') {
                results.push('✅ AjaxPagination class: Available');
            } else {
                results.push('❌ AjaxPagination class: Not found');
            }
            
            // Test window objects
            if (window.ajaxFilter) {
                results.push('✅ window.ajaxFilter: Initialized');
            } else {
                results.push('❌ window.ajaxFilter: Not initialized');
            }
            
            const resultType = results.some(r => r.includes('❌')) ? 'error' : 'success';
            showResult('js-classes-result', results.join('<br>'), resultType);
        }

        function testDOMElements() {
            console.log('Testing DOM Elements...');
            let results = [];
            
            // Test productsGrid
            const productsGrid = document.getElementById('productsGrid');
            if (productsGrid) {
                results.push('✅ productsGrid element: Found');
            } else {
                results.push('❌ productsGrid element: Not found');
            }
            
            // Test pagination section
            const paginationSection = document.querySelector('.pagination-section');
            if (paginationSection) {
                results.push('✅ pagination-section: Found');
            } else {
                results.push('❌ pagination-section: Not found');
            }
            
            // Test pagination links
            const paginationLinks = document.querySelectorAll('.ajax-pagination-link');
            if (paginationLinks.length > 0) {
                results.push(`✅ pagination links: Found ${paginationLinks.length} links`);
            } else {
                results.push('❌ pagination links: Not found');
            }
            
            const resultType = results.some(r => r.includes('❌')) ? 'error' : 'success';
            showResult('dom-elements-result', results.join('<br>'), resultType);
        }

        async function testAjaxAPI() {
            console.log('Testing AJAX API...');
            showResult('ajax-api-result', 'Testing API endpoint...', 'info');
            
            try {
                const response = await fetch(`${window.BASE_URL}/ajax-products.php?page=1&items_per_page=12`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        showResult('ajax-api-result', 
                            `✅ API Test Successful<br>
                            Status: ${response.status}<br>
                            Products: ${data.data.products_count || 0}<br>
                            Total: ${data.data.total_products || 0}`, 
                            'success'
                        );
                    } else {
                        showResult('ajax-api-result', `❌ API Error: ${data.error}`, 'error');
                    }
                } else {
                    showResult('ajax-api-result', `❌ HTTP Error: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('ajax-api-result', `❌ Network Error: ${error.message}`, 'error');
            }
        }

        function testEventListeners() {
            console.log('Testing Event Listeners...');
            let results = [];
            
            // Check if pagination links have event listeners
            const paginationLinks = document.querySelectorAll('.ajax-pagination-link');
            if (paginationLinks.length > 0) {
                results.push(`✅ Found ${paginationLinks.length} pagination links`);
                
                // Test if clicking is prevented (should not navigate)
                let hasPreventDefault = false;
                paginationLinks.forEach(link => {
                    const clonedLink = link.cloneNode(true);
                    clonedLink.addEventListener('click', (e) => {
                        if (e.defaultPrevented) {
                            hasPreventDefault = true;
                        }
                    });
                });
                
                if (hasPreventDefault) {
                    results.push('✅ Event listeners: Properly preventing default');
                } else {
                    results.push('⚠️ Event listeners: May not be preventing default');
                }
            } else {
                results.push('❌ No pagination links found to test');
            }
            
            const resultType = results.some(r => r.includes('❌')) ? 'error' : 'success';
            showResult('event-listeners-result', results.join('<br>'), resultType);
        }

        function openProductsPage() {
            console.log('Opening products page...');
            window.open(`${window.BASE_URL}/products.php`, '_blank');
            showResult('pagination-test-result', '✅ Products page opened in new tab', 'success');
        }

        function testPaginationLinks() {
            console.log('Testing pagination links...');
            showResult('pagination-test-result', 
                `📋 Manual Test Instructions:<br>
                1. Mở products page trong tab mới<br>
                2. Scroll xuống phần pagination<br>
                3. Click vào số trang (2, 3, etc.)<br>
                4. Kiểm tra xem trang có reload không<br>
                5. Nếu không reload và sản phẩm thay đổi = ✅ Success<br>
                6. Nếu trang reload = ❌ Still has issues`, 
                'info'
            );
        }

        // Auto-run basic tests when page loads
        window.addEventListener('load', function() {
            console.log('Page loaded, running basic tests...');
            setTimeout(() => {
                testJavaScriptClasses();
                testDOMElements();
            }, 1000);
        });
    </script>
</body>
</html>
