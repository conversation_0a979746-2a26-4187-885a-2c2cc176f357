<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Loading Effects - Mobile Filter Modal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/mobile-filter-modal.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #f1f5f9;
            border-radius: 15px;
            background: linear-gradient(135deg, #fefbf3, #fef7ed);
        }
        
        .test-button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            margin: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }
        
        .demo-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        /* Demo modal footer */
        .demo-modal-footer {
            display: flex;
            gap: 16px;
            padding: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #fefbf3 100%);
            border-radius: 12px;
            border: 1px solid #fed7aa;
            margin: 20px 0;
        }
        
        .demo-btn {
            flex: 1;
            padding: 14px 20px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            letter-spacing: -0.025em;
        }
        
        .demo-btn-secondary {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            color: #475569;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .demo-btn-primary {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            border: 1px solid #ea580c;
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
        }
        
        .demo-btn-secondary:hover {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            color: #334155;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .demo-btn-primary:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            box-shadow: 0 8px 20px rgba(249, 115, 22, 0.4);
        }
        
        .demo-btn .btn-spinner {
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .log {
            background: #1e293b;
            color: #10b981;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            border: 2px solid #334155;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 600;
            text-align: center;
        }
        
        .status.info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 2px solid #3b82f6;
        }
        
        .status.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 2px solid #22c55e;
        }
        
        .status.warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
            border: 2px solid #f59e0b;
        }
        
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 800;
        }
        
        h2 {
            color: #374151;
            border-bottom: 3px solid #f97316;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        p {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .highlight {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #f59e0b;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Test Loading Effects</h1>
        <p style="text-align: center; font-size: 1.1rem; color: #6b7280;">
            Test các hiệu ứng loading mới cho Mobile Filter Modal
        </p>
        
        <div class="test-section">
            <h2>🎯 Demo Buttons với Loading Effects</h2>
            <p>Các nút demo với hiệu ứng loading giống như trong modal thật:</p>
            
            <div class="demo-modal-footer">
                <button class="demo-btn demo-btn-secondary" id="demoResetBtn">
                    <i class="fas fa-refresh"></i>
                    <span>Đặt lại</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
                <button class="demo-btn demo-btn-primary" id="demoApplyBtn">
                    <i class="fas fa-search"></i>
                    <span>Áp dụng bộ lọc</span>
                    <div class="btn-spinner" style="display: none;"></div>
                </button>
            </div>
            
            <div class="demo-buttons">
                <button class="test-button" onclick="testApplyLoading()">
                    🎯 Test Apply Loading
                </button>
                <button class="test-button" onclick="testResetLoading()">
                    🔄 Test Reset Loading
                </button>
                <button class="test-button" onclick="testBothLoading()">
                    ⚡ Test Both Loading
                </button>
                <button class="test-button" onclick="resetButtons()">
                    🔧 Reset Buttons
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 Loading States Info</h2>
            <div class="highlight">
                <strong>🎨 Enhanced Loading Effects:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Shimmer Effect:</strong> Gradient animation trên nút</li>
                    <li><strong>Pulse Animation:</strong> Box-shadow pulse với màu phù hợp</li>
                    <li><strong>Text Change:</strong> "Đang áp dụng..." / "Đang đặt lại..."</li>
                    <li><strong>Icon Transition:</strong> Fade out icon gốc, fade in spinner</li>
                    <li><strong>Success Animation:</strong> Checkmark xanh khi hoàn thành</li>
                    <li><strong>Haptic Feedback:</strong> Scale animation khi click</li>
                </ul>
            </div>
            
            <div id="statusDisplay" class="status info">
                ✨ Sẵn sàng test loading effects
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Controls</h2>
            <div class="demo-buttons">
                <button class="test-button" onclick="clearLog()">🗑️ Clear Log</button>
                <button class="test-button" onclick="showTechnicalInfo()">🔧 Technical Info</button>
            </div>
            
            <div id="testLog" class="log"></div>
        </div>
    </div>
    
    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('statusDisplay');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            updateStatus('✨ Log cleared, ready for new tests', 'info');
        }
        
        function showTechnicalInfo() {
            log('🔧 TECHNICAL INFO:');
            log('- Loading duration: 1000ms minimum');
            log('- Success animation: 800ms');
            log('- Shimmer effect: 1.5s infinite');
            log('- Pulse animation: 2s infinite');
            log('- Icon transitions: 0.2-0.3s ease');
            log('- Haptic feedback: 150ms scale');
            updateStatus('🔧 Technical information logged', 'info');
        }
        
        function testApplyLoading() {
            const btn = document.getElementById('demoApplyBtn');
            log('🎯 Testing Apply Button Loading...');
            updateStatus('🎯 Testing Apply Button Loading...', 'info');
            
            showApplyLoading(btn);
            
            setTimeout(() => {
                hideApplyLoading(btn);
                log('✅ Apply Button Loading Test Complete');
                updateStatus('✅ Apply Button Loading Test Complete', 'success');
            }, 2000);
        }
        
        function testResetLoading() {
            const btn = document.getElementById('demoResetBtn');
            log('🔄 Testing Reset Button Loading...');
            updateStatus('🔄 Testing Reset Button Loading...', 'info');
            
            showResetLoading(btn);
            
            setTimeout(() => {
                hideResetLoading(btn);
                log('✅ Reset Button Loading Test Complete');
                updateStatus('✅ Reset Button Loading Test Complete', 'success');
            }, 2000);
        }
        
        function testBothLoading() {
            log('⚡ Testing Both Buttons Loading...');
            updateStatus('⚡ Testing Both Buttons Loading...', 'warning');
            
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            showApplyLoading(applyBtn);
            showResetLoading(resetBtn);
            
            setTimeout(() => {
                hideApplyLoading(applyBtn);
                hideResetLoading(resetBtn);
                log('✅ Both Buttons Loading Test Complete');
                updateStatus('✅ Both Buttons Loading Test Complete', 'success');
            }, 2500);
        }
        
        function resetButtons() {
            const applyBtn = document.getElementById('demoApplyBtn');
            const resetBtn = document.getElementById('demoResetBtn');
            
            // Reset apply button
            applyBtn.disabled = false;
            applyBtn.classList.remove('loading');
            const applyText = applyBtn.querySelector('span');
            const applyIcon = applyBtn.querySelector('i:not(.btn-spinner i)');
            const applySpinner = applyBtn.querySelector('.btn-spinner');

            if (applyText) {
                applyText.textContent = 'Áp dụng bộ lọc';
                applyText.style.opacity = '1';
                applyText.style.display = 'inline';
            }
            if (applyIcon) {
                applyIcon.style.opacity = '';
                applyIcon.style.transform = '';
                applyIcon.style.transition = '';
            }
            if (applySpinner) {
                applySpinner.style.display = 'none';
            }
            applyBtn.style.background = '';

            // Reset reset button
            resetBtn.disabled = false;
            resetBtn.classList.remove('loading');
            const resetText = resetBtn.querySelector('span');
            const resetIcon = resetBtn.querySelector('i:not(.btn-spinner i)');
            const resetSpinner = resetBtn.querySelector('.btn-spinner');

            if (resetText) {
                resetText.textContent = 'Đặt lại';
                resetText.style.opacity = '1';
                resetText.style.display = 'inline';
            }
            if (resetIcon) {
                resetIcon.style.opacity = '';
                resetIcon.style.transform = '';
                resetIcon.style.transition = '';
            }
            if (resetSpinner) {
                resetSpinner.style.display = 'none';
            }
            resetBtn.style.background = '';
            
            log('🔧 Buttons reset to original state');
            updateStatus('🔧 Buttons reset to original state', 'info');
        }
        
        // Apply loading functions
        function showApplyLoading(btn) {
            btn.disabled = true;
            btn.classList.add('loading');

            const btnText = btn.querySelector('span');
            const btnIcon = btn.querySelector('i:not(.btn-spinner i)');
            const btnSpinner = btn.querySelector('.btn-spinner');

            if (btnText) {
                btnText.textContent = 'Đang áp dụng...';
                btnText.style.opacity = '1';
                btnText.style.display = 'inline';
            }
            if (btnIcon) {
                btnIcon.style.opacity = '0';
                btnIcon.style.transform = 'scale(0)';
                btnIcon.style.transition = 'all 0.2s ease';
            }
            if (btnSpinner) {
                btnSpinner.style.display = 'inline-flex';
                btnSpinner.innerHTML = '<i class="fas fa-spinner"></i>';
            }

            btn.style.transform = 'scale(0.98)';
            setTimeout(() => btn.style.transform = '', 150);
        }
        
        function hideApplyLoading(btn) {
            const btnSpinner = btn.querySelector('.btn-spinner');
            if (btnSpinner) {
                btnSpinner.innerHTML = '<i class="fas fa-check"></i>';
                btnSpinner.style.color = '#10b981';
                btn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.classList.remove('loading');

                const btnText = btn.querySelector('span');
                const btnIcon = btn.querySelector('i:not(.btn-spinner i)');

                if (btnText) {
                    btnText.textContent = 'Áp dụng bộ lọc';
                    btnText.style.opacity = '1';
                    btnText.style.display = 'inline';
                }
                if (btnIcon) {
                    btnIcon.style.opacity = '';
                    btnIcon.style.transform = '';
                    btnIcon.style.transition = '';
                }
                if (btnSpinner) {
                    btnSpinner.style.display = 'none';
                    btnSpinner.style.color = '';
                }
                btn.style.background = '';
            }, 800);
        }
        
        // Reset loading functions
        function showResetLoading(btn) {
            btn.disabled = true;
            btn.classList.add('loading');

            const btnText = btn.querySelector('span');
            const btnIcon = btn.querySelector('i:not(.btn-spinner i)');
            const btnSpinner = btn.querySelector('.btn-spinner');

            if (btnText) {
                btnText.textContent = 'Đang đặt lại...';
                btnText.style.opacity = '1';
                btnText.style.display = 'inline';
            }
            if (btnIcon) {
                btnIcon.style.opacity = '0';
                btnIcon.style.transform = 'scale(0) rotate(180deg)';
                btnIcon.style.transition = 'all 0.3s ease';
            }
            if (btnSpinner) {
                btnSpinner.style.display = 'inline-flex';
                btnSpinner.innerHTML = '<i class="fas fa-spinner"></i>';
            }

            btn.style.transform = 'scale(0.98)';
            setTimeout(() => btn.style.transform = '', 150);
        }
        
        function hideResetLoading(btn) {
            const btnSpinner = btn.querySelector('.btn-spinner');
            if (btnSpinner) {
                btnSpinner.innerHTML = '<i class="fas fa-check"></i>';
                btnSpinner.style.color = '#10b981';
                btn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                btn.style.color = 'white';
            }

            setTimeout(() => {
                btn.disabled = false;
                btn.classList.remove('loading');

                const btnText = btn.querySelector('span');
                const btnIcon = btn.querySelector('i:not(.btn-spinner i)');

                if (btnText) {
                    btnText.textContent = 'Đặt lại';
                    btnText.style.opacity = '1';
                    btnText.style.display = 'inline';
                }
                if (btnIcon) {
                    btnIcon.style.opacity = '';
                    btnIcon.style.transform = '';
                    btnIcon.style.transition = '';
                }
                if (btnSpinner) {
                    btnSpinner.style.display = 'none';
                    btnSpinner.style.color = '';
                }
                btn.style.background = '';
                btn.style.color = '';
            }, 800);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Loading Effects Test Environment Ready');
            updateStatus('🚀 Ready to test loading effects', 'success');
        });
    </script>
</body>
</html>
