/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./**/*.php', './assets/js/**/*.js'],
  theme: {
    extend: {
      screens: {
        xs: '400px',
      },
      colors: {
        primary: {
          DEFAULT: '#F37321',
          dark: '#D65A0F',
          light: '#FF8A3D',
          lighter: '#FFA66B',
          lightest: '#FFD0AD',
          'ultra-light': '#FFF4EC',
        },
        secondary: {
          DEFAULT: '#2A3B47',
          dark: '#1E2A32',
          light: '#435868',
        },
        accent: {
          DEFAULT: '#4CAF50',
          dark: '#388E3C',
          light: '#81C784',
        },
      },
      boxShadow: {
        'glow-light': '0 0 15px rgba(255, 255, 255, 0.3)',
        'glow-primary': '0 0 15px rgba(243, 115, 33, 0.3)',
        'sm-custom': '0 2px 5px rgba(0, 0, 0, 0.08)',
        'md-custom': '0 4px 8px rgba(0, 0, 0, 0.12)',
        'lg-custom': '0 8px 16px rgba(0, 0, 0, 0.16)',
      },
      fontFamily: {
        sans: ['Montserrat', 'Roboto', 'sans-serif'],
      },
      spacing: {
        72: '18rem',
        84: '21rem',
        96: '24rem',
        4.5: '1.125rem',
        5.5: '1.375rem',
      },
      borderRadius: {
        xl: '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
        large: '1rem',
      },
      fontSize: {
        '5.5xl': '3.5rem',
      },
    },
  },
  plugins: [],
};
