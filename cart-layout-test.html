<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cart Layout - Nội Thất Băng <PERSON>ũ</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Reset và base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f9fafb;
            padding: 2rem 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Professional Cart Layout Styles - Desktop Only */
        @media (min-width: 768px) {
            .cart-item-layout-professional {
                display: grid;
                grid-template-columns: 20px 160px 1fr;
                gap: 1.5rem;
                align-items: flex-start;
                padding: 1.5rem;
            }

            .checkbox-column-professional {
                display: flex;
                align-items: flex-start;
                justify-content: center;
                padding-top: 0.5rem;
            }

            .professional-checkbox-style {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                width: 18px !important;
                height: 18px !important;
                border: 2px solid #3b82f6 !important;
                border-radius: 4px !important;
                background: white !important;
                cursor: pointer !important;
                position: relative !important;
                transition: all 0.2s ease !important;
            }

            .professional-checkbox-style:checked {
                background: #3b82f6 !important;
                border-color: #3b82f6 !important;
            }

            .professional-checkbox-style:checked::after {
                content: '' !important;
                position: absolute !important;
                top: 2px !important;
                left: 5px !important;
                width: 5px !important;
                height: 8px !important;
                border: solid white !important;
                border-width: 0 2px 2px 0 !important;
                transform: rotate(45deg) !important;
            }

            .professional-checkbox-style:hover {
                border-color: #60a5fa !important;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
            }

            .image-column-professional {
                position: relative;
                display: flex;
                justify-content: center;
            }

            .product-image-container-professional {
                width: 160px;
                height: 160px;
                border-radius: 8px;
                overflow: hidden;
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .product-image-professional {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            .product-image-professional:hover {
                transform: scale(1.05);
            }

            .product-image-placeholder-professional {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f3f4f6;
            }

            .content-column-professional {
                display: flex;
                flex-direction: column;
                gap: 1.25rem;
                min-width: 0;
            }

            .product-info-row-professional {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .product-main-info-professional {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .product-details-professional {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                min-width: 0;
            }

            .product-title-professional {
                font-size: 1.125rem;
                font-weight: 600;
                color: #1f2937;
                line-height: 1.4;
                margin: 0;
                word-wrap: break-word;
            }

            .product-title-professional a {
                color: inherit;
                text-decoration: none;
                transition: color 0.2s ease;
            }

            .product-title-professional a:hover {
                color: #F37321;
            }

            .product-price-professional {
                font-size: 1.25rem;
                font-weight: 700;
                color: #F37321;
                margin: 0;
                display: flex;
                align-items: baseline;
                gap: 0.5rem;
            }

            .original-price-professional {
                font-size: 0.875rem;
                color: #9ca3af;
                text-decoration: line-through;
                font-weight: 500;
            }

            .discount-total-section-professional {
                display: grid;
                grid-template-columns: 1fr auto;
                gap: 2rem;
                align-items: center;
            }

            .discount-section-professional {
                display: flex;
                gap: 0.75rem;
                align-items: center;
                flex-wrap: wrap;
            }

            .discount-badge-professional {
                background: #fef2f2;
                color: #dc2626;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                font-weight: 500;
                font-size: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.375rem;
                border: 1px solid #fecaca;
            }

            .savings-badge-professional {
                background: #f0fdf4;
                color: #16a34a;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                font-weight: 500;
                font-size: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.375rem;
                border: 1px solid #bbf7d0;
            }

            .best-price-badge-professional {
                background: #f0f9ff;
                color: #0284c7;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                font-weight: 500;
                font-size: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.375rem;
                border: 1px solid #bae6fd;
            }

            .total-section-professional {
                text-align: right;
                flex-shrink: 0;
            }

            .total-label-professional {
                font-size: 0.8rem;
                color: #6b7280;
                margin-bottom: 0.25rem;
                font-weight: 500;
            }

            .total-amount-professional {
                font-size: 1.375rem;
                font-weight: 700;
                color: #1f2937;
                letter-spacing: -0.025em;
            }

            .controls-row-professional {
                display: grid;
                grid-template-columns: auto 1fr auto;
                gap: 1.5rem;
                align-items: center;
                padding-top: 1rem;
                border-top: 1px solid #f3f4f6;
            }

            .quantity-section-professional {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .quantity-label-professional {
                font-size: 0.875rem;
                color: #6b7280;
                font-weight: 500;
                white-space: nowrap;
            }

            .quantity-controls-professional {
                display: flex;
                align-items: center;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                overflow: hidden;
                background: white;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            }

            .qty-btn-professional {
                width: 32px;
                height: 32px;
                border: none;
                background: white;
                color: #6b7280;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.875rem;
                font-weight: 600;
            }

            .qty-btn-professional:hover {
                background: #FFF4EC;
                color: #F37321;
            }

            .qty-btn-professional:active {
                background: #f3f4f6;
                transform: scale(0.95);
            }

            .qty-input-professional {
                width: 50px !important;
                height: 32px !important;
                border: none !important;
                border-left: 1px solid #d1d5db !important;
                border-right: 1px solid #d1d5db !important;
                text-align: center !important;
                font-weight: 600 !important;
                color: #1f2937 !important;
                font-size: 0.875rem !important;
                background: white !important;
            }

            .qty-input-professional:focus {
                outline: none !important;
                background: #fafafa !important;
                border-left-color: #F37321 !important;
                border-right-color: #F37321 !important;
            }

            .spacer-professional {
                flex: 1;
            }

            .action-buttons-professional {
                display: flex;
                gap: 0.5rem;
            }

            .action-btn-professional {
                padding: 0.5rem 0.875rem;
                border-radius: 6px;
                border: 1px solid;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 0.375rem;
                font-size: 0.8rem;
                text-decoration: none;
                min-width: 85px;
                justify-content: center;
                height: 32px;
            }

            .update-btn-professional {
                background: white;
                color: #F37321;
                border-color: #F37321;
            }

            .update-btn-professional:hover {
                background: #FFF4EC;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
            }

            .delete-btn-professional {
                background: white;
                color: #dc2626;
                border-color: #dc2626;
            }

            .delete-btn-professional:hover {
                background: #fef2f2;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
            }
        }

        /* Cart item container */
        .cart-item-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .cart-item-container:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 2rem; color: #1f2937;">Test Cart Layout Professional</h1>
        
        <!-- Cart Item 1 - Có giảm giá -->
        <div class="cart-item-container">
            <div class="cart-item-layout-professional">
                <!-- Checkbox Column -->
                <div class="checkbox-column-professional">
                    <input type="checkbox" class="professional-checkbox-style" checked>
                </div>

                <!-- Image Column -->
                <div class="image-column-professional">
                    <div class="product-image-container-professional">
                        <img src="https://via.placeholder.com/160x160/F37321/ffffff?text=SOFA"
                             alt="Sofa cao cấp"
                             class="product-image-professional">
                    </div>
                </div>

                <!-- Content Column -->
                <div class="content-column-professional">
                    <!-- Product Info Row -->
                    <div class="product-info-row-professional">
                        <div class="product-main-info-professional">
                            <div class="product-details-professional">
                                <h3 class="product-title-professional">
                                    <a href="#">Sofa Băng Vũ Luxury Collection 3 Chỗ Ngồi</a>
                                </h3>
                                <div class="product-price-professional">
                                    15,500,000₫
                                    <span class="original-price-professional">19,375,000₫</span>
                                </div>
                            </div>
                        </div>

                        <div class="discount-total-section-professional">
                            <div class="discount-section-professional">
                                <div class="discount-badge-professional">
                                    <i class="fas fa-tag"></i>
                                    Giảm 20%
                                </div>
                                <div class="savings-badge-professional">
                                    <i class="fas fa-piggy-bank"></i>
                                    Tiết kiệm 7,750,000₫
                                </div>
                            </div>

                            <div class="total-section-professional">
                                <div class="total-label-professional">Tổng tiền sản phẩm</div>
                                <div class="total-amount-professional">31,000,000₫</div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Row -->
                    <div class="controls-row-professional">
                        <div class="quantity-section-professional">
                            <span class="quantity-label-professional">Số lượng:</span>
                            <div class="quantity-controls-professional">
                                <button class="qty-btn-professional">−</button>
                                <input type="number" value="2" class="qty-input-professional" min="1">
                                <button class="qty-btn-professional">+</button>
                            </div>
                        </div>

                        <div class="spacer-professional"></div>

                        <div class="action-buttons-professional">
                            <button class="action-btn-professional update-btn-professional">
                                <i class="fas fa-sync-alt"></i>
                                Cập nhật
                            </button>
                            <button class="action-btn-professional delete-btn-professional">
                                <i class="fas fa-trash-alt"></i>
                                Xóa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cart Item 2 - Không giảm giá -->
        <div class="cart-item-container">
            <div class="cart-item-layout-professional">
                <!-- Checkbox Column -->
                <div class="checkbox-column-professional">
                    <input type="checkbox" class="professional-checkbox-style" checked>
                </div>

                <!-- Image Column -->
                <div class="image-column-professional">
                    <div class="product-image-container-professional">
                        <img src="https://via.placeholder.com/160x160/8B4513/ffffff?text=TABLE"
                             alt="Bàn ăn gỗ sồi"
                             class="product-image-professional">
                    </div>
                </div>

                <!-- Content Column -->
                <div class="content-column-professional">
                    <!-- Product Info Row -->
                    <div class="product-info-row-professional">
                        <div class="product-main-info-professional">
                            <div class="product-details-professional">
                                <h3 class="product-title-professional">
                                    <a href="#">Bàn Ăn Gỗ Sồi Tự Nhiên Premium 6 Chỗ Ngồi</a>
                                </h3>
                                <div class="product-price-professional">8,500,000₫</div>
                            </div>
                        </div>

                        <div class="discount-total-section-professional">
                            <div class="discount-section-professional">
                                <div class="best-price-badge-professional">
                                    <i class="fas fa-star"></i>
                                    Giá tốt nhất
                                </div>
                            </div>

                            <div class="total-section-professional">
                                <div class="total-label-professional">Tổng tiền sản phẩm</div>
                                <div class="total-amount-professional">8,500,000₫</div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Row -->
                    <div class="controls-row-professional">
                        <div class="quantity-section-professional">
                            <span class="quantity-label-professional">Số lượng:</span>
                            <div class="quantity-controls-professional">
                                <button class="qty-btn-professional">−</button>
                                <input type="number" value="1" class="qty-input-professional" min="1">
                                <button class="qty-btn-professional">+</button>
                            </div>
                        </div>

                        <div class="spacer-professional"></div>

                        <div class="action-buttons-professional">
                            <button class="action-btn-professional update-btn-professional">
                                <i class="fas fa-sync-alt"></i>
                                Cập nhật
                            </button>
                            <button class="action-btn-professional delete-btn-professional">
                                <i class="fas fa-trash-alt"></i>
                                Xóa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
