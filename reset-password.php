<?php
// Bắt đầu session và include các file cần thiết
require_once 'includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Đặt lại mật khẩu';
$page_description = 'Đặt lại mật khẩu tài khoản của bạn tại ' . SITE_NAME;

// Khởi tạo biến
$token = isset($_GET['token']) ? $_GET['token'] : '';
$error = '';
$success = '';
$user = null;

// Kiểm tra token
if (empty($token)) {
    $error = 'Liên kết đặt lại mật khẩu không hợp lệ.';
} else {
    // Kiểm tra token trong cơ sở dữ liệu
    try {
        $stmt = $conn->prepare("SELECT * FROM users WHERE reset_token = :token AND reset_token_expires_at > NOW()");
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $error = 'Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.';
        } else {
            $user = $stmt->fetch();
        }
    } catch (PDOException $e) {
        $error = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
        error_log("Lỗi khi kiểm tra token reset mật khẩu: " . $e->getMessage());
    }
}

// Xử lý form đặt lại mật khẩu
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_password') {
    // Kiểm tra CSRF token
    if (!check_csrf_token($_POST['csrf_token'])) {
        $error = 'Phiên làm việc đã hết hạn. Vui lòng thử lại.';
    } else {
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Kiểm tra mật khẩu
        if (empty($password)) {
            $error = 'Vui lòng nhập mật khẩu mới.';
        } elseif (strlen($password) < 6) {
            $error = 'Mật khẩu phải có ít nhất 6 ký tự.';
        } elseif ($password !== $confirm_password) {
            $error = 'Mật khẩu xác nhận không khớp.';
        } else {
            // Cập nhật mật khẩu
            try {
                // Mã hóa mật khẩu mới
                $hashed_password = password_hash($password, PASSWORD_DEFAULT, ['cost' => HASH_COST]);
                
                // Cập nhật mật khẩu và xóa token
                $stmt = $conn->prepare("UPDATE users SET password = :password, reset_token = NULL, reset_token_expires_at = NULL WHERE id = :id");
                $stmt->bindParam(':password', $hashed_password);
                $stmt->bindParam(':id', $user['id']);
                $stmt->execute();
                
                $success = 'Đặt lại mật khẩu thành công! Bạn có thể đăng nhập với mật khẩu mới.';
                
                // Lưu thông tin đăng nhập để điền sẵn vào form đăng nhập
                $_SESSION['registered_username'] = $user['username'];
                
                // Chuyển hướng đến trang đăng nhập sau 3 giây
                header("Refresh: 3; URL=" . BASE_URL . "/auth.php");
            } catch (PDOException $e) {
                $error = 'Đã xảy ra lỗi khi cập nhật mật khẩu. Vui lòng thử lại sau.';
                error_log("Lỗi khi cập nhật mật khẩu: " . $e->getMessage());
            }
        }
    }
}

// Include header
include_once 'partials/header.php';
?>

<!-- Đặt lại mật khẩu -->
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="py-4 px-6 bg-primary text-white text-center">
                <h2 class="text-2xl font-bold">Đặt lại mật khẩu</h2>
            </div>
            
            <div class="p-6">
                <?php if (!empty($error)): ?>
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                    <p><?php echo $error; ?></p>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6">
                    <p><?php echo $success; ?></p>
                    <p class="mt-2">Bạn sẽ được chuyển hướng đến trang đăng nhập trong 3 giây...</p>
                </div>
                <?php endif; ?>
                
                <?php if (empty($error) && empty($success) && $user): ?>
                <form action="<?php echo BASE_URL; ?>/reset-password.php?token=<?php echo htmlspecialchars($token); ?>" method="POST" class="space-y-4">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="reset_password">
                    
                    <div>
                        <label for="password" class="block text-gray-700 font-medium mb-2">Mật khẩu mới</label>
                        <div class="relative">
                            <input type="password" id="password" name="password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600" onclick="togglePassword('password')" title="Hiện mật khẩu">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <label for="confirm_password" class="block text-gray-700 font-medium mb-2">Xác nhận mật khẩu mới</label>
                        <div class="relative">
                            <input type="password" id="confirm_password" name="confirm_password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600" onclick="togglePassword('confirm_password')" title="Hiện mật khẩu">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
                            Đặt lại mật khẩu
                        </button>
                    </div>
                </form>
                <?php elseif (empty($success)): ?>
                <div class="text-center">
                    <p class="mb-4">Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.</p>
                    <a href="<?php echo BASE_URL; ?>/auth.php" class="text-primary hover:text-primary-dark">Quay lại trang đăng nhập</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
    // Hàm hiển thị/ẩn mật khẩu
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.nextElementSibling.querySelector('i');
        const toggleButton = input.nextElementSibling;

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
            toggleButton.setAttribute('title', 'Ẩn mật khẩu');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
            toggleButton.setAttribute('title', 'Hiện mật khẩu');
        }
    }
</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
