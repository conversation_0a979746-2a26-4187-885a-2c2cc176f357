<?php
// Thiết lập tiêu đề trang
$page_title = '<PERSON><PERSON> mục sản phẩm';
$page_description = 'Tất cả danh mục sản phẩm nội thất cao cấp tại Nội Thất Băng Vũ';

// Include header
include_once 'partials/header.php';

// Lấy tất cả danh mục có trạng thái hiển thị
$all_categories = get_categories(null, 1);

// Phân loại danh mục cha và danh mục con
$parent_categories = [];
$child_categories = [];

foreach ($all_categories as $category) {
    if (empty($category['parent_id'])) {
        $parent_categories[$category['id']] = $category;
        $parent_categories[$category['id']]['children'] = [];
    } else {
        $child_categories[] = $category;
    }
}

// Gán danh mục con vào danh mục cha tương ứng
foreach ($child_categories as $child) {
    if (isset($parent_categories[$child['parent_id']])) {
        $parent_categories[$child['parent_id']]['children'][] = $child;
    }
}
?>

<!-- Breadcrumb -->
<div class="bg-gray-100 py-3">
    <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-gray-600">
            <a href="<?php echo BASE_URL; ?>" class="hover:text-primary">Trang chủ</a>
            <span class="mx-2">/</span>
            <span class="text-gray-800">Danh mục sản phẩm</span>
        </div>
    </div>
</div>

<!-- Categories Header -->
<div class="py-10 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold mb-4 relative inline-block">
                <span class="relative z-10">Danh Mục Sản Phẩm</span>
                <span class="absolute bottom-0 left-0 w-full h-3 bg-primary/10 -z-10 transform -rotate-1"></span>
            </h1>
            <p class="text-gray-600 max-w-2xl mx-auto">Khám phá các danh mục sản phẩm nội thất cao cấp của chúng tôi, được thiết kế để mang đến không gian sống hoàn hảo cho ngôi nhà của bạn</p>
        </div>

        <!-- Parent Categories -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
            <?php foreach ($parent_categories as $category): ?>
            <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-500 h-full border border-gray-100 transform hover:-translate-y-2 group">
                <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" class="block">
                    <div class="aspect-w-4 aspect-h-3 overflow-hidden relative">
                        <?php if (!empty($category['image'])): ?>
                        <img src="<?php echo BASE_URL; ?>/uploads/categories/<?php echo $category['image']; ?>"
                            alt="<?php echo htmlspecialchars($category['name']); ?>"
                            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                        <?php else: ?>
                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-folder text-gray-400 text-4xl"></i>
                        </div>
                        <?php endif; ?>
                        <!-- Overlay gradient -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                </a>
                
                <div class="p-5">
                    <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" class="block">
                        <h2 class="text-xl font-semibold text-gray-800 mb-3 group-hover:text-primary transition-colors duration-300"><?php echo htmlspecialchars($category['name']); ?></h2>
                    </a>
                    
                    <?php if (!empty($category['description'])): ?>
                    <p class="text-gray-600 text-sm line-clamp-2 mb-4"><?php echo htmlspecialchars($category['description']); ?></p>
                    <?php endif; ?>
                    
                    <?php
                    // Đếm số sản phẩm trong danh mục
                    $product_count = count_products($category['id']);
                    ?>
                    <div class="flex justify-between items-center">
                        <div class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium group-hover:bg-primary/10 transition-colors duration-300">
                            <i class="fas fa-box-open mr-2 text-primary"></i>
                            <span><?php echo $product_count; ?> sản phẩm</span>
                        </div>
                        
                        <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" class="text-primary hover:text-primary-dark text-sm font-medium transition-colors duration-300">
                            Xem chi tiết <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                    
                    <?php if (!empty($category['children'])): ?>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Danh mục con:</h3>
                        <div class="flex flex-wrap gap-2">
                            <?php foreach ($category['children'] as $child): ?>
                            <a href="<?php echo BASE_URL; ?>/category.php?slug=<?php echo $child['slug']; ?>" class="text-sm px-3 py-1 bg-gray-50 hover:bg-primary/5 text-gray-600 hover:text-primary rounded-full transition-colors duration-300">
                                <?php echo htmlspecialchars($child['name']); ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="py-16 bg-gray-100">
    <div class="container mx-auto px-4">
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="md:flex">
                <div class="md:w-2/3 p-8 md:p-12">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">Bạn cần tư vấn về sản phẩm?</h2>
                    <p class="text-gray-600 mb-6">Đội ngũ chuyên gia của chúng tôi luôn sẵn sàng hỗ trợ bạn lựa chọn sản phẩm nội thất phù hợp nhất với không gian sống của bạn.</p>
                    <a href="<?php echo BASE_URL; ?>/contact.php" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition duration-300 transform hover:-translate-y-1 hover:shadow-lg">
                        <i class="fas fa-headset mr-2"></i>
                        <span>Liên hệ tư vấn ngay</span>
                    </a>
                </div>
                <div class="md:w-1/3 bg-gray-200 flex items-center justify-center">
                    <img src="<?php echo BASE_URL; ?>/assets/images/contact-cta.jpg" alt="Tư vấn sản phẩm" class="w-full h-full object-cover">
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
